import { Schema } from "@portabletext/schema";
import { ArraySchemaType } from "@sanity/types";
/**
 * @public
 */
type PortableTextBlock = PortableTextTextBlock | PortableTextObject;
/**
 * @public
 */
interface PortableTextTextBlock<TChild = PortableTextSpan | PortableTextObject> {
  _type: string;
  _key: string;
  children: TChild[];
  markDefs?: PortableTextObject[];
  listItem?: string;
  style?: string;
  level?: number;
}
/**
 * @public
 */
interface PortableTextSpan {
  _key: string;
  _type: 'span';
  text: string;
  marks?: string[];
}
/**
 * @public
 */
interface PortableTextObject {
  _type: string;
  _key: string;
  [other: string]: unknown;
}
/**
 * @public
 */
interface TypedObject {
  _type: string;
  _key?: string;
}
/**
 * @public
 */
interface ArbitraryTypedObject extends TypedObject {
  [key: string]: unknown;
}
/**
 * @public
 */
type HtmlParser = (html: string) => Document;
/**
 * @public
 */
type WhiteSpacePasteMode = 'preserve' | 'remove' | 'normalize';
/**
 * @public
 */
interface HtmlDeserializerOptions {
  keyGenerator?: () => string;
  rules?: DeserializerRule[];
  parseHtml?: HtmlParser;
  unstable_whitespaceOnPasteMode?: WhiteSpacePasteMode;
}
/**
 * @public
 */
interface DeserializerRule {
  deserialize: (el: Node, next: (elements: Node | Node[] | NodeList) => TypedObject | TypedObject[] | undefined, createBlock: (props: ArbitraryTypedObject) => {
    _type: string;
    block: ArbitraryTypedObject;
  }) => TypedObject | TypedObject[] | undefined;
}
/**
 * Block normalization options
 *
 * @public
 */
interface BlockNormalizationOptions {
  /**
   * Decorator names that are allowed within portable text blocks, eg `em`, `strong`
   */
  allowedDecorators?: string[];
  /**
   * Name of the portable text block type, if not `block`
   */
  blockTypeName?: string;
  /**
   * Custom key generator function
   */
  keyGenerator?: () => string;
}
/**
 * Normalizes a block by ensuring it has a `_key` property. If the block is a
 * portable text block, additional normalization is applied:
 *
 * - Ensures it has `children` and `markDefs` properties
 * - Ensures it has at least one child (adds an empty span if empty)
 * - Joins sibling spans that has the same marks
 * - Removes decorators that are not allowed according to the schema
 * - Removes marks that have no annotation definition
 *
 * @param node - The block to normalize
 * @param options - Options for normalization process. See {@link BlockNormalizationOptions}
 * @returns Normalized block
 * @public
 */
declare function normalizeBlock(node: TypedObject, options?: BlockNormalizationOptions): Omit<TypedObject | PortableTextTextBlock<TypedObject | PortableTextSpan>, '_key'> & {
  _key: string;
};
/**
 * Generate a random key of the given length
 *
 * @param length - Length of string to generate
 * @returns A string of the given length
 * @public
 */
declare function randomKey(length: number): string;
/**
 * Convert HTML to blocks respecting the block content type's schema
 *
 * @param html - The HTML to convert to blocks
 * @param schemaType - A compiled version of the schema type for the block content
 * @param options - Options for deserializing HTML to blocks
 * @returns Array of blocks
 * @public
 */
declare function htmlToBlocks(html: string, schemaType: ArraySchemaType | Schema, options?: HtmlDeserializerOptions): (TypedObject | PortableTextTextBlock)[];
export { type ArbitraryTypedObject, type BlockNormalizationOptions, type DeserializerRule, type HtmlDeserializerOptions, type HtmlParser, type PortableTextBlock, type PortableTextObject, type PortableTextSpan, type PortableTextTextBlock, type TypedObject, htmlToBlocks, normalizeBlock, randomKey };