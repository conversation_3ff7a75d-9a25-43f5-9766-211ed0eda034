{"version": 3, "file": "utils.js", "sources": ["../../src/apply/utils/getKeyOf.ts", "../../src/apply/utils/array.ts", "../../src/apply/patch/operations/array.ts", "../../src/apply/patch/operations/common.ts", "../../src/apply/patch/operations/number.ts", "../../src/apply/utils/hasOwn.ts", "../../src/apply/utils/isEmpty.ts", "../../src/apply/utils/omit.ts", "../../src/apply/patch/operations/object.ts", "../../src/apply/patch/operations/string.ts", "../../src/apply/patch/applyOp.ts", "../../src/apply/patch/applyNodePatch.ts", "../../src/apply/applyPatchMutation.ts", "../../src/apply/store/utils.ts"], "sourcesContent": ["export function keyOf(value: any): string | null {\n  return (\n    (value !== null &&\n      typeof value === 'object' &&\n      typeof value._key === 'string' &&\n      value._key) ||\n    null\n  )\n}\n", "import {isKeyedElement, type PathElement} from '../../path'\nimport {keyOf} from './getKeyOf'\n\nexport function findTargetIndex<T>(array: T[], pathSegment: PathElement) {\n  if (typeof pathSegment === 'number') {\n    return normalizeIndex(array.length, pathSegment)\n  }\n  if (isKeyedElement(pathSegment)) {\n    const idx = array.findIndex(value => keyOf(value) === pathSegment._key)\n    return idx === -1 ? null : idx\n  }\n  throw new Error(\n    `Expected path segment to be addressing a single array item either by numeric index or by '_key'. Instead saw ${JSON.stringify(\n      pathSegment,\n    )}`,\n  )\n}\n\nexport function getTargetIdx(position: 'before' | 'after', index: number) {\n  return position === 'before' ? index : index + 1\n}\n\n// normalizes the index according to the array length\n// returns null if the normalized index is out of bounds\nexport function normalizeIndex(length: number, index: number) {\n  if (length === 0 && (index === -1 || index === 0)) {\n    return 0\n  }\n  const normalized = index < 0 ? length + index : index\n  return normalized >= length || normalized < 0 ? null : normalized\n}\n\n// non-mutating splice\nexport function splice<T>(arr: T[], start: number, deleteCount: number): T[]\nexport function splice<T>(\n  arr: T[],\n  start: number,\n  deleteCount: number,\n  items: T[],\n): T[]\nexport function splice<T>(\n  arr: T[],\n  start: number,\n  deleteCount: number,\n  items?: T[],\n): T[] {\n  const copy = arr.slice()\n  copy.splice(start, deleteCount, ...(items || []))\n  return copy\n}\n", "import {\n  type InsertOp,\n  type KeyedPathElement,\n  type RelativePosition,\n  type RemoveOp,\n  type ReplaceOp,\n  type TruncateOp,\n  type UpsertOp,\n} from '../../../mutations/operations/types'\nimport {findTargetIndex, getTargetIdx, splice} from '../../utils/array'\n\nexport function insert<\n  O extends InsertOp<unknown[], RelativePosition, number | KeyedPathElement>,\n  CurrentValue extends unknown[],\n>(op: O, currentValue: CurrentValue) {\n  if (!Array.isArray(currentValue)) {\n    throw new TypeError('Cannot apply \"insert()\" on non-array value')\n  }\n\n  const index = findTargetIndex(currentValue, op.referenceItem)\n  if (index === null) {\n    throw new Error(`Found no matching array element to insert ${op.position}`)\n  }\n  // special case for empty arrays\n  if (currentValue.length === 0) {\n    return op.items\n  }\n  return splice(currentValue, getTargetIdx(op.position, index), 0, op.items)\n}\n\nexport function upsert<\n  O extends UpsertOp<unknown[], RelativePosition, number | KeyedPathElement>,\n  CurrentValue extends unknown[],\n>(op: O, currentValue: CurrentValue) {\n  if (!Array.isArray(currentValue)) {\n    throw new TypeError('Cannot apply \"upsert()\" on non-array value')\n  }\n\n  if (op.items.length === 0) {\n    return currentValue\n  }\n  const replaceItemsMap: number[] = []\n  const insertItems: unknown[] = []\n  op.items.forEach((itemToBeUpserted: any, i) => {\n    const existingIndex = currentValue.findIndex(\n      existingItem => (existingItem as any)?._key === itemToBeUpserted._key,\n    )\n    if (existingIndex >= 0) {\n      replaceItemsMap[existingIndex] = i\n    } else {\n      insertItems.push(itemToBeUpserted)\n    }\n  })\n\n  if (replaceItemsMap.length === 0 && insertItems.length == 0) {\n    return currentValue\n  }\n\n  const next = [...currentValue]\n  // Replace existing items\n  for (const i of replaceItemsMap) {\n    next[i] = op.items[replaceItemsMap[i]!]!\n  }\n\n  // Insert the items that doesn't exist\n  return insert(\n    {\n      type: 'insert',\n      items: insertItems,\n      referenceItem: op.referenceItem,\n      position: op.position,\n    },\n    next,\n  )\n}\n\nexport function replace<\n  O extends ReplaceOp<unknown[], number | KeyedPathElement>,\n  CurrentValue extends unknown[],\n>(op: O, currentValue: CurrentValue) {\n  if (!Array.isArray(currentValue)) {\n    throw new TypeError('Cannot apply \"replace()\" on non-array value')\n  }\n\n  const index = findTargetIndex(currentValue, op.referenceItem)\n  if (index === null) {\n    throw new Error(`Found no matching array element to replace`)\n  }\n  return splice(currentValue, index, op.items.length, op.items)\n}\nexport function remove<\n  O extends RemoveOp<number | KeyedPathElement>,\n  CurrentValue extends unknown[],\n>(op: O, currentValue: CurrentValue) {\n  if (!Array.isArray(currentValue)) {\n    throw new TypeError('Cannot apply \"remove()\" on non-array value')\n  }\n\n  const index = findTargetIndex(currentValue, op.referenceItem)\n  if (index === null) {\n    throw new Error(`Found no matching array element to replace`)\n  }\n  return splice(currentValue, index, 1, [])\n}\n\nexport function truncate<O extends TruncateOp, CurrentValue extends unknown[]>(\n  op: O,\n  currentValue: CurrentValue,\n) {\n  if (!Array.isArray(currentValue)) {\n    throw new TypeError('Cannot apply \"truncate()\" on non-array value')\n  }\n\n  return typeof op.endIndex === 'number'\n    ? currentValue\n        .slice(0, op.startIndex)\n        .concat(currentValue.slice(op.endIndex))\n    : currentValue.slice(0, op.startIndex)\n}\n", "import {\n  type SetIfMissingOp,\n  type SetOp,\n  type UnsetOp,\n} from '../../../mutations/operations/types'\n\nexport function set<O extends SetOp<any>, CurrentValue>(\n  op: O,\n  currentValue: CurrentValue,\n) {\n  return op.value\n}\n\nexport function setIfMissing<O extends SetIfMissingOp<any>, CurrentValue>(\n  op: O,\n  currentValue: CurrentValue,\n) {\n  return currentValue ?? op.value\n}\n\nexport function unset<O extends UnsetOp, CurrentValue>(op: O) {\n  return undefined\n}\n", "import {type DecOp, type IncOp} from '../../../mutations/operations/types'\n\nexport function inc<O extends IncOp<number>, CurrentValue extends number>(\n  op: O,\n  currentValue: CurrentValue,\n) {\n  if (typeof currentValue !== 'number') {\n    throw new TypeError('Cannot apply \"inc()\" on non-numeric value')\n  }\n\n  return currentValue + op.amount\n}\n\nexport function dec<O extends DecOp<number>, CurrentValue extends number>(\n  op: O,\n  currentValue: CurrentValue,\n) {\n  if (typeof currentValue !== 'number') {\n    throw new TypeError('Cannot apply \"dec()\" on non-numeric value')\n  }\n\n  return currentValue - op.amount\n}\n", "export const hasOwn = Object.prototype.hasOwnProperty.call.bind(\n  Object.prototype.hasOwnProperty,\n)\n", "import {hasOwn} from './hasOwn'\n\nexport function isEmpty(v: object) {\n  for (const key in v) {\n    if (hasOwn(v, key)) {\n      return false\n    }\n  }\n  return true\n}\n", "export function omit<T, K extends keyof T>(val: T, props: K[]): Omit<T, K> {\n  const copy = {...val}\n  for (const prop of props) {\n    delete copy[prop]\n  }\n  return copy\n}\n", "import {\n  type AssignOp,\n  type UnassignOp,\n} from '../../../mutations/operations/types'\nimport {isObject} from '../../../utils/isObject'\nimport {isEmpty} from '../../utils/isEmpty'\nimport {omit} from '../../utils/omit'\n\nexport function unassign<T extends object, K extends string[]>(\n  op: UnassignOp<K>,\n  currentValue: T,\n) {\n  if (!isObject(currentValue)) {\n    throw new TypeError('Cannot apply \"unassign()\" on non-object value')\n  }\n\n  return op.keys.length === 0\n    ? currentValue\n    : omit(currentValue, op.keys as any[])\n}\n\nexport function assign<T extends object>(op: AssignOp<T>, currentValue: T) {\n  if (!isObject(currentValue)) {\n    throw new TypeError('Cannot apply \"assign()\" on non-object value')\n  }\n\n  return isEmpty(op.value) ? currentValue : {...currentValue, ...op.value}\n}\n", "import {applyPatches, parsePatch} from '@sanity/diff-match-patch'\n\nimport {type DiffMatchPatchOp} from '../../../mutations/operations/types'\n\nexport function diffMatchPatch<\n  O extends DiffMatchPatchOp,\n  CurrentValue extends string,\n>(op: O, currentValue: CurrentValue) {\n  if (typeof currentValue !== 'string') {\n    throw new TypeError('Cannot apply \"diffMatchPatch()\" on non-string value')\n  }\n\n  return applyPatches(parsePatch(op.value), currentValue)[0]\n}\n", "import {\n  type AnyOp,\n  type ArrayOp,\n  type NumberOp,\n  type ObjectOp,\n  type Operation,\n  type StringOp,\n} from '../../mutations/operations/types'\nimport {type AnyArray} from '../../utils/typeUtils'\nimport * as operations from './operations'\nimport {type ApplyOp} from './typings/applyOp'\n\nexport function applyOp<const Op extends AnyOp, const CurrentValue>(\n  op: Op,\n  currentValue: CurrentValue,\n): ApplyOp<Op, CurrentValue>\nexport function applyOp<\n  const Op extends NumberOp,\n  const CurrentValue extends number,\n>(op: Op, currentValue: CurrentValue): ApplyOp<Op, CurrentValue>\nexport function applyOp<\n  const Op extends StringOp,\n  const CurrentValue extends string,\n>(op: Op, currentValue: CurrentValue): ApplyOp<Op, CurrentValue>\nexport function applyOp<\n  const Op extends ObjectOp,\n  const CurrentValue extends {[k in keyof any]: unknown},\n>(op: Op, currentValue: CurrentValue): ApplyOp<Op, CurrentValue>\nexport function applyOp<\n  const Op extends ArrayOp,\n  const CurrentValue extends AnyArray,\n>(op: Op, currentValue: CurrentValue): ApplyOp<Op, CurrentValue>\nexport function applyOp<const Op extends Operation, const CurrentValue>(\n  op: Op,\n  currentValue: CurrentValue,\n): ApplyOp<Op, CurrentValue> {\n  if (!(op.type in operations)) {\n    throw new Error(`Invalid operation type: \"${op.type}\"`)\n  }\n\n  // eslint-disable-next-line import/namespace\n  return (operations[op.type] as CallableFunction)(op, currentValue)\n}\n", "import {type Operation} from '../../mutations/operations/types'\nimport {type NodePatch, type NodePatchList} from '../../mutations/types'\nimport {isArrayElement, isPropertyElement, stringify} from '../../path'\nimport {isObject} from '../../utils/isObject'\nimport {type NormalizeReadOnlyArray} from '../../utils/typeUtils'\nimport {type KeyedPathElement, type Path} from '../'\nimport {findTargetIndex, splice} from '../utils/array'\nimport {applyOp} from './applyOp'\nimport {\n  type ApplyAtPath,\n  type ApplyNodePatch,\n  type ApplyPatches,\n} from './typings/applyNodePatch'\n\nexport function applyPatches<Patches extends NodePatchList, const Doc>(\n  patches: Patches,\n  document: Doc,\n): ApplyPatches<NormalizeReadOnlyArray<Patches>, Doc> {\n  return (patches as NodePatch[]).reduce(\n    (prev, patch) => applyNodePatch(patch, prev) as any,\n    document,\n  ) as any\n}\n\nexport function applyNodePatch<const Patch extends NodePatch, const Doc>(\n  patch: Patch,\n  document: Doc,\n): ApplyNodePatch<Patch, Doc> {\n  return applyAtPath(patch.path, patch.op, document) as any\n}\n\nfunction applyAtPath<P extends Path, O extends Operation, T>(\n  path: P,\n  op: O,\n  value: T,\n): ApplyAtPath<P, O, T> {\n  if (!isNonEmptyArray(path)) {\n    return applyOp(op as any, value) as any\n  }\n\n  const [head, ...tail] = path\n\n  if (isArrayElement(head) && Array.isArray(value)) {\n    return applyInArray(head, tail, op, value) as any\n  }\n\n  if (isPropertyElement(head) && isObject(value)) {\n    return applyInObject(head, tail, op, value) as any\n  }\n\n  throw new Error(\n    `Cannot apply operation of type \"${op.type}\" to path ${stringify(\n      path,\n    )} on ${typeof value} value`,\n  )\n}\n\nfunction applyInObject<Key extends keyof any, T extends {[key in Key]?: any}>(\n  head: Key,\n  tail: Path,\n  op: Operation,\n  object: T,\n) {\n  const current = object[head]\n\n  if (current === undefined && tail.length > 0) {\n    return object\n  }\n\n  // The patch targets the item at the index specified by \"head\"\n  // so forward it to the item\n  const patchedValue = applyAtPath(tail, op, current)\n\n  // If the result of applying it to the item yields the item back we assume it was\n  // a noop and don't modify our value. If we get a new value back, we return a\n  // new array with the modified item replaced\n  return patchedValue === current ? object : {...object, [head]: patchedValue}\n}\n\nfunction applyInArray<T>(\n  head: number | KeyedPathElement,\n  tail: Path,\n  op: Operation,\n  value: T[],\n) {\n  const index = findTargetIndex(value, head!)\n\n  if (index === null) {\n    // partial is default behavior for arrays\n    // the patch targets an index that is out of bounds\n    return value\n  }\n\n  // If the given selector could not be found, return as-is\n  if (index === -1) {\n    return value\n  }\n\n  const current = value[index]!\n\n  // The patch targets the item at the index specified by \"head\"\n  // so forward it to the item\n  const patchedItem = applyAtPath(tail, op, current)\n\n  // If the result of applying it to the item yields the item back we assume it was\n  // a noop and don't modify our value. If we get a new value back, we return a\n  // new array with the modified item replaced\n  return patchedItem === current\n    ? value\n    : splice(value, index, 1, [patchedItem])\n}\n\nfunction isNonEmptyArray<T>(a: T[] | readonly T[]): a is [T, ...T[]] {\n  return a.length > 0\n}\n", "import {type PatchMutation, type SanityDocumentBase} from '../mutations/types'\nimport {type NormalizeReadOnlyArray} from '../utils/typeUtils'\nimport {applyPatches} from './patch/applyNodePatch'\nimport {type ApplyPatches} from './patch/typings/applyNodePatch'\n\nexport type ApplyPatchMutation<\n  Mutation extends PatchMutation,\n  Doc extends SanityDocumentBase,\n> =\n  Mutation extends PatchMutation<infer Patches>\n    ? ApplyPatches<NormalizeReadOnlyArray<Patches>, Doc>\n    : Doc\n\nexport function applyPatchMutation<\n  const Mutation extends PatchMutation,\n  const Doc extends SanityDocumentBase,\n>(mutation: Mutation, document: Doc): ApplyPatchMutation<Mutation, Doc> {\n  if (\n    mutation.options?.ifRevision &&\n    document._rev !== mutation.options.ifRevision\n  ) {\n    throw new Error('Revision mismatch')\n  }\n  if (mutation.id !== document._id) {\n    throw new Error(\n      `Document id mismatch. Refusing to apply mutation for document with id=\"${mutation.id}\" on the given document with id=\"${document._id}\"`,\n    )\n  }\n  return applyPatches(mutation.patches, document) as any\n}\n", "import {type SanityDocumentBase} from '../../mutations/types'\nimport {type StoredDocument} from '../applyInIndex'\n\nexport function hasId(doc: SanityDocumentBase): doc is StoredDocument {\n  return '_id' in doc\n}\nexport function assignId<Doc extends SanityDocumentBase>(\n  doc: Doc,\n  generateId: () => string,\n): Doc & {_id: string} {\n  return hasId(doc) ? doc : {...doc, _id: generateId()}\n}\n"], "names": ["applyPatches"], "mappings": ";;;AAAO,SAAS,MAAM,OAA2B;AAE5C,SAAA,UAAU,QACT,OAAO,SAAU,YACjB,OAAO,MAAM,QAAS,YACtB,MAAM,QACR;AAEJ;ACLgB,SAAA,gBAAmB,OAAY,aAA0B;AACvE,MAAI,OAAO,eAAgB;AAClB,WAAA,eAAe,MAAM,QAAQ,WAAW;AAE7C,MAAA,eAAe,WAAW,GAAG;AACzB,UAAA,MAAM,MAAM,UAAU,CAAA,UAAS,MAAM,KAAK,MAAM,YAAY,IAAI;AAC/D,WAAA,QAAQ,KAAK,OAAO;AAAA,EAAA;AAE7B,QAAM,IAAI;AAAA,IACR,gHAAgH,KAAK;AAAA,MACnH;AAAA,IAAA,CACD;AAAA,EACH;AACF;AAEgB,SAAA,aAAa,UAA8B,OAAe;AACjE,SAAA,aAAa,WAAW,QAAQ,QAAQ;AACjD;AAIgB,SAAA,eAAe,QAAgB,OAAe;AAC5D,MAAI,WAAW,MAAM,UAAU,MAAM,UAAU;AACtC,WAAA;AAET,QAAM,aAAa,QAAQ,IAAI,SAAS,QAAQ;AAChD,SAAO,cAAc,UAAU,aAAa,IAAI,OAAO;AACzD;AAUO,SAAS,OACd,KACA,OACA,aACA,OACK;AACC,QAAA,OAAO,IAAI,MAAM;AACvB,SAAA,KAAK,OAAO,OAAO,aAAa,GAAI,SAAS,CAAG,CAAA,GACzC;AACT;ACtCgB,SAAA,OAGd,IAAO,cAA4B;AAC/B,MAAA,CAAC,MAAM,QAAQ,YAAY;AACvB,UAAA,IAAI,UAAU,4CAA4C;AAGlE,QAAM,QAAQ,gBAAgB,cAAc,GAAG,aAAa;AAC5D,MAAI,UAAU;AACZ,UAAM,IAAI,MAAM,6CAA6C,GAAG,QAAQ,EAAE;AAG5E,SAAI,aAAa,WAAW,IACnB,GAAG,QAEL,OAAO,cAAc,aAAa,GAAG,UAAU,KAAK,GAAG,GAAG,GAAG,KAAK;AAC3E;AAEgB,SAAA,OAGd,IAAO,cAA4B;AAC/B,MAAA,CAAC,MAAM,QAAQ,YAAY;AACvB,UAAA,IAAI,UAAU,4CAA4C;AAG9D,MAAA,GAAG,MAAM,WAAW;AACf,WAAA;AAET,QAAM,kBAA4B,IAC5B,cAAyB,CAAC;AAYhC,MAXA,GAAG,MAAM,QAAQ,CAAC,kBAAuB,MAAM;AAC7C,UAAM,gBAAgB,aAAa;AAAA,MACjC,CAAA,iBAAiB,cAAsB,SAAS,iBAAiB;AAAA,IACnE;AACI,qBAAiB,IACnB,gBAAgB,aAAa,IAAI,IAEjC,YAAY,KAAK,gBAAgB;AAAA,EAAA,CAEpC,GAEG,gBAAgB,WAAW,KAAK,YAAY,UAAU;AACjD,WAAA;AAGH,QAAA,OAAO,CAAC,GAAG,YAAY;AAE7B,aAAW,KAAK;AACd,SAAK,CAAC,IAAI,GAAG,MAAM,gBAAgB,CAAC,CAAE;AAIjC,SAAA;AAAA,IACL;AAAA,MAEE,OAAO;AAAA,MACP,eAAe,GAAG;AAAA,MAClB,UAAU,GAAG;AAAA,IACf;AAAA,IACA;AAAA,EACF;AACF;AAEgB,SAAA,QAGd,IAAO,cAA4B;AAC/B,MAAA,CAAC,MAAM,QAAQ,YAAY;AACvB,UAAA,IAAI,UAAU,6CAA6C;AAGnE,QAAM,QAAQ,gBAAgB,cAAc,GAAG,aAAa;AAC5D,MAAI,UAAU;AACN,UAAA,IAAI,MAAM,4CAA4C;AAE9D,SAAO,OAAO,cAAc,OAAO,GAAG,MAAM,QAAQ,GAAG,KAAK;AAC9D;AACgB,SAAA,OAGd,IAAO,cAA4B;AAC/B,MAAA,CAAC,MAAM,QAAQ,YAAY;AACvB,UAAA,IAAI,UAAU,4CAA4C;AAGlE,QAAM,QAAQ,gBAAgB,cAAc,GAAG,aAAa;AAC5D,MAAI,UAAU;AACN,UAAA,IAAI,MAAM,4CAA4C;AAE9D,SAAO,OAAO,cAAc,OAAO,GAAG,CAAA,CAAE;AAC1C;AAEgB,SAAA,SACd,IACA,cACA;AACI,MAAA,CAAC,MAAM,QAAQ,YAAY;AACvB,UAAA,IAAI,UAAU,8CAA8C;AAG7D,SAAA,OAAO,GAAG,YAAa,WAC1B,aACG,MAAM,GAAG,GAAG,UAAU,EACtB,OAAO,aAAa,MAAM,GAAG,QAAQ,CAAC,IACzC,aAAa,MAAM,GAAG,GAAG,UAAU;AACzC;AChHgB,SAAA,IACd,IACA,cACA;AACA,SAAO,GAAG;AACZ;AAEgB,SAAA,aACd,IACA,cACA;AACA,SAAO,gBAAgB,GAAG;AAC5B;AAEO,SAAS,MAAuC,IAAO;AAE9D;ACpBgB,SAAA,IACd,IACA,cACA;AACA,MAAI,OAAO,gBAAiB;AACpB,UAAA,IAAI,UAAU,2CAA2C;AAGjE,SAAO,eAAe,GAAG;AAC3B;AAEgB,SAAA,IACd,IACA,cACA;AACA,MAAI,OAAO,gBAAiB;AACpB,UAAA,IAAI,UAAU,2CAA2C;AAGjE,SAAO,eAAe,GAAG;AAC3B;ACtBO,MAAM,SAAS,OAAO,UAAU,eAAe,KAAK;AAAA,EACzD,OAAO,UAAU;AACnB;ACAO,SAAS,QAAQ,GAAW;AACjC,aAAW,OAAO;AACZ,QAAA,OAAO,GAAG,GAAG;AACR,aAAA;AAGJ,SAAA;AACT;ACTgB,SAAA,KAA2B,KAAQ,OAAwB;AACnE,QAAA,OAAO,EAAC,GAAG,IAAG;AACpB,aAAW,QAAQ;AACjB,WAAO,KAAK,IAAI;AAEX,SAAA;AACT;ACEgB,SAAA,SACd,IACA,cACA;AACI,MAAA,CAAC,SAAS,YAAY;AAClB,UAAA,IAAI,UAAU,+CAA+C;AAG9D,SAAA,GAAG,KAAK,WAAW,IACtB,eACA,KAAK,cAAc,GAAG,IAAa;AACzC;AAEgB,SAAA,OAAyB,IAAiB,cAAiB;AACrE,MAAA,CAAC,SAAS,YAAY;AAClB,UAAA,IAAI,UAAU,6CAA6C;AAG5D,SAAA,QAAQ,GAAG,KAAK,IAAI,eAAe,EAAC,GAAG,cAAc,GAAG,GAAG,MAAK;AACzE;ACvBgB,SAAA,eAGd,IAAO,cAA4B;AACnC,MAAI,OAAO,gBAAiB;AACpB,UAAA,IAAI,UAAU,qDAAqD;AAG3E,SAAOA,eAAa,WAAW,GAAG,KAAK,GAAG,YAAY,EAAE,CAAC;AAC3D;;;;;;;;;;;;;;;;;ACmBgB,SAAA,QACd,IACA,cAC2B;AACvB,MAAA,EAAE,GAAG,QAAQ;AACf,UAAM,IAAI,MAAM,4BAA4B,GAAG,IAAI,GAAG;AAIxD,SAAQ,WAAW,GAAG,IAAI,EAAuB,IAAI,YAAY;AACnE;AC5BgB,SAAA,aACd,SACA,UACoD;AACpD,SAAQ,QAAwB;AAAA,IAC9B,CAAC,MAAM,UAAU,eAAe,OAAO,IAAI;AAAA,IAC3C;AAAA,EACF;AACF;AAEgB,SAAA,eACd,OACA,UAC4B;AAC5B,SAAO,YAAY,MAAM,MAAM,MAAM,IAAI,QAAQ;AACnD;AAEA,SAAS,YACP,MACA,IACA,OACsB;AAClB,MAAA,CAAC,gBAAgB,IAAI;AAChB,WAAA,QAAQ,IAAW,KAAK;AAGjC,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AAExB,MAAI,eAAe,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC7C,WAAO,aAAa,MAAM,MAAM,IAAI,KAAK;AAG3C,MAAI,kBAAkB,IAAI,KAAK,SAAS,KAAK;AAC3C,WAAO,cAAc,MAAM,MAAM,IAAI,KAAK;AAG5C,QAAM,IAAI;AAAA,IACR,mCAAmC,GAAG,IAAI,aAAa;AAAA,MACrD;AAAA,IAAA,CACD,OAAO,OAAO,KAAK;AAAA,EACtB;AACF;AAEA,SAAS,cACP,MACA,MACA,IACA,QACA;AACM,QAAA,UAAU,OAAO,IAAI;AAEvB,MAAA,YAAY,UAAa,KAAK,SAAS;AAClC,WAAA;AAKT,QAAM,eAAe,YAAY,MAAM,IAAI,OAAO;AAK3C,SAAA,iBAAiB,UAAU,SAAS,EAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,aAAY;AAC7E;AAEA,SAAS,aACP,MACA,MACA,IACA,OACA;AACM,QAAA,QAAQ,gBAAgB,OAAO,IAAK;AAEtC,MAAA,UAAU,QAOV,UAAU;AACL,WAAA;AAGH,QAAA,UAAU,MAAM,KAAK,GAIrB,cAAc,YAAY,MAAM,IAAI,OAAO;AAK1C,SAAA,gBAAgB,UACnB,QACA,OAAO,OAAO,OAAO,GAAG,CAAC,WAAW,CAAC;AAC3C;AAEA,SAAS,gBAAmB,GAAyC;AACnE,SAAO,EAAE,SAAS;AACpB;ACrGgB,SAAA,mBAGd,UAAoB,UAAkD;AACtE,MACE,SAAS,SAAS,cAClB,SAAS,SAAS,SAAS,QAAQ;AAE7B,UAAA,IAAI,MAAM,mBAAmB;AAEjC,MAAA,SAAS,OAAO,SAAS;AAC3B,UAAM,IAAI;AAAA,MACR,0EAA0E,SAAS,EAAE,oCAAoC,SAAS,GAAG;AAAA,IACvI;AAEK,SAAA,aAAa,SAAS,SAAS,QAAQ;AAChD;AC1BO,SAAS,MAAM,KAAgD;AACpE,SAAO,SAAS;AAClB;AACgB,SAAA,SACd,KACA,YACqB;AACd,SAAA,MAAM,GAAG,IAAI,MAAM,EAAC,GAAG,KAAK,KAAK,aAAY;AACtD;"}