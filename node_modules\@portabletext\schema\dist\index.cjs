"use strict";
Object.defineProperty(exports, "__esModule", { value: !0 });
function compileSchema(definition) {
  const styles = (definition.styles ?? []).map((style) => ({
    ...style,
    value: style.name
  }));
  return {
    block: {
      name: definition.block?.name ?? "block"
    },
    span: {
      name: "span"
    },
    styles: styles.some((style) => style.value === "normal") ? styles : [{ value: "normal", name: "normal", title: "Normal" }, ...styles],
    lists: (definition.lists ?? []).map((list) => ({
      ...list,
      value: list.name
    })),
    decorators: (definition.decorators ?? []).map((decorator) => ({
      ...decorator,
      value: decorator.name
    })),
    annotations: (definition.annotations ?? []).map((annotation) => ({
      ...annotation,
      fields: annotation.fields ?? []
    })),
    blockObjects: (definition.blockObjects ?? []).map((blockObject) => ({
      ...blockObject,
      fields: blockObject.fields ?? []
    })),
    inlineObjects: (definition.inlineObjects ?? []).map((inlineObject) => ({
      ...inlineObject,
      fields: inlineObject.fields ?? []
    }))
  };
}
function defineSchema(definition) {
  return definition;
}
exports.compileSchema = compileSchema;
exports.defineSchema = defineSchema;
//# sourceMappingURL=index.cjs.map
