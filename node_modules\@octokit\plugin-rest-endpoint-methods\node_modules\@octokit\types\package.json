{"name": "@octokit/types", "version": "12.6.0", "publishConfig": {"access": "public"}, "description": "Shared TypeScript definitions for Octokit projects", "dependencies": {"@octokit/openapi-types": "^20.0.0"}, "repository": "github:octokit/types.ts", "keywords": ["github", "api", "sdk", "toolkit", "typescript"], "author": "<PERSON> (https://twitter.com/gr2m)", "license": "MIT", "devDependencies": {"@octokit/tsconfig": "^2.0.0", "@types/node": ">= 8", "github-openapi-graphql-query": "^4.0.0", "handlebars": "^4.7.6", "json-schema-to-typescript": "^13.0.0", "lodash.set": "^4.3.2", "npm-run-all2": "^6.0.0", "pascal-case": "^3.1.1", "prettier": "^3.0.0", "semantic-release": "^23.0.0", "semantic-release-plugin-update-version-in-files": "^1.0.0", "sort-keys": "^5.0.0", "string-to-jsdoc-comment": "^1.0.0", "typedoc": "^0.25.0", "typescript": "^5.0.0"}, "octokit": {"openapi-version": "14.0.0"}, "files": ["dist-types/**"], "types": "dist-types/index.d.ts", "sideEffects": false}