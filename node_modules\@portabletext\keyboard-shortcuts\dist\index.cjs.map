{"version": 3, "file": "index.cjs", "sources": ["../src/is-apple.ts", "../src/is-keyboard-shortcut.ts", "../src/keyboard-shortcuts.ts", "../src/common-shortcuts.ts"], "sourcesContent": ["export const IS_APPLE =\n  typeof window !== 'undefined' &&\n  /Mac|iPod|iPhone|iPad/.test(window.navigator.userAgent)\n", "import type {KeyboardEventDefinition} from './keyboard-event-definition'\n\n/**\n * Checks if a keyboard event matches a keyboard shortcut definition.\n */\nexport function isKeyboardShortcut<\n  TKeyboardEvent extends Pick<\n    KeyboardEvent,\n    'key' | 'code' | 'shiftKey' | 'altKey' | 'ctrlKey' | 'metaKey'\n  > = Pick<\n    KeyboardEvent,\n    'key' | 'code' | 'shiftKey' | 'altKey' | 'ctrlKey' | 'metaKey'\n  >,\n>(definition: KeyboardEventDefinition, event: TKeyboardEvent) {\n  if (!isCorrectModifiers(definition, event)) {\n    return false\n  }\n\n  if (\n    definition.code !== undefined &&\n    definition.code.toLowerCase() === event.code.toLowerCase()\n  ) {\n    return true\n  }\n\n  return (\n    definition.key !== undefined &&\n    definition.key.toLowerCase() === event.key.toLowerCase()\n  )\n}\n\nfunction isCorrectModifiers<\n  TKeyboardEvent extends Pick<\n    KeyboardEvent,\n    'key' | 'code' | 'shiftKey' | 'altKey' | 'ctrlKey' | 'metaKey'\n  > = Pick<\n    KeyboardEvent,\n    'key' | 'code' | 'shiftKey' | 'altKey' | 'ctrlKey' | 'metaKey'\n  >,\n>(definition: KeyboardEventDefinition, event: TKeyboardEvent) {\n  return (\n    (definition.ctrl === event.ctrlKey || definition.ctrl === undefined) &&\n    (definition.meta === event.metaKey || definition.meta === undefined) &&\n    (definition.shift === event.shiftKey || definition.shift === undefined) &&\n    (definition.alt === event.altKey || definition.alt === undefined)\n  )\n}\n", "import {IS_APPLE} from './is-apple'\nimport {isKeyboardShortcut} from './is-keyboard-shortcut'\nimport type {KeyboardEventDefinition} from './keyboard-event-definition'\n\n/**\n * @beta\n * Definition of a keyboard shortcut with platform-specific keyboard event\n * definitions.\n *\n * `default` keyboard event definitions are required while the `apple`\n * keyboard event definitions are optional.\n *\n * @example\n * ```typescript\n * const boldShortcut: KeyboardShortcutDefinition = {\n *   default: [{\n *     key: 'B',\n *     alt: false,\n *     ctrl: true,\n *     meta: false,\n *     shift: false,\n *   }],\n *   apple: [{\n *     key: 'B',\n *     alt: false,\n *     ctrl: false,\n *     meta: true,\n *     shift: false,\n *   }],\n * }\n * ```\n */\nexport type KeyboardShortcutDefinition = {\n  default: ReadonlyArray<KeyboardEventDefinition>\n  apple?: ReadonlyArray<KeyboardEventDefinition>\n}\n\n/**\n * @beta\n * A resolved keyboard shortcut for the current platform that has been\n * processed by `createKeyboardShortcut(...)` to select the appropriate\n * platform-specific key combination. The `guard` function determines if the\n * shortcut applies to the current `KeyboardEvent`, while `keys` contains the\n * display-friendly key combination for the current platform.\n */\nexport type KeyboardShortcut<\n  TKeyboardEvent extends Pick<\n    KeyboardEvent,\n    'key' | 'code' | 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey'\n  > = Pick<\n    KeyboardEvent,\n    'key' | 'code' | 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey'\n  >,\n> = {\n  guard: (event: TKeyboardEvent) => boolean\n  keys: ReadonlyArray<string>\n}\n\n/**\n * @beta\n * Creates a `KeyboardShortcut` from a `KeyboardShortcutDefinition`.\n *\n * `default` keyboard event definitions are required while the `apple`\n * keyboard event definitions are optional.\n *\n * @example\n * ```typescript\n * const shortcut = createKeyboardShortcut({\n *   default: [{\n *     key: 'B',\n *     alt: false,\n *     ctrl: true,\n *     meta: false,\n *     shift: false,\n *   }],\n *   apple: [{\n *     key: 'B',\n *     alt: false,\n *     ctrl: false,\n *     meta: true,\n *     shift: false,\n *   }],\n * })\n * ```\n */\nexport function createKeyboardShortcut<\n  TKeyboardEvent extends Pick<\n    KeyboardEvent,\n    'key' | 'code' | 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey'\n  > = Pick<\n    KeyboardEvent,\n    'key' | 'code' | 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey'\n  >,\n>(definition: KeyboardShortcutDefinition): KeyboardShortcut<TKeyboardEvent> {\n  if (IS_APPLE) {\n    const appleDefinition = definition.apple ?? definition.default\n    const firstDefinition = appleDefinition.at(0)\n\n    return {\n      guard: (event) =>\n        appleDefinition.some((definition) =>\n          isKeyboardShortcut(definition, event),\n        ),\n      keys: [\n        ...(firstDefinition?.meta ? ['⌘'] : []),\n        ...(firstDefinition?.ctrl ? ['Ctrl'] : []),\n        ...(firstDefinition?.alt ? ['Option'] : []),\n        ...(firstDefinition?.shift ? ['Shift'] : []),\n        ...(firstDefinition?.key !== undefined\n          ? [firstDefinition.key]\n          : firstDefinition?.code !== undefined\n            ? [firstDefinition.code]\n            : []),\n      ],\n    }\n  }\n\n  const firstDefinition = definition.default.at(0)\n\n  return {\n    guard: (event) =>\n      definition.default.some((definition) =>\n        isKeyboardShortcut(definition, event),\n      ),\n    keys: [\n      ...(firstDefinition?.meta ? ['Meta'] : []),\n      ...(firstDefinition?.ctrl ? ['Ctrl'] : []),\n      ...(firstDefinition?.alt ? ['Alt'] : []),\n      ...(firstDefinition?.shift ? ['Shift'] : []),\n      ...(firstDefinition?.key !== undefined\n        ? [firstDefinition.key]\n        : firstDefinition?.code !== undefined\n          ? [firstDefinition.code]\n          : []),\n    ],\n  }\n}\n", "import {createKeyboardShortcut} from './keyboard-shortcuts'\n\n/**\n * @beta\n */\nexport const bold = createKeyboardShortcut({\n  default: [\n    {\n      key: 'B',\n      alt: false,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: 'B',\n      alt: false,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const italic = createKeyboardShortcut({\n  default: [\n    {\n      key: 'I',\n      alt: false,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: 'I',\n      alt: false,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const code = createKeyboardShortcut({\n  default: [\n    {\n      key: \"'\",\n      alt: false,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: \"'\",\n      alt: false,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const underline = createKeyboardShortcut({\n  default: [\n    {\n      key: 'U',\n      alt: false,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: 'U',\n      alt: false,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const strikeThrough = createKeyboardShortcut({\n  default: [\n    {\n      key: 'X',\n      alt: false,\n      ctrl: true,\n      meta: false,\n      shift: true,\n    },\n  ],\n  apple: [\n    {\n      key: 'X',\n      alt: false,\n      ctrl: false,\n      meta: true,\n      shift: true,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const link = createKeyboardShortcut({\n  default: [\n    {\n      key: 'K',\n      alt: false,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: 'K',\n      alt: false,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const normal = createKeyboardShortcut({\n  default: [\n    {\n      key: '0',\n      code: 'Digit0',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n    {\n      key: '0',\n      code: 'Numpad0',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: '0',\n      code: 'Digit0',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n    {\n      key: '0',\n      code: 'Numpad0',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const h1 = createKeyboardShortcut({\n  default: [\n    {\n      key: '1',\n      code: 'Digit1',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n    {\n      key: '1',\n      code: 'Numpad1',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: '1',\n      code: 'Digit1',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n    {\n      key: '1',\n      code: 'Numpad1',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const h2 = createKeyboardShortcut({\n  default: [\n    {\n      key: '2',\n      code: 'Digit2',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n    {\n      key: '2',\n      code: 'Numpad2',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: '2',\n      code: 'Digit2',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n    {\n      key: '2',\n      code: 'Numpad2',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const h3 = createKeyboardShortcut({\n  default: [\n    {\n      key: '3',\n      code: 'Digit3',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n    {\n      key: '3',\n      code: 'Numpad3',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: '3',\n      code: 'Digit3',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n    {\n      key: '3',\n      code: 'Numpad3',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const h4 = createKeyboardShortcut({\n  default: [\n    {\n      key: '4',\n      code: 'Digit4',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n    {\n      key: '4',\n      code: 'Numpad4',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: '4',\n      code: 'Digit4',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n    {\n      key: '4',\n      code: 'Numpad4',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const h5 = createKeyboardShortcut({\n  default: [\n    {\n      key: '5',\n      code: 'Digit5',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n    {\n      key: '5',\n      code: 'Numpad5',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: '5',\n      code: 'Digit5',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n    {\n      key: '5',\n      code: 'Numpad5',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const h6 = createKeyboardShortcut({\n  default: [\n    {\n      key: '6',\n      code: 'Digit6',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n    {\n      key: '6',\n      code: 'Numpad6',\n      alt: true,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: '6',\n      code: 'Digit6',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n    {\n      key: '6',\n      code: 'Numpad6',\n      alt: true,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const blockquote = createKeyboardShortcut({\n  default: [\n    {\n      key: 'Q',\n      alt: false,\n      ctrl: true,\n      meta: false,\n      shift: true,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const undo = createKeyboardShortcut({\n  default: [\n    {\n      key: 'Z',\n      alt: false,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n  ],\n  apple: [\n    {\n      key: 'Z',\n      alt: false,\n      ctrl: false,\n      meta: true,\n      shift: false,\n    },\n  ],\n})\n\n/**\n * @beta\n */\nexport const redo = createKeyboardShortcut({\n  default: [\n    {\n      key: 'Y',\n      alt: false,\n      ctrl: true,\n      meta: false,\n      shift: false,\n    },\n    {\n      key: 'Z',\n      alt: false,\n      ctrl: true,\n      meta: false,\n      shift: true,\n    },\n  ],\n  apple: [\n    {\n      key: 'Z',\n      alt: false,\n      ctrl: false,\n      meta: true,\n      shift: true,\n    },\n  ],\n})\n"], "names": ["IS_APPLE", "window", "test", "navigator", "userAgent", "isKeyboardShortcut", "definition", "event", "isCorrectModifiers", "code", "undefined", "toLowerCase", "key", "ctrl", "ctrl<PERSON>ey", "meta", "metaKey", "shift", "shift<PERSON>ey", "alt", "altKey", "createKeyboardShortcut", "appleDefinition", "apple", "default", "firstDefinition", "at", "guard", "some", "keys", "bold", "italic", "underline", "strikeThrough", "link", "normal", "h1", "h2", "h3", "h4", "h5", "h6", "blockquote", "undo", "redo"], "mappings": ";;AAAO,MAAMA,WACX,OAAOC,SAAW,OAClB,uBAAuBC,KAAKD,OAAOE,UAAUC,SAAS;ACGjD,SAASC,mBAQdC,YAAqCC,OAAuB;AAC5D,SAAKC,mBAAmBF,YAAYC,KAAK,IAKvCD,WAAWG,SAASC,UACpBJ,WAAWG,KAAKE,YAAAA,MAAkBJ,MAAME,KAAKE,gBAEtC,KAIPL,WAAWM,QAAQF,UACnBJ,WAAWM,IAAID,YAAAA,MAAkBJ,MAAMK,IAAID,YAAAA,IAZpC;AAcX;AAEA,SAASH,mBAQPF,YAAqCC,OAAuB;AAC5D,UACGD,WAAWO,SAASN,MAAMO,WAAWR,WAAWO,SAASH,YACzDJ,WAAWS,SAASR,MAAMS,WAAWV,WAAWS,SAASL,YACzDJ,WAAWW,UAAUV,MAAMW,YAAYZ,WAAWW,UAAUP,YAC5DJ,WAAWa,QAAQZ,MAAMa,UAAUd,WAAWa,QAAQT;AAE3D;ACuCO,SAASW,uBAQdf,YAA0E;AAC1E,MAAIN,UAAU;AACZ,UAAMsB,kBAAkBhB,WAAWiB,SAASjB,WAAWkB,SACjDC,mBAAkBH,gBAAgBI,GAAG,CAAC;AAE5C,WAAO;AAAA,MACLC,OAAQpB,WACNe,gBAAgBM,KAAMtB,CAAAA,gBACpBD,mBAAmBC,aAAYC,KAAK,CACtC;AAAA,MACFsB,MAAM,CACJ,GAAIJ,kBAAiBV,OAAO,CAAC,QAAG,IAAI,CAAA,GACpC,GAAIU,kBAAiBZ,OAAO,CAAC,MAAM,IAAI,IACvC,GAAIY,kBAAiBN,MAAM,CAAC,QAAQ,IAAI,CAAA,GACxC,GAAIM,kBAAiBR,QAAQ,CAAC,OAAO,IAAI,CAAA,GACzC,GAAIQ,kBAAiBb,QAAQF,SACzB,CAACe,iBAAgBb,GAAG,IACpBa,kBAAiBhB,SAASC,SACxB,CAACe,iBAAgBhB,IAAI,IACrB,CAAA,CAAG;AAAA,IAAA;AAAA,EAGf;AAEA,QAAMgB,kBAAkBnB,WAAWkB,QAAQE,GAAG,CAAC;AAE/C,SAAO;AAAA,IACLC,OAAQpB,CAAAA,UACND,WAAWkB,QAAQI,KAAMtB,CAAAA,gBACvBD,mBAAmBC,aAAYC,KAAK,CACtC;AAAA,IACFsB,MAAM,CACJ,GAAIJ,iBAAiBV,OAAO,CAAC,MAAM,IAAI,CAAA,GACvC,GAAIU,iBAAiBZ,OAAO,CAAC,MAAM,IAAI,IACvC,GAAIY,iBAAiBN,MAAM,CAAC,KAAK,IAAI,CAAA,GACrC,GAAIM,iBAAiBR,QAAQ,CAAC,OAAO,IAAI,CAAA,GACzC,GAAIQ,iBAAiBb,QAAQF,SACzB,CAACe,gBAAgBb,GAAG,IACpBa,iBAAiBhB,SAASC,SACxB,CAACe,gBAAgBhB,IAAI,IACrB,CAAA,CAAG;AAAA,EAAA;AAGf;ACnIO,MAAMqB,OAAOT,uBAAuB;AAAA,EACzCG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYc,SAASV,uBAAuB;AAAA,EAC3CG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYR,OAAOY,uBAAuB;AAAA,EACzCG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYe,YAAYX,uBAAuB;AAAA,EAC9CG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYgB,gBAAgBZ,uBAAuB;AAAA,EAClDG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYiB,OAAOb,uBAAuB;AAAA,EACzCG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYkB,SAASd,uBAAuB;AAAA,EAC3CG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYmB,KAAKf,uBAAuB;AAAA,EACvCG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYoB,KAAKhB,uBAAuB;AAAA,EACvCG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYqB,KAAKjB,uBAAuB;AAAA,EACvCG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYsB,KAAKlB,uBAAuB;AAAA,EACvCG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYuB,KAAKnB,uBAAuB;AAAA,EACvCG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYwB,KAAKpB,uBAAuB;AAAA,EACvCG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLH,MAAM;AAAA,IACNU,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKYyB,aAAarB,uBAAuB;AAAA,EAC/CG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKY0B,OAAOtB,uBAAuB;AAAA,EACzCG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC,GAKY2B,OAAOvB,uBAAuB;AAAA,EACzCG,SAAS,CACP;AAAA,IACEZ,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,GAET;AAAA,IACEL,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAAA,EAEHM,OAAO,CACL;AAAA,IACEX,KAAK;AAAA,IACLO,KAAK;AAAA,IACLN,MAAM;AAAA,IACNE,MAAM;AAAA,IACNE,OAAO;AAAA,EAAA,CACR;AAEL,CAAC;;;;;;;;;;;;;;;;;;"}