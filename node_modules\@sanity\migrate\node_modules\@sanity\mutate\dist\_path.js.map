{"version": 3, "file": "_path.js", "sources": ["../src/path/utils/normalize.ts"], "sourcesContent": ["import {parse} from '../parser/parse'\nimport {type Path} from '../types'\n\nexport function normalize(path: string | Readonly<Path>): Readonly<Path> {\n  return typeof path === 'string' ? parse(path) : path\n}\n"], "names": [], "mappings": ";;;AAGO,SAAS,UAAU,MAA+C;AACvE,SAAO,OAAO,QAAS,WAAW,MAAM,IAAI,IAAI;AAClD;"}