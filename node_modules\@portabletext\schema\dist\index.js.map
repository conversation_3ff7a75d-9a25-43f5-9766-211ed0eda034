{"version": 3, "file": "index.js", "sources": ["../src/compile-schema.ts", "../src/define-schema.ts"], "sourcesContent": ["import type {SchemaDefinition} from './define-schema'\nimport type {Schema} from './schema'\n\n/**\n * @public\n */\nexport function compileSchema(definition: SchemaDefinition): Schema {\n  const styles = (definition.styles ?? []).map((style) => ({\n    ...style,\n    value: style.name,\n  }))\n\n  return {\n    block: {\n      name: definition.block?.name ?? 'block',\n    },\n    span: {\n      name: 'span',\n    },\n    styles: !styles.some((style) => style.value === 'normal')\n      ? [{value: 'normal', name: 'normal', title: 'Normal'}, ...styles]\n      : styles,\n    lists: (definition.lists ?? []).map((list) => ({\n      ...list,\n      value: list.name,\n    })),\n    decorators: (definition.decorators ?? []).map((decorator) => ({\n      ...decorator,\n      value: decorator.name,\n    })),\n    annotations: (definition.annotations ?? []).map((annotation) => ({\n      ...annotation,\n      fields: annotation.fields ?? [],\n    })),\n    blockObjects: (definition.blockObjects ?? []).map((blockObject) => ({\n      ...blockObject,\n      fields: blockObject.fields ?? [],\n    })),\n    inlineObjects: (definition.inlineObjects ?? []).map((inlineObject) => ({\n      ...inlineObject,\n      fields: inlineObject.fields ?? [],\n    })),\n  }\n}\n", "import type {BaseDefinition, FieldDefinition} from './schema'\n\n/**\n * @public\n */\nexport type SchemaDefinition = {\n  block?: {\n    name: string\n  }\n  styles?: ReadonlyArray<StyleDefinition>\n  lists?: ReadonlyArray<ListDefinition>\n  decorators?: ReadonlyArray<DecoratorDefinition>\n  annotations?: ReadonlyArray<AnnotationDefinition>\n  blockObjects?: ReadonlyArray<BlockObjectDefinition>\n  inlineObjects?: ReadonlyArray<InlineObjectDefinition>\n}\n\n/**\n * @public\n * A helper wrapper that adds editor support, such as autocomplete and type checking, for a schema definition.\n * @example\n * ```ts\n * import { defineSchema } from '@portabletext/editor'\n *\n * const schemaDefinition = defineSchema({\n *  decorators: [{name: 'strong'}, {name: 'em'}, {name: 'underline'}],\n *  annotations: [{name: 'link'}],\n *  styles: [\n *    {name: 'normal'},\n *    {name: 'h1'},\n *    {name: 'h2'},\n *    {name: 'h3'},\n *    {name: 'blockquote'},\n *  ],\n *  lists: [],\n *  inlineObjects: [],\n *  blockObjects: [],\n * }\n * ```\n */\nexport function defineSchema<const TSchemaDefinition extends SchemaDefinition>(\n  definition: TSchemaDefinition,\n): TSchemaDefinition {\n  return definition\n}\n\n/**\n * @public\n */\nexport type StyleDefinition<\n  TBaseDefinition extends BaseDefinition = BaseDefinition,\n> = TBaseDefinition\n\n/**\n * @public\n */\nexport type ListDefinition<\n  TBaseDefinition extends BaseDefinition = BaseDefinition,\n> = TBaseDefinition\n\n/**\n * @public\n */\nexport type DecoratorDefinition<\n  TBaseDefinition extends BaseDefinition = BaseDefinition,\n> = TBaseDefinition\n\n/**\n * @public\n */\nexport type AnnotationDefinition<\n  TBaseDefinition extends BaseDefinition = BaseDefinition,\n> = TBaseDefinition & {\n  fields?: ReadonlyArray<FieldDefinition>\n}\n\n/**\n * @public\n */\nexport type BlockObjectDefinition<\n  TBaseDefinition extends BaseDefinition = BaseDefinition,\n> = TBaseDefinition & {\n  fields?: ReadonlyArray<FieldDefinition>\n}\n\n/**\n * @public\n */\nexport type InlineObjectDefinition<\n  TBaseDefinition extends BaseDefinition = BaseDefinition,\n> = TBaseDefinition & {\n  fields?: ReadonlyArray<FieldDefinition>\n}\n"], "names": [], "mappings": "AAMO,SAAS,cAAc,YAAsC;AAClE,QAAM,UAAU,WAAW,UAAU,CAAA,GAAI,IAAI,CAAC,WAAW;AAAA,IACvD,GAAG;AAAA,IACH,OAAO,MAAM;AAAA,EAAA,EACb;AAEF,SAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM,WAAW,OAAO,QAAQ;AAAA,IAAA;AAAA,IAElC,MAAM;AAAA,MACJ,MAAM;AAAA,IAAA;AAAA,IAER,QAAS,OAAO,KAAK,CAAC,UAAU,MAAM,UAAU,QAAQ,IAEpD,SADA,CAAC,EAAC,OAAO,UAAU,MAAM,UAAU,OAAO,SAAA,GAAW,GAAG,MAAM;AAAA,IAElE,QAAQ,WAAW,SAAS,CAAA,GAAI,IAAI,CAAC,UAAU;AAAA,MAC7C,GAAG;AAAA,MACH,OAAO,KAAK;AAAA,IAAA,EACZ;AAAA,IACF,aAAa,WAAW,cAAc,CAAA,GAAI,IAAI,CAAC,eAAe;AAAA,MAC5D,GAAG;AAAA,MACH,OAAO,UAAU;AAAA,IAAA,EACjB;AAAA,IACF,cAAc,WAAW,eAAe,CAAA,GAAI,IAAI,CAAC,gBAAgB;AAAA,MAC/D,GAAG;AAAA,MACH,QAAQ,WAAW,UAAU,CAAA;AAAA,IAAC,EAC9B;AAAA,IACF,eAAe,WAAW,gBAAgB,CAAA,GAAI,IAAI,CAAC,iBAAiB;AAAA,MAClE,GAAG;AAAA,MACH,QAAQ,YAAY,UAAU,CAAA;AAAA,IAAC,EAC/B;AAAA,IACF,gBAAgB,WAAW,iBAAiB,CAAA,GAAI,IAAI,CAAC,kBAAkB;AAAA,MACrE,GAAG;AAAA,MACH,QAAQ,aAAa,UAAU,CAAA;AAAA,IAAC,EAChC;AAAA,EAAA;AAEN;ACHO,SAAS,aACd,YACmB;AACnB,SAAO;AACT;"}