{"name": "@octokit/plugin-rest-endpoint-methods", "version": "10.4.1", "description": "Octokit plugin adding one method for all of api.github.com REST API endpoints", "repository": "github:octokit/plugin-rest-endpoint-methods.js", "keywords": ["github", "api", "sdk", "toolkit"], "author": "<PERSON> (https://twitter.com/gr2m)", "license": "MIT", "dependencies": {"@octokit/types": "^12.6.0"}, "devDependencies": {"@octokit/core": "^5.0.0", "@octokit/tsconfig": "^2.0.0", "@types/fetch-mock": "^7.3.1", "@types/jest": "^29.0.0", "@types/node": "^20.0.0", "@types/sinon": "^17.0.0", "esbuild": "^0.20.0", "fetch-mock": "npm:@gr2m/fetch-mock@^9.11.0-pull-request-644.1", "github-openapi-graphql-query": "^4.3.1", "glob": "^10.2.6", "jest": "^29.0.0", "lodash.camelcase": "^4.3.0", "lodash.set": "^4.3.2", "lodash.upperfirst": "^4.3.1", "mustache": "^4.0.0", "npm-run-all2": "^6.0.0", "prettier": "3.2.5", "semantic-release-plugin-update-version-in-files": "^1.0.0", "sinon": "^17.0.0", "sort-keys": "^5.0.0", "string-to-jsdoc-comment": "^1.0.0", "ts-jest": "^29.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"@octokit/core": "5"}, "publishConfig": {"access": "public"}, "engines": {"node": ">= 18"}, "files": ["dist-*/**", "bin/**"], "main": "dist-node/index.js", "browser": "dist-web/index.js", "types": "dist-types/index.d.ts", "module": "dist-src/index.js", "sideEffects": false}