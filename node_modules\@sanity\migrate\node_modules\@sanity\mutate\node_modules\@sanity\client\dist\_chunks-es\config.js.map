{"version": 3, "file": "config.js", "sources": ["../../src/generateHelpUrl.ts", "../../src/validators.ts", "../../src/util/once.ts", "../../src/warnings.ts", "../../src/config.ts"], "sourcesContent": ["const BASE_URL = 'https://www.sanity.io/help/'\n\nexport function generateHelpUrl(slug: string) {\n  return BASE_URL + slug\n}\n", "import type {Any, InitializedClientConfig} from './types'\n\nconst VALID_ASSET_TYPES = ['image', 'file']\nconst VALID_INSERT_LOCATIONS = ['before', 'after', 'replace']\n\nexport const dataset = (name: string) => {\n  if (!/^(~[a-z0-9]{1}[-\\w]{0,63}|[a-z0-9]{1}[-\\w]{0,63})$/.test(name)) {\n    throw new Error(\n      'Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters',\n    )\n  }\n}\n\nexport const projectId = (id: string) => {\n  if (!/^[-a-z0-9]+$/i.test(id)) {\n    throw new Error('`projectId` can only contain only a-z, 0-9 and dashes')\n  }\n}\n\nexport const validateAssetType = (type: string) => {\n  if (VALID_ASSET_TYPES.indexOf(type) === -1) {\n    throw new Error(`Invalid asset type: ${type}. Must be one of ${VALID_ASSET_TYPES.join(', ')}`)\n  }\n}\n\nexport const validateObject = (op: string, val: Any) => {\n  if (val === null || typeof val !== 'object' || Array.isArray(val)) {\n    throw new Error(`${op}() takes an object of properties`)\n  }\n}\n\nexport const validateDocumentId = (op: string, id: string) => {\n  if (typeof id !== 'string' || !/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(id) || id.includes('..')) {\n    throw new Error(`${op}(): \"${id}\" is not a valid document ID`)\n  }\n}\n\nexport const requireDocumentId = (op: string, doc: Record<string, Any>) => {\n  if (!doc._id) {\n    throw new Error(`${op}() requires that the document contains an ID (\"_id\" property)`)\n  }\n\n  validateDocumentId(op, doc._id)\n}\n\nexport const validateInsert = (at: string, selector: string, items: Any[]) => {\n  const signature = 'insert(at, selector, items)'\n  if (VALID_INSERT_LOCATIONS.indexOf(at) === -1) {\n    const valid = VALID_INSERT_LOCATIONS.map((loc) => `\"${loc}\"`).join(', ')\n    throw new Error(`${signature} takes an \"at\"-argument which is one of: ${valid}`)\n  }\n\n  if (typeof selector !== 'string') {\n    throw new Error(`${signature} takes a \"selector\"-argument which must be a string`)\n  }\n\n  if (!Array.isArray(items)) {\n    throw new Error(`${signature} takes an \"items\"-argument which must be an array`)\n  }\n}\n\nexport const hasDataset = (config: InitializedClientConfig): string => {\n  if (!config.dataset) {\n    throw new Error('`dataset` must be provided to perform queries')\n  }\n\n  return config.dataset || ''\n}\n\nexport const requestTag = (tag: string) => {\n  if (typeof tag !== 'string' || !/^[a-z0-9._-]{1,75}$/i.test(tag)) {\n    throw new Error(\n      `Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.`,\n    )\n  }\n\n  return tag\n}\n\nexport const resourceConfig = (config: InitializedClientConfig): void => {\n  if (!config['~experimental_resource']) {\n    throw new Error('`resource` must be provided to perform resource queries')\n  }\n  const {type, id} = config['~experimental_resource']\n\n  switch (type) {\n    case 'dataset': {\n      const segments = id.split('.')\n      if (segments.length !== 2) {\n        throw new Error('Dataset resource ID must be in the format \"project.dataset\"')\n      }\n      return\n    }\n    case 'dashboard':\n    case 'media-library':\n    case 'canvas': {\n      return\n    }\n    default:\n      // @ts-expect-error - handle all supported resource types\n      throw new Error(`Unsupported resource type: ${type.toString()}`)\n  }\n}\n\nexport const resourceGuard = (service: string, config: InitializedClientConfig): void => {\n  if (config['~experimental_resource']) {\n    throw new Error(`\\`${service}\\` does not support resource-based operations`)\n  }\n}\n", "import type {Any} from '../types'\n\nexport function once(fn: Any) {\n  let didCall = false\n  let returnValue: Any\n  return (...args: Any[]) => {\n    if (didCall) {\n      return returnValue\n    }\n    returnValue = fn(...args)\n    didCall = true\n    return returnValue\n  }\n}\n", "import {generateHelpUrl} from './generateHelpUrl'\nimport {type Any} from './types'\nimport {once} from './util/once'\n\nconst createWarningPrinter = (message: string[]) =>\n  // eslint-disable-next-line no-console\n  once((...args: Any[]) => console.warn(message.join(' '), ...args))\n\nexport const printCdnAndWithCredentialsWarning = createWarningPrinter([\n  `Because you set \\`withCredentials\\` to true, we will override your \\`useCdn\\``,\n  `setting to be false since (cookie-based) credentials are never set on the CDN`,\n])\n\nexport const printCdnWarning = createWarningPrinter([\n  `Since you haven't set a value for \\`useCdn\\`, we will deliver content using our`,\n  `global, edge-cached API-CDN. If you wish to have content delivered faster, set`,\n  `\\`useCdn: false\\` to use the Live API. Note: You may incur higher costs using the live API.`,\n])\n\nexport const printCdnPreviewDraftsWarning = createWarningPrinter([\n  `The Sanity client is configured with the \\`perspective\\` set to \\`drafts\\` or \\`previewDrafts\\`, which doesn't support the API-CDN.`,\n  `The Live API will be used instead. Set \\`useCdn: false\\` in your configuration to hide this warning.`,\n])\n\nexport const printPreviewDraftsDeprecationWarning = createWarningPrinter([\n  `The \\`previewDrafts\\` perspective has been renamed to  \\`drafts\\` and will be removed in a future API version`,\n])\n\nexport const printBrowserTokenWarning = createWarningPrinter([\n  'You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.',\n  `See ${generateHelpUrl(\n    'js-client-browser-token',\n  )} for more information and how to hide this warning.`,\n])\n\nexport const printCredentialedTokenWarning = createWarningPrinter([\n  'You have configured Sanity client to use a token, but also provided `withCredentials: true`.',\n  'This is no longer supported - only token will be used - remove `withCredentials: true`.',\n])\n\nexport const printNoApiVersionSpecifiedWarning = createWarningPrinter([\n  'Using the Sanity client without specifying an API version is deprecated.',\n  `See ${generateHelpUrl('js-client-api-version')}`,\n])\n\nexport const printNoDefaultExport = createWarningPrinter([\n  'The default export of @sanity/client has been deprecated. Use the named export `createClient` instead.',\n])\n", "import {generateHelpUrl} from './generateHelpUrl'\nimport type {ClientConfig, ClientPerspective, InitializedClientConfig} from './types'\nimport * as validate from './validators'\nimport * as warnings from './warnings'\n\nconst defaultCdnHost = 'apicdn.sanity.io'\nexport const defaultConfig = {\n  apiHost: 'https://api.sanity.io',\n  apiVersion: '1',\n  useProjectHostname: true,\n  stega: {enabled: false},\n} satisfies ClientConfig\n\nconst LOCALHOSTS = ['localhost', '127.0.0.1', '0.0.0.0']\nconst isLocal = (host: string) => LOCALHOSTS.indexOf(host) !== -1\n\nfunction validateApiVersion(apiVersion: string) {\n  if (apiVersion === '1' || apiVersion === 'X') {\n    return\n  }\n\n  const apiDate = new Date(apiVersion)\n  const apiVersionValid =\n    /^\\d{4}-\\d{2}-\\d{2}$/.test(apiVersion) && apiDate instanceof Date && apiDate.getTime() > 0\n\n  if (!apiVersionValid) {\n    throw new Error('Invalid API version string, expected `1` or date in format `YYYY-MM-DD`')\n  }\n}\n\n/**\n * @internal - it may have breaking changes in any release\n */\nexport function validateApiPerspective(\n  perspective: unknown,\n): asserts perspective is ClientPerspective {\n  if (Array.isArray(perspective) && perspective.length > 1 && perspective.includes('raw')) {\n    throw new TypeError(\n      `Invalid API perspective value: \"raw\". The raw-perspective can not be combined with other perspectives`,\n    )\n  }\n}\n\nexport const initConfig = (\n  config: Partial<ClientConfig>,\n  prevConfig: Partial<ClientConfig>,\n): InitializedClientConfig => {\n  const specifiedConfig = {\n    ...prevConfig,\n    ...config,\n    stega: {\n      ...(typeof prevConfig.stega === 'boolean'\n        ? {enabled: prevConfig.stega}\n        : prevConfig.stega || defaultConfig.stega),\n      ...(typeof config.stega === 'boolean' ? {enabled: config.stega} : config.stega || {}),\n    },\n  }\n  if (!specifiedConfig.apiVersion) {\n    warnings.printNoApiVersionSpecifiedWarning()\n  }\n\n  const newConfig = {\n    ...defaultConfig,\n    ...specifiedConfig,\n  } as InitializedClientConfig\n  const projectBased = newConfig.useProjectHostname && !newConfig['~experimental_resource']\n\n  if (typeof Promise === 'undefined') {\n    const helpUrl = generateHelpUrl('js-client-promise-polyfill')\n    throw new Error(`No native Promise-implementation found, polyfill needed - see ${helpUrl}`)\n  }\n\n  if (projectBased && !newConfig.projectId) {\n    throw new Error('Configuration must contain `projectId`')\n  }\n\n  if (newConfig['~experimental_resource']) {\n    validate.resourceConfig(newConfig)\n  }\n\n  if (typeof newConfig.perspective !== 'undefined') {\n    validateApiPerspective(newConfig.perspective)\n  }\n\n  if ('encodeSourceMap' in newConfig) {\n    throw new Error(\n      `It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?`,\n    )\n  }\n  if ('encodeSourceMapAtPath' in newConfig) {\n    throw new Error(\n      `It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?`,\n    )\n  }\n  if (typeof newConfig.stega.enabled !== 'boolean') {\n    throw new Error(`stega.enabled must be a boolean, received ${newConfig.stega.enabled}`)\n  }\n  if (newConfig.stega.enabled && newConfig.stega.studioUrl === undefined) {\n    throw new Error(`stega.studioUrl must be defined when stega.enabled is true`)\n  }\n  if (\n    newConfig.stega.enabled &&\n    typeof newConfig.stega.studioUrl !== 'string' &&\n    typeof newConfig.stega.studioUrl !== 'function'\n  ) {\n    throw new Error(\n      `stega.studioUrl must be a string or a function, received ${newConfig.stega.studioUrl}`,\n    )\n  }\n\n  const isBrowser = typeof window !== 'undefined' && window.location && window.location.hostname\n  const isLocalhost = isBrowser && isLocal(window.location.hostname)\n\n  const hasToken = Boolean(newConfig.token)\n  if (newConfig.withCredentials && hasToken) {\n    warnings.printCredentialedTokenWarning()\n    newConfig.withCredentials = false\n  }\n\n  if (isBrowser && isLocalhost && hasToken && newConfig.ignoreBrowserTokenWarning !== true) {\n    warnings.printBrowserTokenWarning()\n  } else if (typeof newConfig.useCdn === 'undefined') {\n    warnings.printCdnWarning()\n  }\n\n  if (projectBased) {\n    validate.projectId(newConfig.projectId!)\n  }\n\n  if (newConfig.dataset) {\n    validate.dataset(newConfig.dataset)\n  }\n\n  if ('requestTagPrefix' in newConfig) {\n    // Allow setting and unsetting request tag prefix\n    newConfig.requestTagPrefix = newConfig.requestTagPrefix\n      ? validate.requestTag(newConfig.requestTagPrefix).replace(/\\.+$/, '')\n      : undefined\n  }\n\n  newConfig.apiVersion = `${newConfig.apiVersion}`.replace(/^v/, '')\n  newConfig.isDefaultApi = newConfig.apiHost === defaultConfig.apiHost\n\n  if (newConfig.useCdn === true && newConfig.withCredentials) {\n    warnings.printCdnAndWithCredentialsWarning()\n  }\n\n  // If `useCdn` is undefined, we treat it as `true`\n  newConfig.useCdn = newConfig.useCdn !== false && !newConfig.withCredentials\n\n  validateApiVersion(newConfig.apiVersion)\n\n  const hostParts = newConfig.apiHost.split('://', 2)\n  const protocol = hostParts[0]\n  const host = hostParts[1]\n  const cdnHost = newConfig.isDefaultApi ? defaultCdnHost : host\n\n  if (projectBased) {\n    newConfig.url = `${protocol}://${newConfig.projectId}.${host}/v${newConfig.apiVersion}`\n    newConfig.cdnUrl = `${protocol}://${newConfig.projectId}.${cdnHost}/v${newConfig.apiVersion}`\n  } else {\n    newConfig.url = `${newConfig.apiHost}/v${newConfig.apiVersion}`\n    newConfig.cdnUrl = newConfig.url\n  }\n\n  return newConfig\n}\n"], "names": ["warnings.printNoApiVersionSpecifiedWarning", "validate.resourceConfig", "warnings.printCredentialedTokenWarning", "warnings.printBrowserTokenWarning", "warnings.printCdnWarning", "validate.projectId", "validate.dataset", "validate.requestTag", "warnings.printCdnAndWithCredentialsWarning"], "mappings": "AAAA,MAAM,WAAW;AAEV,SAAS,gBAAgB,MAAc;AAC5C,SAAO,WAAW;AACpB;ACFA,MAAM,oBAAoB,CAAC,SAAS,MAAM,GACpC,yBAAyB,CAAC,UAAU,SAAS,SAAS,GAE/C,UAAU,CAAC,SAAiB;AACnC,MAAA,CAAC,qDAAqD,KAAK,IAAI;AACjE,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAEJ,GAEa,YAAY,CAAC,OAAe;AACnC,MAAA,CAAC,gBAAgB,KAAK,EAAE;AACpB,UAAA,IAAI,MAAM,uDAAuD;AAE3E,GAEa,oBAAoB,CAAC,SAAiB;AAC7C,MAAA,kBAAkB,QAAQ,IAAI,MAAM;AAChC,UAAA,IAAI,MAAM,uBAAuB,IAAI,oBAAoB,kBAAkB,KAAK,IAAI,CAAC,EAAE;AAEjG,GAEa,iBAAiB,CAAC,IAAY,QAAa;AACtD,MAAI,QAAQ,QAAQ,OAAO,OAAQ,YAAY,MAAM,QAAQ,GAAG;AAC9D,UAAM,IAAI,MAAM,GAAG,EAAE,kCAAkC;AAE3D,GAEa,qBAAqB,CAAC,IAAY,OAAe;AACxD,MAAA,OAAO,MAAO,YAAY,CAAC,iCAAiC,KAAK,EAAE,KAAK,GAAG,SAAS,IAAI;AAC1F,UAAM,IAAI,MAAM,GAAG,EAAE,QAAQ,EAAE,8BAA8B;AAEjE,GAEa,oBAAoB,CAAC,IAAY,QAA6B;AACzE,MAAI,CAAC,IAAI;AACP,UAAM,IAAI,MAAM,GAAG,EAAE,+DAA+D;AAGnE,qBAAA,IAAI,IAAI,GAAG;AAChC,GAEa,iBAAiB,CAAC,IAAY,UAAkB,UAAiB;AAC5E,QAAM,YAAY;AAClB,MAAI,uBAAuB,QAAQ,EAAE,MAAM,IAAI;AACvC,UAAA,QAAQ,uBAAuB,IAAI,CAAC,QAAQ,IAAI,GAAG,GAAG,EAAE,KAAK,IAAI;AACvE,UAAM,IAAI,MAAM,GAAG,SAAS,4CAA4C,KAAK,EAAE;AAAA,EAAA;AAGjF,MAAI,OAAO,YAAa;AACtB,UAAM,IAAI,MAAM,GAAG,SAAS,qDAAqD;AAG/E,MAAA,CAAC,MAAM,QAAQ,KAAK;AACtB,UAAM,IAAI,MAAM,GAAG,SAAS,mDAAmD;AAEnF,GAEa,aAAa,CAAC,WAA4C;AACrE,MAAI,CAAC,OAAO;AACJ,UAAA,IAAI,MAAM,+CAA+C;AAGjE,SAAO,OAAO,WAAW;AAC3B,GAEa,aAAa,CAAC,QAAgB;AACzC,MAAI,OAAO,OAAQ,YAAY,CAAC,uBAAuB,KAAK,GAAG;AAC7D,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAGK,SAAA;AACT,GAEa,iBAAiB,CAAC,WAA0C;AACnE,MAAA,CAAC,OAAO,wBAAwB;AAC5B,UAAA,IAAI,MAAM,yDAAyD;AAE3E,QAAM,EAAC,MAAM,OAAM,OAAO,wBAAwB;AAElD,UAAQ,MAAM;AAAA,IACZ,KAAK,WAAW;AAEd,UADiB,GAAG,MAAM,GAAG,EAChB,WAAW;AAChB,cAAA,IAAI,MAAM,6DAA6D;AAE/E;AAAA,IAAA;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH;AAAA,IAEF;AAEE,YAAM,IAAI,MAAM,8BAA8B,KAAK,SAAU,CAAA,EAAE;AAAA,EAAA;AAErE,GAEa,gBAAgB,CAAC,SAAiB,WAA0C;AACvF,MAAI,OAAO,wBAAwB;AACjC,UAAM,IAAI,MAAM,KAAK,OAAO,+CAA+C;AAE/E;AC1GO,SAAS,KAAK,IAAS;AAC5B,MAAI,UAAU,IACV;AACG,SAAA,IAAI,UACL,YAGJ,cAAc,GAAG,GAAG,IAAI,GACxB,UAAU,KACH;AAEX;ACTA,MAAM,uBAAuB,CAAC;AAAA;AAAA,EAE5B,KAAK,IAAI,SAAgB,QAAQ,KAAK,QAAQ,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,GAEtD,oCAAoC,qBAAqB;AAAA,EACpE;AAAA,EACA;AACF,CAAC,GAEY,kBAAkB,qBAAqB;AAAA,EAClD;AAAA,EACA;AAAA,EACA;AACF,CAAC,GAEY,+BAA+B,qBAAqB;AAAA,EAC/D;AAAA,EACA;AACF,CAAC,GAEY,uCAAuC,qBAAqB;AAAA,EACvE;AACF,CAAC,GAEY,2BAA2B,qBAAqB;AAAA,EAC3D;AAAA,EACA,OAAO;AAAA,IACL;AAAA,EAAA,CACD;AACH,CAAC,GAEY,gCAAgC,qBAAqB;AAAA,EAChE;AAAA,EACA;AACF,CAAC,GAEY,oCAAoC,qBAAqB;AAAA,EACpE;AAAA,EACA,OAAO,gBAAgB,uBAAuB,CAAC;AACjD,CAAC,GAEY,uBAAuB,qBAAqB;AAAA,EACvD;AACF,CAAC,GC1CK,iBAAiB,oBACV,gBAAgB;AAAA,EAC3B,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,OAAO,EAAC,SAAS,GAAK;AACxB,GAEM,aAAa,CAAC,aAAa,aAAa,SAAS,GACjD,UAAU,CAAC,SAAiB,WAAW,QAAQ,IAAI,MAAM;AAE/D,SAAS,mBAAmB,YAAoB;AAC1C,MAAA,eAAe,OAAO,eAAe;AACvC;AAGI,QAAA,UAAU,IAAI,KAAK,UAAU;AAI/B,MAAA,EAFF,sBAAsB,KAAK,UAAU,KAAK,mBAAmB,QAAQ,QAAQ,QAAY,IAAA;AAGnF,UAAA,IAAI,MAAM,yEAAyE;AAE7F;AAKO,SAAS,uBACd,aAC0C;AACtC,MAAA,MAAM,QAAQ,WAAW,KAAK,YAAY,SAAS,KAAK,YAAY,SAAS,KAAK;AACpF,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAEJ;AAEa,MAAA,aAAa,CACxB,QACA,eAC4B;AAC5B,QAAM,kBAAkB;AAAA,IACtB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,MACL,GAAI,OAAO,WAAW,SAAU,YAC5B,EAAC,SAAS,WAAW,UACrB,WAAW,SAAS,cAAc;AAAA,MACtC,GAAI,OAAO,OAAO,SAAU,YAAY,EAAC,SAAS,OAAO,MAAK,IAAI,OAAO,SAAS,CAAA;AAAA,IAAC;AAAA,EAEvF;AACK,kBAAgB,cACnBA,kCAA2C;AAG7C,QAAM,YAAY;AAAA,IAChB,GAAG;AAAA,IACH,GAAG;AAAA,KAEC,eAAe,UAAU,sBAAsB,CAAC,UAAU,wBAAwB;AAEpF,MAAA,OAAO,UAAY,KAAa;AAC5B,UAAA,UAAU,gBAAgB,4BAA4B;AAC5D,UAAM,IAAI,MAAM,iEAAiE,OAAO,EAAE;AAAA,EAAA;AAGxF,MAAA,gBAAgB,CAAC,UAAU;AACvB,UAAA,IAAI,MAAM,wCAAwC;AAW1D,MARI,UAAU,wBAAwB,KACpCC,eAAwB,SAAS,GAG/B,OAAO,UAAU,cAAgB,OACnC,uBAAuB,UAAU,WAAW,GAG1C,qBAAqB;AACvB,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAEF,MAAI,2BAA2B;AAC7B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAEE,MAAA,OAAO,UAAU,MAAM,WAAY;AACrC,UAAM,IAAI,MAAM,6CAA6C,UAAU,MAAM,OAAO,EAAE;AAExF,MAAI,UAAU,MAAM,WAAW,UAAU,MAAM,cAAc;AACrD,UAAA,IAAI,MAAM,4DAA4D;AAG5E,MAAA,UAAU,MAAM,WAChB,OAAO,UAAU,MAAM,aAAc,YACrC,OAAO,UAAU,MAAM,aAAc;AAErC,UAAM,IAAI;AAAA,MACR,4DAA4D,UAAU,MAAM,SAAS;AAAA,IACvF;AAGF,QAAM,YAAY,OAAO,SAAW,OAAe,OAAO,YAAY,OAAO,SAAS,UAChF,cAAc,aAAa,QAAQ,OAAO,SAAS,QAAQ,GAE3D,WAAW,EAAQ,UAAU;AAC/B,YAAU,mBAAmB,aAC/BC,8BAAuC,GACvC,UAAU,kBAAkB,KAG1B,aAAa,eAAe,YAAY,UAAU,8BAA8B,KAClFC,6BACS,OAAO,UAAU,SAAW,OACrCC,mBAGE,gBACFC,UAAmB,UAAU,SAAU,GAGrC,UAAU,WACZC,QAAiB,UAAU,OAAO,GAGhC,sBAAsB,cAExB,UAAU,mBAAmB,UAAU,mBACnCC,WAAoB,UAAU,gBAAgB,EAAE,QAAQ,QAAQ,EAAE,IAClE,SAGN,UAAU,aAAa,GAAG,UAAU,UAAU,GAAG,QAAQ,MAAM,EAAE,GACjE,UAAU,eAAe,UAAU,YAAY,cAAc,SAEzD,UAAU,WAAW,MAAQ,UAAU,mBACzCC,kCAA2C,GAI7C,UAAU,SAAS,UAAU,WAAW,MAAS,CAAC,UAAU,iBAE5D,mBAAmB,UAAU,UAAU;AAEvC,QAAM,YAAY,UAAU,QAAQ,MAAM,OAAO,CAAC,GAC5C,WAAW,UAAU,CAAC,GACtB,OAAO,UAAU,CAAC,GAClB,UAAU,UAAU,eAAe,iBAAiB;AAE1D,SAAI,gBACF,UAAU,MAAM,GAAG,QAAQ,MAAM,UAAU,SAAS,IAAI,IAAI,KAAK,UAAU,UAAU,IACrF,UAAU,SAAS,GAAG,QAAQ,MAAM,UAAU,SAAS,IAAI,OAAO,KAAK,UAAU,UAAU,OAE3F,UAAU,MAAM,GAAG,UAAU,OAAO,KAAK,UAAU,UAAU,IAC7D,UAAU,SAAS,UAAU,MAGxB;AACT;"}