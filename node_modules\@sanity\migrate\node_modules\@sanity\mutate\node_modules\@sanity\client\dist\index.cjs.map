{"version": 3, "file": "index.cjs", "sources": ["../src/http/errors.ts", "../src/http/request.ts", "../src/data/eventsource.ts", "../src/util/getSelection.ts", "../src/data/patch.ts", "../src/data/transaction.ts", "../src/http/requestOptions.ts", "../src/data/encodeQueryString.ts", "../src/data/dataMethods.ts", "../src/assets/AssetsClient.ts", "../src/util/defaults.ts", "../src/util/pick.ts", "../src/data/eventsourcePolyfill.ts", "../src/data/reconnectOnConnectionFailure.ts", "../src/data/listen.ts", "../src/util/shareReplayLatest.ts", "../src/data/live.ts", "../src/datasets/DatasetsClient.ts", "../src/projects/ProjectsClient.ts", "../src/users/UsersClient.ts", "../src/SanityClient.ts", "../src/defineCreateClient.ts", "../src/defineDeprecatedCreateClient.ts", "../src/http/nodeMiddleware.ts", "../src/index.ts"], "sourcesContent": ["import type {ActionError, Any, ErrorProps, MutationError} from '../types'\n\nconst MAX_ITEMS_IN_ERROR_MESSAGE = 5\n\n/** @public */\nexport class ClientError extends Error {\n  response: ErrorProps['response']\n  statusCode: ErrorProps['statusCode'] = 400\n  responseBody: ErrorProps['responseBody']\n  details: ErrorProps['details']\n\n  constructor(res: Any) {\n    const props = extractErrorProps(res)\n    super(props.message)\n    Object.assign(this, props)\n  }\n}\n\n/** @public */\nexport class ServerError extends Error {\n  response: ErrorProps['response']\n  statusCode: ErrorProps['statusCode'] = 500\n  responseBody: ErrorProps['responseBody']\n  details: ErrorProps['details']\n\n  constructor(res: Any) {\n    const props = extractErrorProps(res)\n    super(props.message)\n    Object.assign(this, props)\n  }\n}\n\nfunction extractErrorProps(res: Any): ErrorProps {\n  const body = res.body\n  const props = {\n    response: res,\n    statusCode: res.statusCode,\n    responseBody: stringifyBody(body, res),\n    message: '',\n    details: undefined as Any,\n  }\n\n  // API/Boom style errors ({statusCode, error, message})\n  if (body.error && body.message) {\n    props.message = `${body.error} - ${body.message}`\n    return props\n  }\n\n  // Mutation errors (specifically)\n  if (isMutationError(body) || isActionError(body)) {\n    const allItems = body.error.items || []\n    const items = allItems\n      .slice(0, MAX_ITEMS_IN_ERROR_MESSAGE)\n      .map((item) => item.error?.description)\n      .filter(Boolean)\n    let itemsStr = items.length ? `:\\n- ${items.join('\\n- ')}` : ''\n    if (allItems.length > MAX_ITEMS_IN_ERROR_MESSAGE) {\n      itemsStr += `\\n...and ${allItems.length - MAX_ITEMS_IN_ERROR_MESSAGE} more`\n    }\n    props.message = `${body.error.description}${itemsStr}`\n    props.details = body.error\n    return props\n  }\n\n  // Query/database errors ({error: {description, other, arb, props}})\n  if (body.error && body.error.description) {\n    props.message = body.error.description\n    props.details = body.error\n    return props\n  }\n\n  // Other, more arbitrary errors\n  props.message = body.error || body.message || httpErrorMessage(res)\n  return props\n}\n\nfunction isMutationError(body: Any): body is MutationError {\n  return (\n    isPlainObject(body) &&\n    isPlainObject(body.error) &&\n    body.error.type === 'mutationError' &&\n    typeof body.error.description === 'string'\n  )\n}\n\nfunction isActionError(body: Any): body is ActionError {\n  return (\n    isPlainObject(body) &&\n    isPlainObject(body.error) &&\n    body.error.type === 'actionError' &&\n    typeof body.error.description === 'string'\n  )\n}\n\nfunction isPlainObject(obj: Any): obj is Record<string, unknown> {\n  return typeof obj === 'object' && obj !== null && !Array.isArray(obj)\n}\n\nfunction httpErrorMessage(res: Any) {\n  const statusMessage = res.statusMessage ? ` ${res.statusMessage}` : ''\n  return `${res.method}-request to ${res.url} resulted in HTTP ${res.statusCode}${statusMessage}`\n}\n\nfunction stringifyBody(body: Any, res: Any) {\n  const contentType = (res.headers['content-type'] || '').toLowerCase()\n  const isJson = contentType.indexOf('application/json') !== -1\n  return isJson ? JSON.stringify(body, null, 2) : body\n}\n\n/** @public */\nexport class CorsOriginError extends Error {\n  projectId: string\n  addOriginUrl?: URL\n\n  constructor({projectId}: {projectId: string}) {\n    super('CorsOriginError')\n    this.name = 'CorsOriginError'\n    this.projectId = projectId\n\n    const url = new URL(`https://sanity.io/manage/project/${projectId}/api`)\n    if (typeof location !== 'undefined') {\n      const {origin} = location\n      url.searchParams.set('cors', 'add')\n      url.searchParams.set('origin', origin)\n      this.addOriginUrl = url\n      this.message = `The current origin is not allowed to connect to the Live Content API. Add it here: ${url}`\n    } else {\n      this.message = `The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${url}`\n    }\n  }\n}\n", "import {getIt, type Middlewares, type Requester} from 'get-it'\nimport {jsonRequest, jsonResponse, observable, progress, retry} from 'get-it/middleware'\nimport {Observable} from 'rxjs'\n\nimport type {Any} from '../types'\nimport {ClientError, ServerError} from './errors'\n\nconst httpError = {\n  onResponse: (res: Any) => {\n    if (res.statusCode >= 500) {\n      throw new ServerError(res)\n    } else if (res.statusCode >= 400) {\n      throw new ClientError(res)\n    }\n\n    return res\n  },\n}\n\nfunction printWarnings() {\n  const seen: Record<string, boolean> = {}\n  return {\n    onResponse: (res: Any) => {\n      const warn = res.headers['x-sanity-warning']\n      const warnings = Array.isArray(warn) ? warn : [warn]\n      for (const msg of warnings) {\n        if (!msg || seen[msg]) continue\n        seen[msg] = true\n        console.warn(msg) // eslint-disable-line no-console\n      }\n      return res\n    },\n  }\n}\n\n/** @internal */\nexport function defineHttpRequest(envMiddleware: Middlewares): Requester {\n  return getIt([\n    retry({shouldRetry}),\n    ...envMiddleware,\n    printWarnings(),\n    jsonRequest(),\n    jsonResponse(),\n    progress(),\n    httpError,\n    observable({implementation: Observable}),\n  ])\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction shouldRetry(err: any, attempt: number, options: any) {\n  // Allow opting out of retries\n  if (options.maxRetries === 0) return false\n\n  // By default `retry.shouldRetry` doesn't retry on server errors so we add our own logic.\n\n  const isSafe = options.method === 'GET' || options.method === 'HEAD'\n  const uri = options.uri || options.url\n  const isQuery = uri.startsWith('/data/query')\n  const isRetriableResponse =\n    err.response &&\n    (err.response.statusCode === 429 ||\n      err.response.statusCode === 502 ||\n      err.response.statusCode === 503)\n\n  // We retry the following errors:\n  // - 429 means that the request was rate limited. It's a bit difficult\n  //   to know exactly how long it makes sense to wait and/or how many\n  //   attempts we should retry, but the backoff should alleviate the\n  //   additional load.\n  // - 502/503 can occur when certain components struggle to talk to their\n  //   upstream dependencies. This is most likely a temporary problem\n  //   and retrying makes sense.\n\n  if ((isSafe || isQuery) && isRetriableResponse) return true\n\n  return retry.shouldRetry(err, attempt, options)\n}\n", "import {defer, isObservable, mergeMap, Observable, of} from 'rxjs'\n\nimport {type Any} from '../types'\n\n/**\n * @public\n * Thrown if the EventSource connection could not be established.\n * Note that ConnectionFailedErrors are rare, and disconnects will normally be handled by the EventSource instance itself and emitted as `reconnect` events.\n */\nexport class ConnectionFailedError extends Error {\n  readonly name = 'ConnectionFailedError'\n}\n\n/**\n * The listener has been told to explicitly disconnect.\n *  This is a rare situation, but may occur if the API knows reconnect attempts will fail,\n *  eg in the case of a deleted dataset, a blocked project or similar events.\n * @public\n */\nexport class DisconnectError extends Error {\n  readonly name = 'DisconnectError'\n  readonly reason?: string\n  constructor(message: string, reason?: string, options: ErrorOptions = {}) {\n    super(message, options)\n    this.reason = reason\n  }\n}\n\n/**\n * @public\n * The server sent a `channelError` message. Usually indicative of a bad or malformed request\n */\nexport class ChannelError extends Error {\n  readonly name = 'ChannelError'\n  readonly data?: unknown\n  constructor(message: string, data: unknown) {\n    super(message)\n    this.data = data\n  }\n}\n\n/**\n * @public\n * The server sent an `error`-event to tell the client that an unexpected error has happened.\n */\nexport class MessageError extends Error {\n  readonly name = 'MessageError'\n  readonly data?: unknown\n  constructor(message: string, data: unknown, options: ErrorOptions = {}) {\n    super(message, options)\n    this.data = data\n  }\n}\n\n/**\n * @public\n * An error occurred while parsing the message sent by the server as JSON. Should normally not happen.\n */\nexport class MessageParseError extends Error {\n  readonly name = 'MessageParseError'\n}\n\n/**\n * @public\n */\nexport interface ServerSentEvent<Name extends string> {\n  type: Name\n  id?: string\n  data?: unknown\n}\n\n// Always listen for these events, no matter what\nconst REQUIRED_EVENTS = ['channelError', 'disconnect']\n\n/**\n * @internal\n */\nexport type EventSourceEvent<Name extends string> = ServerSentEvent<Name>\n\n/**\n * @internal\n */\nexport type EventSourceInstance = InstanceType<typeof globalThis.EventSource>\n\n/**\n * Sanity API specific EventSource handler shared between the listen and live APIs\n *\n * Since the `EventSource` API is not provided by all environments, this function enables custom initialization of the EventSource instance\n * for runtimes that requires polyfilling or custom setup logic (e.g. custom HTTP headers)\n * via the passed `initEventSource` function which must return an EventSource instance.\n *\n * Possible errors to be thrown on the returned observable are:\n * - {@link MessageError}\n * - {@link MessageParseError}\n * - {@link ChannelError}\n * - {@link DisconnectError}\n * - {@link ConnectionFailedError}\n *\n * @param initEventSource - A function that returns an EventSource instance or an Observable that resolves to an EventSource instance\n * @param events - an array of named events from the API to listen for.\n *\n * @internal\n */\nexport function connectEventSource<EventName extends string>(\n  initEventSource: () => EventSourceInstance | Observable<EventSourceInstance>,\n  events: EventName[],\n) {\n  return defer(() => {\n    const es = initEventSource()\n    return isObservable(es) ? es : of(es)\n  }).pipe(mergeMap((es) => connectWithESInstance(es, events))) as Observable<\n    ServerSentEvent<EventName>\n  >\n}\n\n/**\n * Provides an observable from the passed EventSource instance, subscribing to the passed list of names of events types to listen for\n * Handles connection logic, adding/removing event listeners, payload parsing, error propagation, etc.\n *\n * @param es - The EventSource instance\n * @param events - List of event names to listen for\n */\nfunction connectWithESInstance<EventTypeName extends string>(\n  es: EventSourceInstance,\n  events: EventTypeName[],\n) {\n  return new Observable<EventSourceEvent<EventTypeName>>((observer) => {\n    const emitOpen = (events as string[]).includes('open')\n    const emitReconnect = (events as string[]).includes('reconnect')\n\n    // EventSource will emit a regular Event if it fails to connect, however the API may also emit an `error` MessageEvent\n    // So we need to handle both cases\n    function onError(evt: MessageEvent | Event) {\n      // If the event has a `data` property, then it`s a MessageEvent emitted by the API and we should forward the error\n      if ('data' in evt) {\n        const [parseError, event] = parseEvent(evt as MessageEvent)\n        observer.error(\n          parseError\n            ? new MessageParseError('Unable to parse EventSource error message', {cause: event})\n            : new MessageError((event?.data as {message: string}).message, event),\n        )\n        return\n      }\n\n      // We should never be in a disconnected state. By default, EventSource will reconnect\n      // automatically, but in some cases (like when a laptop lid is closed), it will trigger onError\n      // if it can't reconnect.\n      // see https://html.spec.whatwg.org/multipage/server-sent-events.html#sse-processing-model\n      if (es.readyState === es.CLOSED) {\n        // In these cases we'll signal to consumers (via the error path) that a retry/reconnect is needed.\n        observer.error(new ConnectionFailedError('EventSource connection failed'))\n      } else if (emitReconnect) {\n        observer.next({type: 'reconnect' as EventTypeName})\n      }\n    }\n\n    function onOpen() {\n      // The open event of the EventSource API is fired when a connection with an event source is opened.\n      observer.next({type: 'open' as EventTypeName})\n    }\n\n    function onMessage(message: MessageEvent) {\n      const [parseError, event] = parseEvent(message)\n      if (parseError) {\n        observer.error(\n          new MessageParseError('Unable to parse EventSource message', {cause: parseError}),\n        )\n        return\n      }\n      if (message.type === 'channelError') {\n        // An error occurred. This is different from a network-level error (which will be emitted as 'error').\n        // Possible causes are things such as malformed filters, non-existant datasets or similar.\n        observer.error(new ChannelError(extractErrorMessage(event?.data), event.data))\n        return\n      }\n      if (message.type === 'disconnect') {\n        // The listener has been told to explicitly disconnect and not reconnect.\n        // This is a rare situation, but may occur if the API knows reconnect attempts will fail,\n        // eg in the case of a deleted dataset, a blocked project or similar events.\n        observer.error(\n          new DisconnectError(\n            `Server disconnected client: ${\n              (event.data as {reason?: string})?.reason || 'unknown error'\n            }`,\n          ),\n        )\n        return\n      }\n      observer.next({\n        type: message.type as EventTypeName,\n        id: message.lastEventId,\n        ...(event.data ? {data: event.data} : {}),\n      })\n    }\n\n    es.addEventListener('error', onError)\n\n    if (emitOpen) {\n      es.addEventListener('open', onOpen)\n    }\n\n    // Make sure we have a unique list of events types to avoid listening multiple times,\n    const cleanedEvents = [...new Set([...REQUIRED_EVENTS, ...events])]\n      // filter out events that are handled separately\n      .filter((type) => type !== 'error' && type !== 'open' && type !== 'reconnect')\n\n    cleanedEvents.forEach((type: string) => es.addEventListener(type, onMessage))\n\n    return () => {\n      es.removeEventListener('error', onError)\n      if (emitOpen) {\n        es.removeEventListener('open', onOpen)\n      }\n      cleanedEvents.forEach((type: string) => es.removeEventListener(type, onMessage))\n      es.close()\n    }\n  })\n}\n\nfunction parseEvent(\n  message: MessageEvent,\n): [null, {type: string; id: string; data?: unknown}] | [Error, null] {\n  try {\n    const data = typeof message.data === 'string' && JSON.parse(message.data)\n    return [\n      null,\n      {\n        type: message.type,\n        id: message.lastEventId,\n        ...(isEmptyObject(data) ? {} : {data}),\n      },\n    ]\n  } catch (err) {\n    return [err as Error, null]\n  }\n}\n\nfunction extractErrorMessage(err: Any) {\n  if (!err.error) {\n    return err.message || 'Unknown listener error'\n  }\n\n  if (err.error.description) {\n    return err.error.description\n  }\n\n  return typeof err.error === 'string' ? err.error : JSON.stringify(err.error, null, 2)\n}\n\nfunction isEmptyObject(data: object) {\n  for (const _ in data) {\n    return false\n  }\n  return true\n}\n", "import type {MutationSelection} from '../types'\n\nexport function getSelection(sel: unknown): MutationSelection {\n  if (typeof sel === 'string') {\n    return {id: sel}\n  }\n\n  if (Array.isArray(sel)) {\n    return {query: '*[_id in $ids]', params: {ids: sel}}\n  }\n\n  if (typeof sel === 'object' && sel !== null && 'query' in sel && typeof sel.query === 'string') {\n    return 'params' in sel && typeof sel.params === 'object' && sel.params !== null\n      ? {query: sel.query, params: sel.params}\n      : {query: sel.query}\n  }\n\n  const selectionOpts = [\n    '* Document ID (<docId>)',\n    '* Array of document IDs',\n    '* Object containing `query`',\n  ].join('\\n')\n\n  throw new Error(`Unknown selection - must be one of:\\n\\n${selectionOpts}`)\n}\n", "import {type Observable} from 'rxjs'\n\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {\n  AllDocumentIdsMutationOptions,\n  AllDocumentsMutationOptions,\n  Any,\n  AttributeSet,\n  BaseMutationOptions,\n  FirstDocumentIdMutationOptions,\n  FirstDocumentMutationOptions,\n  MultipleMutationResult,\n  PatchMutationOperation,\n  PatchOperations,\n  PatchSelection,\n  SanityDocument,\n  SingleMutationResult,\n} from '../types'\nimport {getSelection} from '../util/getSelection'\nimport {validateInsert, validateObject} from '../validators'\n\n/** @internal */\nexport class BasePatch {\n  protected selection: PatchSelection\n  protected operations: PatchOperations\n  constructor(selection: PatchSelection, operations: PatchOperations = {}) {\n    this.selection = selection\n    this.operations = operations\n  }\n\n  /**\n   * Sets the given attributes to the document. Does NOT merge objects.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to set. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"value\"\\}\n   */\n  set(attrs: AttributeSet): this {\n    return this._assign('set', attrs)\n  }\n\n  /**\n   * Sets the given attributes to the document if they are not currently set. Does NOT merge objects.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to set. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"value\"\\}\n   */\n  setIfMissing(attrs: AttributeSet): this {\n    return this._assign('setIfMissing', attrs)\n  }\n\n  /**\n   * Performs a \"diff-match-patch\" operation on the string attributes provided.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to perform operation on. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"dmp\"\\}\n   */\n  diffMatchPatch(attrs: AttributeSet): this {\n    validateObject('diffMatchPatch', attrs)\n    return this._assign('diffMatchPatch', attrs)\n  }\n\n  /**\n   * Unsets the attribute paths provided.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attribute paths to unset.\n   */\n  unset(attrs: string[]): this {\n    if (!Array.isArray(attrs)) {\n      throw new Error('unset(attrs) takes an array of attributes to unset, non-array given')\n    }\n\n    this.operations = Object.assign({}, this.operations, {unset: attrs})\n    return this\n  }\n\n  /**\n   * Increment a numeric value. Each entry in the argument is either an attribute or a JSON path. The value may be a positive or negative integer or floating-point value. The operation will fail if target value is not a numeric value, or doesn't exist.\n   *\n   * @param attrs - Object of attribute paths to increment, values representing the number to increment by.\n   */\n  inc(attrs: {[key: string]: number}): this {\n    return this._assign('inc', attrs)\n  }\n\n  /**\n   * Decrement a numeric value. Each entry in the argument is either an attribute or a JSON path. The value may be a positive or negative integer or floating-point value. The operation will fail if target value is not a numeric value, or doesn't exist.\n   *\n   * @param attrs - Object of attribute paths to decrement, values representing the number to decrement by.\n   */\n  dec(attrs: {[key: string]: number}): this {\n    return this._assign('dec', attrs)\n  }\n\n  /**\n   * Provides methods for modifying arrays, by inserting, appending and replacing elements via a JSONPath expression.\n   *\n   * @param at - Location to insert at, relative to the given selector, or 'replace' the matched path\n   * @param selector - JSONPath expression, eg `comments[-1]` or `blocks[_key==\"abc123\"]`\n   * @param items - Array of items to insert/replace\n   */\n  insert(at: 'before' | 'after' | 'replace', selector: string, items: Any[]): this {\n    validateInsert(at, selector, items)\n    return this._assign('insert', {[at]: selector, items})\n  }\n\n  /**\n   * Append the given items to the array at the given JSONPath\n   *\n   * @param selector - Attribute/path to append to, eg `comments` or `person.hobbies`\n   * @param items - Array of items to append to the array\n   */\n  append(selector: string, items: Any[]): this {\n    return this.insert('after', `${selector}[-1]`, items)\n  }\n\n  /**\n   * Prepend the given items to the array at the given JSONPath\n   *\n   * @param selector - Attribute/path to prepend to, eg `comments` or `person.hobbies`\n   * @param items - Array of items to prepend to the array\n   */\n  prepend(selector: string, items: Any[]): this {\n    return this.insert('before', `${selector}[0]`, items)\n  }\n\n  /**\n   * Change the contents of an array by removing existing elements and/or adding new elements.\n   *\n   * @param selector - Attribute or JSONPath expression for array\n   * @param start - Index at which to start changing the array (with origin 0). If greater than the length of the array, actual starting index will be set to the length of the array. If negative, will begin that many elements from the end of the array (with origin -1) and will be set to 0 if absolute value is greater than the length of the array.x\n   * @param deleteCount - An integer indicating the number of old array elements to remove.\n   * @param items - The elements to add to the array, beginning at the start index. If you don't specify any elements, splice() will only remove elements from the array.\n   */\n  splice(selector: string, start: number, deleteCount?: number, items?: Any[]): this {\n    // Negative indexes doesn't mean the same in Sanity as they do in JS;\n    // -1 means \"actually at the end of the array\", which allows inserting\n    // at the end of the array without knowing its length. We therefore have\n    // to substract negative indexes by one to match JS. If you want Sanity-\n    // behaviour, just use `insert('replace', selector, items)` directly\n    const delAll = typeof deleteCount === 'undefined' || deleteCount === -1\n    const startIndex = start < 0 ? start - 1 : start\n    const delCount = delAll ? -1 : Math.max(0, start + deleteCount)\n    const delRange = startIndex < 0 && delCount >= 0 ? '' : delCount\n    const rangeSelector = `${selector}[${startIndex}:${delRange}]`\n    return this.insert('replace', rangeSelector, items || [])\n  }\n\n  /**\n   * Adds a revision clause, preventing the document from being patched if the `_rev` property does not match the given value\n   *\n   * @param rev - Revision to lock the patch to\n   */\n  ifRevisionId(rev: string): this {\n    this.operations.ifRevisionID = rev\n    return this\n  }\n\n  /**\n   * Return a plain JSON representation of the patch\n   */\n  serialize(): PatchMutationOperation {\n    return {...getSelection(this.selection), ...this.operations}\n  }\n\n  /**\n   * Return a plain JSON representation of the patch\n   */\n  toJSON(): PatchMutationOperation {\n    return this.serialize()\n  }\n\n  /**\n   * Clears the patch of all operations\n   */\n  reset(): this {\n    this.operations = {}\n    return this\n  }\n\n  protected _assign(op: keyof PatchOperations, props: Any, merge = true): this {\n    validateObject(op, props)\n    this.operations = Object.assign({}, this.operations, {\n      [op]: Object.assign({}, (merge && this.operations[op]) || {}, props),\n    })\n    return this\n  }\n\n  protected _set(op: keyof PatchOperations, props: Any): this {\n    return this._assign(op, props, false)\n  }\n}\n\n/** @public */\nexport class ObservablePatch extends BasePatch {\n  #client?: ObservableSanityClient\n\n  constructor(\n    selection: PatchSelection,\n    operations?: PatchOperations,\n    client?: ObservableSanityClient,\n  ) {\n    super(selection, operations)\n    this.#client = client\n  }\n\n  /**\n   * Clones the patch\n   */\n  clone(): ObservablePatch {\n    return new ObservablePatch(this.selection, {...this.operations}, this.#client)\n  }\n\n  /**\n   * Commit the patch, returning an observable that produces the first patched document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Commit the patch, returning an observable that produces an array of the mutated documents\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Commit the patch, returning an observable that produces a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: FirstDocumentIdMutationOptions): Observable<SingleMutationResult>\n  /**\n   * Commit the patch, returning an observable that produces a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: AllDocumentIdsMutationOptions): Observable<MultipleMutationResult>\n  /**\n   * Commit the patch, returning an observable that produces the first patched document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?:\n      | FirstDocumentMutationOptions\n      | AllDocumentsMutationOptions\n      | FirstDocumentIdMutationOptions\n      | AllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    if (!this.#client) {\n      throw new Error(\n        'No `client` passed to patch, either provide one or pass the ' +\n          'patch to a clients `mutate()` method',\n      )\n    }\n\n    const returnFirst = typeof this.selection === 'string'\n    const opts = Object.assign({returnFirst, returnDocuments: true}, options)\n    return this.#client.mutate<R>({patch: this.serialize()} as Any, opts)\n  }\n}\n\n/** @public */\nexport class Patch extends BasePatch {\n  #client?: SanityClient\n  constructor(selection: PatchSelection, operations?: PatchOperations, client?: SanityClient) {\n    super(selection, operations)\n    this.#client = client\n  }\n\n  /**\n   * Clones the patch\n   */\n  clone(): Patch {\n    return new Patch(this.selection, {...this.operations}, this.#client)\n  }\n\n  /**\n   * Commit the patch, returning a promise that resolves to the first patched document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Commit the patch, returning a promise that resolves to an array of the mutated documents\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Commit the patch, returning a promise that resolves to a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: FirstDocumentIdMutationOptions): Promise<SingleMutationResult>\n  /**\n   * Commit the patch, returning a promise that resolves to a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: AllDocumentIdsMutationOptions): Promise<MultipleMutationResult>\n  /**\n   * Commit the patch, returning a promise that resolves to the first patched document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?:\n      | FirstDocumentMutationOptions\n      | AllDocumentsMutationOptions\n      | FirstDocumentIdMutationOptions\n      | AllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    if (!this.#client) {\n      throw new Error(\n        'No `client` passed to patch, either provide one or pass the ' +\n          'patch to a clients `mutate()` method',\n      )\n    }\n\n    const returnFirst = typeof this.selection === 'string'\n    const opts = Object.assign({returnFirst, returnDocuments: true}, options)\n    return this.#client.mutate<R>({patch: this.serialize()} as Any, opts)\n  }\n}\n", "import type {Observable} from 'rxjs'\n\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {\n  Any,\n  BaseMutationOptions,\n  IdentifiedSanityDocumentStub,\n  MultipleMutationResult,\n  Mutation,\n  MutationSelection,\n  PatchOperations,\n  SanityDocument,\n  SanityDocumentStub,\n  SingleMutationResult,\n  TransactionAllDocumentIdsMutationOptions,\n  TransactionAllDocumentsMutationOptions,\n  TransactionFirstDocumentIdMutationOptions,\n  TransactionFirstDocumentMutationOptions,\n} from '../types'\nimport * as validators from '../validators'\nimport {ObservablePatch, Patch} from './patch'\n\n/** @public */\nexport type PatchBuilder = (patch: Patch) => Patch\n/** @public */\nexport type ObservablePatchBuilder = (patch: ObservablePatch) => ObservablePatch\n\nconst defaultMutateOptions = {returnDocuments: false}\n\n/** @internal */\nexport class BaseTransaction {\n  protected operations: Mutation[]\n  protected trxId?: string\n  constructor(operations: Mutation[] = [], transactionId?: string) {\n    this.operations = operations\n    this.trxId = transactionId\n  }\n  /**\n   * Creates a new Sanity document. If `_id` is provided and already exists, the mutation will fail. If no `_id` is given, one will automatically be generated by the database.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create. Requires a `_type` property.\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(doc: SanityDocumentStub<R>): this {\n    validators.validateObject('create', doc)\n    return this._add({create: doc})\n  }\n\n  /**\n   * Creates a new Sanity document. If a document with the same `_id` already exists, the create operation will be ignored.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create if it does not already exist. Requires `_id` and `_type` properties.\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    doc: IdentifiedSanityDocumentStub<R>,\n  ): this {\n    const op = 'createIfNotExists'\n    validators.validateObject(op, doc)\n    validators.requireDocumentId(op, doc)\n    return this._add({[op]: doc})\n  }\n\n  /**\n   * Creates a new Sanity document, or replaces an existing one if the same `_id` is already used.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create or replace. Requires `_id` and `_type` properties.\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    doc: IdentifiedSanityDocumentStub<R>,\n  ): this {\n    const op = 'createOrReplace'\n    validators.validateObject(op, doc)\n    validators.requireDocumentId(op, doc)\n    return this._add({[op]: doc})\n  }\n\n  /**\n   * Deletes the document with the given document ID\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param documentId - Document ID to delete\n   */\n  delete(documentId: string): this {\n    validators.validateDocumentId('delete', documentId)\n    return this._add({delete: {id: documentId}})\n  }\n\n  /**\n   * Gets the current transaction ID, if any\n   */\n  transactionId(): string | undefined\n  /**\n   * Set the ID of this transaction.\n   *\n   * @param id - Transaction ID\n   */\n  transactionId(id: string): this\n  transactionId(id?: string): this | string | undefined {\n    if (!id) {\n      return this.trxId\n    }\n\n    this.trxId = id\n    return this\n  }\n\n  /**\n   * Return a plain JSON representation of the transaction\n   */\n  serialize(): Mutation[] {\n    return [...this.operations]\n  }\n\n  /**\n   * Return a plain JSON representation of the transaction\n   */\n  toJSON(): Mutation[] {\n    return this.serialize()\n  }\n\n  /**\n   * Clears the transaction of all operations\n   */\n  reset(): this {\n    this.operations = []\n    return this\n  }\n\n  protected _add(mut: Mutation): this {\n    this.operations.push(mut)\n    return this\n  }\n}\n\n/** @public */\nexport class Transaction extends BaseTransaction {\n  #client?: SanityClient\n  constructor(operations?: Mutation[], client?: SanityClient, transactionId?: string) {\n    super(operations, transactionId)\n    this.#client = client\n  }\n\n  /**\n   * Clones the transaction\n   */\n  clone(): Transaction {\n    return new Transaction([...this.operations], this.#client, this.trxId)\n  }\n\n  /**\n   * Commit the transaction, returning a promise that resolves to the first mutated document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any>>(\n    options: TransactionFirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Commit the transaction, returning a promise that resolves to an array of the mutated documents\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any>>(\n    options: TransactionAllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Commit the transaction, returning a promise that resolves to a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: TransactionFirstDocumentIdMutationOptions): Promise<SingleMutationResult>\n  /**\n   * Commit the transaction, returning a promise that resolves to a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: TransactionAllDocumentIdsMutationOptions): Promise<MultipleMutationResult>\n  /**\n   * Commit the transaction, returning a promise that resolves to a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options?: BaseMutationOptions): Promise<MultipleMutationResult>\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?:\n      | TransactionFirstDocumentMutationOptions\n      | TransactionAllDocumentsMutationOptions\n      | TransactionFirstDocumentIdMutationOptions\n      | TransactionAllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    if (!this.#client) {\n      throw new Error(\n        'No `client` passed to transaction, either provide one or pass the ' +\n          'transaction to a clients `mutate()` method',\n      )\n    }\n\n    return this.#client.mutate<R>(\n      this.serialize() as Any,\n      Object.assign({transactionId: this.trxId}, defaultMutateOptions, options || {}),\n    )\n  }\n\n  /**\n   * Performs a patch on the given document ID. Can either be a builder function or an object of patch operations.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param documentId - Document ID to perform the patch operation on\n   * @param patchOps - Operations to perform, or a builder function\n   */\n  patch(documentId: string, patchOps?: PatchBuilder | PatchOperations): this\n  /**\n   * Performs a patch on the given selection. Can either be a builder function or an object of patch operations.\n   *\n   * @param selection - An object with `query` and optional `params`, defining which document(s) to patch\n   * @param patchOps - Operations to perform, or a builder function\n   */\n  patch(patch: MutationSelection, patchOps?: PatchBuilder | PatchOperations): this\n  /**\n   * Adds the given patch instance to the transaction.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param patch - Patch to execute\n   */\n  patch(patch: Patch): this\n  patch(\n    patchOrDocumentId: Patch | MutationSelection | string,\n    patchOps?: PatchBuilder | PatchOperations,\n  ): this {\n    const isBuilder = typeof patchOps === 'function'\n    const isPatch = typeof patchOrDocumentId !== 'string' && patchOrDocumentId instanceof Patch\n    const isMutationSelection =\n      typeof patchOrDocumentId === 'object' &&\n      ('query' in patchOrDocumentId || 'id' in patchOrDocumentId)\n\n    // transaction.patch(client.patch('documentId').inc({visits: 1}))\n    if (isPatch) {\n      return this._add({patch: patchOrDocumentId.serialize()})\n    }\n\n    // patch => patch.inc({visits: 1}).set({foo: 'bar'})\n    if (isBuilder) {\n      const patch = patchOps(new Patch(patchOrDocumentId, {}, this.#client))\n      if (!(patch instanceof Patch)) {\n        throw new Error('function passed to `patch()` must return the patch')\n      }\n\n      return this._add({patch: patch.serialize()})\n    }\n\n    /**\n     * transaction.patch(\n     *   {query: \"*[_type == 'person' && points >= $threshold]\", params: { threshold: 100 }},\n     *   {dec: { points: 100 }, inc: { bonuses: 1 }}\n     * )\n     */\n    if (isMutationSelection) {\n      const patch = new Patch(patchOrDocumentId, patchOps || {}, this.#client)\n      return this._add({patch: patch.serialize()})\n    }\n\n    return this._add({patch: {id: patchOrDocumentId, ...patchOps}})\n  }\n}\n\n/** @public */\nexport class ObservableTransaction extends BaseTransaction {\n  #client?: ObservableSanityClient\n  constructor(operations?: Mutation[], client?: ObservableSanityClient, transactionId?: string) {\n    super(operations, transactionId)\n    this.#client = client\n  }\n\n  /**\n   * Clones the transaction\n   */\n  clone(): ObservableTransaction {\n    return new ObservableTransaction([...this.operations], this.#client, this.trxId)\n  }\n\n  /**\n   * Commit the transaction, returning an observable that produces the first mutated document\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any>>(\n    options: TransactionFirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Commit the transaction, returning an observable that produces an array of the mutated documents\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit<R extends Record<string, Any>>(\n    options: TransactionAllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Commit the transaction, returning an observable that produces a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: TransactionFirstDocumentIdMutationOptions): Observable<SingleMutationResult>\n  /**\n   * Commit the transaction, returning an observable that produces a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options: TransactionAllDocumentIdsMutationOptions): Observable<MultipleMutationResult>\n  /**\n   * Commit the transaction, returning an observable that produces a mutation result object\n   *\n   * @param options - Options for the mutation operation\n   */\n  commit(options?: BaseMutationOptions): Observable<MultipleMutationResult>\n  commit<R extends Record<string, Any> = Record<string, Any>>(\n    options?:\n      | TransactionFirstDocumentMutationOptions\n      | TransactionAllDocumentsMutationOptions\n      | TransactionFirstDocumentIdMutationOptions\n      | TransactionAllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    if (!this.#client) {\n      throw new Error(\n        'No `client` passed to transaction, either provide one or pass the ' +\n          'transaction to a clients `mutate()` method',\n      )\n    }\n\n    return this.#client.mutate<R>(\n      this.serialize() as Any,\n      Object.assign({transactionId: this.trxId}, defaultMutateOptions, options || {}),\n    )\n  }\n\n  /**\n   * Performs a patch on the given document ID. Can either be a builder function or an object of patch operations.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param documentId - Document ID to perform the patch operation on\n   * @param patchOps - Operations to perform, or a builder function\n   */\n  patch(documentId: string, patchOps?: ObservablePatchBuilder | PatchOperations): this\n  /**\n   * Adds the given patch instance to the transaction.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param patch - ObservablePatch to execute\n   */\n  patch(patch: ObservablePatch): this\n  patch(\n    patchOrDocumentId: ObservablePatch | string,\n    patchOps?: ObservablePatchBuilder | PatchOperations,\n  ): this {\n    const isBuilder = typeof patchOps === 'function'\n    const isPatch =\n      typeof patchOrDocumentId !== 'string' && patchOrDocumentId instanceof ObservablePatch\n\n    // transaction.patch(client.patch('documentId').inc({visits: 1}))\n    if (isPatch) {\n      return this._add({patch: patchOrDocumentId.serialize()})\n    }\n\n    // patch => patch.inc({visits: 1}).set({foo: 'bar'})\n    if (isBuilder) {\n      const patch = patchOps(new ObservablePatch(patchOrDocumentId, {}, this.#client))\n      if (!(patch instanceof ObservablePatch)) {\n        throw new Error('function passed to `patch()` must return the patch')\n      }\n\n      return this._add({patch: patch.serialize()})\n    }\n\n    return this._add({patch: {id: patchOrDocumentId, ...patchOps}})\n  }\n}\n", "import type {RequestOptions} from 'get-it'\n\nimport type {Any} from '../types'\n\nconst projectHeader = 'X-Sanity-Project-ID'\n\nexport function requestOptions(config: Any, overrides: Any = {}): Omit<RequestOptions, 'url'> {\n  const headers: Any = {}\n\n  const token = overrides.token || config.token\n  if (token) {\n    headers.Authorization = `Bearer ${token}`\n  }\n\n  if (!overrides.useGlobalApi && !config.useProjectHostname && config.projectId) {\n    headers[projectHeader] = config.projectId\n  }\n\n  const withCredentials = Boolean(\n    typeof overrides.withCredentials === 'undefined'\n      ? config.withCredentials\n      : overrides.withCredentials,\n  )\n\n  const timeout = typeof overrides.timeout === 'undefined' ? config.timeout : overrides.timeout\n  return Object.assign({}, overrides, {\n    headers: Object.assign({}, headers, overrides.headers || {}),\n    timeout: typeof timeout === 'undefined' ? 5 * 60 * 1000 : timeout,\n    proxy: overrides.proxy || config.proxy,\n    json: true,\n    withCredentials,\n    fetch:\n      typeof overrides.fetch === 'object' && typeof config.fetch === 'object'\n        ? {...config.fetch, ...overrides.fetch}\n        : overrides.fetch || config.fetch,\n  })\n}\n", "import type {Any, ListenParams, QueryParams} from '../types'\n\nexport const encodeQueryString = ({\n  query,\n  params = {},\n  options = {},\n}: {\n  query: string\n  params?: ListenParams | QueryParams\n  options?: Any\n}) => {\n  const searchParams = new URLSearchParams()\n  // We generally want tag at the start of the query string\n  const {tag, includeMutations, returnQuery, ...opts} = options\n  // We're using `append` instead of `set` to support React Native: https://github.com/facebook/react-native/blob/1982c4722fcc51aa87e34cf562672ee4aff540f1/packages/react-native/Libraries/Blob/URL.js#L86-L88\n  if (tag) searchParams.append('tag', tag)\n  searchParams.append('query', query)\n\n  // Iterate params, the keys are prefixed with `$` and their values JSON stringified\n  for (const [key, value] of Object.entries(params)) {\n    searchParams.append(`$${key}`, JSON.stringify(value))\n  }\n  // Options are passed as-is\n  for (const [key, value] of Object.entries(opts)) {\n    // Skip falsy values\n    if (value) searchParams.append(key, `${value}`)\n  }\n\n  // `returnQuery` is default `true`, so needs an explicit `false` handling\n  if (returnQuery === false) searchParams.append('returnQuery', 'false')\n\n  // `includeMutations` is default `true`, so needs an explicit `false` handling\n  if (includeMutations === false) searchParams.append('includeMutations', 'false')\n\n  return `?${searchParams}`\n}\n", "import {from, type MonoTypeOperatorFunction, Observable} from 'rxjs'\nimport {combineLatestWith, filter, map} from 'rxjs/operators'\n\nimport {validateApiPerspective} from '../config'\nimport {requestOptions} from '../http/requestOptions'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport {stegaClean} from '../stega/stegaClean'\nimport type {\n  Action,\n  AllDocumentIdsMutationOptions,\n  AllDocumentsMutationOptions,\n  Any,\n  BaseActionOptions,\n  BaseMutationOptions,\n  FirstDocumentIdMutationOptions,\n  FirstDocumentMutationOptions,\n  HttpRequest,\n  HttpRequestEvent,\n  IdentifiedSanityDocumentStub,\n  InitializedClientConfig,\n  InitializedStegaConfig,\n  MultipleActionResult,\n  MultipleMutationResult,\n  Mutation,\n  MutationSelection,\n  QueryOptions,\n  RawQueryResponse,\n  RequestObservableOptions,\n  RequestOptions,\n  SanityDocument,\n  SingleActionResult,\n  SingleMutationResult,\n} from '../types'\nimport {getSelection} from '../util/getSelection'\nimport * as validate from '../validators'\nimport * as validators from '../validators'\nimport {printCdnPreviewDraftsWarning, printPreviewDraftsDeprecationWarning} from '../warnings'\nimport {encodeQueryString} from './encodeQueryString'\nimport {ObservablePatch, Patch} from './patch'\nimport {ObservableTransaction, Transaction} from './transaction'\n\ntype Client = SanityClient | ObservableSanityClient\n\nconst excludeFalsey = (param: Any, defValue: Any) => {\n  const value = typeof param === 'undefined' ? defValue : param\n  return param === false ? undefined : value\n}\n\nconst getMutationQuery = (options: BaseMutationOptions = {}) => {\n  return {\n    dryRun: options.dryRun,\n    returnIds: true,\n    returnDocuments: excludeFalsey(options.returnDocuments, true),\n    visibility: options.visibility || 'sync',\n    autoGenerateArrayKeys: options.autoGenerateArrayKeys,\n    skipCrossDatasetReferenceValidation: options.skipCrossDatasetReferenceValidation,\n  }\n}\n\nconst isResponse = (event: Any) => event.type === 'response'\nconst getBody = (event: Any) => event.body\n\nconst indexBy = (docs: Any[], attr: Any) =>\n  docs.reduce((indexed, doc) => {\n    indexed[attr(doc)] = doc\n    return indexed\n  }, Object.create(null))\n\nconst getQuerySizeLimit = 11264\n\n/** @internal */\nexport function _fetch<R, Q>(\n  client: Client,\n  httpRequest: HttpRequest,\n  _stega: InitializedStegaConfig,\n  query: string,\n  _params: Q = {} as Q,\n  options: QueryOptions = {},\n): Observable<RawQueryResponse<R> | R> {\n  const stega =\n    'stega' in options\n      ? {\n          ...(_stega || {}),\n          ...(typeof options.stega === 'boolean' ? {enabled: options.stega} : options.stega || {}),\n        }\n      : _stega\n  const params = stega.enabled ? stegaClean(_params) : _params\n  const mapResponse =\n    options.filterResponse === false ? (res: Any) => res : (res: Any) => res.result\n\n  const {cache, next, ...opts} = {\n    // Opt out of setting a `signal` on an internal `fetch` if one isn't provided.\n    // This is necessary in React Server Components to avoid opting out of Request Memoization.\n    useAbortSignal: typeof options.signal !== 'undefined',\n    // Set `resultSourceMap' when stega is enabled, as it's required for encoding.\n    resultSourceMap: stega.enabled ? 'withKeyArraySelector' : options.resultSourceMap,\n    ...options,\n    // Default to not returning the query, unless `filterResponse` is `false`,\n    // or `returnQuery` is explicitly set. `true` is the default in Content Lake, so skip if truthy\n    returnQuery: options.filterResponse === false && options.returnQuery !== false,\n  }\n  const reqOpts =\n    typeof cache !== 'undefined' || typeof next !== 'undefined'\n      ? {...opts, fetch: {cache, next}}\n      : opts\n\n  const $request = _dataRequest(client, httpRequest, 'query', {query, params}, reqOpts)\n  return stega.enabled\n    ? $request.pipe(\n        combineLatestWith(\n          from(\n            import('../stega/stegaEncodeSourceMap').then(\n              ({stegaEncodeSourceMap}) => stegaEncodeSourceMap,\n            ),\n          ),\n        ),\n        map(\n          ([res, stegaEncodeSourceMap]: [\n            Any,\n            (typeof import('../stega/stegaEncodeSourceMap'))['stegaEncodeSourceMap'],\n          ]) => {\n            const result = stegaEncodeSourceMap(res.result, res.resultSourceMap, stega)\n            return mapResponse({...res, result})\n          },\n        ),\n      )\n    : $request.pipe(map(mapResponse))\n}\n\n/** @internal */\nexport function _getDocument<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  id: string,\n  opts: {signal?: AbortSignal; tag?: string} = {},\n): Observable<SanityDocument<R> | undefined> {\n  const options = {\n    uri: _getDataUrl(client, 'doc', id),\n    json: true,\n    tag: opts.tag,\n    signal: opts.signal,\n  }\n  return _requestObservable<SanityDocument<R> | undefined>(client, httpRequest, options).pipe(\n    filter(isResponse),\n    map((event) => event.body.documents && event.body.documents[0]),\n  )\n}\n\n/** @internal */\nexport function _getDocuments<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  ids: string[],\n  opts: {signal?: AbortSignal; tag?: string} = {},\n): Observable<(SanityDocument<R> | null)[]> {\n  const options = {\n    uri: _getDataUrl(client, 'doc', ids.join(',')),\n    json: true,\n    tag: opts.tag,\n    signal: opts.signal,\n  }\n  return _requestObservable<(SanityDocument<R> | null)[]>(client, httpRequest, options).pipe(\n    filter(isResponse),\n    map((event: Any) => {\n      const indexed = indexBy(event.body.documents || [], (doc: Any) => doc._id)\n      return ids.map((id) => indexed[id] || null)\n    }),\n  )\n}\n\n/** @internal */\nexport function _createIfNotExists<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  doc: IdentifiedSanityDocumentStub<R>,\n  options?:\n    | AllDocumentIdsMutationOptions\n    | AllDocumentsMutationOptions\n    | BaseMutationOptions\n    | FirstDocumentIdMutationOptions\n    | FirstDocumentMutationOptions,\n): Observable<\n  SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n> {\n  validators.requireDocumentId('createIfNotExists', doc)\n  return _create<R>(client, httpRequest, doc, 'createIfNotExists', options)\n}\n\n/** @internal */\nexport function _createOrReplace<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  doc: IdentifiedSanityDocumentStub<R>,\n  options?:\n    | AllDocumentIdsMutationOptions\n    | AllDocumentsMutationOptions\n    | BaseMutationOptions\n    | FirstDocumentIdMutationOptions\n    | FirstDocumentMutationOptions,\n): Observable<\n  SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n> {\n  validators.requireDocumentId('createOrReplace', doc)\n  return _create<R>(client, httpRequest, doc, 'createOrReplace', options)\n}\n\n/** @internal */\nexport function _delete<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  selection: string | MutationSelection,\n  options?:\n    | AllDocumentIdsMutationOptions\n    | AllDocumentsMutationOptions\n    | BaseMutationOptions\n    | FirstDocumentIdMutationOptions\n    | FirstDocumentMutationOptions,\n): Observable<\n  SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n> {\n  return _dataRequest(\n    client,\n    httpRequest,\n    'mutate',\n    {mutations: [{delete: getSelection(selection)}]},\n    options,\n  )\n}\n\n/** @internal */\nexport function _mutate<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  mutations: Mutation<R>[] | Patch | ObservablePatch | Transaction | ObservableTransaction,\n  options?:\n    | AllDocumentIdsMutationOptions\n    | AllDocumentsMutationOptions\n    | BaseMutationOptions\n    | FirstDocumentIdMutationOptions\n    | FirstDocumentMutationOptions,\n): Observable<\n  SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n> {\n  let mut: Mutation | Mutation[]\n  if (mutations instanceof Patch || mutations instanceof ObservablePatch) {\n    mut = {patch: mutations.serialize()}\n  } else if (mutations instanceof Transaction || mutations instanceof ObservableTransaction) {\n    mut = mutations.serialize()\n  } else {\n    mut = mutations\n  }\n\n  const muts = Array.isArray(mut) ? mut : [mut]\n  const transactionId = (options && options.transactionId) || undefined\n  return _dataRequest(client, httpRequest, 'mutate', {mutations: muts, transactionId}, options)\n}\n\n/**\n * @internal\n */\nexport function _action(\n  client: Client,\n  httpRequest: HttpRequest,\n  actions: Action | Action[],\n  options?: BaseActionOptions,\n): Observable<SingleActionResult | MultipleActionResult> {\n  const acts = Array.isArray(actions) ? actions : [actions]\n  const transactionId = (options && options.transactionId) || undefined\n  const skipCrossDatasetReferenceValidation =\n    (options && options.skipCrossDatasetReferenceValidation) || undefined\n  const dryRun = (options && options.dryRun) || undefined\n\n  return _dataRequest(\n    client,\n    httpRequest,\n    'actions',\n    {actions: acts, transactionId, skipCrossDatasetReferenceValidation, dryRun},\n    options,\n  )\n}\n\n/**\n * @internal\n */\nexport function _dataRequest(\n  client: Client,\n  httpRequest: HttpRequest,\n  endpoint: string,\n  body: Any,\n  options: Any = {},\n): Any {\n  const isMutation = endpoint === 'mutate'\n  const isAction = endpoint === 'actions'\n  const isQuery = endpoint === 'query'\n\n  // Check if the query string is within a configured threshold,\n  // in which case we can use GET. Otherwise, use POST.\n  const strQuery = isMutation || isAction ? '' : encodeQueryString(body)\n  const useGet = !isMutation && !isAction && strQuery.length < getQuerySizeLimit\n  const stringQuery = useGet ? strQuery : ''\n  const returnFirst = options.returnFirst\n  const {timeout, token, tag, headers, returnQuery, lastLiveEventId, cacheMode} = options\n\n  const uri = _getDataUrl(client, endpoint, stringQuery)\n\n  const reqOptions = {\n    method: useGet ? 'GET' : 'POST',\n    uri: uri,\n    json: true,\n    body: useGet ? undefined : body,\n    query: isMutation && getMutationQuery(options),\n    timeout,\n    headers,\n    token,\n    tag,\n    returnQuery,\n    perspective: options.perspective,\n    resultSourceMap: options.resultSourceMap,\n    lastLiveEventId: Array.isArray(lastLiveEventId) ? lastLiveEventId[0] : lastLiveEventId,\n    cacheMode: cacheMode,\n    canUseCdn: isQuery,\n    signal: options.signal,\n    fetch: options.fetch,\n    useAbortSignal: options.useAbortSignal,\n    useCdn: options.useCdn,\n  }\n\n  return _requestObservable(client, httpRequest, reqOptions).pipe(\n    filter(isResponse),\n    map(getBody),\n    map((res) => {\n      if (!isMutation) {\n        return res\n      }\n\n      // Should we return documents?\n      const results = res.results || []\n      if (options.returnDocuments) {\n        return returnFirst\n          ? results[0] && results[0].document\n          : results.map((mut: Any) => mut.document)\n      }\n\n      // Return a reduced subset\n      const key = returnFirst ? 'documentId' : 'documentIds'\n      const ids = returnFirst ? results[0] && results[0].id : results.map((mut: Any) => mut.id)\n      return {\n        transactionId: res.transactionId,\n        results: results,\n        [key]: ids,\n      }\n    }),\n  )\n}\n\n/**\n * @internal\n */\nexport function _create<R extends Record<string, Any>>(\n  client: Client,\n  httpRequest: HttpRequest,\n  doc: Any,\n  op: Any,\n  options: Any = {},\n): Observable<\n  SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n> {\n  const mutation = {[op]: doc}\n  const opts = Object.assign({returnFirst: true, returnDocuments: true}, options)\n  return _dataRequest(client, httpRequest, 'mutate', {mutations: [mutation]}, opts)\n}\n\nconst hasDataConfig = (client: Client) =>\n  (client.config().dataset !== undefined && client.config().projectId !== undefined) ||\n  client.config()['~experimental_resource'] !== undefined\n\nconst isQuery = (client: Client, uri: string) =>\n  hasDataConfig(client) && uri.startsWith(_getDataUrl(client, 'query'))\n\nconst isMutate = (client: Client, uri: string) =>\n  hasDataConfig(client) && uri.startsWith(_getDataUrl(client, 'mutate'))\n\nconst isDoc = (client: Client, uri: string) =>\n  hasDataConfig(client) && uri.startsWith(_getDataUrl(client, 'doc', ''))\n\nconst isListener = (client: Client, uri: string) =>\n  hasDataConfig(client) && uri.startsWith(_getDataUrl(client, 'listen'))\n\nconst isHistory = (client: Client, uri: string) =>\n  hasDataConfig(client) && uri.startsWith(_getDataUrl(client, 'history', ''))\n\nconst isData = (client: Client, uri: string) =>\n  uri.startsWith('/data/') ||\n  isQuery(client, uri) ||\n  isMutate(client, uri) ||\n  isDoc(client, uri) ||\n  isListener(client, uri) ||\n  isHistory(client, uri)\n\n/**\n * @internal\n */\nexport function _requestObservable<R>(\n  client: Client,\n  httpRequest: HttpRequest,\n  options: RequestObservableOptions,\n): Observable<HttpRequestEvent<R>> {\n  const uri = options.url || (options.uri as string)\n  const config = client.config()\n\n  // If the `canUseCdn`-option is not set we detect it automatically based on the method + URL.\n  // Only the /data endpoint is currently available through API-CDN.\n  const canUseCdn =\n    typeof options.canUseCdn === 'undefined'\n      ? ['GET', 'HEAD'].indexOf(options.method || 'GET') >= 0 && isData(client, uri)\n      : options.canUseCdn\n\n  let useCdn = (options.useCdn ?? config.useCdn) && canUseCdn\n\n  const tag =\n    options.tag && config.requestTagPrefix\n      ? [config.requestTagPrefix, options.tag].join('.')\n      : options.tag || config.requestTagPrefix\n\n  if (tag && options.tag !== null) {\n    options.query = {tag: validate.requestTag(tag), ...options.query}\n  }\n\n  // GROQ query-only parameters\n  if (['GET', 'HEAD', 'POST'].indexOf(options.method || 'GET') >= 0 && isQuery(client, uri)) {\n    const resultSourceMap = options.resultSourceMap ?? config.resultSourceMap\n    if (resultSourceMap !== undefined && resultSourceMap !== false) {\n      options.query = {resultSourceMap, ...options.query}\n    }\n    const perspectiveOption = options.perspective || config.perspective\n    if (typeof perspectiveOption !== 'undefined') {\n      if (perspectiveOption === 'previewDrafts') {\n        printPreviewDraftsDeprecationWarning()\n      }\n      validateApiPerspective(perspectiveOption)\n      options.query = {\n        perspective: Array.isArray(perspectiveOption)\n          ? perspectiveOption.join(',')\n          : perspectiveOption,\n        ...options.query,\n      }\n      // If the perspective is set to `drafts` or multiple perspectives we can't use the CDN, the API will throw\n      if (\n        ((Array.isArray(perspectiveOption) && perspectiveOption.length > 0) ||\n          // previewDrafts was renamed to drafts, but keep for backwards compat\n          perspectiveOption === 'previewDrafts' ||\n          perspectiveOption === 'drafts') &&\n        useCdn\n      ) {\n        useCdn = false\n        printCdnPreviewDraftsWarning()\n      }\n    }\n\n    if (options.lastLiveEventId) {\n      options.query = {...options.query, lastLiveEventId: options.lastLiveEventId}\n    }\n\n    if (options.returnQuery === false) {\n      options.query = {returnQuery: 'false', ...options.query}\n    }\n\n    if (useCdn && options.cacheMode == 'noStale') {\n      options.query = {cacheMode: 'noStale', ...options.query}\n    }\n  }\n\n  const reqOptions = requestOptions(\n    config,\n    Object.assign({}, options, {\n      url: _getUrl(client, uri, useCdn),\n    }),\n  ) as RequestOptions\n\n  const request = new Observable<HttpRequestEvent<R>>((subscriber) =>\n    httpRequest(reqOptions, config.requester!).subscribe(subscriber),\n  )\n\n  return options.signal ? request.pipe(_withAbortSignal(options.signal)) : request\n}\n\n/**\n * @internal\n */\nexport function _request<R>(client: Client, httpRequest: HttpRequest, options: Any): Observable<R> {\n  const observable = _requestObservable<R>(client, httpRequest, options).pipe(\n    filter((event: Any) => event.type === 'response'),\n    map((event: Any) => event.body),\n  )\n\n  return observable\n}\n\n/**\n * @internal\n */\nexport function _getDataUrl(client: Client, operation: string, path?: string): string {\n  const config = client.config()\n  if (config['~experimental_resource']) {\n    validators.resourceConfig(config)\n    const resourceBase = resourceDataBase(config)\n    const uri = path !== undefined ? `${operation}/${path}` : operation\n    return `${resourceBase}/${uri}`.replace(/\\/($|\\?)/, '$1')\n  }\n  const catalog = validators.hasDataset(config)\n  const baseUri = `/${operation}/${catalog}`\n  const uri = path !== undefined ? `${baseUri}/${path}` : baseUri\n  return `/data${uri}`.replace(/\\/($|\\?)/, '$1')\n}\n\n/**\n * @internal\n */\nexport function _getUrl(client: Client, uri: string, canUseCdn = false): string {\n  const {url, cdnUrl} = client.config()\n  const base = canUseCdn ? cdnUrl : url\n  return `${base}/${uri.replace(/^\\//, '')}`\n}\n\n/**\n * @internal\n */\nfunction _withAbortSignal<T>(signal: AbortSignal): MonoTypeOperatorFunction<T> {\n  return (input) => {\n    return new Observable((observer) => {\n      const abort = () => observer.error(_createAbortError(signal))\n\n      if (signal && signal.aborted) {\n        abort()\n        return\n      }\n      const subscription = input.subscribe(observer)\n      signal.addEventListener('abort', abort)\n      return () => {\n        signal.removeEventListener('abort', abort)\n        subscription.unsubscribe()\n      }\n    })\n  }\n}\n// DOMException is supported on most modern browsers and Node.js 18+.\n// @see https://developer.mozilla.org/en-US/docs/Web/API/DOMException#browser_compatibility\nconst isDomExceptionSupported = Boolean(globalThis.DOMException)\n\n/**\n * @internal\n * @param signal\n * Original source copied from https://github.com/sindresorhus/ky/blob/740732c78aad97e9aec199e9871bdbf0ae29b805/source/errors/DOMException.ts\n * TODO: When targeting Node.js 18, use `signal.throwIfAborted()` (https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/throwIfAborted)\n */\nfunction _createAbortError(signal?: AbortSignal) {\n  /*\n  NOTE: Use DomException with AbortError name as specified in MDN docs (https://developer.mozilla.org/en-US/docs/Web/API/AbortController/abort)\n  > When abort() is called, the fetch() promise rejects with an Error of type DOMException, with name AbortError.\n  */\n  if (isDomExceptionSupported) {\n    return new DOMException(signal?.reason ?? 'The operation was aborted.', 'AbortError')\n  }\n\n  // DOMException not supported. Fall back to use of error and override name.\n  const error = new Error(signal?.reason ?? 'The operation was aborted.')\n  error.name = 'AbortError'\n\n  return error\n}\n\nconst resourceDataBase = (config: InitializedClientConfig): string => {\n  if (!config['~experimental_resource']) {\n    throw new Error('`resource` must be provided to perform resource queries')\n  }\n  const {type, id} = config['~experimental_resource']\n\n  switch (type) {\n    case 'dataset': {\n      const segments = id.split('.')\n      if (segments.length !== 2) {\n        throw new Error('Dataset ID must be in the format \"project.dataset\"')\n      }\n      return `/projects/${segments[0]}/datasets/${segments[1]}`\n    }\n    case 'canvas': {\n      return `/canvases/${id}`\n    }\n    case 'media-library': {\n      return `/media-libraries/${id}`\n    }\n    case 'dashboard': {\n      return `/dashboards/${id}`\n    }\n    default:\n      // @ts-expect-error - handle all supported resource types\n      throw new Error(`Unsupported resource type: ${type.toString()}`)\n  }\n}\n", "import {lastValue<PERSON>rom, type Observable} from 'rxjs'\nimport {filter, map} from 'rxjs/operators'\n\nimport {_requestObservable} from '../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {\n  Any,\n  HttpRequest,\n  HttpRequestEvent,\n  InitializedClientConfig,\n  ResponseEvent,\n  SanityAssetDocument,\n  SanityImageAssetDocument,\n  UploadBody,\n  UploadClientConfig,\n} from '../types'\nimport * as validators from '../validators'\n\n/** @internal */\nexport class ObservableAssetsClient {\n  #client: ObservableSanityClient\n  #httpRequest: HttpRequest\n  constructor(client: ObservableSanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Uploads a file asset to the configured dataset\n   *\n   * @param assetType - Asset type (file)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'file',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Observable<HttpRequestEvent<{document: SanityAssetDocument}>>\n\n  /**\n   * Uploads an image asset to the configured dataset\n   *\n   * @param assetType - Asset type (image)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Observable<HttpRequestEvent<{document: SanityImageAssetDocument}>>\n  /**\n   * Uploads a file or an image asset to the configured dataset\n   *\n   * @param assetType - Asset type (file/image)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'file' | 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Observable<HttpRequestEvent<{document: SanityAssetDocument | SanityImageAssetDocument}>>\n  upload(\n    assetType: 'file' | 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Observable<HttpRequestEvent<{document: SanityAssetDocument | SanityImageAssetDocument}>> {\n    return _upload(this.#client, this.#httpRequest, assetType, body, options)\n  }\n}\n\n/** @internal */\nexport class AssetsClient {\n  #client: SanityClient\n  #httpRequest: HttpRequest\n  constructor(client: SanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Uploads a file asset to the configured dataset\n   *\n   * @param assetType - Asset type (file)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'file',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Promise<SanityAssetDocument>\n  /**\n   * Uploads an image asset to the configured dataset\n   *\n   * @param assetType - Asset type (image)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Promise<SanityImageAssetDocument>\n  /**\n   * Uploads a file or an image asset to the configured dataset\n   *\n   * @param assetType - Asset type (file/image)\n   * @param body - Asset content - can be a browser File instance, a Blob, a Node.js Buffer instance or a Node.js ReadableStream.\n   * @param options - Options to use for the upload\n   */\n  upload(\n    assetType: 'file' | 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Promise<SanityAssetDocument | SanityImageAssetDocument>\n  upload(\n    assetType: 'file' | 'image',\n    body: UploadBody,\n    options?: UploadClientConfig,\n  ): Promise<SanityAssetDocument | SanityImageAssetDocument> {\n    const observable = _upload(this.#client, this.#httpRequest, assetType, body, options)\n    return lastValueFrom(\n      observable.pipe(\n        filter((event: Any) => event.type === 'response'),\n        map(\n          (event) =>\n            (event as ResponseEvent<{document: SanityAssetDocument | SanityImageAssetDocument}>)\n              .body.document,\n        ),\n      ),\n    )\n  }\n}\n\nfunction _upload(\n  client: SanityClient | ObservableSanityClient,\n  httpRequest: HttpRequest,\n  assetType: 'image' | 'file',\n  body: UploadBody,\n  opts: UploadClientConfig = {},\n): Observable<HttpRequestEvent<{document: SanityAssetDocument | SanityImageAssetDocument}>> {\n  validators.validateAssetType(assetType)\n\n  // If an empty array is given, explicitly set `none` to override API defaults\n  let meta = opts.extract || undefined\n  if (meta && !meta.length) {\n    meta = ['none']\n  }\n\n  const config = client.config()\n  const options = optionsFromFile(opts, body)\n  const {tag, label, title, description, creditLine, filename, source} = options\n  const query: Any = {\n    label,\n    title,\n    description,\n    filename,\n    meta,\n    creditLine,\n  }\n  if (source) {\n    query.sourceId = source.id\n    query.sourceName = source.name\n    query.sourceUrl = source.url\n  }\n\n  return _requestObservable(client, httpRequest, {\n    tag,\n    method: 'POST',\n    timeout: options.timeout || 0,\n    uri: buildAssetUploadUrl(config, assetType),\n    headers: options.contentType ? {'Content-Type': options.contentType} : {},\n    query,\n    body,\n  })\n}\n\nfunction buildAssetUploadUrl(config: InitializedClientConfig, assetType: 'image' | 'file'): string {\n  const assetTypeEndpoint = assetType === 'image' ? 'images' : 'files'\n\n  if (config['~experimental_resource']) {\n    const {type, id} = config['~experimental_resource']\n    switch (type) {\n      case 'dataset': {\n        throw new Error(\n          'Assets are not supported for dataset resources, yet. Configure the client with `{projectId: <projectId>, dataset: <datasetId>}` instead.',\n        )\n      }\n      case 'canvas': {\n        return `/canvases/${id}/assets/${assetTypeEndpoint}`\n      }\n      case 'media-library': {\n        return `/media-libraries/${id}/upload`\n      }\n      case 'dashboard': {\n        return `/dashboards/${id}/assets/${assetTypeEndpoint}`\n      }\n      default:\n        // @ts-expect-error - handle all supported resource types\n        throw new Error(`Unsupported resource type: ${type.toString()}`)\n    }\n  }\n\n  const dataset = validators.hasDataset(config)\n  return `assets/${assetTypeEndpoint}/${dataset}`\n}\n\nfunction optionsFromFile(opts: Record<string, Any>, file: Any) {\n  if (typeof File === 'undefined' || !(file instanceof File)) {\n    return opts\n  }\n\n  return Object.assign(\n    {\n      filename: opts.preserveFilename === false ? undefined : file.name,\n      contentType: file.type,\n    },\n    opts,\n  )\n}\n", "import type {Any} from '../types'\n\nexport default (obj: Any, defaults: Any) =>\n  Object.keys(defaults)\n    .concat(Object.keys(obj))\n    .reduce((target, prop) => {\n      target[prop] = typeof obj[prop] === 'undefined' ? defaults[prop] : obj[prop]\n\n      return target\n    }, {} as Any)\n", "import {type Any} from '../types'\n\nexport const pick = (obj: Any, props: Any) =>\n  props.reduce((selection: Any, prop: Any) => {\n    if (typeof obj[prop] === 'undefined') {\n      return selection\n    }\n\n    selection[prop] = obj[prop]\n    return selection\n  }, {})\n", "import {defer, shareReplay} from 'rxjs'\nimport {map} from 'rxjs/operators'\n\nexport const eventSourcePolyfill = defer(() => import('@sanity/eventsource')).pipe(\n  map(({default: EventSource}) => EventSource as unknown as typeof globalThis.EventSource),\n  shareReplay(1),\n)\n", "import {\n  catchError,\n  concat,\n  mergeMap,\n  Observable,\n  of,\n  type OperatorFunction,\n  throwError,\n  timer,\n} from 'rxjs'\n\nimport {ConnectionFailedError} from './eventsource'\n\n/**\n * Note: connection failure is not the same as network disconnect which may happen more frequent.\n * The EventSource instance will automatically reconnect in case of a network disconnect, however,\n * in some rare cases a ConnectionFailed Error will be thrown and this operator explicitly retries these\n */\nexport function reconnectOnConnectionFailure<T>(): OperatorFunction<T, T | {type: 'reconnect'}> {\n  return function (source: Observable<T>) {\n    return source.pipe(\n      catchError((err, caught) => {\n        if (err instanceof ConnectionFailedError) {\n          return concat(of({type: 'reconnect' as const}), timer(1000).pipe(mergeMap(() => caught)))\n        }\n        return throwError(() => err)\n      }),\n    )\n  }\n}\n", "import {Observable, of, throwError} from 'rxjs'\nimport {filter, map} from 'rxjs/operators'\n\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport {\n  type Any,\n  type ListenEvent,\n  type ListenOptions,\n  type ListenParams,\n  type MutationEvent,\n} from '../types'\nimport defaults from '../util/defaults'\nimport {pick} from '../util/pick'\nimport {_getDataUrl} from './dataMethods'\nimport {encodeQueryString} from './encodeQueryString'\nimport {connectEventSource} from './eventsource'\nimport {eventSourcePolyfill} from './eventsourcePolyfill'\nimport {reconnectOnConnectionFailure} from './reconnectOnConnectionFailure'\n\n// Limit is 16K for a _request_, eg including headers. Have to account for an\n// unknown range of headers, but an average EventSource request from Chrome seems\n// to have around 700 bytes of cruft, so let us account for 1.2K to be \"safe\"\nconst MAX_URL_LENGTH = 16000 - 1200\n\nconst possibleOptions = [\n  'includePreviousRevision',\n  'includeResult',\n  'includeMutations',\n  'includeAllVersions',\n  'visibility',\n  'effectFormat',\n  'tag',\n]\n\nconst defaultOptions = {\n  includeResult: true,\n}\n\n/**\n * Set up a listener that will be notified when mutations occur on documents matching the provided query/filter.\n *\n * @param query - GROQ-filter to listen to changes for\n * @param params - Optional query parameters\n * @param options - Optional listener options\n * @public\n */\nexport function _listen<R extends Record<string, Any> = Record<string, Any>>(\n  this: SanityClient | ObservableSanityClient,\n  query: string,\n  params?: ListenParams,\n): Observable<MutationEvent<R>>\n/**\n * Set up a listener that will be notified when mutations occur on documents matching the provided query/filter.\n *\n * @param query - GROQ-filter to listen to changes for\n * @param params - Optional query parameters\n * @param options - Optional listener options\n * @public\n */\nexport function _listen<R extends Record<string, Any> = Record<string, Any>>(\n  this: SanityClient | ObservableSanityClient,\n  query: string,\n  params?: ListenParams,\n  options?: ListenOptions,\n): Observable<ListenEvent<R>>\n/** @public */\nexport function _listen<R extends Record<string, Any> = Record<string, Any>>(\n  this: SanityClient | ObservableSanityClient,\n  query: string,\n  params?: ListenParams,\n  opts: ListenOptions = {},\n): Observable<MutationEvent<R> | ListenEvent<R>> {\n  const {url, token, withCredentials, requestTagPrefix} = this.config()\n  const tag = opts.tag && requestTagPrefix ? [requestTagPrefix, opts.tag].join('.') : opts.tag\n  const options = {...defaults(opts, defaultOptions), tag}\n  const listenOpts = pick(options, possibleOptions)\n  const qs = encodeQueryString({query, params, options: {tag, ...listenOpts}})\n\n  const uri = `${url}${_getDataUrl(this, 'listen', qs)}`\n  if (uri.length > MAX_URL_LENGTH) {\n    return throwError(() => new Error('Query too large for listener'))\n  }\n\n  const listenFor = options.events ? options.events : ['mutation']\n\n  const esOptions: EventSourceInit & {headers?: Record<string, string>} = {}\n  if (withCredentials) {\n    esOptions.withCredentials = true\n  }\n\n  if (token) {\n    esOptions.headers = {\n      Authorization: `Bearer ${token}`,\n    }\n  }\n\n  const initEventSource = () =>\n    // use polyfill if there is no global EventSource or if we need to set headers\n    (typeof EventSource === 'undefined' || esOptions.headers\n      ? eventSourcePolyfill\n      : of(EventSource)\n    ).pipe(map((EventSource) => new EventSource(uri, esOptions)))\n\n  return connectEventSource(initEventSource, listenFor).pipe(\n    reconnectOnConnectionFailure(),\n    filter((event) => listenFor.includes(event.type)),\n    map(\n      (event) =>\n        ({\n          type: event.type,\n          ...('data' in event ? (event.data as object) : {}),\n        }) as MutationEvent<R> | ListenEvent<R>,\n    ),\n  )\n}\n", "import {\n  finalize,\n  merge,\n  type MonoTypeOperatorFunction,\n  Observable,\n  share,\n  type ShareConfig,\n  tap,\n} from 'rxjs'\n\nexport type ShareReplayLatestConfig<T> = ShareConfig<T> & {predicate: (value: T) => boolean}\n\n/**\n * A variant of share that takes a predicate function to determine which value to replay to new subscribers\n * @param predicate - Predicate function to determine which value to replay\n */\nexport function shareReplayLatest<T>(predicate: (value: T) => boolean): MonoTypeOperatorFunction<T>\n\n/**\n * A variant of share that takes a predicate function to determine which value to replay to new subscribers\n * @param config - ShareConfig with additional predicate function\n */\nexport function shareReplayLatest<T>(\n  config: ShareReplayLatestConfig<T>,\n): MonoTypeOperatorFunction<T>\n\n/**\n * A variant of share that takes a predicate function to determine which value to replay to new subscribers\n * @param configOrPredicate - Predicate function to determine which value to replay\n * @param config - Optional ShareConfig\n */\nexport function shareReplayLatest<T>(\n  configOrPredicate: ShareReplayLatestConfig<T> | ShareReplayLatestConfig<T>['predicate'],\n  config?: ShareConfig<T>,\n) {\n  return _shareReplayLatest(\n    typeof configOrPredicate === 'function'\n      ? {predicate: configOrPredicate, ...config}\n      : configOrPredicate,\n  )\n}\nfunction _shareReplayLatest<T>(config: ShareReplayLatestConfig<T>): MonoTypeOperatorFunction<T> {\n  return (source: Observable<T>) => {\n    let latest: T | undefined\n    let emitted = false\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const {predicate, ...shareConfig} = config\n\n    const wrapped = source.pipe(\n      tap((value) => {\n        if (config.predicate(value)) {\n          emitted = true\n          latest = value\n        }\n      }),\n      finalize(() => {\n        emitted = false\n        latest = undefined\n      }),\n      share(shareConfig),\n    )\n    const emitLatest = new Observable<T>((subscriber) => {\n      if (emitted) {\n        subscriber.next(\n          // this cast is safe because of the emitted check which asserts that we got T from the source\n          latest as T,\n        )\n      }\n      subscriber.complete()\n    })\n    return merge(wrapped, emitLatest)\n  }\n}\n", "import {catchError, concat, EMPTY, mergeMap, Observable, of} from 'rxjs'\nimport {finalize, map} from 'rxjs/operators'\n\nimport {CorsOriginError} from '../http/errors'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {\n  LiveEvent,\n  LiveEventGoAway,\n  LiveEventMessage,\n  LiveEventReconnect,\n  LiveEventRestart,\n  LiveEventWelcome,\n  SyncTag,\n} from '../types'\nimport {shareReplayLatest} from '../util/shareReplayLatest'\nimport * as validate from '../validators'\nimport {_getDataUrl} from './dataMethods'\nimport {connectEventSource} from './eventsource'\nimport {eventSourcePolyfill} from './eventsourcePolyfill'\nimport {reconnectOnConnectionFailure} from './reconnectOnConnectionFailure'\n\nconst requiredApiVersion = '2021-03-25'\n\n/**\n * @public\n */\nexport class LiveClient {\n  #client: SanityClient | ObservableSanityClient\n  constructor(client: SanityClient | ObservableSanityClient) {\n    this.#client = client\n  }\n\n  /**\n   * Requires `apiVersion` to be `2021-03-25` or later.\n   */\n  events({\n    includeDrafts = false,\n    tag: _tag,\n  }: {\n    includeDrafts?: boolean\n    /**\n     * Optional request tag for the listener. Use to identify the request in logs.\n     *\n     * @defaultValue `undefined`\n     */\n    tag?: string\n  } = {}): Observable<LiveEvent> {\n    validate.resourceGuard('live', this.#client.config())\n    const {\n      projectId,\n      apiVersion: _apiVersion,\n      token,\n      withCredentials,\n      requestTagPrefix,\n    } = this.#client.config()\n    const apiVersion = _apiVersion.replace(/^v/, '')\n    if (apiVersion !== 'X' && apiVersion < requiredApiVersion) {\n      throw new Error(\n        `The live events API requires API version ${requiredApiVersion} or later. ` +\n          `The current API version is ${apiVersion}. ` +\n          `Please update your API version to use this feature.`,\n      )\n    }\n    if (includeDrafts && !token && !withCredentials) {\n      throw new Error(\n        `The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.`,\n      )\n    }\n    const path = _getDataUrl(this.#client, 'live/events')\n    const url = new URL(this.#client.getUrl(path, false))\n    const tag = _tag && requestTagPrefix ? [requestTagPrefix, _tag].join('.') : _tag\n    if (tag) {\n      url.searchParams.set('tag', tag)\n    }\n    if (includeDrafts) {\n      url.searchParams.set('includeDrafts', 'true')\n    }\n    const esOptions: EventSourceInit & {headers?: Record<string, string>} = {}\n    if (includeDrafts && token) {\n      esOptions.headers = {\n        Authorization: `Bearer ${token}`,\n      }\n    }\n    if (includeDrafts && withCredentials) {\n      esOptions.withCredentials = true\n    }\n\n    const key = `${url.href}::${JSON.stringify(esOptions)}`\n    const existing = eventsCache.get(key)\n\n    if (existing) {\n      return existing\n    }\n\n    const initEventSource = () =>\n      // use polyfill if there is no global EventSource or if we need to set headers\n      (typeof EventSource === 'undefined' || esOptions.headers\n        ? eventSourcePolyfill\n        : of(EventSource)\n      ).pipe(map((EventSource) => new EventSource(url.href, esOptions)))\n\n    const events = connectEventSource(initEventSource, [\n      'message',\n      'restart',\n      'welcome',\n      'reconnect',\n      'goaway',\n    ]).pipe(\n      reconnectOnConnectionFailure(),\n      map((event) => {\n        if (event.type === 'message') {\n          const {data, ...rest} = event\n          // Splat data properties from the eventsource message onto the returned event\n          return {...rest, tags: (data as {tags: SyncTag[]}).tags} as LiveEventMessage\n        }\n        return event as LiveEventRestart | LiveEventReconnect | LiveEventWelcome | LiveEventGoAway\n      }),\n    )\n\n    // Detect if CORS is allowed, the way the CORS is checked supports preflight caching, so when the EventSource boots up it knows it sees the preflight was already made and we're good to go\n    const checkCors = fetchObservable(url, {\n      method: 'OPTIONS',\n      mode: 'cors',\n      credentials: esOptions.withCredentials ? 'include' : 'omit',\n      headers: esOptions.headers,\n    }).pipe(\n      mergeMap(() => EMPTY),\n      catchError(() => {\n        // If the request fails, then we assume it was due to CORS, and we rethrow a special error that allows special handling in userland\n        throw new CorsOriginError({projectId: projectId!})\n      }),\n    )\n    const observable = concat(checkCors, events).pipe(\n      finalize(() => eventsCache.delete(key)),\n      shareReplayLatest({\n        predicate: (event) => event.type === 'welcome',\n      }),\n    )\n    eventsCache.set(key, observable)\n    return observable\n  }\n}\n\nfunction fetchObservable(url: URL, init: RequestInit) {\n  return new Observable((observer) => {\n    const controller = new AbortController()\n    const signal = controller.signal\n    fetch(url, {...init, signal: controller.signal}).then(\n      (response) => {\n        observer.next(response)\n        observer.complete()\n      },\n      (err) => {\n        if (!signal.aborted) {\n          observer.error(err)\n        }\n      },\n    )\n    return () => controller.abort()\n  })\n}\n\nconst eventsCache = new Map<string, Observable<LiveEvent>>()\n", "import {lastValue<PERSON>rom, type Observable} from 'rxjs'\n\nimport {_request} from '../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {DatasetAclMode, DatasetResponse, DatasetsResponse, HttpRequest} from '../types'\nimport * as validate from '../validators'\n\n/** @internal */\nexport class ObservableDatasetsClient {\n  #client: ObservableSanityClient\n  #httpRequest: HttpRequest\n  constructor(client: ObservableSanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Create a new dataset with the given name\n   *\n   * @param name - Name of the dataset to create\n   * @param options - Options for the dataset\n   */\n  create(name: string, options?: {aclMode?: DatasetAclMode}): Observable<DatasetResponse> {\n    return _modify<DatasetResponse>(this.#client, this.#httpRequest, 'PUT', name, options)\n  }\n\n  /**\n   * Edit a dataset with the given name\n   *\n   * @param name - Name of the dataset to edit\n   * @param options - New options for the dataset\n   */\n  edit(name: string, options?: {aclMode?: DatasetAclMode}): Observable<DatasetResponse> {\n    return _modify<DatasetResponse>(this.#client, this.#httpRequest, 'PATCH', name, options)\n  }\n\n  /**\n   * Delete a dataset with the given name\n   *\n   * @param name - Name of the dataset to delete\n   */\n  delete(name: string): Observable<{deleted: true}> {\n    return _modify<{deleted: true}>(this.#client, this.#httpRequest, 'DELETE', name)\n  }\n\n  /**\n   * Fetch a list of datasets for the configured project\n   */\n  list(): Observable<DatasetsResponse> {\n    return _request<DatasetsResponse>(this.#client, this.#httpRequest, {\n      uri: '/datasets',\n      tag: null,\n    })\n  }\n}\n\n/** @internal */\nexport class DatasetsClient {\n  #client: SanityClient\n  #httpRequest: HttpRequest\n  constructor(client: SanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Create a new dataset with the given name\n   *\n   * @param name - Name of the dataset to create\n   * @param options - Options for the dataset\n   */\n  create(name: string, options?: {aclMode?: DatasetAclMode}): Promise<DatasetResponse> {\n    validate.resourceGuard('dataset', this.#client.config())\n    return lastValueFrom(\n      _modify<DatasetResponse>(this.#client, this.#httpRequest, 'PUT', name, options),\n    )\n  }\n\n  /**\n   * Edit a dataset with the given name\n   *\n   * @param name - Name of the dataset to edit\n   * @param options - New options for the dataset\n   */\n  edit(name: string, options?: {aclMode?: DatasetAclMode}): Promise<DatasetResponse> {\n    validate.resourceGuard('dataset', this.#client.config())\n    return lastValueFrom(\n      _modify<DatasetResponse>(this.#client, this.#httpRequest, 'PATCH', name, options),\n    )\n  }\n\n  /**\n   * Delete a dataset with the given name\n   *\n   * @param name - Name of the dataset to delete\n   */\n  delete(name: string): Promise<{deleted: true}> {\n    validate.resourceGuard('dataset', this.#client.config())\n    return lastValueFrom(_modify<{deleted: true}>(this.#client, this.#httpRequest, 'DELETE', name))\n  }\n\n  /**\n   * Fetch a list of datasets for the configured project\n   */\n  list(): Promise<DatasetsResponse> {\n    validate.resourceGuard('dataset', this.#client.config())\n    return lastValueFrom(\n      _request<DatasetsResponse>(this.#client, this.#httpRequest, {uri: '/datasets', tag: null}),\n    )\n  }\n}\n\nfunction _modify<R = unknown>(\n  client: SanityClient | ObservableSanityClient,\n  httpRequest: HttpRequest,\n  method: 'DELETE' | 'PATCH' | 'PUT',\n  name: string,\n  options?: {aclMode?: DatasetAclMode},\n) {\n  validate.resourceGuard('dataset', client.config())\n  validate.dataset(name)\n  return _request<R>(client, httpRequest, {\n    method,\n    uri: `/datasets/${name}`,\n    body: options,\n    tag: null,\n  })\n}\n", "import {lastValue<PERSON>rom, type Observable} from 'rxjs'\n\nimport {_request} from '../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {HttpRequest, SanityProject} from '../types'\nimport * as validate from '../validators'\n\n/** @internal */\nexport class ObservableProjectsClient {\n  #client: ObservableSanityClient\n  #httpRequest: HttpRequest\n  constructor(client: ObservableSanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Fetch a list of projects the authenticated user has access to.\n   *\n   * @param options - Options for the list request\n   * @param options.includeMembers - Whether to include members in the response (default: true)\n   */\n  list(options?: {includeMembers?: true}): Observable<SanityProject[]>\n  list(options?: {includeMembers?: false}): Observable<Omit<SanityProject, 'members'>[]>\n  list(options?: {\n    includeMembers?: boolean\n  }): Observable<SanityProject[] | Omit<SanityProject, 'members'>[]> {\n    validate.resourceGuard('projects', this.#client.config())\n    const uri = options?.includeMembers === false ? '/projects?includeMembers=false' : '/projects'\n    return _request<SanityProject[]>(this.#client, this.#httpRequest, {uri})\n  }\n\n  /**\n   * Fetch a project by project ID\n   *\n   * @param projectId - ID of the project to fetch\n   */\n  getById(projectId: string): Observable<SanityProject> {\n    validate.resourceGuard('projects', this.#client.config())\n    return _request<SanityProject>(this.#client, this.#httpRequest, {uri: `/projects/${projectId}`})\n  }\n}\n\n/** @internal */\nexport class ProjectsClient {\n  #client: SanityClient\n  #httpRequest: HttpRequest\n  constructor(client: SanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Fetch a list of projects the authenticated user has access to.\n   *\n   * @param options - Options for the list request\n   * @param options.includeMembers - Whether to include members in the response (default: true)\n   */\n  list(options?: {includeMembers?: true}): Promise<SanityProject[]>\n  list(options?: {includeMembers?: false}): Promise<Omit<SanityProject, 'members'>[]>\n  list(options?: {includeMembers?: boolean}): Promise<SanityProject[]> {\n    validate.resourceGuard('projects', this.#client.config())\n    const uri = options?.includeMembers === false ? '/projects?includeMembers=false' : '/projects'\n    return lastValueFrom(_request<SanityProject[]>(this.#client, this.#httpRequest, {uri}))\n  }\n\n  /**\n   * Fetch a project by project ID\n   *\n   * @param projectId - ID of the project to fetch\n   */\n  getById(projectId: string): Promise<SanityProject> {\n    validate.resourceGuard('projects', this.#client.config())\n    return lastValueFrom(\n      _request<SanityProject>(this.#client, this.#httpRequest, {uri: `/projects/${projectId}`}),\n    )\n  }\n}\n", "import {lastValueFrom, type Observable} from 'rxjs'\n\nimport {_request} from '../data/dataMethods'\nimport type {ObservableSanityClient, SanityClient} from '../SanityClient'\nimport type {CurrentSanityUser, HttpRequest, SanityUser} from '../types'\n\n/** @public */\nexport class ObservableUsersClient {\n  #client: ObservableSanityClient\n  #httpRequest: HttpRequest\n  constructor(client: ObservableSanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Fetch a user by user ID\n   *\n   * @param id - User ID of the user to fetch. If `me` is provided, a minimal response including the users role is returned.\n   */\n  getById<T extends 'me' | string>(\n    id: T,\n  ): Observable<T extends 'me' ? CurrentSanityUser : SanityUser> {\n    return _request<T extends 'me' ? CurrentSanityUser : SanityUser>(\n      this.#client,\n      this.#httpRequest,\n      {uri: `/users/${id}`},\n    )\n  }\n}\n\n/** @public */\nexport class UsersClient {\n  #client: SanityClient\n  #httpRequest: HttpRequest\n  constructor(client: SanityClient, httpRequest: HttpRequest) {\n    this.#client = client\n    this.#httpRequest = httpRequest\n  }\n\n  /**\n   * Fetch a user by user ID\n   *\n   * @param id - User ID of the user to fetch. If `me` is provided, a minimal response including the users role is returned.\n   */\n  getById<T extends 'me' | string>(\n    id: T,\n  ): Promise<T extends 'me' ? CurrentSanityUser : SanityUser> {\n    return lastValueFrom(\n      _request<T extends 'me' ? CurrentSanityUser : SanityUser>(this.#client, this.#httpRequest, {\n        uri: `/users/${id}`,\n      }),\n    )\n  }\n}\n", "import {lastValue<PERSON>rom, Observable} from 'rxjs'\n\nimport {AssetsClient, ObservableAssetsClient} from './assets/AssetsClient'\nimport {defaultConfig, initConfig} from './config'\nimport * as dataMethods from './data/dataMethods'\nimport {_listen} from './data/listen'\nimport {LiveClient} from './data/live'\nimport {ObservablePatch, Patch} from './data/patch'\nimport {ObservableTransaction, Transaction} from './data/transaction'\nimport {DatasetsClient, ObservableDatasetsClient} from './datasets/DatasetsClient'\nimport {ObservableProjectsClient, ProjectsClient} from './projects/ProjectsClient'\nimport type {\n  Action,\n  AllDocumentIdsMutationOptions,\n  AllDocumentsMutationOptions,\n  Any,\n  BaseActionOptions,\n  BaseMutationOptions,\n  ClientConfig,\n  ClientReturn,\n  FilteredResponseQueryOptions,\n  FirstDocumentIdMutationOptions,\n  FirstDocumentMutationOptions,\n  HttpRequest,\n  IdentifiedSanityDocumentStub,\n  InitializedClientConfig,\n  MultipleActionResult,\n  MultipleMutationResult,\n  Mutation,\n  MutationSelection,\n  PatchOperations,\n  PatchSelection,\n  QueryOptions,\n  QueryParams,\n  QueryWithoutParams,\n  RawQuerylessQueryResponse,\n  RawQueryResponse,\n  RawRequestOptions,\n  SanityDocument,\n  SanityDocumentStub,\n  SingleActionResult,\n  SingleMutationResult,\n  UnfilteredResponseQueryOptions,\n  UnfilteredResponseWithoutQuery,\n} from './types'\nimport {ObservableUsersClient, UsersClient} from './users/UsersClient'\n\nexport type {\n  _listen,\n  AssetsClient,\n  DatasetsClient,\n  LiveClient,\n  ObservableAssetsClient,\n  ObservableDatasetsClient,\n  ObservableProjectsClient,\n  ObservableUsersClient,\n  ProjectsClient,\n  UsersClient,\n}\n\n/** @public */\nexport class ObservableSanityClient {\n  assets: ObservableAssetsClient\n  datasets: ObservableDatasetsClient\n  live: LiveClient\n  projects: ObservableProjectsClient\n  users: ObservableUsersClient\n\n  /**\n   * Private properties\n   */\n  #clientConfig: InitializedClientConfig\n  #httpRequest: HttpRequest\n\n  /**\n   * Instance properties\n   */\n  listen = _listen\n\n  constructor(httpRequest: HttpRequest, config: ClientConfig = defaultConfig) {\n    this.config(config)\n\n    this.#httpRequest = httpRequest\n\n    this.assets = new ObservableAssetsClient(this, this.#httpRequest)\n    this.datasets = new ObservableDatasetsClient(this, this.#httpRequest)\n    this.live = new LiveClient(this)\n    this.projects = new ObservableProjectsClient(this, this.#httpRequest)\n    this.users = new ObservableUsersClient(this, this.#httpRequest)\n  }\n\n  /**\n   * Clone the client - returns a new instance\n   */\n  clone(): ObservableSanityClient {\n    return new ObservableSanityClient(this.#httpRequest, this.config())\n  }\n\n  /**\n   * Returns the current client configuration\n   */\n  config(): InitializedClientConfig\n  /**\n   * Reconfigure the client. Note that this _mutates_ the current client.\n   */\n  config(newConfig?: Partial<ClientConfig>): this\n  config(newConfig?: Partial<ClientConfig>): ClientConfig | this {\n    if (newConfig === undefined) {\n      return {...this.#clientConfig}\n    }\n\n    if (this.#clientConfig && this.#clientConfig.allowReconfigure === false) {\n      throw new Error(\n        'Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client',\n      )\n    }\n\n    this.#clientConfig = initConfig(newConfig, this.#clientConfig || {})\n    return this\n  }\n\n  /**\n   * Clone the client with a new (partial) configuration.\n   *\n   * @param newConfig - New client configuration properties, shallowly merged with existing configuration\n   */\n  withConfig(newConfig?: Partial<ClientConfig>): ObservableSanityClient {\n    const thisConfig = this.config()\n    return new ObservableSanityClient(this.#httpRequest, {\n      ...thisConfig,\n      ...newConfig,\n      stega: {\n        ...(thisConfig.stega || {}),\n        ...(typeof newConfig?.stega === 'boolean'\n          ? {enabled: newConfig.stega}\n          : newConfig?.stega || {}),\n      },\n    })\n  }\n\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams = QueryWithoutParams,\n    const G extends string = string,\n  >(query: G, params?: Q | QueryWithoutParams): Observable<ClientReturn<G, R>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Optional request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: G,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options?: FilteredResponseQueryOptions,\n  ): Observable<ClientReturn<G, R>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: string,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options: UnfilteredResponseQueryOptions,\n  ): Observable<RawQueryResponse<ClientReturn<G, R>>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: G,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options: UnfilteredResponseWithoutQuery,\n  ): Observable<RawQuerylessQueryResponse<ClientReturn<G, R>>>\n  fetch<R, Q, const G extends string>(\n    query: G,\n    params?: Q,\n    options?: QueryOptions,\n  ): Observable<RawQueryResponse<R> | R> {\n    return dataMethods._fetch<R, Q>(\n      this,\n      this.#httpRequest,\n      this.#clientConfig.stega,\n      query,\n      params,\n      options,\n    )\n  }\n\n  /**\n   * Fetch a single document with the given ID.\n   *\n   * @param id - Document ID to fetch\n   * @param options - Request options\n   */\n  getDocument<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options?: {tag?: string},\n  ): Observable<SanityDocument<R> | undefined> {\n    return dataMethods._getDocument<R>(this, this.#httpRequest, id, options)\n  }\n\n  /**\n   * Fetch multiple documents in one request.\n   * Should be used sparingly - performing a query is usually a better option.\n   * The order/position of documents is preserved based on the original array of IDs.\n   * If any of the documents are missing, they will be replaced by a `null` entry in the returned array\n   *\n   * @param ids - Document IDs to fetch\n   * @param options - Request options\n   */\n  getDocuments<R extends Record<string, Any> = Record<string, Any>>(\n    ids: string[],\n    options?: {tag?: string},\n  ): Observable<(SanityDocument<R> | null)[]> {\n    return dataMethods._getDocuments<R>(this, this.#httpRequest, ids, options)\n  }\n\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns an observable that resolves to an array containing the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns an observable that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Observable<SingleMutationResult>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns an observable that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Observable<MultipleMutationResult>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return dataMethods._create<R>(this, this.#httpRequest, document, 'create', options)\n  }\n\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns an observable that resolves to an array containing the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns an observable that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Observable<SingleMutationResult>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns an observable that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Observable<MultipleMutationResult>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return dataMethods._createIfNotExists<R>(this, this.#httpRequest, document, options)\n  }\n\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns an observable that resolves to an array containing the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns an observable that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Observable<SingleMutationResult>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns an observable that resolves to a mutation result object containing the created document ID.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Observable<MultipleMutationResult>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns an observable that resolves to the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return dataMethods._createOrReplace<R>(this, this.#httpRequest, document, options)\n  }\n\n  /**\n   * Deletes a document with the given document ID.\n   * Returns an observable that resolves to the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns an observable that resolves to an array containing the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns an observable that resolves to a mutation result object containing the deleted document ID.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete(id: string, options: FirstDocumentIdMutationOptions): Observable<SingleMutationResult>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns an observable that resolves to a mutation result object containing the deleted document ID.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete(id: string, options: AllDocumentIdsMutationOptions): Observable<MultipleMutationResult>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns an observable that resolves to the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns an observable that resolves to first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns an observable that resolves to an array containing the deleted documents.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns an observable that resolves to a mutation result object containing the ID of the first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete(\n    selection: MutationSelection,\n    options: FirstDocumentIdMutationOptions,\n  ): Observable<SingleMutationResult>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns an observable that resolves to a mutation result object containing the document IDs that were deleted.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete(\n    selection: MutationSelection,\n    options: AllDocumentIdsMutationOptions,\n  ): Observable<MultipleMutationResult>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns an observable that resolves to first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: string | MutationSelection,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return dataMethods._delete<R>(this, this.#httpRequest, selection, options)\n  }\n\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns an observable that resolves to the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options: FirstDocumentMutationOptions,\n  ): Observable<SanityDocument<R>>\n  /**\n   * Perform mutation operations against the configured dataset.\n   * Returns an observable that resolves to an array of the mutated documents.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options: AllDocumentsMutationOptions,\n  ): Observable<SanityDocument<R>[]>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns an observable that resolves to a mutation result object containing the document ID of the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options: FirstDocumentIdMutationOptions,\n  ): Observable<SingleMutationResult>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns an observable that resolves to a mutation result object containing the mutated document IDs.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options: AllDocumentIdsMutationOptions,\n  ): Observable<MultipleMutationResult>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns an observable that resolves to the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options?: BaseMutationOptions,\n  ): Observable<SanityDocument<R>>\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | ObservablePatch | ObservableTransaction,\n    options?:\n      | FirstDocumentMutationOptions\n      | AllDocumentsMutationOptions\n      | FirstDocumentIdMutationOptions\n      | AllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Observable<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return dataMethods._mutate<R>(this, this.#httpRequest, operations, options)\n  }\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param documentId - Document ID to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentId: string, operations?: PatchOperations): ObservablePatch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param documentIds - Array of document IDs to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentIds: string[], operations?: PatchOperations): ObservablePatch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - An object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(selection: MutationSelection, operations?: PatchOperations): ObservablePatch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - Document ID, an array of document IDs, or an object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(selection: PatchSelection, operations?: PatchOperations): ObservablePatch {\n    return new ObservablePatch(selection, operations, this)\n  }\n\n  /**\n   * Create a new transaction of mutations\n   *\n   * @param operations - Optional array of mutation operations to initialize the transaction instance with\n   */\n  transaction<R extends Record<string, Any> = Record<string, Any>>(\n    operations?: Mutation<R>[],\n  ): ObservableTransaction {\n    return new ObservableTransaction(operations, this)\n  }\n\n  /**\n   * Perform action operations against the configured dataset\n   *\n   * @param operations - Action operation(s) to execute\n   * @param options - Action options\n   */\n  action(\n    operations: Action | Action[],\n    options?: BaseActionOptions,\n  ): Observable<SingleActionResult | MultipleActionResult> {\n    return dataMethods._action(this, this.#httpRequest, operations, options)\n  }\n\n  /**\n   * Perform an HTTP request against the Sanity API\n   *\n   * @param options - Request options\n   */\n  request<R = Any>(options: RawRequestOptions): Observable<R> {\n    return dataMethods._request(this, this.#httpRequest, options)\n  }\n\n  /**\n   * Get a Sanity API URL for the URI provided\n   *\n   * @param uri - URI/path to build URL for\n   * @param canUseCdn - Whether or not to allow using the API CDN for this route\n   */\n  getUrl(uri: string, canUseCdn?: boolean): string {\n    return dataMethods._getUrl(this, uri, canUseCdn)\n  }\n\n  /**\n   * Get a Sanity API URL for the data operation and path provided\n   *\n   * @param operation - Data operation (eg `query`, `mutate`, `listen` or similar)\n   * @param path - Path to append after the operation\n   */\n  getDataUrl(operation: string, path?: string): string {\n    return dataMethods._getDataUrl(this, operation, path)\n  }\n}\n\n/** @public */\nexport class SanityClient {\n  assets: AssetsClient\n  datasets: DatasetsClient\n  live: LiveClient\n  projects: ProjectsClient\n  users: UsersClient\n\n  /**\n   * Observable version of the Sanity client, with the same configuration as the promise-based one\n   */\n  observable: ObservableSanityClient\n\n  /**\n   * Private properties\n   */\n  #clientConfig: InitializedClientConfig\n  #httpRequest: HttpRequest\n\n  /**\n   * Instance properties\n   */\n  listen = _listen\n\n  constructor(httpRequest: HttpRequest, config: ClientConfig = defaultConfig) {\n    this.config(config)\n\n    this.#httpRequest = httpRequest\n\n    this.assets = new AssetsClient(this, this.#httpRequest)\n    this.datasets = new DatasetsClient(this, this.#httpRequest)\n    this.live = new LiveClient(this)\n    this.projects = new ProjectsClient(this, this.#httpRequest)\n    this.users = new UsersClient(this, this.#httpRequest)\n\n    this.observable = new ObservableSanityClient(httpRequest, config)\n  }\n\n  /**\n   * Clone the client - returns a new instance\n   */\n  clone(): SanityClient {\n    return new SanityClient(this.#httpRequest, this.config())\n  }\n\n  /**\n   * Returns the current client configuration\n   */\n  config(): InitializedClientConfig\n  /**\n   * Reconfigure the client. Note that this _mutates_ the current client.\n   */\n  config(newConfig?: Partial<ClientConfig>): this\n  config(newConfig?: Partial<ClientConfig>): ClientConfig | this {\n    if (newConfig === undefined) {\n      return {...this.#clientConfig}\n    }\n\n    if (this.#clientConfig && this.#clientConfig.allowReconfigure === false) {\n      throw new Error(\n        'Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client',\n      )\n    }\n\n    if (this.observable) {\n      this.observable.config(newConfig)\n    }\n\n    this.#clientConfig = initConfig(newConfig, this.#clientConfig || {})\n    return this\n  }\n\n  /**\n   * Clone the client with a new (partial) configuration.\n   *\n   * @param newConfig - New client configuration properties, shallowly merged with existing configuration\n   */\n  withConfig(newConfig?: Partial<ClientConfig>): SanityClient {\n    const thisConfig = this.config()\n    return new SanityClient(this.#httpRequest, {\n      ...thisConfig,\n      ...newConfig,\n      stega: {\n        ...(thisConfig.stega || {}),\n        ...(typeof newConfig?.stega === 'boolean'\n          ? {enabled: newConfig.stega}\n          : newConfig?.stega || {}),\n      },\n    })\n  }\n\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams = QueryWithoutParams,\n    const G extends string = string,\n  >(query: G, params?: Q | QueryWithoutParams): Promise<ClientReturn<G, R>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Optional request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: G,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options?: FilteredResponseQueryOptions,\n  ): Promise<ClientReturn<G, R>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: G,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options: UnfilteredResponseQueryOptions,\n  ): Promise<RawQueryResponse<ClientReturn<G, R>>>\n  /**\n   * Perform a GROQ-query against the configured dataset.\n   *\n   * @param query - GROQ-query to perform\n   * @param params - Optional query parameters\n   * @param options - Request options\n   */\n  fetch<\n    R = Any,\n    Q extends QueryWithoutParams | QueryParams = QueryParams,\n    const G extends string = string,\n  >(\n    query: G,\n    params: Q extends QueryWithoutParams ? QueryWithoutParams : Q,\n    options: UnfilteredResponseWithoutQuery,\n  ): Promise<RawQuerylessQueryResponse<ClientReturn<G, R>>>\n  fetch<R, Q, const G extends string>(\n    query: G,\n    params?: Q,\n    options?: QueryOptions,\n  ): Promise<RawQueryResponse<ClientReturn<G, R>> | ClientReturn<G, R>> {\n    return lastValueFrom(\n      dataMethods._fetch<ClientReturn<G, R>, Q>(\n        this,\n        this.#httpRequest,\n        this.#clientConfig.stega,\n        query,\n        params,\n        options,\n      ),\n    )\n  }\n\n  /**\n   * Fetch a single document with the given ID.\n   *\n   * @param id - Document ID to fetch\n   * @param options - Request options\n   */\n  getDocument<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options?: {signal?: AbortSignal; tag?: string},\n  ): Promise<SanityDocument<R> | undefined> {\n    return lastValueFrom(dataMethods._getDocument<R>(this, this.#httpRequest, id, options))\n  }\n\n  /**\n   * Fetch multiple documents in one request.\n   * Should be used sparingly - performing a query is usually a better option.\n   * The order/position of documents is preserved based on the original array of IDs.\n   * If any of the documents are missing, they will be replaced by a `null` entry in the returned array\n   *\n   * @param ids - Document IDs to fetch\n   * @param options - Request options\n   */\n  getDocuments<R extends Record<string, Any> = Record<string, Any>>(\n    ids: string[],\n    options?: {signal?: AbortSignal; tag?: string},\n  ): Promise<(SanityDocument<R> | null)[]> {\n    return lastValueFrom(dataMethods._getDocuments<R>(this, this.#httpRequest, ids, options))\n  }\n\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns a promise that resolves to an array containing the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns a promise that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Promise<SingleMutationResult>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns a promise that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Promise<MultipleMutationResult>\n  /**\n   * Create a document. Requires a `_type` property. If no `_id` is provided, it will be generated by the database.\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  create<R extends Record<string, Any> = Record<string, Any>>(\n    document: SanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return lastValueFrom(\n      dataMethods._create<R>(this, this.#httpRequest, document, 'create', options),\n    )\n  }\n\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns a promise that resolves to an array containing the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns a promise that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Promise<SingleMutationResult>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns a promise that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Promise<MultipleMutationResult>\n  /**\n   * Create a document if no document with the same ID already exists.\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to create\n   * @param options - Mutation options\n   */\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  createIfNotExists<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return lastValueFrom(\n      dataMethods._createIfNotExists<R>(this, this.#httpRequest, document, options),\n    )\n  }\n\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns a promise that resolves to an array containing the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns a promise that resolves to a mutation result object containing the ID of the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: FirstDocumentIdMutationOptions,\n  ): Promise<SingleMutationResult>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns a promise that resolves to a mutation result object containing the created document ID.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options: AllDocumentIdsMutationOptions,\n  ): Promise<MultipleMutationResult>\n  /**\n   * Create a document if it does not exist, or replace a document with the same document ID\n   * Returns a promise that resolves to the created document.\n   *\n   * @param document - Document to either create or replace\n   * @param options - Mutation options\n   */\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  createOrReplace<R extends Record<string, Any> = Record<string, Any>>(\n    document: IdentifiedSanityDocumentStub<R>,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return lastValueFrom(\n      dataMethods._createOrReplace<R>(this, this.#httpRequest, document, options),\n    )\n  }\n\n  /**\n   * Deletes a document with the given document ID.\n   * Returns a promise that resolves to the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns a promise that resolves to an array containing the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns a promise that resolves to a mutation result object containing the deleted document ID.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete(id: string, options: FirstDocumentIdMutationOptions): Promise<SingleMutationResult>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns a promise that resolves to a mutation result object containing the deleted document ID.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete(id: string, options: AllDocumentIdsMutationOptions): Promise<MultipleMutationResult>\n  /**\n   * Deletes a document with the given document ID.\n   * Returns a promise that resolves to the deleted document.\n   *\n   * @param id - Document ID to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    id: string,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns a promise that resolves to first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns a promise that resolves to an array containing the deleted documents.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns a promise that resolves to a mutation result object containing the ID of the first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete(\n    selection: MutationSelection,\n    options: FirstDocumentIdMutationOptions,\n  ): Promise<SingleMutationResult>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns a promise that resolves to a mutation result object containing the document IDs that were deleted.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete(\n    selection: MutationSelection,\n    options: AllDocumentIdsMutationOptions,\n  ): Promise<MultipleMutationResult>\n  /**\n   * Deletes one or more documents matching the given query or document ID.\n   * Returns a promise that resolves to first deleted document.\n   *\n   * @param selection - An object with either an `id` or `query` key defining what to delete\n   * @param options - Options for the mutation\n   */\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: MutationSelection,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  delete<R extends Record<string, Any> = Record<string, Any>>(\n    selection: string | MutationSelection,\n    options?:\n      | AllDocumentIdsMutationOptions\n      | AllDocumentsMutationOptions\n      | BaseMutationOptions\n      | FirstDocumentIdMutationOptions\n      | FirstDocumentMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return lastValueFrom(dataMethods._delete<R>(this, this.#httpRequest, selection, options))\n  }\n\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns a promise that resolves to the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options: FirstDocumentMutationOptions,\n  ): Promise<SanityDocument<R>>\n  /**\n   * Perform mutation operations against the configured dataset.\n   * Returns a promise that resolves to an array of the mutated documents.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options: AllDocumentsMutationOptions,\n  ): Promise<SanityDocument<R>[]>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns a promise that resolves to a mutation result object containing the document ID of the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options: FirstDocumentIdMutationOptions,\n  ): Promise<SingleMutationResult>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns a promise that resolves to a mutation result object containing the mutated document IDs.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options: AllDocumentIdsMutationOptions,\n  ): Promise<MultipleMutationResult>\n  /**\n   * Perform mutation operations against the configured dataset\n   * Returns a promise that resolves to the first mutated document.\n   *\n   * @param operations - Mutation operations to execute\n   * @param options - Mutation options\n   */\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options?: BaseMutationOptions,\n  ): Promise<SanityDocument<R>>\n  mutate<R extends Record<string, Any> = Record<string, Any>>(\n    operations: Mutation<R>[] | Patch | Transaction,\n    options?:\n      | FirstDocumentMutationOptions\n      | AllDocumentsMutationOptions\n      | FirstDocumentIdMutationOptions\n      | AllDocumentIdsMutationOptions\n      | BaseMutationOptions,\n  ): Promise<\n    SanityDocument<R> | SanityDocument<R>[] | SingleMutationResult | MultipleMutationResult\n  > {\n    return lastValueFrom(dataMethods._mutate<R>(this, this.#httpRequest, operations, options))\n  }\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param documentId - Document ID to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentId: string, operations?: PatchOperations): Patch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param documentIds - Array of document IDs to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentIds: string[], operations?: PatchOperations): Patch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - An object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(selection: MutationSelection, operations?: PatchOperations): Patch\n\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - Document ID, an array of document IDs, or an object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentId: PatchSelection, operations?: PatchOperations): Patch {\n    return new Patch(documentId, operations, this)\n  }\n\n  /**\n   * Create a new transaction of mutations\n   *\n   * @param operations - Optional array of mutation operations to initialize the transaction instance with\n   */\n  transaction<R extends Record<string, Any> = Record<string, Any>>(\n    operations?: Mutation<R>[],\n  ): Transaction {\n    return new Transaction(operations, this)\n  }\n\n  /**\n   * Perform action operations against the configured dataset\n   * Returns a promise that resolves to the transaction result\n   *\n   * @param operations - Action operation(s) to execute\n   * @param options - Action options\n   */\n  action(\n    operations: Action | Action[],\n    options?: BaseActionOptions,\n  ): Promise<SingleActionResult | MultipleActionResult> {\n    return lastValueFrom(dataMethods._action(this, this.#httpRequest, operations, options))\n  }\n\n  /**\n   * Perform a request against the Sanity API\n   * NOTE: Only use this for Sanity API endpoints, not for your own APIs!\n   *\n   * @param options - Request options\n   * @returns Promise resolving to the response body\n   */\n  request<R = Any>(options: RawRequestOptions): Promise<R> {\n    return lastValueFrom(dataMethods._request<R>(this, this.#httpRequest, options))\n  }\n\n  /**\n   * Perform an HTTP request a `/data` sub-endpoint\n   * NOTE: Considered internal, thus marked as deprecated. Use `request` instead.\n   *\n   * @deprecated - Use `request()` or your own HTTP library instead\n   * @param endpoint - Endpoint to hit (mutate, query etc)\n   * @param body - Request body\n   * @param options - Request options\n   * @internal\n   */\n  dataRequest(endpoint: string, body: unknown, options?: BaseMutationOptions): Promise<Any> {\n    return lastValueFrom(dataMethods._dataRequest(this, this.#httpRequest, endpoint, body, options))\n  }\n\n  /**\n   * Get a Sanity API URL for the URI provided\n   *\n   * @param uri - URI/path to build URL for\n   * @param canUseCdn - Whether or not to allow using the API CDN for this route\n   */\n  getUrl(uri: string, canUseCdn?: boolean): string {\n    return dataMethods._getUrl(this, uri, canUseCdn)\n  }\n\n  /**\n   * Get a Sanity API URL for the data operation and path provided\n   *\n   * @param operation - Data operation (eg `query`, `mutate`, `listen` or similar)\n   * @param path - Path to append after the operation\n   */\n  getDataUrl(operation: string, path?: string): string {\n    return dataMethods._getDataUrl(this, operation, path)\n  }\n}\n", "import type {Middlewares} from 'get-it'\n\nimport {defineHttpRequest} from './http/request'\nimport type {Any, ClientConfig, HttpRequest} from './types'\n\nexport {validateApiPerspective} from './config'\nexport {\n  ChannelError,\n  connectEventSource,\n  ConnectionFailedError,\n  DisconnectError,\n  type EventSourceEvent,\n  type EventSourceInstance,\n  MessageError,\n  MessageParseError,\n  type ServerSentEvent,\n} from './data/eventsource'\nexport * from './data/patch'\nexport * from './data/transaction'\nexport {ClientError, CorsOriginError, ServerError} from './http/errors'\nexport * from './SanityClient'\nexport * from './types'\n\n/** @alpha */\nexport {adapter as unstable__adapter, environment as unstable__environment} from 'get-it'\n\n/**\n * Create the `requester` and `createClient` exports, that have environment specific middleware for node and browsers\n * @internal\n */\nexport default function defineCreateClientExports<\n  SanityClientType,\n  ClientConfigType extends ClientConfig,\n>(\n  envMiddleware: Middlewares,\n  ClassConstructor: new (httpRequest: HttpRequest, config: ClientConfigType) => SanityClientType,\n) {\n  // Set the http client to use for requests, and its environment specific middleware\n  const defaultRequester = defineHttpRequest(envMiddleware)\n\n  const createClient = (config: ClientConfigType) => {\n    const clientRequester = defineHttpRequest(envMiddleware)\n    return new ClassConstructor(\n      (options, requester) =>\n        (requester || clientRequester)({\n          maxRedirects: 0,\n          maxRetries: config.maxRetries,\n          retryDelay: config.retryDelay,\n          ...options,\n        } as Any),\n      config,\n    )\n  }\n\n  return {requester: defaultRequester, createClient}\n}\n", "import {printNoDefaultExport} from './warnings'\n\n/* @internal */\nexport function defineDeprecatedCreateClient<SanityClientType, ClientConfigType>(\n  createClient: (config: ClientConfigType) => SanityClientType,\n) {\n  return function deprecatedCreateClient(config: ClientConfigType) {\n    printNoDefaultExport()\n    return createClient(config)\n  }\n}\n", "import {agent, debug, headers} from 'get-it/middleware'\n\nimport {name, version} from '../../package.json'\n\nconst middleware = [\n  debug({verbose: true, namespace: 'sanity:client'}),\n  headers({'User-Agent': `${name} ${version}`}),\n\n  // Enable keep-alive, and in addition limit the number of sockets that can be opened.\n  // This avoids opening too many connections to the server if someone tries to execute\n  // a bunch of requests in parallel. It's recommended to have a concurrency limit\n  // at a \"higher limit\" (i.e. you shouldn't actually execute hundreds of requests in parallel),\n  // and this is mainly to minimize the impact for the network and server.\n  //\n  // We're currently matching the same defaults as browsers:\n  // https://stackoverflow.com/questions/26003756/is-there-a-limit-practical-or-otherwise-to-the-number-of-web-sockets-a-page-op\n  agent({\n    keepAlive: true,\n    maxSockets: 30,\n    maxTotalSockets: 256,\n  }),\n]\n\nexport default middleware\n", "import defineCreateClientExports, {type ClientConfig, SanityClient} from './defineCreateClient'\nimport {defineDeprecatedCreateClient} from './defineDeprecatedCreateClient'\nimport envMiddleware from './http/nodeMiddleware'\n\nexport * from './defineCreateClient'\n\nconst exp = defineCreateClientExports<SanityClient, ClientConfig>(envMiddleware, SanityClient)\n\n/** @public */\nexport const requester = exp.requester\n\n/**\n * @remarks\n * As of API version `v2025-02-19`, the default perspective used by the client has changed from `raw` to `published`. {@link https://www.sanity.io/changelog/676aaa9d-2da6-44fb-abe5-580f28047c10|Changelog}\n * @public\n */\nexport const createClient = exp.createClient\n\n/**\n * @public\n * @deprecated Use the named export `createClient` instead of the `default` export\n */\nconst deprecatedCreateClient = defineDeprecatedCreateClient(createClient)\nexport default deprecatedCreateClient\n"], "names": ["getIt", "retry", "jsonRequest", "jsonResponse", "progress", "observable", "Observable", "<PERSON><PERSON><PERSON><PERSON>", "defer", "isObservable", "of", "mergeMap", "validateObject", "validateInsert", "validators.validateObject", "validators.requireDocumentId", "validators.validateDocumentId", "config", "stega<PERSON>lean", "combineLatestWith", "from", "map", "filter", "validate.requestTag", "printPreviewDraftsDeprecationWarning", "validateApiPerspective", "printCdnPreviewDraftsWarning", "validators.resourceConfig", "uri", "validators.hasDataset", "lastValueFrom", "validators.validateAssetType", "defaults", "EventSource", "shareReplay", "catchError", "concat", "timer", "throwError", "tap", "finalize", "share", "merge", "validate.resourceGuard", "EMPTY", "name", "validate.dataset", "defaultConfig", "initConfig", "dataMethods._fetch", "dataMethods._getDocument", "dataMethods._getDocuments", "dataMethods._create", "dataMethods._createIfNotExists", "dataMethods._createOrReplace", "dataMethods._delete", "dataMethods._mutate", "dataMethods._action", "dataMethods._request", "dataMethods._getUrl", "dataMethods._getDataUrl", "dataMethods._dataRequest", "requester", "createClient", "printNoDefaultExport", "debug", "headers", "agent", "envMiddleware"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAKO,MAAM,oBAAoB,MAAM;AAAA,EACrC;AAAA,EACA,aAAuC;AAAA,EACvC;AAAA,EACA;AAAA,EAEA,YAAY,KAAU;AACd,UAAA,QAAQ,kBAAkB,GAAG;AACnC,UAAM,MAAM,OAAO,GACnB,OAAO,OAAO,MAAM,KAAK;AAAA,EAAA;AAE7B;AAGO,MAAM,oBAAoB,MAAM;AAAA,EACrC;AAAA,EACA,aAAuC;AAAA,EACvC;AAAA,EACA;AAAA,EAEA,YAAY,KAAU;AACd,UAAA,QAAQ,kBAAkB,GAAG;AACnC,UAAM,MAAM,OAAO,GACnB,OAAO,OAAO,MAAM,KAAK;AAAA,EAAA;AAE7B;AAEA,SAAS,kBAAkB,KAAsB;AACzC,QAAA,OAAO,IAAI,MACX,QAAQ;AAAA,IACZ,UAAU;AAAA,IACV,YAAY,IAAI;AAAA,IAChB,cAAc,cAAc,MAAM,GAAG;AAAA,IACrC,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AAGI,MAAA,KAAK,SAAS,KAAK;AACrB,WAAA,MAAM,UAAU,GAAG,KAAK,KAAK,MAAM,KAAK,OAAO,IACxC;AAIT,MAAI,gBAAgB,IAAI,KAAK,cAAc,IAAI,GAAG;AAC1C,UAAA,WAAW,KAAK,MAAM,SAAS,CAAA,GAC/B,QAAQ,SACX,MAAM,GAAG,CAA0B,EACnC,IAAI,CAAC,SAAS,KAAK,OAAO,WAAW,EACrC,OAAO,OAAO;AACb,QAAA,WAAW,MAAM,SAAS;AAAA,IAAQ,MAAM,KAAK;AAAA,GAAM,CAAC,KAAK;AACzD,WAAA,SAAS,SAAS,MACpB,YAAY;AAAA,SAAY,SAAS,SAAS,CAA0B,UAEtE,MAAM,UAAU,GAAG,KAAK,MAAM,WAAW,GAAG,QAAQ,IACpD,MAAM,UAAU,KAAK,OACd;AAAA,EAAA;AAIL,SAAA,KAAK,SAAS,KAAK,MAAM,eAC3B,MAAM,UAAU,KAAK,MAAM,aAC3B,MAAM,UAAU,KAAK,OACd,UAIT,MAAM,UAAU,KAAK,SAAS,KAAK,WAAW,iBAAiB,GAAG,GAC3D;AACT;AAEA,SAAS,gBAAgB,MAAkC;AACzD,SACE,cAAc,IAAI,KAClB,cAAc,KAAK,KAAK,KACxB,KAAK,MAAM,SAAS,mBACpB,OAAO,KAAK,MAAM,eAAgB;AAEtC;AAEA,SAAS,cAAc,MAAgC;AACrD,SACE,cAAc,IAAI,KAClB,cAAc,KAAK,KAAK,KACxB,KAAK,MAAM,SAAS,iBACpB,OAAO,KAAK,MAAM,eAAgB;AAEtC;AAEA,SAAS,cAAc,KAA0C;AACxD,SAAA,OAAO,OAAQ,YAAY,QAAQ,QAAQ,CAAC,MAAM,QAAQ,GAAG;AACtE;AAEA,SAAS,iBAAiB,KAAU;AAClC,QAAM,gBAAgB,IAAI,gBAAgB,IAAI,IAAI,aAAa,KAAK;AAC7D,SAAA,GAAG,IAAI,MAAM,eAAe,IAAI,GAAG,qBAAqB,IAAI,UAAU,GAAG,aAAa;AAC/F;AAEA,SAAS,cAAc,MAAW,KAAU;AAG1C,UAFqB,IAAI,QAAQ,cAAc,KAAK,IAAI,cAC7B,QAAQ,kBAAkB,MAAM,KAC3C,KAAK,UAAU,MAAM,MAAM,CAAC,IAAI;AAClD;AAGO,MAAM,wBAAwB,MAAM;AAAA,EACzC;AAAA,EACA;AAAA,EAEA,YAAY,EAAC,aAAiC;AAC5C,UAAM,iBAAiB,GACvB,KAAK,OAAO,mBACZ,KAAK,YAAY;AAEjB,UAAM,MAAM,IAAI,IAAI,oCAAoC,SAAS,MAAM;AACnE,QAAA,OAAO,WAAa,KAAa;AAC7B,YAAA,EAAC,WAAU;AACjB,UAAI,aAAa,IAAI,QAAQ,KAAK,GAClC,IAAI,aAAa,IAAI,UAAU,MAAM,GACrC,KAAK,eAAe,KACpB,KAAK,UAAU,sFAAsF,GAAG;AAAA,IAC1G;AACO,WAAA,UAAU,yGAAyG,GAAG;AAAA,EAAA;AAGjI;AC3HA,MAAM,YAAY;AAAA,EAChB,YAAY,CAAC,QAAa;AACxB,QAAI,IAAI,cAAc;AACd,YAAA,IAAI,YAAY,GAAG;AACpB,QAAI,IAAI,cAAc;AACrB,YAAA,IAAI,YAAY,GAAG;AAGpB,WAAA;AAAA,EAAA;AAEX;AAEA,SAAS,gBAAgB;AACvB,QAAM,OAAgC,CAAC;AAChC,SAAA;AAAA,IACL,YAAY,CAAC,QAAa;AACxB,YAAM,OAAO,IAAI,QAAQ,kBAAkB,GACrC,WAAW,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AACnD,iBAAW,OAAO;AACZ,SAAC,OAAO,KAAK,GAAG,MACpB,KAAK,GAAG,IAAI,IACZ,QAAQ,KAAK,GAAG;AAEX,aAAA;AAAA,IAAA;AAAA,EAEX;AACF;AAGO,SAAS,kBAAkB,eAAuC;AACvE,SAAOA,YAAM;AAAA,IACXC,aAAA,MAAM,EAAC,aAAY;AAAA,IACnB,GAAG;AAAA,IACH,cAAc;AAAA,IACdC,yBAAY;AAAA,IACZC,0BAAa;AAAA,IACbC,sBAAS;AAAA,IACT;AAAA,IACAC,wBAAW,EAAC,gBAAgBC,gBAAW,CAAA;AAAA,EAAA,CACxC;AACH;AAGA,SAAS,YAAY,KAAU,SAAiB,SAAc;AAExD,MAAA,QAAQ,eAAe,EAAU,QAAA;AAIrC,QAAM,SAAS,QAAQ,WAAW,SAAS,QAAQ,WAAW,QAExDC,YADM,QAAQ,OAAO,QAAQ,KACf,WAAW,aAAa,GACtC,sBACJ,IAAI,aACH,IAAI,SAAS,eAAe,OAC3B,IAAI,SAAS,eAAe,OAC5B,IAAI,SAAS,eAAe;AAW3B,UAAA,UAAUA,aAAY,sBAA4B,KAEhDN,mBAAM,YAAY,KAAK,SAAS,OAAO;AAChD;ACpEO,MAAM,8BAA8B,MAAM;AAAA,EACtC,OAAO;AAClB;AAQO,MAAM,wBAAwB,MAAM;AAAA,EAChC,OAAO;AAAA,EACP;AAAA,EACT,YAAY,SAAiB,QAAiB,UAAwB,CAAA,GAAI;AACxE,UAAM,SAAS,OAAO,GACtB,KAAK,SAAS;AAAA,EAAA;AAElB;AAMO,MAAM,qBAAqB,MAAM;AAAA,EAC7B,OAAO;AAAA,EACP;AAAA,EACT,YAAY,SAAiB,MAAe;AACpC,UAAA,OAAO,GACb,KAAK,OAAO;AAAA,EAAA;AAEhB;AAMO,MAAM,qBAAqB,MAAM;AAAA,EAC7B,OAAO;AAAA,EACP;AAAA,EACT,YAAY,SAAiB,MAAe,UAAwB,CAAA,GAAI;AACtE,UAAM,SAAS,OAAO,GACtB,KAAK,OAAO;AAAA,EAAA;AAEhB;AAMO,MAAM,0BAA0B,MAAM;AAAA,EAClC,OAAO;AAClB;AAYA,MAAM,kBAAkB,CAAC,gBAAgB,YAAY;AA+BrC,SAAA,mBACd,iBACA,QACA;AACA,SAAOO,WAAM,MAAM;AACjB,UAAM,KAAK,gBAAgB;AAC3B,WAAOC,KAAa,aAAA,EAAE,IAAI,KAAKC,KAAAA,GAAG,EAAE;AAAA,EAAA,CACrC,EAAE,KAAKC,KAAAA,SAAS,CAAC,OAAO,sBAAsB,IAAI,MAAM,CAAC,CAAC;AAG7D;AASA,SAAS,sBACP,IACA,QACA;AACO,SAAA,IAAIL,KAAAA,WAA4C,CAAC,aAAa;AAC7D,UAAA,WAAY,OAAoB,SAAS,MAAM,GAC/C,gBAAiB,OAAoB,SAAS,WAAW;AAI/D,aAAS,QAAQ,KAA2B;AAE1C,UAAI,UAAU,KAAK;AACjB,cAAM,CAAC,YAAY,KAAK,IAAI,WAAW,GAAmB;AACjD,iBAAA;AAAA,UACP,aACI,IAAI,kBAAkB,6CAA6C,EAAC,OAAO,MAAA,CAAM,IACjF,IAAI,cAAc,OAAO,MAA2B,SAAS,KAAK;AAAA,QACxE;AACA;AAAA,MAAA;AAOE,SAAG,eAAe,GAAG,SAEvB,SAAS,MAAM,IAAI,sBAAsB,+BAA+B,CAAC,IAChE,iBACT,SAAS,KAAK,EAAC,MAAM,aAA6B;AAAA,IAAA;AAItD,aAAS,SAAS;AAEhB,eAAS,KAAK,EAAC,MAAM,OAAA,CAAwB;AAAA,IAAA;AAG/C,aAAS,UAAU,SAAuB;AACxC,YAAM,CAAC,YAAY,KAAK,IAAI,WAAW,OAAO;AAC9C,UAAI,YAAY;AACL,iBAAA;AAAA,UACP,IAAI,kBAAkB,uCAAuC,EAAC,OAAO,WAAW,CAAA;AAAA,QAClF;AACA;AAAA,MAAA;AAEE,UAAA,QAAQ,SAAS,gBAAgB;AAG1B,iBAAA,MAAM,IAAI,aAAa,oBAAoB,OAAO,IAAI,GAAG,MAAM,IAAI,CAAC;AAC7E;AAAA,MAAA;AAEE,UAAA,QAAQ,SAAS,cAAc;AAIxB,iBAAA;AAAA,UACP,IAAI;AAAA,YACF,+BACG,MAAM,MAA4B,UAAU,eAC/C;AAAA,UAAA;AAAA,QAEJ;AACA;AAAA,MAAA;AAEF,eAAS,KAAK;AAAA,QACZ,MAAM,QAAQ;AAAA,QACd,IAAI,QAAQ;AAAA,QACZ,GAAI,MAAM,OAAO,EAAC,MAAM,MAAM,KAAA,IAAQ,CAAA;AAAA,MAAC,CACxC;AAAA,IAAA;AAGA,OAAA,iBAAiB,SAAS,OAAO,GAEhC,YACF,GAAG,iBAAiB,QAAQ,MAAM;AAI9B,UAAA,gBAAgB,CAAC,GAAG,oBAAI,IAAI,CAAC,GAAG,iBAAiB,GAAG,MAAM,CAAC,CAAC,EAE/D,OAAO,CAAC,SAAS,SAAS,WAAW,SAAS,UAAU,SAAS,WAAW;AAEjE,WAAA,cAAA,QAAQ,CAAC,SAAiB,GAAG,iBAAiB,MAAM,SAAS,CAAC,GAErE,MAAM;AACR,SAAA,oBAAoB,SAAS,OAAO,GACnC,YACF,GAAG,oBAAoB,QAAQ,MAAM,GAEvC,cAAc,QAAQ,CAAC,SAAiB,GAAG,oBAAoB,MAAM,SAAS,CAAC,GAC/E,GAAG,MAAM;AAAA,IACX;AAAA,EAAA,CACD;AACH;AAEA,SAAS,WACP,SACoE;AAChE,MAAA;AACI,UAAA,OAAO,OAAO,QAAQ,QAAS,YAAY,KAAK,MAAM,QAAQ,IAAI;AACjE,WAAA;AAAA,MACL;AAAA,MACA;AAAA,QACE,MAAM,QAAQ;AAAA,QACd,IAAI,QAAQ;AAAA,QACZ,GAAI,cAAc,IAAI,IAAI,CAAA,IAAK,EAAC,KAAI;AAAA,MAAA;AAAA,IAExC;AAAA,WACO,KAAK;AACL,WAAA,CAAC,KAAc,IAAI;AAAA,EAAA;AAE9B;AAEA,SAAS,oBAAoB,KAAU;AAChC,SAAA,IAAI,QAIL,IAAI,MAAM,cACL,IAAI,MAAM,cAGZ,OAAO,IAAI,SAAU,WAAW,IAAI,QAAQ,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,IAP3E,IAAI,WAAW;AAQ1B;AAEA,SAAS,cAAc,MAAc;AACnC,aAAW,KAAK;AACP,WAAA;AAEF,SAAA;AACT;AC5PO,SAAS,aAAa,KAAiC;AAC5D,MAAI,OAAO,OAAQ;AACV,WAAA,EAAC,IAAI,IAAG;AAGb,MAAA,MAAM,QAAQ,GAAG;AACnB,WAAO,EAAC,OAAO,kBAAkB,QAAQ,EAAC,KAAK,MAAI;AAGjD,MAAA,OAAO,OAAQ,YAAY,QAAQ,QAAQ,WAAW,OAAO,OAAO,IAAI,SAAU;AAC7E,WAAA,YAAY,OAAO,OAAO,IAAI,UAAW,YAAY,IAAI,WAAW,OACvE,EAAC,OAAO,IAAI,OAAO,QAAQ,IAAI,OAAA,IAC/B,EAAC,OAAO,IAAI,MAAK;AAGvB,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK;AAAA,CAAI;AAEX,QAAM,IAAI,MAAM;AAAA;AAAA,EAA0C,aAAa,EAAE;AAC3E;ACFO,MAAM,UAAU;AAAA,EACX;AAAA,EACA;AAAA,EACV,YAAY,WAA2B,aAA8B,IAAI;AAClE,SAAA,YAAY,WACjB,KAAK,aAAa;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASpB,IAAI,OAA2B;AACtB,WAAA,KAAK,QAAQ,OAAO,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlC,aAAa,OAA2B;AAC/B,WAAA,KAAK,QAAQ,gBAAgB,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS3C,eAAe,OAA2B;AACxC,WAAAM,OAAAA,eAAe,kBAAkB,KAAK,GAC/B,KAAK,QAAQ,kBAAkB,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS7C,MAAM,OAAuB;AACvB,QAAA,CAAC,MAAM,QAAQ,KAAK;AAChB,YAAA,IAAI,MAAM,qEAAqE;AAGlF,WAAA,KAAA,aAAa,OAAO,OAAO,CAAC,GAAG,KAAK,YAAY,EAAC,OAAO,MAAK,CAAC,GAC5D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQT,IAAI,OAAsC;AACjC,WAAA,KAAK,QAAQ,OAAO,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlC,IAAI,OAAsC;AACjC,WAAA,KAAK,QAAQ,OAAO,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUlC,OAAO,IAAoC,UAAkB,OAAoB;AAC/E,WAAAC,OAAA,eAAe,IAAI,UAAU,KAAK,GAC3B,KAAK,QAAQ,UAAU,EAAC,CAAC,EAAE,GAAG,UAAU,OAAM;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASvD,OAAO,UAAkB,OAAoB;AAC3C,WAAO,KAAK,OAAO,SAAS,GAAG,QAAQ,QAAQ,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAStD,QAAQ,UAAkB,OAAoB;AAC5C,WAAO,KAAK,OAAO,UAAU,GAAG,QAAQ,OAAO,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWtD,OAAO,UAAkB,OAAe,aAAsB,OAAqB;AAMjF,UAAM,SAAS,OAAO,cAAgB,OAAe,gBAAgB,IAC/D,aAAa,QAAQ,IAAI,QAAQ,IAAI,OACrC,WAAW,SAAS,KAAK,KAAK,IAAI,GAAG,QAAQ,WAAW,GACxD,WAAW,aAAa,KAAK,YAAY,IAAI,KAAK,UAClD,gBAAgB,GAAG,QAAQ,IAAI,UAAU,IAAI,QAAQ;AAC3D,WAAO,KAAK,OAAO,WAAW,eAAe,SAAS,CAAA,CAAE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1D,aAAa,KAAmB;AACzB,WAAA,KAAA,WAAW,eAAe,KACxB;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMT,YAAoC;AAC3B,WAAA,EAAC,GAAG,aAAa,KAAK,SAAS,GAAG,GAAG,KAAK,WAAU;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM7D,SAAiC;AAC/B,WAAO,KAAK,UAAU;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,QAAc;AACP,WAAA,KAAA,aAAa,CAAA,GACX;AAAA,EAAA;AAAA,EAGC,QAAQ,IAA2B,OAAY,QAAQ,IAAY;AAC5D,WAAAD,sBAAA,IAAI,KAAK,GACxB,KAAK,aAAa,OAAO,OAAO,IAAI,KAAK,YAAY;AAAA,MACnD,CAAC,EAAE,GAAG,OAAO,OAAO,IAAK,SAAS,KAAK,WAAW,EAAE,KAAM,CAAA,GAAI,KAAK;AAAA,IACpE,CAAA,GACM;AAAA,EAAA;AAAA,EAGC,KAAK,IAA2B,OAAkB;AAC1D,WAAO,KAAK,QAAQ,IAAI,OAAO,EAAK;AAAA,EAAA;AAExC;AAGO,MAAM,wBAAwB,UAAU;AAAA,EAC7C;AAAA,EAEA,YACE,WACA,YACA,QACA;AACA,UAAM,WAAW,UAAU,GAC3B,KAAK,UAAU;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,QAAyB;AAChB,WAAA,IAAI,gBAAgB,KAAK,WAAW,EAAC,GAAG,KAAK,WAAA,GAAa,KAAK,OAAO;AAAA,EAAA;AAAA,EAuC/E,OACE,SAQA;AACA,QAAI,CAAC,KAAK;AACR,YAAM,IAAI;AAAA,QACR;AAAA,MAEF;AAGF,UAAM,cAAc,OAAO,KAAK,aAAc,UACxC,OAAO,OAAO,OAAO,EAAC,aAAa,iBAAiB,GAAA,GAAO,OAAO;AACjE,WAAA,KAAK,QAAQ,OAAU,EAAC,OAAO,KAAK,YAAW,GAAU,IAAI;AAAA,EAAA;AAExE;AAGO,MAAM,cAAc,UAAU;AAAA,EACnC;AAAA,EACA,YAAY,WAA2B,YAA8B,QAAuB;AAC1F,UAAM,WAAW,UAAU,GAC3B,KAAK,UAAU;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,QAAe;AACN,WAAA,IAAI,MAAM,KAAK,WAAW,EAAC,GAAG,KAAK,WAAA,GAAa,KAAK,OAAO;AAAA,EAAA;AAAA,EAuCrE,OACE,SAQA;AACA,QAAI,CAAC,KAAK;AACR,YAAM,IAAI;AAAA,QACR;AAAA,MAEF;AAGF,UAAM,cAAc,OAAO,KAAK,aAAc,UACxC,OAAO,OAAO,OAAO,EAAC,aAAa,iBAAiB,GAAA,GAAO,OAAO;AACjE,WAAA,KAAK,QAAQ,OAAU,EAAC,OAAO,KAAK,YAAW,GAAU,IAAI;AAAA,EAAA;AAExE;AC7TA,MAAM,uBAAuB,EAAC,iBAAiB,GAAK;AAG7C,MAAM,gBAAgB;AAAA,EACjB;AAAA,EACA;AAAA,EACV,YAAY,aAAyB,CAAC,GAAG,eAAwB;AAC1D,SAAA,aAAa,YAClB,KAAK,QAAQ;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQf,OAA4D,KAAkC;AACjF,WAAAE,OAAA,eAAe,UAAU,GAAG,GAChC,KAAK,KAAK,EAAC,QAAQ,KAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAShC,kBACE,KACM;AACN,UAAM,KAAK;AACX,WAAAA,sBAA0B,IAAI,GAAG,GACjCC,yBAA6B,IAAI,GAAG,GAC7B,KAAK,KAAK,EAAC,CAAC,EAAE,GAAG,KAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS9B,gBACE,KACM;AACN,UAAM,KAAK;AACX,WAAAD,sBAA0B,IAAI,GAAG,GACjCC,yBAA6B,IAAI,GAAG,GAC7B,KAAK,KAAK,EAAC,CAAC,EAAE,GAAG,KAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS9B,OAAO,YAA0B;AAC/B,WAAAC,0BAA8B,UAAU,UAAU,GAC3C,KAAK,KAAK,EAAC,QAAQ,EAAC,IAAI,WAAU,EAAA,CAAE;AAAA,EAAA;AAAA,EAa7C,cAAc,IAAwC;AACpD,WAAK,MAIL,KAAK,QAAQ,IACN,QAJE,KAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAUhB,YAAwB;AACf,WAAA,CAAC,GAAG,KAAK,UAAU;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAM5B,SAAqB;AACnB,WAAO,KAAK,UAAU;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,QAAc;AACP,WAAA,KAAA,aAAa,CAAA,GACX;AAAA,EAAA;AAAA,EAGC,KAAK,KAAqB;AAC7B,WAAA,KAAA,WAAW,KAAK,GAAG,GACjB;AAAA,EAAA;AAEX;AAGO,MAAM,oBAAoB,gBAAgB;AAAA,EAC/C;AAAA,EACA,YAAY,YAAyB,QAAuB,eAAwB;AAClF,UAAM,YAAY,aAAa,GAC/B,KAAK,UAAU;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,QAAqB;AACZ,WAAA,IAAI,YAAY,CAAC,GAAG,KAAK,UAAU,GAAG,KAAK,SAAS,KAAK,KAAK;AAAA,EAAA;AAAA,EAqCvE,OACE,SAQA;AACA,QAAI,CAAC,KAAK;AACR,YAAM,IAAI;AAAA,QACR;AAAA,MAEF;AAGF,WAAO,KAAK,QAAQ;AAAA,MAClB,KAAK,UAAU;AAAA,MACf,OAAO,OAAO,EAAC,eAAe,KAAK,SAAQ,sBAAsB,WAAW,CAAE,CAAA;AAAA,IAChF;AAAA,EAAA;AAAA,EAyBF,MACE,mBACA,UACM;AACN,UAAM,YAAY,OAAO,YAAa,YAChC,UAAU,OAAO,qBAAsB,YAAY,6BAA6B,OAChF,sBACJ,OAAO,qBAAsB,aAC5B,WAAW,qBAAqB,QAAQ;AAGvC,QAAA;AACF,aAAO,KAAK,KAAK,EAAC,OAAO,kBAAkB,UAAA,GAAY;AAIzD,QAAI,WAAW;AACP,YAAA,QAAQ,SAAS,IAAI,MAAM,mBAAmB,IAAI,KAAK,OAAO,CAAC;AACrE,UAAI,EAAE,iBAAiB;AACf,cAAA,IAAI,MAAM,oDAAoD;AAGtE,aAAO,KAAK,KAAK,EAAC,OAAO,MAAM,UAAA,GAAY;AAAA,IAAA;AAS7C,QAAI,qBAAqB;AACjB,YAAA,QAAQ,IAAI,MAAM,mBAAmB,YAAY,CAAC,GAAG,KAAK,OAAO;AACvE,aAAO,KAAK,KAAK,EAAC,OAAO,MAAM,UAAA,GAAY;AAAA,IAAA;AAGtC,WAAA,KAAK,KAAK,EAAC,OAAO,EAAC,IAAI,mBAAmB,GAAG,SAAQ,GAAE;AAAA,EAAA;AAElE;AAGO,MAAM,8BAA8B,gBAAgB;AAAA,EACzD;AAAA,EACA,YAAY,YAAyB,QAAiC,eAAwB;AAC5F,UAAM,YAAY,aAAa,GAC/B,KAAK,UAAU;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,QAA+B;AACtB,WAAA,IAAI,sBAAsB,CAAC,GAAG,KAAK,UAAU,GAAG,KAAK,SAAS,KAAK,KAAK;AAAA,EAAA;AAAA,EAqCjF,OACE,SAQA;AACA,QAAI,CAAC,KAAK;AACR,YAAM,IAAI;AAAA,QACR;AAAA,MAEF;AAGF,WAAO,KAAK,QAAQ;AAAA,MAClB,KAAK,UAAU;AAAA,MACf,OAAO,OAAO,EAAC,eAAe,KAAK,SAAQ,sBAAsB,WAAW,CAAE,CAAA;AAAA,IAChF;AAAA,EAAA;AAAA,EAkBF,MACE,mBACA,UACM;AACA,UAAA,YAAY,OAAO,YAAa;AAEpC,QAAA,OAAO,qBAAsB,YAAY,6BAA6B;AAItE,aAAO,KAAK,KAAK,EAAC,OAAO,kBAAkB,UAAA,GAAY;AAIzD,QAAI,WAAW;AACP,YAAA,QAAQ,SAAS,IAAI,gBAAgB,mBAAmB,IAAI,KAAK,OAAO,CAAC;AAC/E,UAAI,EAAE,iBAAiB;AACf,cAAA,IAAI,MAAM,oDAAoD;AAGtE,aAAO,KAAK,KAAK,EAAC,OAAO,MAAM,UAAA,GAAY;AAAA,IAAA;AAGtC,WAAA,KAAK,KAAK,EAAC,OAAO,EAAC,IAAI,mBAAmB,GAAG,SAAQ,GAAE;AAAA,EAAA;AAElE;AC1XA,MAAM,gBAAgB;AAEf,SAAS,eAAeC,SAAa,YAAiB,IAAiC;AAC5F,QAAM,UAAe,CAAA,GAEf,QAAQ,UAAU,SAASA,QAAO;AACpC,YACF,QAAQ,gBAAgB,UAAU,KAAK,KAGrC,CAAC,UAAU,gBAAgB,CAACA,QAAO,sBAAsBA,QAAO,cAClE,QAAQ,aAAa,IAAIA,QAAO;AAGlC,QAAM,kBAAkB,CACtB,EAAA,OAAO,UAAU,kBAAoB,MACjCA,QAAO,kBACP,UAAU,kBAGV,UAAU,OAAO,UAAU,UAAY,MAAcA,QAAO,UAAU,UAAU;AACtF,SAAO,OAAO,OAAO,CAAC,GAAG,WAAW;AAAA,IAClC,SAAS,OAAO,OAAO,CAAA,GAAI,SAAS,UAAU,WAAW,EAAE;AAAA,IAC3D,SAAS,OAAO,UAAY,MAAc,IAAI,KAAK,MAAO;AAAA,IAC1D,OAAO,UAAU,SAASA,QAAO;AAAA,IACjC,MAAM;AAAA,IACN;AAAA,IACA,OACE,OAAO,UAAU,SAAU,YAAY,OAAOA,QAAO,SAAU,WAC3D,EAAC,GAAGA,QAAO,OAAO,GAAG,UAAU,UAC/B,UAAU,SAASA,QAAO;AAAA,EAAA,CACjC;AACH;AClCO,MAAM,oBAAoB,CAAC;AAAA,EAChC;AAAA,EACA,SAAS,CAAC;AAAA,EACV,UAAU,CAAA;AACZ,MAIM;AACE,QAAA,eAAe,IAAI,gBAAA,GAEnB,EAAC,KAAK,kBAAkB,aAAa,GAAG,KAAA,IAAQ;AAElD,SAAK,aAAa,OAAO,OAAO,GAAG,GACvC,aAAa,OAAO,SAAS,KAAK;AAGlC,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM;AAC9C,iBAAa,OAAO,IAAI,GAAG,IAAI,KAAK,UAAU,KAAK,CAAC;AAGtD,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,IAAI;AAExC,aAAO,aAAa,OAAO,KAAK,GAAG,KAAK,EAAE;AAIhD,SAAI,gBAAgB,MAAO,aAAa,OAAO,eAAe,OAAO,GAGjE,qBAAqB,MAAO,aAAa,OAAO,oBAAoB,OAAO,GAExE,IAAI,YAAY;AACzB,GCQM,gBAAgB,CAAC,OAAY,aAE1B,UAAU,KAAQ,SADX,OAAO,QAAU,MAAc,WAAW,OAIpD,mBAAmB,CAAC,UAA+B,CAAA,OAChD;AAAA,EACL,QAAQ,QAAQ;AAAA,EAChB,WAAW;AAAA,EACX,iBAAiB,cAAc,QAAQ,iBAAiB,EAAI;AAAA,EAC5D,YAAY,QAAQ,cAAc;AAAA,EAClC,uBAAuB,QAAQ;AAAA,EAC/B,qCAAqC,QAAQ;AAC/C,IAGI,aAAa,CAAC,UAAe,MAAM,SAAS,YAC5C,UAAU,CAAC,UAAe,MAAM,MAEhC,UAAU,CAAC,MAAa,SAC5B,KAAK,OAAO,CAAC,SAAS,SACpB,QAAQ,KAAK,GAAG,CAAC,IAAI,KACd,UACC,uBAAA,OAAO,IAAI,CAAC,GAElB,oBAAoB;AAGV,SAAA,OACd,QACA,aACA,QACA,OACA,UAAa,CAAA,GACb,UAAwB,IACa;AAC/B,QAAA,QACJ,WAAW,UACP;AAAA,IACE,GAAI,UAAU,CAAC;AAAA,IACf,GAAI,OAAO,QAAQ,SAAU,YAAY,EAAC,SAAS,QAAQ,MAAK,IAAI,QAAQ,SAAS,CAAA;AAAA,EACvF,IACA,QACA,SAAS,MAAM,UAAUC,WAAAA,WAAW,OAAO,IAAI,SAC/C,cACJ,QAAQ,mBAAmB,KAAQ,CAAC,QAAa,MAAM,CAAC,QAAa,IAAI,QAErE,EAAC,OAAO,MAAM,GAAG,SAAQ;AAAA;AAAA;AAAA,IAG7B,gBAAgB,OAAO,QAAQ,SAAW;AAAA;AAAA,IAE1C,iBAAiB,MAAM,UAAU,yBAAyB,QAAQ;AAAA,IAClE,GAAG;AAAA;AAAA;AAAA,IAGH,aAAa,QAAQ,mBAAmB,MAAS,QAAQ,gBAAgB;AAAA,EAC3E,GACM,UACJ,OAAO,QAAU,OAAe,OAAO,OAAS,MAC5C,EAAC,GAAG,MAAM,OAAO,EAAC,OAAO,KAAK,EAAA,IAC9B,MAEA,WAAW,aAAa,QAAQ,aAAa,SAAS,EAAC,OAAO,OAAM,GAAG,OAAO;AAC7E,SAAA,MAAM,UACT,SAAS;AAAA,IACPC,UAAA;AAAA,MACEC,KAAA;AAAA,QACE,QAAO,QAAA,EAAA,KAAA,WAAA;AAAA,iBAAA,QAAA,wCAA+B;AAAA,QAAE,CAAA,EAAA,KAAA,SAAA,GAAA;AAAA,iBAAA,EAAA;AAAA,QAAA,CAAA,EAAA;AAAA,UACtC,CAAC,EAAC,qBAAA,MAA0B;AAAA,QAAA;AAAA,MAC9B;AAAA,IAEJ;AAAA,IACAC,UAAA;AAAA,MACE,CAAC,CAAC,KAAK,oBAAoB,MAGrB;AACJ,cAAM,SAAS,qBAAqB,IAAI,QAAQ,IAAI,iBAAiB,KAAK;AAC1E,eAAO,YAAY,EAAC,GAAG,KAAK,QAAO;AAAA,MAAA;AAAA,IACrC;AAAA,EAGJ,IAAA,SAAS,KAAKA,UAAA,IAAI,WAAW,CAAC;AACpC;AAGO,SAAS,aACd,QACA,aACA,IACA,OAA6C,CAAA,GACF;AAC3C,QAAM,UAAU;AAAA,IACd,KAAK,YAAY,QAAQ,OAAO,EAAE;AAAA,IAClC,MAAM;AAAA,IACN,KAAK,KAAK;AAAA,IACV,QAAQ,KAAK;AAAA,EACf;AACA,SAAO,mBAAkD,QAAQ,aAAa,OAAO,EAAE;AAAA,IACrFC,UAAAA,OAAO,UAAU;AAAA,IACjBD,cAAI,CAAC,UAAU,MAAM,KAAK,aAAa,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,EAChE;AACF;AAGO,SAAS,cACd,QACA,aACA,KACA,OAA6C,CAAA,GACH;AAC1C,QAAM,UAAU;AAAA,IACd,KAAK,YAAY,QAAQ,OAAO,IAAI,KAAK,GAAG,CAAC;AAAA,IAC7C,MAAM;AAAA,IACN,KAAK,KAAK;AAAA,IACV,QAAQ,KAAK;AAAA,EACf;AACA,SAAO,mBAAiD,QAAQ,aAAa,OAAO,EAAE;AAAA,IACpFC,UAAAA,OAAO,UAAU;AAAA,IACjBD,UAAA,IAAI,CAAC,UAAe;AACZ,YAAA,UAAU,QAAQ,MAAM,KAAK,aAAa,CAAA,GAAI,CAAC,QAAa,IAAI,GAAG;AACzE,aAAO,IAAI,IAAI,CAAC,OAAO,QAAQ,EAAE,KAAK,IAAI;AAAA,IAC3C,CAAA;AAAA,EACH;AACF;AAGO,SAAS,mBACd,QACA,aACA,KACA,SAQA;AACW,SAAAN,OAAA,kBAAkB,qBAAqB,GAAG,GAC9C,QAAW,QAAQ,aAAa,KAAK,qBAAqB,OAAO;AAC1E;AAGO,SAAS,iBACd,QACA,aACA,KACA,SAQA;AACW,SAAAA,OAAA,kBAAkB,mBAAmB,GAAG,GAC5C,QAAW,QAAQ,aAAa,KAAK,mBAAmB,OAAO;AACxE;AAGO,SAAS,QACd,QACA,aACA,WACA,SAQA;AACO,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,EAAC,WAAW,CAAC,EAAC,QAAQ,aAAa,SAAS,EAAC,CAAC,EAAC;AAAA,IAC/C;AAAA,EACF;AACF;AAGO,SAAS,QACd,QACA,aACA,WACA,SAQA;AACI,MAAA;AACA,uBAAqB,SAAS,qBAAqB,kBACrD,MAAM,EAAC,OAAO,UAAU,gBACf,qBAAqB,eAAe,qBAAqB,wBAClE,MAAM,UAAU,UAAA,IAEhB,MAAM;AAGR,QAAM,OAAO,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,GAAG,GACtC,gBAAiB,WAAW,QAAQ,iBAAkB;AACrD,SAAA,aAAa,QAAQ,aAAa,UAAU,EAAC,WAAW,MAAM,cAAa,GAAG,OAAO;AAC9F;AAKO,SAAS,QACd,QACA,aACA,SACA,SACuD;AACjD,QAAA,OAAO,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO,GAClD,gBAAiB,WAAW,QAAQ,iBAAkB,QACtD,sCACH,WAAW,QAAQ,uCAAwC,QACxD,SAAU,WAAW,QAAQ,UAAW;AAEvC,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,EAAC,SAAS,MAAM,eAAe,qCAAqC,OAAM;AAAA,IAC1E;AAAA,EACF;AACF;AAKO,SAAS,aACd,QACA,aACA,UACA,MACA,UAAe,IACV;AACC,QAAA,aAAa,aAAa,UAC1B,WAAW,aAAa,WACxBR,WAAU,aAAa,SAIvB,WAAW,cAAc,WAAW,KAAK,kBAAkB,IAAI,GAC/D,SAAS,CAAC,cAAc,CAAC,YAAY,SAAS,SAAS,mBACvD,cAAc,SAAS,WAAW,IAClC,cAAc,QAAQ,aACtB,EAAC,SAAS,OAAO,KAAK,SAAS,aAAa,iBAAiB,cAAa,SAE1E,MAAM,YAAY,QAAQ,UAAU,WAAW,GAE/C,aAAa;AAAA,IACjB,QAAQ,SAAS,QAAQ;AAAA,IACzB;AAAA,IACA,MAAM;AAAA,IACN,MAAM,SAAS,SAAY;AAAA,IAC3B,OAAO,cAAc,iBAAiB,OAAO;AAAA,IAC7C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,QAAQ;AAAA,IACrB,iBAAiB,QAAQ;AAAA,IACzB,iBAAiB,MAAM,QAAQ,eAAe,IAAI,gBAAgB,CAAC,IAAI;AAAA,IACvE;AAAA,IACA,WAAWA;AAAAA,IACX,QAAQ,QAAQ;AAAA,IAChB,OAAO,QAAQ;AAAA,IACf,gBAAgB,QAAQ;AAAA,IACxB,QAAQ,QAAQ;AAAA,EAClB;AAEA,SAAO,mBAAmB,QAAQ,aAAa,UAAU,EAAE;AAAA,IACzDe,UAAAA,OAAO,UAAU;AAAA,IACjBD,UAAAA,IAAI,OAAO;AAAA,IACXA,UAAA,IAAI,CAAC,QAAQ;AACX,UAAI,CAAC;AACI,eAAA;AAIH,YAAA,UAAU,IAAI,WAAW,CAAC;AAChC,UAAI,QAAQ;AACV,eAAO,cACH,QAAQ,CAAC,KAAK,QAAQ,CAAC,EAAE,WACzB,QAAQ,IAAI,CAAC,QAAa,IAAI,QAAQ;AAI5C,YAAM,MAAM,cAAc,eAAe,eACnC,MAAM,cAAc,QAAQ,CAAC,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,IAAI,CAAC,QAAa,IAAI,EAAE;AACjF,aAAA;AAAA,QACL,eAAe,IAAI;AAAA,QACnB;AAAA,QACA,CAAC,GAAG,GAAG;AAAA,MACT;AAAA,IACD,CAAA;AAAA,EACH;AACF;AAKO,SAAS,QACd,QACA,aACA,KACA,IACA,UAAe,IAGf;AACA,QAAM,WAAW,EAAC,CAAC,EAAE,GAAG,OAClB,OAAO,OAAO,OAAO,EAAC,aAAa,IAAM,iBAAiB,GAAA,GAAO,OAAO;AACvE,SAAA,aAAa,QAAQ,aAAa,UAAU,EAAC,WAAW,CAAC,QAAQ,EAAC,GAAG,IAAI;AAClF;AAEA,MAAM,gBAAgB,CAAC,WACpB,OAAO,OAAA,EAAS,YAAY,UAAa,OAAO,SAAS,cAAc,UACxE,OAAO,OAAO,EAAE,wBAAwB,MAAM,QAE1C,UAAU,CAAC,QAAgB,QAC/B,cAAc,MAAM,KAAK,IAAI,WAAW,YAAY,QAAQ,OAAO,CAAC,GAEhE,WAAW,CAAC,QAAgB,QAChC,cAAc,MAAM,KAAK,IAAI,WAAW,YAAY,QAAQ,QAAQ,CAAC,GAEjE,QAAQ,CAAC,QAAgB,QAC7B,cAAc,MAAM,KAAK,IAAI,WAAW,YAAY,QAAQ,OAAO,EAAE,CAAC,GAElE,aAAa,CAAC,QAAgB,QAClC,cAAc,MAAM,KAAK,IAAI,WAAW,YAAY,QAAQ,QAAQ,CAAC,GAEjE,YAAY,CAAC,QAAgB,QACjC,cAAc,MAAM,KAAK,IAAI,WAAW,YAAY,QAAQ,WAAW,EAAE,CAAC,GAEtE,SAAS,CAAC,QAAgB,QAC9B,IAAI,WAAW,QAAQ,KACvB,QAAQ,QAAQ,GAAG,KACnB,SAAS,QAAQ,GAAG,KACpB,MAAM,QAAQ,GAAG,KACjB,WAAW,QAAQ,GAAG,KACtB,UAAU,QAAQ,GAAG;AAKP,SAAA,mBACd,QACA,aACA,SACiC;AACjC,QAAM,MAAM,QAAQ,OAAQ,QAAQ,KAC9BJ,WAAS,OAAO,OAAA,GAIhB,YACJ,OAAO,QAAQ,YAAc,MACzB,CAAC,OAAO,MAAM,EAAE,QAAQ,QAAQ,UAAU,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG,IAC3E,QAAQ;AAEd,MAAI,UAAU,QAAQ,UAAUA,SAAO,WAAW;AAElD,QAAM,MACJ,QAAQ,OAAOA,SAAO,mBAClB,CAACA,SAAO,kBAAkB,QAAQ,GAAG,EAAE,KAAK,GAAG,IAC/C,QAAQ,OAAOA,SAAO;AAO5B,MALI,OAAO,QAAQ,QAAQ,SACzB,QAAQ,QAAQ,EAAC,KAAKM,OAAAA,WAAoB,GAAG,GAAG,GAAG,QAAQ,MAAA,IAIzD,CAAC,OAAO,QAAQ,MAAM,EAAE,QAAQ,QAAQ,UAAU,KAAK,KAAK,KAAK,QAAQ,QAAQ,GAAG,GAAG;AACnF,UAAA,kBAAkB,QAAQ,mBAAmBN,SAAO;AACtD,wBAAoB,UAAa,oBAAoB,OACvD,QAAQ,QAAQ,EAAC,iBAAiB,GAAG,QAAQ;AAEzC,UAAA,oBAAoB,QAAQ,eAAeA,SAAO;AACpD,WAAO,oBAAsB,QAC3B,sBAAsB,mBACxBO,OAAAA,wCAEFC,OAAAA,uBAAuB,iBAAiB,GACxC,QAAQ,QAAQ;AAAA,MACd,aAAa,MAAM,QAAQ,iBAAiB,IACxC,kBAAkB,KAAK,GAAG,IAC1B;AAAA,MACJ,GAAG,QAAQ;AAAA,IAAA,IAIT,MAAM,QAAQ,iBAAiB,KAAK,kBAAkB,SAAS;AAAA,IAE/D,sBAAsB,mBACtB,sBAAsB,aACxB,WAEA,SAAS,IACTC,oCAIA,KAAA,QAAQ,oBACV,QAAQ,QAAQ,EAAC,GAAG,QAAQ,OAAO,iBAAiB,QAAQ,oBAG1D,QAAQ,gBAAgB,OAC1B,QAAQ,QAAQ,EAAC,aAAa,SAAS,GAAG,QAAQ,UAGhD,UAAU,QAAQ,aAAa,cACjC,QAAQ,QAAQ,EAAC,WAAW,WAAW,GAAG,QAAQ;EAAK;AAI3D,QAAM,aAAa;AAAA,IACjBT;AAAAA,IACA,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,QAAQ,KAAK,MAAM;AAAA,IACjC,CAAA;AAAA,EAAA,GAGG,UAAU,IAAIX,KAAA;AAAA,IAAgC,CAAC,eACnD,YAAY,YAAYW,SAAO,SAAU,EAAE,UAAU,UAAU;AAAA,EACjE;AAEO,SAAA,QAAQ,SAAS,QAAQ,KAAK,iBAAiB,QAAQ,MAAM,CAAC,IAAI;AAC3E;AAKgB,SAAA,SAAY,QAAgB,aAA0B,SAA6B;AAMjG,SALmB,mBAAsB,QAAQ,aAAa,OAAO,EAAE;AAAA,IACrEK,UAAAA,OAAO,CAAC,UAAe,MAAM,SAAS,UAAU;AAAA,IAChDD,cAAI,CAAC,UAAe,MAAM,IAAI;AAAA,EAChC;AAGF;AAKgB,SAAA,YAAY,QAAgB,WAAmB,MAAuB;AAC9E,QAAAJ,WAAS,OAAO,OAAO;AACzB,MAAAA,SAAO,wBAAwB,GAAG;AACpCU,WAAAA,eAA0BV,QAAM;AAC1B,UAAA,eAAe,iBAAiBA,QAAM,GACtCW,OAAM,SAAS,SAAY,GAAG,SAAS,IAAI,IAAI,KAAK;AAC1D,WAAO,GAAG,YAAY,IAAIA,IAAG,GAAG,QAAQ,YAAY,IAAI;AAAA,EAAA;AAEpD,QAAA,UAAUC,kBAAsBZ,QAAM,GACtC,UAAU,IAAI,SAAS,IAAI,OAAO;AAExC,SAAO,QADK,SAAS,SAAY,GAAG,OAAO,IAAI,IAAI,KAAK,OACtC,GAAG,QAAQ,YAAY,IAAI;AAC/C;AAKO,SAAS,QAAQ,QAAgB,KAAa,YAAY,IAAe;AAC9E,QAAM,EAAC,KAAK,WAAU,OAAO,OAAO;AAE7B,SAAA,GADM,YAAY,SAAS,GACpB,IAAI,IAAI,QAAQ,OAAO,EAAE,CAAC;AAC1C;AAKA,SAAS,iBAAoB,QAAkD;AAC7E,SAAO,CAAC,UACC,IAAIX,KAAA,WAAW,CAAC,aAAa;AAClC,UAAM,QAAQ,MAAM,SAAS,MAAM,kBAAkB,MAAM,CAAC;AAExD,QAAA,UAAU,OAAO,SAAS;AACtB,YAAA;AACN;AAAA,IAAA;AAEI,UAAA,eAAe,MAAM,UAAU,QAAQ;AAC7C,WAAA,OAAO,iBAAiB,SAAS,KAAK,GAC/B,MAAM;AACX,aAAO,oBAAoB,SAAS,KAAK,GACzC,aAAa,YAAY;AAAA,IAC3B;AAAA,EAAA,CACD;AAEL;AAGA,MAAM,0BAA0B,EAAQ,WAAW;AAQnD,SAAS,kBAAkB,QAAsB;AAK3C,MAAA;AACF,WAAO,IAAI,aAAa,QAAQ,UAAU,8BAA8B,YAAY;AAItF,QAAM,QAAQ,IAAI,MAAM,QAAQ,UAAU,4BAA4B;AACtE,SAAA,MAAM,OAAO,cAEN;AACT;AAEA,MAAM,mBAAmB,CAACW,YAA4C;AAChE,MAAA,CAACA,QAAO,wBAAwB;AAC5B,UAAA,IAAI,MAAM,yDAAyD;AAE3E,QAAM,EAAC,MAAM,OAAMA,QAAO,wBAAwB;AAElD,UAAQ,MAAM;AAAA,IACZ,KAAK,WAAW;AACR,YAAA,WAAW,GAAG,MAAM,GAAG;AAC7B,UAAI,SAAS,WAAW;AAChB,cAAA,IAAI,MAAM,oDAAoD;AAEtE,aAAO,aAAa,SAAS,CAAC,CAAC,aAAa,SAAS,CAAC,CAAC;AAAA,IAAA;AAAA,IAEzD,KAAK;AACH,aAAO,aAAa,EAAE;AAAA,IAExB,KAAK;AACH,aAAO,oBAAoB,EAAE;AAAA,IAE/B,KAAK;AACH,aAAO,eAAe,EAAE;AAAA,IAE1B;AAEE,YAAM,IAAI,MAAM,8BAA8B,KAAK,SAAU,CAAA,EAAE;AAAA,EAAA;AAErE;ACnkBO,MAAM,uBAAuB;AAAA,EAClC;AAAA,EACA;AAAA,EACA,YAAY,QAAgC,aAA0B;AAC/D,SAAA,UAAU,QACf,KAAK,eAAe;AAAA,EAAA;AAAA,EAwCtB,OACE,WACA,MACA,SAC0F;AAC1F,WAAO,QAAQ,KAAK,SAAS,KAAK,cAAc,WAAW,MAAM,OAAO;AAAA,EAAA;AAE5E;AAGO,MAAM,aAAa;AAAA,EACxB;AAAA,EACA;AAAA,EACA,YAAY,QAAsB,aAA0B;AACrD,SAAA,UAAU,QACf,KAAK,eAAe;AAAA,EAAA;AAAA,EAuCtB,OACE,WACA,MACA,SACyD;AACnD,UAAA,aAAa,QAAQ,KAAK,SAAS,KAAK,cAAc,WAAW,MAAM,OAAO;AAC7E,WAAAa,KAAA;AAAA,MACL,WAAW;AAAA,QACTR,UAAAA,OAAO,CAAC,UAAe,MAAM,SAAS,UAAU;AAAA,QAChDD,UAAA;AAAA,UACE,CAAC,UACE,MACE,KAAK;AAAA,QAAA;AAAA,MACZ;AAAA,IAEJ;AAAA,EAAA;AAEJ;AAEA,SAAS,QACP,QACA,aACA,WACA,MACA,OAA2B,IAC+D;AAC1FU,SAAAA,kBAA6B,SAAS;AAGlC,MAAA,OAAO,KAAK,WAAW;AACvB,UAAQ,CAAC,KAAK,WAChB,OAAO,CAAC,MAAM;AAGhB,QAAMd,WAAS,OAAO,UAChB,UAAU,gBAAgB,MAAM,IAAI,GACpC,EAAC,KAAK,OAAO,OAAO,aAAa,YAAY,UAAU,OAAM,IAAI,SACjE,QAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAI,WACF,MAAM,WAAW,OAAO,IACxB,MAAM,aAAa,OAAO,MAC1B,MAAM,YAAY,OAAO,MAGpB,mBAAmB,QAAQ,aAAa;AAAA,IAC7C;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,QAAQ,WAAW;AAAA,IAC5B,KAAK,oBAAoBA,UAAQ,SAAS;AAAA,IAC1C,SAAS,QAAQ,cAAc,EAAC,gBAAgB,QAAQ,YAAA,IAAe,CAAC;AAAA,IACxE;AAAA,IACA;AAAA,EAAA,CACD;AACH;AAEA,SAAS,oBAAoBA,UAAiC,WAAqC;AAC3F,QAAA,oBAAoB,cAAc,UAAU,WAAW;AAEzD,MAAAA,SAAO,wBAAwB,GAAG;AACpC,UAAM,EAAC,MAAM,OAAMA,SAAO,wBAAwB;AAClD,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MAEF,KAAK;AACI,eAAA,aAAa,EAAE,WAAW,iBAAiB;AAAA,MAEpD,KAAK;AACH,eAAO,oBAAoB,EAAE;AAAA,MAE/B,KAAK;AACI,eAAA,eAAe,EAAE,WAAW,iBAAiB;AAAA,MAEtD;AAEE,cAAM,IAAI,MAAM,8BAA8B,KAAK,SAAU,CAAA,EAAE;AAAA,IAAA;AAAA,EACnE;AAGI,QAAA,UAAUY,OAAW,WAAWZ,QAAM;AACrC,SAAA,UAAU,iBAAiB,IAAI,OAAO;AAC/C;AAEA,SAAS,gBAAgB,MAA2B,MAAW;AAC7D,SAAI,OAAO,OAAS,OAAe,EAAE,gBAAgB,QAC5C,OAGF,OAAO;AAAA,IACZ;AAAA,MACE,UAAU,KAAK,qBAAqB,KAAQ,SAAY,KAAK;AAAA,MAC7D,aAAa,KAAK;AAAA,IACpB;AAAA,IACA;AAAA,EACF;AACF;AC5NA,IAAe,WAAA,CAAC,KAAUe,cACxB,OAAO,KAAKA,SAAQ,EACjB,OAAO,OAAO,KAAK,GAAG,CAAC,EACvB,OAAO,CAAC,QAAQ,UACf,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI,IAAM,MAAcA,UAAS,IAAI,IAAI,IAAI,IAAI,GAEpE,SACN,CAAA,CAAS;ACPH,MAAA,OAAO,CAAC,KAAU,UAC7B,MAAM,OAAO,CAAC,WAAgB,UACxB,OAAO,IAAI,IAAI,IAAM,QAIzB,UAAU,IAAI,IAAI,IAAI,IAAI,IACnB,YACN,EAAE,GCPM,sBAAsBxB,KAAAA,MAAM,MAAM,OAAO,qBAAqB,CAAC,EAAE;AAAA,EAC5Ea,UAAAA,IAAI,CAAC,EAAC,SAASY,aAAA,MAAiBA,YAAuD;AAAA,EACvFC,KAAAA,YAAY,CAAC;AACf;ACYO,SAAS,+BAAgF;AAC9F,SAAO,SAAU,QAAuB;AACtC,WAAO,OAAO;AAAA,MACZC,gBAAW,CAAC,KAAK,WACX,eAAe,wBACVC,YAAO1B,KAAAA,GAAG,EAAC,MAAM,YAAA,CAAqB,GAAG2B,WAAM,GAAI,EAAE,KAAK1B,KAAA,SAAS,MAAM,MAAM,CAAC,CAAC,IAEnF2B,gBAAW,MAAM,GAAG,CAC5B;AAAA,IACH;AAAA,EACF;AACF;ACPA,MAAM,iBAAiB,OAEjB,kBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAEM,iBAAiB;AAAA,EACrB,eAAe;AACjB;AA8BO,SAAS,QAEd,OACA,QACA,OAAsB,CAAA,GACyB;AACzC,QAAA,EAAC,KAAK,OAAO,iBAAiB,iBAAoB,IAAA,KAAK,UACvD,MAAM,KAAK,OAAO,mBAAmB,CAAC,kBAAkB,KAAK,GAAG,EAAE,KAAK,GAAG,IAAI,KAAK,KACnF,UAAU,EAAC,GAAG,SAAS,MAAM,cAAc,GAAG,OAC9C,aAAa,KAAK,SAAS,eAAe,GAC1C,KAAK,kBAAkB,EAAC,OAAO,QAAQ,SAAS,EAAC,KAAK,GAAG,WAAU,EAAA,CAAE,GAErE,MAAM,GAAG,GAAG,GAAG,YAAY,MAAM,UAAU,EAAE,CAAC;AACpD,MAAI,IAAI,SAAS;AACf,WAAOA,gBAAW,MAAM,IAAI,MAAM,8BAA8B,CAAC;AAG7D,QAAA,YAAY,QAAQ,SAAS,QAAQ,SAAS,CAAC,UAAU,GAEzD,YAAkE,CAAC;AACzE,SAAI,oBACF,UAAU,kBAAkB,KAG1B,UACF,UAAU,UAAU;AAAA,IAClB,eAAe,UAAU,KAAK;AAAA,MAW3B,mBAPiB;AAAA;AAAA,KAErB,OAAO,cAAgB,OAAe,UAAU,UAC7C,sBACA5B,QAAG,WAAW,GAChB,KAAKW,UAAA,IAAI,CAACY,iBAAgB,IAAIA,aAAY,KAAK,SAAS,CAAC,CAAC;AAAA,KAEnB,SAAS,EAAE;AAAA,IACpD,6BAA6B;AAAA,IAC7BX,iBAAO,CAAC,UAAU,UAAU,SAAS,MAAM,IAAI,CAAC;AAAA,IAChDD,UAAA;AAAA,MACE,CAAC,WACE;AAAA,QACC,MAAM,MAAM;AAAA,QACZ,GAAI,UAAU,QAAS,MAAM,OAAkB,CAAA;AAAA,MACjD;AAAA,IAAA;AAAA,EAEN;AACF;ACnFgB,SAAA,kBACd,mBACAJ,SACA;AACO,SAAA;AAAA,IACL,OAAO,qBAAsB,aACzB,EAAC,WAAW,mBAAmB,GAAGA,YAClC;AAAA,EACN;AACF;AACA,SAAS,mBAAsBA,SAAiE;AAC9F,SAAO,CAAC,WAA0B;AAChC,QAAI,QACA,UAAU;AAGd,UAAM,EAAC,WAAW,GAAG,gBAAeA,SAE9B,UAAU,OAAO;AAAA,MACrBsB,KAAA,IAAI,CAAC,UAAU;AACT,QAAAtB,QAAO,UAAU,KAAK,MACxB,UAAU,IACV,SAAS;AAAA,MAAA,CAEZ;AAAA,MACDuB,KAAAA,SAAS,MAAM;AACb,kBAAU,IACV,SAAS;AAAA,MAAA,CACV;AAAA,MACDC,KAAAA,MAAM,WAAW;AAAA,IAEb,GAAA,aAAa,IAAInC,gBAAc,CAAC,eAAe;AAC/C,iBACF,WAAW;AAAA;AAAA,QAET;AAAA,MAAA,GAGJ,WAAW,SAAS;AAAA,IAAA,CACrB;AACM,WAAAoC,KAAA,MAAM,SAAS,UAAU;AAAA,EAClC;AACF;ACpDA,MAAM,qBAAqB;AAKpB,MAAM,WAAW;AAAA,EACtB;AAAA,EACA,YAAY,QAA+C;AACzD,SAAK,UAAU;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,OAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,KAAK;AAAA,EACP,IAQI,IAA2B;AAC7BC,WAAAA,cAAuB,QAAQ,KAAK,QAAQ,QAAQ;AAC9C,UAAA;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,IAAA,IACE,KAAK,QAAQ,UACX,aAAa,YAAY,QAAQ,MAAM,EAAE;AAC3C,QAAA,eAAe,OAAO,aAAa;AACrC,YAAM,IAAI;AAAA,QACR,4CAA4C,kBAAkB,yCAC9B,UAAU;AAAA,MAE5C;AAEE,QAAA,iBAAiB,CAAC,SAAS,CAAC;AAC9B,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAEI,UAAA,OAAO,YAAY,KAAK,SAAS,aAAa,GAC9C,MAAM,IAAI,IAAI,KAAK,QAAQ,OAAO,MAAM,EAAK,CAAC,GAC9C,MAAM,QAAQ,mBAAmB,CAAC,kBAAkB,IAAI,EAAE,KAAK,GAAG,IAAI;AACxE,WACF,IAAI,aAAa,IAAI,OAAO,GAAG,GAE7B,iBACF,IAAI,aAAa,IAAI,iBAAiB,MAAM;AAE9C,UAAM,YAAkE,CAAC;AACrE,qBAAiB,UACnB,UAAU,UAAU;AAAA,MAClB,eAAe,UAAU,KAAK;AAAA,IAG9B,IAAA,iBAAiB,oBACnB,UAAU,kBAAkB;AAG9B,UAAM,MAAM,GAAG,IAAI,IAAI,KAAK,KAAK,UAAU,SAAS,CAAC,IAC/C,WAAW,YAAY,IAAI,GAAG;AAEhC,QAAA;AACK,aAAA;AAUT,UAAM,SAAS,mBAPS;AAAA;AAAA,OAErB,OAAO,cAAgB,OAAe,UAAU,UAC7C,sBACAjC,KAAAA,GAAG,WAAW,GAChB,KAAKW,UAAAA,IAAI,CAACY,iBAAgB,IAAIA,aAAY,IAAI,MAAM,SAAS,CAAC,CAAC;AAAA,OAEhB;AAAA,MACjD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAA,EAAE;AAAA,MACD,6BAA6B;AAAA,MAC7BZ,UAAA,IAAI,CAAC,UAAU;AACT,YAAA,MAAM,SAAS,WAAW;AAC5B,gBAAM,EAAC,MAAM,GAAG,KAAA,IAAQ;AAExB,iBAAO,EAAC,GAAG,MAAM,MAAO,KAA2B,KAAI;AAAA,QAAA;AAElD,eAAA;AAAA,MACR,CAAA;AAAA,IAAA,GAIG,YAAY,gBAAgB,KAAK;AAAA,MACrC,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,aAAa,UAAU,kBAAkB,YAAY;AAAA,MACrD,SAAS,UAAU;AAAA,IACpB,CAAA,EAAE;AAAA,MACDV,KAAA,SAAS,MAAMiC,KAAAA,KAAK;AAAA,MACpBT,KAAAA,WAAW,MAAM;AAEf,cAAM,IAAI,gBAAgB,EAAC,WAAsB;AAAA,MAClD,CAAA;AAAA,IAEG,GAAA,aAAaC,KAAO,OAAA,WAAW,MAAM,EAAE;AAAA,MAC3CI,UAAAA,SAAS,MAAM,YAAY,OAAO,GAAG,CAAC;AAAA,MACtC,kBAAkB;AAAA,QAChB,WAAW,CAAC,UAAU,MAAM,SAAS;AAAA,MACtC,CAAA;AAAA,IACH;AACY,WAAA,YAAA,IAAI,KAAK,UAAU,GACxB;AAAA,EAAA;AAEX;AAEA,SAAS,gBAAgB,KAAU,MAAmB;AAC7C,SAAA,IAAIlC,KAAAA,WAAW,CAAC,aAAa;AAClC,UAAM,aAAa,IAAI,gBAAgB,GACjC,SAAS,WAAW;AACpB,WAAA,MAAA,KAAK,EAAC,GAAG,MAAM,QAAQ,WAAW,OAAO,CAAA,EAAE;AAAA,MAC/C,CAAC,aAAa;AACZ,iBAAS,KAAK,QAAQ,GACtB,SAAS,SAAS;AAAA,MACpB;AAAA,MACA,CAAC,QAAQ;AACF,eAAO,WACV,SAAS,MAAM,GAAG;AAAA,MAAA;AAAA,IAEtB,GAEK,MAAM,WAAW,MAAM;AAAA,EAAA,CAC/B;AACH;AAEA,MAAM,kCAAkB,IAAmC;AC1JpD,MAAM,yBAAyB;AAAA,EACpC;AAAA,EACA;AAAA,EACA,YAAY,QAAgC,aAA0B;AAC/D,SAAA,UAAU,QACf,KAAK,eAAe;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAStB,OAAOuC,OAAc,SAAmE;AACtF,WAAO,QAAyB,KAAK,SAAS,KAAK,cAAc,OAAOA,OAAM,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASvF,KAAKA,OAAc,SAAmE;AACpF,WAAO,QAAyB,KAAK,SAAS,KAAK,cAAc,SAASA,OAAM,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzF,OAAOA,OAA2C;AAChD,WAAO,QAAyB,KAAK,SAAS,KAAK,cAAc,UAAUA,KAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMjF,OAAqC;AACnC,WAAO,SAA2B,KAAK,SAAS,KAAK,cAAc;AAAA,MACjE,KAAK;AAAA,MACL,KAAK;AAAA,IAAA,CACN;AAAA,EAAA;AAEL;AAGO,MAAM,eAAe;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,YAAY,QAAsB,aAA0B;AACrD,SAAA,UAAU,QACf,KAAK,eAAe;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAStB,OAAOA,OAAc,SAAgE;AACnF,WAAAF,OAAAA,cAAuB,WAAW,KAAK,QAAQ,OAAQ,CAAA,GAChDb,KAAA;AAAA,MACL,QAAyB,KAAK,SAAS,KAAK,cAAc,OAAOe,OAAM,OAAO;AAAA,IAChF;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,KAAKA,OAAc,SAAgE;AACjF,WAAAF,OAAAA,cAAuB,WAAW,KAAK,QAAQ,OAAQ,CAAA,GAChDb,KAAA;AAAA,MACL,QAAyB,KAAK,SAAS,KAAK,cAAc,SAASe,OAAM,OAAO;AAAA,IAClF;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQF,OAAOA,OAAwC;AAC7C,WAAAF,OAAAA,cAAuB,WAAW,KAAK,QAAQ,OAAQ,CAAA,GAChDb,KAAAA,cAAc,QAAyB,KAAK,SAAS,KAAK,cAAc,UAAUe,KAAI,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhG,OAAkC;AAChC,WAAAF,OAAAA,cAAuB,WAAW,KAAK,QAAQ,OAAQ,CAAA,GAChDb,KAAA;AAAA,MACL,SAA2B,KAAK,SAAS,KAAK,cAAc,EAAC,KAAK,aAAa,KAAK,KAAK,CAAA;AAAA,IAC3F;AAAA,EAAA;AAEJ;AAEA,SAAS,QACP,QACA,aACA,QACAe,OACA,SACA;AACA,SAAAF,qBAAuB,WAAW,OAAO,OAAQ,CAAA,GACjDG,OAAS,QAAQD,KAAI,GACd,SAAY,QAAQ,aAAa;AAAA,IACtC;AAAA,IACA,KAAK,aAAaA,KAAI;AAAA,IACtB,MAAM;AAAA,IACN,KAAK;AAAA,EAAA,CACN;AACH;ACvHO,MAAM,yBAAyB;AAAA,EACpC;AAAA,EACA;AAAA,EACA,YAAY,QAAgC,aAA0B;AAC/D,SAAA,UAAU,QACf,KAAK,eAAe;AAAA,EAAA;AAAA,EAWtB,KAAK,SAE8D;AACjEF,WAAAA,cAAuB,YAAY,KAAK,QAAQ,QAAQ;AACxD,UAAM,MAAM,SAAS,mBAAmB,KAAQ,mCAAmC;AACnF,WAAO,SAA0B,KAAK,SAAS,KAAK,cAAc,EAAC,KAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzE,QAAQ,WAA8C;AACpD,WAAAA,OAAAA,cAAuB,YAAY,KAAK,QAAQ,OAAA,CAAQ,GACjD,SAAwB,KAAK,SAAS,KAAK,cAAc,EAAC,KAAK,aAAa,SAAS,IAAG;AAAA,EAAA;AAEnG;AAGO,MAAM,eAAe;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,YAAY,QAAsB,aAA0B;AACrD,SAAA,UAAU,QACf,KAAK,eAAe;AAAA,EAAA;AAAA,EAWtB,KAAK,SAAgE;AACnEA,WAAAA,cAAuB,YAAY,KAAK,QAAQ,QAAQ;AACxD,UAAM,MAAM,SAAS,mBAAmB,KAAQ,mCAAmC;AAC5E,WAAAb,KAAA,cAAc,SAA0B,KAAK,SAAS,KAAK,cAAc,EAAC,IAAG,CAAC,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxF,QAAQ,WAA2C;AACjD,WAAAa,OAAAA,cAAuB,YAAY,KAAK,QAAQ,OAAQ,CAAA,GACjDb,KAAA;AAAA,MACL,SAAwB,KAAK,SAAS,KAAK,cAAc,EAAC,KAAK,aAAa,SAAS,GAAG,CAAA;AAAA,IAC1F;AAAA,EAAA;AAEJ;ACtEO,MAAM,sBAAsB;AAAA,EACjC;AAAA,EACA;AAAA,EACA,YAAY,QAAgC,aAA0B;AAC/D,SAAA,UAAU,QACf,KAAK,eAAe;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQtB,QACE,IAC6D;AACtD,WAAA;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,EAAC,KAAK,UAAU,EAAE,GAAE;AAAA,IACtB;AAAA,EAAA;AAEJ;AAGO,MAAM,YAAY;AAAA,EACvB;AAAA,EACA;AAAA,EACA,YAAY,QAAsB,aAA0B;AACrD,SAAA,UAAU,QACf,KAAK,eAAe;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQtB,QACE,IAC0D;AACnD,WAAAA,KAAA;AAAA,MACL,SAA0D,KAAK,SAAS,KAAK,cAAc;AAAA,QACzF,KAAK,UAAU,EAAE;AAAA,MAClB,CAAA;AAAA,IACH;AAAA,EAAA;AAEJ;ACOO,MAAM,uBAAuB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AAAA,EAET,YAAY,aAA0Bb,WAAuB8B,sBAAe;AAC1E,SAAK,OAAO9B,QAAM,GAElB,KAAK,eAAe,aAEpB,KAAK,SAAS,IAAI,uBAAuB,MAAM,KAAK,YAAY,GAChE,KAAK,WAAW,IAAI,yBAAyB,MAAM,KAAK,YAAY,GACpE,KAAK,OAAO,IAAI,WAAW,IAAI,GAC/B,KAAK,WAAW,IAAI,yBAAyB,MAAM,KAAK,YAAY,GACpE,KAAK,QAAQ,IAAI,sBAAsB,MAAM,KAAK,YAAY;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMhE,QAAgC;AAC9B,WAAO,IAAI,uBAAuB,KAAK,cAAc,KAAK,QAAQ;AAAA,EAAA;AAAA,EAWpE,OAAO,WAAwD;AAC7D,QAAI,cAAc;AACT,aAAA,EAAC,GAAG,KAAK,cAAa;AAG/B,QAAI,KAAK,iBAAiB,KAAK,cAAc,qBAAqB;AAChE,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAGF,WAAA,KAAK,gBAAgB+B,kBAAW,WAAW,KAAK,iBAAiB,CAAE,CAAA,GAC5D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQT,WAAW,WAA2D;AAC9D,UAAA,aAAa,KAAK,OAAO;AACxB,WAAA,IAAI,uBAAuB,KAAK,cAAc;AAAA,MACnD,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,QACL,GAAI,WAAW,SAAS,CAAC;AAAA,QACzB,GAAI,OAAO,WAAW,SAAU,YAC5B,EAAC,SAAS,UAAU,MAAK,IACzB,WAAW,SAAS,CAAA;AAAA,MAAC;AAAA,IAC3B,CACD;AAAA,EAAA;AAAA,EA6DH,MACE,OACA,QACA,SACqC;AACrC,WAAOC;AAAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK,cAAc;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,YACE,IACA,SAC2C;AAC3C,WAAOC,aAA4B,MAAM,KAAK,cAAc,IAAI,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYzE,aACE,KACA,SAC0C;AAC1C,WAAOC,cAA6B,MAAM,KAAK,cAAc,KAAK,OAAO;AAAA,EAAA;AAAA,EA0D3E,OACE,UACA,SAQA;AACA,WAAOC,QAAuB,MAAM,KAAK,cAAc,UAAU,UAAU,OAAO;AAAA,EAAA;AAAA,EA0DpF,kBACE,UACA,SAQA;AACA,WAAOC,mBAAkC,MAAM,KAAK,cAAc,UAAU,OAAO;AAAA,EAAA;AAAA,EA0DrF,gBACE,UACA,SAQA;AACA,WAAOC,iBAAgC,MAAM,KAAK,cAAc,UAAU,OAAO;AAAA,EAAA;AAAA,EA2GnF,OACE,WACA,SAQA;AACA,WAAOC,QAAuB,MAAM,KAAK,cAAc,WAAW,OAAO;AAAA,EAAA;AAAA,EA0D3E,OACE,YACA,SAQA;AACA,WAAOC,QAAuB,MAAM,KAAK,cAAc,YAAY,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqC5E,MAAM,WAA2B,YAA+C;AAC9E,WAAO,IAAI,gBAAgB,WAAW,YAAY,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxD,YACE,YACuB;AAChB,WAAA,IAAI,sBAAsB,YAAY,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASnD,OACE,YACA,SACuD;AACvD,WAAOC,QAAoB,MAAM,KAAK,cAAc,YAAY,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzE,QAAiB,SAA2C;AAC1D,WAAOC,SAAqB,MAAM,KAAK,cAAc,OAAO;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS9D,OAAO,KAAa,WAA6B;AAC/C,WAAOC,QAAoB,MAAM,KAAK,SAAS;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASjD,WAAW,WAAmB,MAAuB;AACnD,WAAOC,YAAwB,MAAM,WAAW,IAAI;AAAA,EAAA;AAExD;AAGO,MAAM,aAAa;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AAAA,EAET,YAAY,aAA0B3C,WAAuB8B,sBAAe;AACrE,SAAA,OAAO9B,QAAM,GAElB,KAAK,eAAe,aAEpB,KAAK,SAAS,IAAI,aAAa,MAAM,KAAK,YAAY,GACtD,KAAK,WAAW,IAAI,eAAe,MAAM,KAAK,YAAY,GAC1D,KAAK,OAAO,IAAI,WAAW,IAAI,GAC/B,KAAK,WAAW,IAAI,eAAe,MAAM,KAAK,YAAY,GAC1D,KAAK,QAAQ,IAAI,YAAY,MAAM,KAAK,YAAY,GAEpD,KAAK,aAAa,IAAI,uBAAuB,aAAaA,QAAM;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,EAMlE,QAAsB;AACpB,WAAO,IAAI,aAAa,KAAK,cAAc,KAAK,QAAQ;AAAA,EAAA;AAAA,EAW1D,OAAO,WAAwD;AAC7D,QAAI,cAAc;AACT,aAAA,EAAC,GAAG,KAAK,cAAa;AAG/B,QAAI,KAAK,iBAAiB,KAAK,cAAc,qBAAqB;AAChE,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAGF,WAAI,KAAK,cACP,KAAK,WAAW,OAAO,SAAS,GAGlC,KAAK,gBAAgB+B,kBAAW,WAAW,KAAK,iBAAiB,CAAE,CAAA,GAC5D;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQT,WAAW,WAAiD;AACpD,UAAA,aAAa,KAAK,OAAO;AACxB,WAAA,IAAI,aAAa,KAAK,cAAc;AAAA,MACzC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,QACL,GAAI,WAAW,SAAS,CAAC;AAAA,QACzB,GAAI,OAAO,WAAW,SAAU,YAC5B,EAAC,SAAS,UAAU,MAAK,IACzB,WAAW,SAAS,CAAA;AAAA,MAAC;AAAA,IAC3B,CACD;AAAA,EAAA;AAAA,EA6DH,MACE,OACA,QACA,SACoE;AAC7D,WAAAlB,KAAA;AAAA,MACLmB;AAAAA,QACE;AAAA,QACA,KAAK;AAAA,QACL,KAAK,cAAc;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IAEJ;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASF,YACE,IACA,SACwC;AACjC,WAAAnB,KAAAA,cAAcoB,aAA4B,MAAM,KAAK,cAAc,IAAI,OAAO,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYxF,aACE,KACA,SACuC;AAChC,WAAApB,KAAAA,cAAcqB,cAA6B,MAAM,KAAK,cAAc,KAAK,OAAO,CAAC;AAAA,EAAA;AAAA,EA0D1F,OACE,UACA,SAQA;AACO,WAAArB,KAAA;AAAA,MACLsB,QAAuB,MAAM,KAAK,cAAc,UAAU,UAAU,OAAO;AAAA,IAC7E;AAAA,EAAA;AAAA,EA0DF,kBACE,UACA,SAQA;AACO,WAAAtB,KAAA;AAAA,MACLuB,mBAAkC,MAAM,KAAK,cAAc,UAAU,OAAO;AAAA,IAC9E;AAAA,EAAA;AAAA,EA0DF,gBACE,UACA,SAQA;AACO,WAAAvB,KAAA;AAAA,MACLwB,iBAAgC,MAAM,KAAK,cAAc,UAAU,OAAO;AAAA,IAC5E;AAAA,EAAA;AAAA,EA2GF,OACE,WACA,SAQA;AACO,WAAAxB,KAAAA,cAAcyB,QAAuB,MAAM,KAAK,cAAc,WAAW,OAAO,CAAC;AAAA,EAAA;AAAA,EA0D1F,OACE,YACA,SAQA;AACO,WAAAzB,KAAAA,cAAc0B,QAAuB,MAAM,KAAK,cAAc,YAAY,OAAO,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqC3F,MAAM,YAA4B,YAAqC;AACrE,WAAO,IAAI,MAAM,YAAY,YAAY,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ/C,YACE,YACa;AACN,WAAA,IAAI,YAAY,YAAY,IAAI;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUzC,OACE,YACA,SACoD;AAC7C,WAAA1B,KAAAA,cAAc2B,QAAoB,MAAM,KAAK,cAAc,YAAY,OAAO,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUxF,QAAiB,SAAwC;AACvD,WAAO3B,KAAAA,cAAc4B,SAAwB,MAAM,KAAK,cAAc,OAAO,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAahF,YAAY,UAAkB,MAAe,SAA6C;AACjF,WAAA5B,KAAA,cAAc+B,aAAyB,MAAM,KAAK,cAAc,UAAU,MAAM,OAAO,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASjG,OAAO,KAAa,WAA6B;AAC/C,WAAOF,QAAoB,MAAM,KAAK,SAAS;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASjD,WAAW,WAAmB,MAAuB;AACnD,WAAOC,YAAwB,MAAM,WAAW,IAAI;AAAA,EAAA;AAExD;ACx3CwB,SAAA,0BAItB,eACA,kBACA;AAkBA,SAAO,EAAC,WAhBiB,kBAAkB,aAAa,GAgBnB,cAdhB,CAAC3C,YAA6B;AAC3C,UAAA,kBAAkB,kBAAkB,aAAa;AACvD,WAAO,IAAI;AAAA,MACT,CAAC,SAAS6C,gBACPA,cAAa,iBAAiB;AAAA,QAC7B,cAAc;AAAA,QACd,YAAY7C,QAAO;AAAA,QACnB,YAAYA,QAAO;AAAA,QACnB,GAAG;AAAA,MAAA,CACG;AAAA,MACVA;AAAA,IACF;AAAA,EAAA,EAG+C;AACnD;ACpDO,SAAS,6BACd8C,eACA;AACA,SAAO,SAAgC9C,UAA0B;AAC1C,WAAA+C,OAAA,qBAAA,GACdD,cAAa9C,QAAM;AAAA,EAC5B;AACF;;ACNA,MAAM,aAAa;AAAA,EACjBgD,aAAAA,MAAM,EAAC,SAAS,IAAM,WAAW,iBAAgB;AAAA,EACjDC,aAAA,QAAQ,EAAC,cAAc,GAAG,IAAI,IAAI,OAAO,IAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU5CC,mBAAM;AAAA,IACJ,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,iBAAiB;AAAA,EAClB,CAAA;AACH,GCfM,MAAM,0BAAsDC,YAAe,YAAY,GAGhF,YAAY,IAAI,WAOhB,eAAe,IAAI,cAM1B,yBAAyB,6BAA6B,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}