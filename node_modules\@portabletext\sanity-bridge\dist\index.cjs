"use strict";
Object.defineProperty(exports, "__esModule", { value: !0 });
var schema = require("@sanity/schema"), types = require("@sanity/types"), startCase = require("lodash.startcase"), getRandomValues = require("get-random-values-esm");
function _interopDefaultCompat(e) {
  return e && typeof e == "object" && "default" in e ? e : { default: e };
}
var startCase__default = /* @__PURE__ */ _interopDefaultCompat(startCase), getRandomValues__default = /* @__PURE__ */ _interopDefaultCompat(getRandomValues);
function createPortableTextMemberSchemaTypes(portableTextType) {
  if (!portableTextType)
    throw new Error("Parameter 'portabletextType' missing (required)");
  const blockType = portableTextType.of?.find(findBlockType);
  if (!blockType)
    throw new Error("Block type is not defined in this schema (required)");
  const childrenField = blockType.fields?.find(
    (field) => field.name === "children"
  );
  if (!childrenField)
    throw new Error("Children field for block type found in schema (required)");
  const ofType = childrenField.type.of;
  if (!ofType)
    throw new Error(
      "Valid types for block children not found in schema (required)"
    );
  const spanType = ofType.find((memberType) => memberType.name === "span");
  if (!spanType)
    throw new Error("Span type not found in schema (required)");
  const inlineObjectTypes = ofType.filter(
    (memberType) => memberType.name !== "span"
  ) || [], blockObjectTypes = portableTextType.of?.filter(
    (field) => field.name !== blockType.name
  ) || [];
  return {
    styles: resolveEnabledStyles(blockType),
    decorators: resolveEnabledDecorators(spanType),
    lists: resolveEnabledListItems(blockType),
    block: blockType,
    span: spanType,
    portableText: portableTextType,
    inlineObjects: inlineObjectTypes,
    blockObjects: blockObjectTypes,
    annotations: spanType.annotations
  };
}
function resolveEnabledStyles(blockType) {
  const styleField = blockType.fields?.find(
    (btField) => btField.name === "style"
  );
  if (!styleField)
    throw new Error(
      "A field with name 'style' is not defined in the block type (required)."
    );
  const textStyles = styleField.type.options?.list && styleField.type.options.list?.filter(
    (style) => style.value
  );
  if (!textStyles || textStyles.length === 0)
    throw new Error(
      "The style fields need at least one style defined. I.e: {title: 'Normal', value: 'normal'}."
    );
  return textStyles;
}
function resolveEnabledDecorators(spanType) {
  return spanType.decorators;
}
function resolveEnabledListItems(blockType) {
  const listField = blockType.fields?.find(
    (btField) => btField.name === "listItem"
  );
  if (!listField)
    throw new Error(
      "A field with name 'listItem' is not defined in the block type (required)."
    );
  const listItems = listField.type.options?.list && listField.type.options.list.filter((list) => list.value);
  if (!listItems)
    throw new Error("The list field need at least to be an empty array");
  return listItems;
}
function findBlockType(type) {
  return type.type ? findBlockType(type.type) : type.name === "block" ? type : null;
}
function portableTextMemberSchemaTypesToSchema(schema2) {
  return {
    annotations: schema2.annotations.map((annotation) => ({
      name: annotation.name,
      fields: annotation.fields.map((field) => ({
        name: field.name,
        type: field.type.jsonType,
        title: field.type.title
      })),
      title: annotation.title
    })),
    block: {
      name: schema2.block.name
    },
    blockObjects: schema2.blockObjects.map((blockObject) => ({
      name: blockObject.name,
      fields: blockObject.fields.map((field) => ({
        name: field.name,
        type: field.type.jsonType,
        title: field.type.title
      })),
      title: blockObject.title
    })),
    decorators: schema2.decorators.map((decorator) => ({
      name: decorator.value,
      title: decorator.title,
      value: decorator.value
    })),
    inlineObjects: schema2.inlineObjects.map((inlineObject) => ({
      name: inlineObject.name,
      fields: inlineObject.fields.map((field) => ({
        name: field.name,
        type: field.type.jsonType,
        title: field.type.title
      })),
      title: inlineObject.title
    })),
    span: {
      name: schema2.span.name
    },
    styles: schema2.styles.map((style) => ({
      name: style.value,
      title: style.title,
      value: style.value
    })),
    lists: schema2.lists.map((list) => ({
      name: list.value,
      title: list.title,
      value: list.value
    }))
  };
}
function sanitySchemaToPortableTextSchema(sanitySchema) {
  const portableTextMemberSchemaTypes = createPortableTextMemberSchemaTypes(
    sanitySchema.hasOwnProperty("jsonType") ? sanitySchema : compileType(sanitySchema)
  );
  return portableTextMemberSchemaTypesToSchema(portableTextMemberSchemaTypes);
}
function compileType(rawType) {
  return schema.Schema.compile({
    name: "blockTypeSchema",
    types: [rawType]
  }).get(rawType.name);
}
const keyGenerator = () => randomKey(12), getByteHexTable = /* @__PURE__ */ (() => {
  let table;
  return () => {
    if (table)
      return table;
    table = [];
    for (let i = 0; i < 256; ++i)
      table[i] = (i + 256).toString(16).slice(1);
    return table;
  };
})();
function whatwgRNG(length = 16) {
  const rnds8 = new Uint8Array(length);
  return getRandomValues__default.default(rnds8), rnds8;
}
function randomKey(length) {
  const table = getByteHexTable();
  return whatwgRNG(length).reduce((str, n) => str + table[n], "").slice(0, length);
}
const temporaryImageBlockObjectName = `tmp-${keyGenerator()}-image`, temporaryUrlBlockObjectName = `tmp-${keyGenerator()}-url`, temporaryImageInlineObjectName = `tmp-${keyGenerator()}-image`, temporaryUrlInlineObjectName = `tmp-${keyGenerator()}-url`, temporaryBlockObjectNames = {
  image: temporaryImageBlockObjectName,
  url: temporaryUrlBlockObjectName
}, temporaryInlineObjectNames = {
  image: temporaryImageInlineObjectName,
  url: temporaryUrlInlineObjectName
}, blockObjectNames = {
  [temporaryImageBlockObjectName]: "image",
  [temporaryUrlBlockObjectName]: "url"
}, inlineObjectNames = {
  [temporaryImageInlineObjectName]: "image",
  [temporaryUrlInlineObjectName]: "url"
}, defaultObjectTitles = {
  image: "Image",
  url: "URL"
};
function compileSchemaDefinitionToPortableTextMemberSchemaTypes(definition) {
  const blockObjects = definition?.blockObjects?.map(
    (blockObject) => types.defineType({
      type: "object",
      // Very naive way to work around `SanitySchema.compile` adding default
      // fields to objects with certain names.
      name: temporaryBlockObjectNames[blockObject.name] ?? blockObject.name,
      title: blockObject.title === void 0 ? (
        // This avoids the default title which is a title case of the object name
        defaultObjectTitles[blockObject.name]
      ) : blockObject.title,
      fields: blockObject.fields?.map((field) => ({
        name: field.name,
        type: field.type,
        title: field.title ?? startCase__default.default(field.name)
      })) ?? []
    })
  ) ?? [], inlineObjects = definition?.inlineObjects?.map(
    (inlineObject) => types.defineType({
      type: "object",
      // Very naive way to work around `SanitySchema.compile` adding default
      // fields to objects with certain names.
      name: temporaryInlineObjectNames[inlineObject.name] ?? inlineObject.name,
      title: inlineObject.title === void 0 ? (
        // This avoids the default title which is a title case of the object name
        defaultObjectTitles[inlineObject.name]
      ) : inlineObject.title,
      fields: inlineObject.fields?.map((field) => ({
        name: field.name,
        type: field.type,
        title: field.title ?? startCase__default.default(field.name)
      })) ?? []
    })
  ) ?? [], portableTextSchema = types.defineField({
    type: "array",
    name: "portable-text",
    of: [
      ...blockObjects.map((blockObject) => ({ type: blockObject.name })),
      {
        type: "block",
        name: "block",
        of: inlineObjects.map((inlineObject) => ({ type: inlineObject.name })),
        marks: {
          decorators: definition?.decorators?.map((decorator) => ({
            title: decorator.title ?? startCase__default.default(decorator.name),
            value: decorator.name
          })) ?? [],
          annotations: definition?.annotations?.map((annotation) => ({
            name: annotation.name,
            type: "object",
            title: annotation.title,
            fields: annotation.fields?.map((field) => ({
              name: field.name,
              title: field.title ?? startCase__default.default(field.name),
              type: field.type
            })) ?? []
          })) ?? []
        },
        lists: definition?.lists?.map((list) => ({
          value: list.name,
          title: list.title ?? startCase__default.default(list.name)
        })) ?? [],
        styles: definition?.styles?.map((style) => ({
          value: style.name,
          title: style.title ?? startCase__default.default(style.name)
        })) ?? []
      }
    ]
  }), schema$1 = schema.Schema.compile({
    types: [portableTextSchema, ...blockObjects, ...inlineObjects]
  }).get("portable-text"), pteSchema = createPortableTextMemberSchemaTypes(schema$1);
  return {
    ...pteSchema,
    portableText: {
      ...pteSchema.portableText,
      of: pteSchema.portableText.of.map((schemaType) => {
        if (!types.isObjectSchemaType(schemaType))
          return schemaType;
        const nameMapping = blockObjectNames[schemaType.name];
        return {
          ...schemaType,
          name: nameMapping ?? schemaType.name,
          fields: schemaType.fields.map((field) => field.name !== "children" || !types.isArraySchemaType(field.type) ? field : {
            ...field,
            type: {
              of: field.type.of.map((ofSchemaType) => {
                const nameMapping2 = inlineObjectNames[ofSchemaType.name];
                return nameMapping2 ? {
                  ...ofSchemaType,
                  name: nameMapping2
                } : ofSchemaType;
              })
            }
          })
        };
      })
    },
    blockObjects: pteSchema.blockObjects.map(
      (blockObject) => blockObjectNames[blockObject.name] !== void 0 ? {
        ...blockObject,
        name: blockObjectNames[blockObject.name],
        type: {
          ...blockObject.type,
          name: blockObjectNames[blockObject.name]
        }
      } : blockObject
    ),
    inlineObjects: pteSchema.inlineObjects.map(
      (inlineObject) => inlineObjectNames[inlineObject.name] !== void 0 ? {
        ...inlineObject,
        name: inlineObjectNames[inlineObject.name]
      } : inlineObject
    )
  };
}
exports.compileSchemaDefinitionToPortableTextMemberSchemaTypes = compileSchemaDefinitionToPortableTextMemberSchemaTypes;
exports.createPortableTextMemberSchemaTypes = createPortableTextMemberSchemaTypes;
exports.portableTextMemberSchemaTypesToSchema = portableTextMemberSchemaTypesToSchema;
exports.sanitySchemaToPortableTextSchema = sanitySchemaToPortableTextSchema;
//# sourceMappingURL=index.cjs.map
