{"version": 3, "file": "selector.get-text-before.cjs", "sources": ["../../src/selectors/selector.get-text-before.ts"], "sourcesContent": ["import type {EditorSelector} from '../editor/editor-selector'\nimport {getSelectionStartPoint} from '../utils'\nimport {getBlockStartPoint} from '../utils/util.get-block-start-point'\nimport {getFocusBlock} from './selector.get-focus-block'\nimport {getSelectionText} from './selector.get-selection-text'\n\n/**\n * @public\n */\nexport const getBlockTextBefore: EditorSelector<string> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return ''\n  }\n\n  const startPoint = getSelectionStartPoint(snapshot.context.selection)\n  const block = getFocusBlock({\n    ...snapshot,\n    context: {\n      ...snapshot.context,\n      selection: {\n        anchor: startPoint,\n        focus: startPoint,\n      },\n    },\n  })\n\n  if (!block) {\n    return ''\n  }\n\n  const startOfBlock = getBlockStartPoint({\n    context: snapshot.context,\n    block,\n  })\n\n  return getSelectionText({\n    ...snapshot,\n    context: {\n      ...snapshot.context,\n      selection: {\n        anchor: startOfBlock,\n        focus: startPoint,\n      },\n    },\n  })\n}\n"], "names": ["getBlockTextBefore", "snapshot", "context", "selection", "startPoint", "getSelectionStartPoint", "block", "getFocusBlock", "anchor", "focus", "startOfBlock", "getBlockStartPoint", "getSelectionText"], "mappings": ";;AASO,MAAMA,qBAA8CC,CAAAA,aAAa;AACtE,MAAI,CAACA,SAASC,QAAQC;AACpB,WAAO;AAGT,QAAMC,aAAaC,iBAAAA,uBAAuBJ,SAASC,QAAQC,SAAS,GAC9DG,QAAQC,2CAAc;AAAA,IAC1B,GAAGN;AAAAA,IACHC,SAAS;AAAA,MACP,GAAGD,SAASC;AAAAA,MACZC,WAAW;AAAA,QACTK,QAAQJ;AAAAA,QACRK,OAAOL;AAAAA,MAAAA;AAAAA,IACT;AAAA,EACF,CACD;AAED,MAAI,CAACE;AACH,WAAO;AAGT,QAAMI,eAAeC,iBAAAA,mBAAmB;AAAA,IACtCT,SAASD,SAASC;AAAAA,IAClBI;AAAAA,EAAAA,CACD;AAED,SAAOM,8CAAiB;AAAA,IACtB,GAAGX;AAAAA,IACHC,SAAS;AAAA,MACP,GAAGD,SAASC;AAAAA,MACZC,WAAW;AAAA,QACTK,QAAQE;AAAAA,QACRD,OAAOL;AAAAA,MAAAA;AAAAA,IACT;AAAA,EACF,CACD;AACH;;"}