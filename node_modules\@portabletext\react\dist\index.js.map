{"version": 3, "file": "index.js", "sources": ["../src/components/list.tsx", "../src/components/marks.tsx", "../src/warnings.ts", "../src/components/unknown.tsx", "../src/components/defaults.tsx", "../src/components/merge.ts", "../src/react-portable-text.tsx"], "sourcesContent": ["import type {PortableTextListComponent, PortableTextListItemComponent} from '../types'\n\nexport const defaultLists: Record<'number' | 'bullet', PortableTextListComponent> = {\n  number: ({children}) => <ol>{children}</ol>,\n  bullet: ({children}) => <ul>{children}</ul>,\n}\n\nexport const DefaultListItem: PortableTextListItemComponent = ({children}) => <li>{children}</li>\n", "import type {TypedObject} from '@portabletext/types'\n\nimport type {PortableTextMarkComponent} from '../types'\n\ninterface DefaultLink extends TypedObject {\n  _type: 'link'\n  href: string\n}\n\nconst link: PortableTextMarkComponent<DefaultLink> = ({children, value}) => (\n  <a href={value?.href}>{children}</a>\n)\n\nconst underlineStyle = {textDecoration: 'underline'}\n\nexport const defaultMarks: Record<string, PortableTextMarkComponent | undefined> = {\n  em: ({children}) => <em>{children}</em>,\n  strong: ({children}) => <strong>{children}</strong>,\n  code: ({children}) => <code>{children}</code>,\n  underline: ({children}) => <span style={underlineStyle}>{children}</span>,\n  'strike-through': ({children}) => <del>{children}</del>,\n  link,\n}\n", "const getTemplate = (type: string, prop: string): string =>\n  `[@portabletext/react] Unknown ${type}, specify a component for it in the \\`components.${prop}\\` prop`\n\nexport const unknownTypeWarning = (typeName: string): string =>\n  getTemplate(`block type \"${typeName}\"`, 'types')\n\nexport const unknownMarkWarning = (markType: string): string =>\n  getTemplate(`mark type \"${markType}\"`, 'marks')\n\nexport const unknownBlockStyleWarning = (blockStyle: string): string =>\n  getTemplate(`block style \"${blockStyle}\"`, 'block')\n\nexport const unknownListStyleWarning = (listStyle: string): string =>\n  getTemplate(`list style \"${listStyle}\"`, 'list')\n\nexport const unknownListItemStyleWarning = (listStyle: string): string =>\n  getTemplate(`list item style \"${listStyle}\"`, 'listItem')\n\nexport function printWarning(message: string): void {\n  console.warn(message)\n}\n", "import type {PortableTextReactComponents} from '../types'\nimport {unknownTypeWarning} from '../warnings'\n\nconst hidden = {display: 'none'}\n\nexport const DefaultUnknownType: PortableTextReactComponents['unknownType'] = ({\n  value,\n  isInline,\n}) => {\n  const warning = unknownTypeWarning(value._type)\n  return isInline ? <span style={hidden}>{warning}</span> : <div style={hidden}>{warning}</div>\n}\n\nexport const DefaultUnknownMark: PortableTextReactComponents['unknownMark'] = ({\n  markType,\n  children,\n}) => {\n  return <span className={`unknown__pt__mark__${markType}`}>{children}</span>\n}\n\nexport const DefaultUnknownBlockStyle: PortableTextReactComponents['unknownBlockStyle'] = ({\n  children,\n}) => {\n  return <p>{children}</p>\n}\n\nexport const DefaultUnknownList: PortableTextReactComponents['unknownList'] = ({children}) => {\n  return <ul>{children}</ul>\n}\n\nexport const DefaultUnknownListItem: PortableTextReactComponents['unknownListItem'] = ({\n  children,\n}) => {\n  return <li>{children}</li>\n}\n", "import type {PortableTextBlockStyle} from '@portabletext/types'\nimport type {JSX} from 'react'\n\nimport type {PortableTextBlockComponent, PortableTextReactComponents} from '../types'\nimport {DefaultListItem, defaultLists} from './list'\nimport {defaultMarks} from './marks'\nimport {\n  DefaultUnknownBlockStyle,\n  DefaultUnknownList,\n  DefaultUnknownListItem,\n  DefaultUnknownMark,\n  DefaultUnknownType,\n} from './unknown'\n\nexport const DefaultHardBreak = (): JSX.Element => <br />\n\nexport const defaultBlockStyles: Record<\n  PortableTextBlockStyle,\n  PortableTextBlockComponent | undefined\n> = {\n  normal: ({children}) => <p>{children}</p>,\n  blockquote: ({children}) => <blockquote>{children}</blockquote>,\n  h1: ({children}) => <h1>{children}</h1>,\n  h2: ({children}) => <h2>{children}</h2>,\n  h3: ({children}) => <h3>{children}</h3>,\n  h4: ({children}) => <h4>{children}</h4>,\n  h5: ({children}) => <h5>{children}</h5>,\n  h6: ({children}) => <h6>{children}</h6>,\n}\n\nexport const defaultComponents: PortableTextReactComponents = {\n  types: {},\n\n  block: defaultBlockStyles,\n  marks: defaultMarks,\n  list: defaultLists,\n  listItem: DefaultListItem,\n  hardBreak: DefaultHardBreak,\n\n  unknownType: DefaultUnknownType,\n  unknownMark: DefaultUnknownMark,\n  unknownList: DefaultUnknownList,\n  unknownListItem: DefaultUnknownListItem,\n  unknownBlockStyle: DefaultUnknownBlockStyle,\n}\n", "import type {PortableTextComponents, PortableTextReactComponents} from '../types'\n\nexport function mergeComponents(\n  parent: PortableTextReactComponents,\n  overrides: PortableTextComponents,\n): PortableTextReactComponents {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const {block, list, listItem, marks, types, ...rest} = overrides\n  // @todo figure out how to not `as ...` these\n  return {\n    ...parent,\n    block: mergeDeeply(parent, overrides, 'block') as PortableTextReactComponents['block'],\n    list: mergeDeeply(parent, overrides, 'list') as PortableTextReactComponents['list'],\n    listItem: mergeDeeply(parent, overrides, 'listItem') as PortableTextReactComponents['listItem'],\n    marks: mergeDeeply(parent, overrides, 'marks') as PortableTextReactComponents['marks'],\n    types: mergeDeeply(parent, overrides, 'types') as PortableTextReactComponents['types'],\n    ...rest,\n  }\n}\n\nfunction mergeDeeply(\n  parent: PortableTextReactComponents,\n  overrides: PortableTextComponents,\n  key: 'block' | 'list' | 'listItem' | 'marks' | 'types',\n): PortableTextReactComponents[typeof key] {\n  const override = overrides[key]\n  const parentVal = parent[key]\n\n  if (typeof override === 'function') {\n    return override\n  }\n\n  if (override && typeof parentVal === 'function') {\n    return override\n  }\n\n  if (override) {\n    return {...parentVal, ...override} as PortableTextReactComponents[typeof key]\n  }\n\n  return parentVal\n}\n", "import type {ToolkitNestedPortableTextSpan, ToolkitTextNode} from '@portabletext/toolkit'\nimport {\n  buildMarksTree,\n  isPortableTextBlock,\n  isPortableTextListItemBlock,\n  isPortableTextToolkitList,\n  isPortableTextToolkitSpan,\n  isPortableTextToolkitTextNode,\n  LIST_NEST_MODE_HTML,\n  nestLists,\n  spanToPlainText,\n} from '@portabletext/toolkit'\nimport type {\n  PortableTextBlock,\n  PortableTextListItemBlock,\n  PortableTextMarkDefinition,\n  PortableTextSpan,\n  TypedObject,\n} from '@portabletext/types'\nimport {type JSX, type ReactNode, useMemo} from 'react'\n\nimport {defaultComponents} from './components/defaults'\nimport {mergeComponents} from './components/merge'\nimport type {\n  MissingComponentHandler,\n  NodeRenderer,\n  PortableTextProps,\n  PortableTextReactComponents,\n  ReactPortableTextList,\n  Serializable,\n  SerializedBlock,\n} from './types'\nimport {\n  printWarning,\n  unknownBlockStyleWarning,\n  unknownListItemStyleWarning,\n  unknownListStyleWarning,\n  unknownMarkWarning,\n  unknownTypeWarning,\n} from './warnings'\n\nexport function PortableText<B extends TypedObject = PortableTextBlock>({\n  value: input,\n  components: componentOverrides,\n  listNestingMode,\n  onMissingComponent: missingComponentHandler = printWarning,\n}: PortableTextProps<B>): JSX.Element {\n  const handleMissingComponent = missingComponentHandler || noop\n  const blocks = Array.isArray(input) ? input : [input]\n  const nested = nestLists(blocks, listNestingMode || LIST_NEST_MODE_HTML)\n\n  const components = useMemo(() => {\n    return componentOverrides\n      ? mergeComponents(defaultComponents, componentOverrides)\n      : defaultComponents\n  }, [componentOverrides])\n\n  const renderNode = useMemo(\n    () => getNodeRenderer(components, handleMissingComponent),\n    [components, handleMissingComponent],\n  )\n  const rendered = nested.map((node, index) =>\n    renderNode({node: node, index, isInline: false, renderNode}),\n  )\n\n  return <>{rendered}</>\n}\n\nconst getNodeRenderer = (\n  components: PortableTextReactComponents,\n  handleMissingComponent: MissingComponentHandler,\n): NodeRenderer => {\n  function renderNode<N extends TypedObject>(options: Serializable<N>): ReactNode {\n    const {node, index, isInline} = options\n    const key = node._key || `node-${index}`\n\n    if (isPortableTextToolkitList(node)) {\n      return renderList(node, index, key)\n    }\n\n    if (isPortableTextListItemBlock(node)) {\n      return renderListItem(node, index, key)\n    }\n\n    if (isPortableTextToolkitSpan(node)) {\n      return renderSpan(node, index, key)\n    }\n\n    if (hasCustomComponentForNode(node)) {\n      return renderCustomBlock(node, index, key, isInline)\n    }\n\n    if (isPortableTextBlock(node)) {\n      return renderBlock(node, index, key, isInline)\n    }\n\n    if (isPortableTextToolkitTextNode(node)) {\n      return renderText(node, key)\n    }\n\n    return renderUnknownType(node, index, key, isInline)\n  }\n\n  function hasCustomComponentForNode(node: TypedObject): boolean {\n    return node._type in components.types\n  }\n\n  /* eslint-disable react/jsx-no-bind */\n  function renderListItem(\n    node: PortableTextListItemBlock<PortableTextMarkDefinition, PortableTextSpan>,\n    index: number,\n    key: string,\n  ) {\n    const tree = serializeBlock({node, index, isInline: false, renderNode})\n    const renderer = components.listItem\n    const handler = typeof renderer === 'function' ? renderer : renderer[node.listItem]\n    const Li = handler || components.unknownListItem\n\n    if (Li === components.unknownListItem) {\n      const style = node.listItem || 'bullet'\n      handleMissingComponent(unknownListItemStyleWarning(style), {\n        type: style,\n        nodeType: 'listItemStyle',\n      })\n    }\n\n    let children = tree.children\n    if (node.style && node.style !== 'normal') {\n      // Wrap any other style in whatever the block serializer says to use\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const {listItem, ...blockNode} = node\n      children = renderNode({node: blockNode, index, isInline: false, renderNode})\n    }\n\n    return (\n      <Li key={key} value={node} index={index} isInline={false} renderNode={renderNode}>\n        {children}\n      </Li>\n    )\n  }\n\n  function renderList(node: ReactPortableTextList, index: number, key: string) {\n    const children = node.children.map((child, childIndex) =>\n      renderNode({\n        node: child._key ? child : {...child, _key: `li-${index}-${childIndex}`},\n        index: childIndex,\n        isInline: false,\n        renderNode,\n      }),\n    )\n\n    const component = components.list\n    const handler = typeof component === 'function' ? component : component[node.listItem]\n    const List = handler || components.unknownList\n\n    if (List === components.unknownList) {\n      const style = node.listItem || 'bullet'\n      handleMissingComponent(unknownListStyleWarning(style), {nodeType: 'listStyle', type: style})\n    }\n\n    return (\n      <List key={key} value={node} index={index} isInline={false} renderNode={renderNode}>\n        {children}\n      </List>\n    )\n  }\n\n  function renderSpan(node: ToolkitNestedPortableTextSpan, _index: number, key: string) {\n    const {markDef, markType, markKey} = node\n    const Span = components.marks[markType] || components.unknownMark\n    const children = node.children.map((child, childIndex) =>\n      renderNode({node: child, index: childIndex, isInline: true, renderNode}),\n    )\n\n    if (Span === components.unknownMark) {\n      handleMissingComponent(unknownMarkWarning(markType), {nodeType: 'mark', type: markType})\n    }\n\n    return (\n      <Span\n        key={key}\n        text={spanToPlainText(node)}\n        value={markDef}\n        markType={markType}\n        markKey={markKey}\n        renderNode={renderNode}\n      >\n        {children}\n      </Span>\n    )\n  }\n\n  function renderBlock(node: PortableTextBlock, index: number, key: string, isInline: boolean) {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const {_key, ...props} = serializeBlock({node, index, isInline, renderNode})\n    const style = props.node.style || 'normal'\n    const handler =\n      typeof components.block === 'function' ? components.block : components.block[style]\n    const Block = handler || components.unknownBlockStyle\n\n    if (Block === components.unknownBlockStyle) {\n      handleMissingComponent(unknownBlockStyleWarning(style), {\n        nodeType: 'blockStyle',\n        type: style,\n      })\n    }\n\n    return <Block key={key} {...props} value={props.node} renderNode={renderNode} />\n  }\n\n  function renderText(node: ToolkitTextNode, key: string) {\n    if (node.text === '\\n') {\n      const HardBreak = components.hardBreak\n      return HardBreak ? <HardBreak key={key} /> : '\\n'\n    }\n\n    return node.text\n  }\n\n  function renderUnknownType(node: TypedObject, index: number, key: string, isInline: boolean) {\n    const nodeOptions = {\n      value: node,\n      isInline,\n      index,\n      renderNode,\n    }\n\n    handleMissingComponent(unknownTypeWarning(node._type), {nodeType: 'block', type: node._type})\n\n    const UnknownType = components.unknownType\n    return <UnknownType key={key} {...nodeOptions} />\n  }\n\n  function renderCustomBlock(node: TypedObject, index: number, key: string, isInline: boolean) {\n    const nodeOptions = {\n      value: node,\n      isInline,\n      index,\n      renderNode,\n    }\n\n    const Node = components.types[node._type]\n    return Node ? <Node key={key} {...nodeOptions} /> : null\n  }\n  /* eslint-enable react/jsx-no-bind */\n\n  return renderNode\n}\n\nfunction serializeBlock(options: Serializable<PortableTextBlock>): SerializedBlock {\n  const {node, index, isInline, renderNode} = options\n  const tree = buildMarksTree(node)\n  const children = tree.map((child, i) =>\n    renderNode({node: child, isInline: true, index: i, renderNode}),\n  )\n\n  return {\n    _key: node._key || `block-${index}`,\n    children,\n    index,\n    isInline,\n    node,\n  }\n}\n\nfunction noop() {\n  // Intentional noop\n}\n"], "names": ["defaultLists", "number", "children", "jsx", "bullet", "DefaultListItem", "link", "value", "href", "underlineStyle", "textDecoration", "defaultMarks", "em", "strong", "code", "underline", "style", "strike-through", "getTemplate", "type", "prop", "unknownTypeWarning", "typeName", "unknownMarkW<PERSON>ning", "markType", "unknownBlockStyleWarning", "blockStyle", "unknownListStyle<PERSON><PERSON>ning", "listStyle", "unknownListItemStyleWarning", "printWarning", "message", "console", "warn", "hidden", "display", "DefaultUnknownType", "isInline", "warning", "_type", "DefaultUnknownMark", "className", "DefaultUnknownBlockStyle", "DefaultUnknownList", "DefaultUnknownListItem", "DefaultHardBreak", "defaultBlockStyles", "normal", "blockquote", "h1", "h2", "h3", "h4", "h5", "h6", "defaultComponents", "types", "block", "marks", "list", "listItem", "hardBreak", "unknownType", "unknownMark", "unknownList", "unknownListItem", "unknownBlockStyle", "mergeComponents", "parent", "overrides", "rest", "_excluded", "_objectSpread", "mergeDeeply", "key", "override", "parentVal", "PortableText", "input", "components", "componentOverrides", "listNestingMode", "onMissingComponent", "missing<PERSON>om<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleMissingComponent", "noop", "blocks", "Array", "isArray", "nested", "nestLists", "LIST_NEST_MODE_HTML", "useMemo", "renderNode", "getNode<PERSON><PERSON><PERSON>", "rendered", "map", "node", "index", "options", "_key", "isPortableTextToolkitList", "renderList", "isPortableTextListItemBlock", "renderListItem", "isPortableTextToolkitSpan", "renderSpan", "hasCustomComponentForNode", "renderCustomBlock", "isPortableTextBlock", "renderBlock", "isPortableTextToolkitTextNode", "renderText", "renderUnknownType", "tree", "serializeBlock", "renderer", "Li", "nodeType", "blockNode", "_objectWithoutProperties", "_excluded2", "child", "childIndex", "component", "List", "_index", "markDef", "<PERSON><PERSON><PERSON>", "Span", "text", "spanToPlainText", "_serializeBlock", "props", "_excluded3", "Block", "HardBreak", "nodeOptions", "UnknownType", "Node", "buildMarksTree", "i"], "mappings": ";;;;;;;;;;;;;;AAEO,MAAMA,YAAuE,GAAA;IAClFC,MAAA,EAAQA,CAAC;MAACC;IAAc,MAAA,eAACC,GAAA,CAAA,IAAA;MAAID;KAAS,CAAA;IACtCE,MAAA,EAAQA,CAAC;MAACF;UAAc,eAACC,GAAA,CAAA,IAAA;MAAID;IAAS,CAAA;EACxC,CAAA;EAEaG,eAAiD,GAAAA,CAAC;IAACH;GAAc,KAAA,eAAAC,GAAA,CAAC;IAAID;GAAS,CCEtF;EAAAI,IAAA,GAA+CA,CAAC;IAACJ,QAAA;IAAUK;EAAA,CAAA,KAC9D,eAAAJ,GAAA,CAAA,GAAA,EAAA;IAAEK,IAAA,EAAMD,OAAOC,IAAO;IAAAN;EAAS,CAAA,CAAA;EAG5BO,cAAiB,GAAA;IAACC,cAAgB,EAAA;EAAW;EAEtCC,YAAsE,GAAA;IACjFC,EAAA,EAAIA,CAAC;MAACV;IAAc,MAAA,eAACC,GAAA,CAAA,IAAA;MAAID;KAAS,CAAA;IAClCW,MAAA,EAAQA,CAAC;MAACX;IAAc,MAAA,eAACC,GAAA,CAAA,QAAA;MAAQD;KAAS,CAAA;IAC1CY,IAAA,EAAMA,CAAC;MAACZ;IAAc,MAAA,eAACC,GAAA,CAAA,MAAA;MAAMD;KAAS,CAAA;IACtCa,SAAA,EAAWA,CAAC;MAACb;IAAA,CAAA,KAAe,eAAAC,GAAA,CAAA,MAAA,EAAA;MAAKa,KAAA,EAAOP,cAAiB;MAAAP;KAAS,CAAA;IAClE,gBAAA,EAAkBe,CAAC;MAACf;IAAc,MAAA,eAACC,GAAA,CAAA,KAAA;MAAKD;KAAS,CAAA;IACjDI;EACF,CCtBM;EAAAY,WAAA,GAAcA,CAACC,IAAA,EAAcC,IACjC,KAAA,iCAAiCD,IAAI,oDAAoDC,IAAI,SAElF;EAAAC,kBAAA,GAAsBC,QAAA,IACjCJ,YAAY,eAAeI,QAAQ,GAAK,EAAA,OAAO,CAEpC;EAAAC,kBAAA,GAAsBC,QACjC,IAAAN,WAAA,CAAY,cAAcM,QAAQ,GAAK,EAAA,OAAO;EAEnCC,wBAA2B,GAACC,UACvC,IAAAR,WAAA,CAAY,gBAAgBQ,UAAU,KAAK,OAAO,CAAA;EAEvCC,uBAA0B,GAACC,SACtC,IAAAV,WAAA,CAAY,eAAeU,SAAS,GAAA,EAAK,MAAM,CAAA;EAEpCC,2BAA8B,GAACD,aAC1CV,WAAY,CAAA,oBAAoBU,SAAS,GAAA,EAAK,UAAU,CAAA;AAEnD,SAASE,aAAaC,OAAuB,EAAA;EAClDC,OAAA,CAAQC,KAAKF,OAAO,CAAA;AACtB;ACjBA,MAAMG,SAAS;IAACC,OAAA,EAAS;EAAM,CAAA;EAElBC,qBAAiEA,CAAC;IAC7E7B,KAAA;IACA8B;EACF,CAAM,KAAA;IACE,MAAAC,OAAA,GAAUjB,kBAAmB,CAAAd,KAAA,CAAMgC,KAAK,CAAA;IACvC,OAAAF,WAAY,eAAAlC,GAAA,CAAA,MAAA,EAAA;MAAKa,KAAA,EAAOkB;MAAShC,QAAQ,EAAAoC;IAAA,CAAA,CAAA,GAAW,mBAAA,KAAA,EAAA;MAAItB,OAAOkB,MAAS;MAAAhC,QAAA,EAAQoC;KAAA,CAAA;EACzF,CAAA;EAEaE,qBAAiEA,CAAC;IAC7EhB,QAAA;IACAtB;EACF,CACS,KAAA,eAAAC,GAAA,CAAC;IAAKsC,SAAW,EAAA,sBAAsBjB,QAAQ,EAAA;IAAKtB;GAAS,CAGzD;EAAAwC,wBAAA,GAA6EA,CAAC;IACzFxC;EACF,CAAA,KACU,mBAAA,GAAA,EAAA;IAAGA;EAAS,CAAA,CAAA;EAGTyC,qBAAiEA,CAAC;IAACzC;EAAQ,CAAA,KAC9E,mBAAA,IAAA,EAAA;IAAIA;EAAS,CAAA,CAAA;EAGV0C,yBAAyEA,CAAC;IACrF1C;EACF,CAAA,KACS,eAACC,GAAA,CAAA,IAAA;IAAID;GAAS,CAAA;ECnBV2C,gBAAmB,GAAAA,CAAA,KAAoB,eAAA1C,GAAA,CAAA,IAAA,EAAA,CAAA,CAAG;EAE1C2C,kBAGT,GAAA;IACFC,MAAA,EAAQA,CAAC;MAAC7C;IAAc,MAAA,eAACC,GAAA,CAAA,GAAA;MAAGD;KAAS,CAAA;IACrC8C,UAAA,EAAYA,CAAC;MAAC9C;IAAc,MAAA,eAACC,GAAA,CAAA,YAAA;MAAYD;KAAS,CAAA;IAClD+C,EAAA,EAAIA,CAAC;MAAC/C;IAAc,MAAA,eAACC,GAAA,CAAA,IAAA;MAAID;KAAS,CAAA;IAClCgD,EAAA,EAAIA,CAAC;MAAChD;IAAc,MAAA,eAACC,GAAA,CAAA,IAAA;MAAID;KAAS,CAAA;IAClCiD,EAAA,EAAIA,CAAC;MAACjD;IAAc,MAAA,eAACC,GAAA,CAAA,IAAA;MAAID;KAAS,CAAA;IAClCkD,EAAA,EAAIA,CAAC;MAAClD;IAAc,MAAA,eAACC,GAAA,CAAA,IAAA;MAAID;KAAS,CAAA;IAClCmD,EAAA,EAAIA,CAAC;MAACnD;IAAc,MAAA,eAACC,GAAA,CAAA,IAAA;MAAID;KAAS,CAAA;IAClCoD,EAAA,EAAIA,CAAC;MAACpD;UAAc,eAACC,GAAA,CAAA,IAAA;MAAID;IAAS,CAAA;EACpC,CAAA;EAEaqD,iBAAiD,GAAA;IAC5DC,OAAO,CAAC,CAAA;IAERC,KAAO,EAAAX,kBAAA;IACPY,KAAO,EAAA/C,YAAA;IACPgD,IAAM,EAAA3D,YAAA;IACN4D,QAAU,EAAAvD,eAAA;IACVwD,SAAW,EAAAhB,gBAAA;IAEXiB,WAAa,EAAA1B,kBAAA;IACb2B,WAAa,EAAAvB,kBAAA;IACbwB,WAAa,EAAArB,kBAAA;IACbsB,eAAiB,EAAArB,sBAAA;IACjBsB,iBAAmB,EAAAxB;EACrB,CAAA;AC1CgB,SAAAyB,eAAAA,CACdC,QACAC,SAC6B,EAAA;EAEvB,MAAA;MAACZ;MAAOE,IAAM;MAAAC,QAAA;MAAUF;MAAOF;KAAkB,GAAAa,SAAA;IAARC,gCAAQD,SAAA,EAAAE,SAAA;EAEhD,OAAAC,aAAA,CAAAA,aAAA,KACFJ,MAAA;IACHX,KAAO,EAAAgB,WAAA,CAAYL,MAAQ,EAAAC,SAAA,EAAW,OAAO,CAAA;IAC7CV,IAAM,EAAAc,WAAA,CAAYL,MAAQ,EAAAC,SAAA,EAAW,MAAM,CAAA;IAC3CT,QAAU,EAAAa,WAAA,CAAYL,MAAQ,EAAAC,SAAA,EAAW,UAAU,CAAA;IACnDX,KAAO,EAAAe,WAAA,CAAYL,MAAQ,EAAAC,SAAA,EAAW,OAAO,CAAA;IAC7Cb,KAAO,EAAAiB,WAAA,CAAYL,MAAQ,EAAAC,SAAA,EAAW,OAAO;EAAA,GAC1CC,IAAA;AAEP;AAEA,SAASG,WAAAA,CACPL,MACA,EAAAC,SAAA,EACAK,GACyC,EAAA;EACzC,MAAMC,WAAWN,SAAU,CAAAK,GAAG,CACxB;IAAAE,SAAA,GAAYR,OAAOM,GAAG,CAAA;EAM5B,OAJI,OAAOC,QAAA,IAAa,UAIpB,IAAAA,QAAA,IAAY,OAAOC,SAAc,IAAA,UAAA,GAC5BD,QAGL,GAAAA,QAAA,GAAAH,aAAA,CAAAA,aAAA,KACSI,SAAW,GAAGD,YAGpBC,SAAA;AACT;ACAO,SAASC,YAAwDA,CAAA;EACtEtE,KAAO,EAAAuE,KAAA;EACPC,UAAY,EAAAC,kBAAA;EACZC,eAAA;EACAC,oBAAoBC,uBAA0B,GAAArD;AAChD,CAAsC,EAAA;EACpC,MAAMsD,sBAAyB,GAAAD,uBAAA,IAA2BE,IACpD;IAAAC,MAAA,GAASC,KAAM,CAAAC,OAAA,CAAQV,KAAK,CAAA,GAAIA,KAAQ,GAAA,CAACA,KAAK,CAAA;IAC9CW,SAASC,SAAU,CAAAJ,MAAA,EAAQL,eAAmB,IAAAU,mBAAmB,CAEjE;IAAAZ,UAAA,GAAaa,OAAQ,CAAA,MAClBZ,qBACHb,eAAgB,CAAAZ,iBAAA,EAAmByB,kBAAkB,CAAA,GACrDzB,iBACH,EAAA,CAACyB,kBAAkB,CAAC;IAEjBa,UAAa,GAAAD,OAAA,CACjB,MAAME,eAAgB,CAAAf,UAAA,EAAYK,sBAAsB,CAAA,EACxD,CAACL,YAAYK,sBAAsB,CAAA,CAAA;IAE/BW,WAAWN,MAAO,CAAAO,GAAA,CAAI,CAACC,IAAM,EAAAC,KAAA,KACjCL,UAAW,CAAA;MAACI;MAAYC,KAAO;MAAA7D,QAAA,EAAU,CAAO,CAAA;MAAAwD;IAAW,CAAA,CAC7D,CAAA;EAEA;IAAU3F,QAAA,EAAS6F;EAAA,CAAA,CAAA;AACrB;AAEA,MAAMD,eAAA,GAAkBA,CACtBf,UAAA,EACAK,sBACiB,KAAA;EACjB,SAASS,WAAkCM,OAAqC,EAAA;IACxE,MAAA;QAACF,IAAM;QAAAC,KAAA;QAAO7D;MAAQ,CAAA,GAAI8D;MAC1BzB,GAAM,GAAAuB,IAAA,CAAKG,IAAQ,IAAA,QAAQF,KAAK,EAAA;IAElC,OAAAG,yBAAA,CAA0BJ,IAAI,CAAA,GACzBK,UAAW,CAAAL,IAAA,EAAMC,OAAOxB,GAAG,CAAA,GAGhC6B,2BAA4B,CAAAN,IAAI,CAC3B,GAAAO,cAAA,CAAeP,MAAMC,KAAO,EAAAxB,GAAG,CAGpC,GAAA+B,yBAAA,CAA0BR,IAAI,CAAA,GACzBS,WAAWT,IAAM,EAAAC,KAAA,EAAOxB,GAAG,CAAA,GAGhCiC,yBAA0B,CAAAV,IAAI,IACzBW,iBAAkB,CAAAX,IAAA,EAAMC,KAAO,EAAAxB,GAAA,EAAKrC,QAAQ,CAAA,GAGjDwE,oBAAoBZ,IAAI,CAAA,GACnBa,WAAY,CAAAb,IAAA,EAAMC,KAAO,EAAAxB,GAAA,EAAKrC,QAAQ,CAG3C,GAAA0E,6BAAA,CAA8Bd,IAAI,CAAA,GAC7Be,UAAW,CAAAf,IAAA,EAAMvB,GAAG,CAAA,GAGtBuC,iBAAkB,CAAAhB,IAAA,EAAMC,KAAO,EAAAxB,GAAA,EAAKrC,QAAQ,CAAA;EAAA;EAGrD,SAASsE,0BAA0BV,IAA4B,EAAA;IACtD,OAAAA,IAAA,CAAK1D,SAASwC,UAAW,CAAAvB,KAAA;EAAA;EAIzB,SAAAgD,cAAAA,CACPP,IACA,EAAAC,KAAA,EACAxB,GACA,EAAA;IACM,MAAAwC,IAAA,GAAOC,eAAe;QAAClB,IAAA;QAAMC;QAAO7D,QAAU,EAAA,CAAA,CAAA;QAAOwD;OAAW,CAChE;MAAAuB,QAAA,GAAWrC,WAAWnB,QAEtB;MAAAyD,EAAA,GAAA,CADU,OAAOD,QAAa,IAAA,UAAA,GAAaA,WAAWA,QAAS,CAAAnB,IAAA,CAAKrC,QAAQ,CAAA,KAC5DmB,UAAW,CAAAd,eAAA;IAE7B,IAAAoD,EAAA,KAAOtC,WAAWd,eAAiB,EAAA;MAC/B,MAAAjD,KAAA,GAAQiF,KAAKrC,QAAY,IAAA,QAAA;MACRwB,sBAAA,CAAAvD,2BAAA,CAA4Bb,KAAK,CAAG,EAAA;QACzDG,IAAM,EAAAH,KAAA;QACNsG,QAAU,EAAA;MAAA,CACX,CAAA;IAAA;IAGH,IAAIpH,WAAWgH,IAAK,CAAAhH,QAAA;IACpB,IAAI+F,IAAK,CAAAjF,KAAA,IAASiF,IAAK,CAAAjF,KAAA,KAAU,QAAU,EAAA;MAGzC,MAAM;UAAC4C;QAAa,CAAa,GAAAqC,IAAA;QAAbsB,SAAA,GAAAC,wBAAA,CAAavB,IAAA,EAAAwB,UAAA;MACtBvH,QAAA,GAAA2F,UAAA,CAAW;QAACI,IAAM,EAAAsB,SAAA;QAAWrB;QAAO7D,QAAU,EAAA,CAAA,CAAA;QAAOwD;OAAW,CAAA;IAAA;IAI3E,OAAA,eAAA1F,GAAA,CAACkH;MAAa9G,KAAO,EAAA0F,IAAA;MAAMC,KAAc;MAAA7D,QAAA,EAAU,CAAO,CAAA;MAAAwD,UAAA;MACvD3F;IAAA,CAAA,EADMwE,GAET,CAAA;EAAA;EAIK,SAAA4B,UAAAA,CAAWL,IAA6B,EAAAC,KAAA,EAAexB,GAAa,EAAA;IACrE,MAAAxE,QAAA,GAAW+F,KAAK/F,QAAS,CAAA8F,GAAA,CAAI,CAAC0B,KAAO,EAAAC,UAAA,KACzC9B,UAAW,CAAA;QACTI,IAAM,EAAAyB,KAAA,CAAMtB,IAAO,GAAAsB,KAAA,GAAAlD,aAAA,CAAAA,aAAA,KAAYkD,KAAO;UAAAtB,IAAA,EAAM,MAAMF,KAAK,IAAIyB,UAAU;QAAE,EAAA;QACvEzB,KAAO,EAAAyB,UAAA;QACPtF,QAAU,EAAA,CAAA,CAAA;QACVwD;MACD,CAAA,CAGG,CAAA;MAAA+B,SAAA,GAAY7C,UAAW,CAAApB,IAAA;MAEvBkE,IADU,GAAA,CAAA,OAAOD,SAAc,IAAA,UAAA,GAAaA,SAAY,GAAAA,SAAA,CAAU3B,IAAK,CAAArC,QAAQ,MAC7DmB,UAAW,CAAAf,WAAA;IAE/B,IAAA6D,IAAA,KAAS9C,WAAWf,WAAa,EAAA;MAC7B,MAAAhD,KAAA,GAAQiF,KAAKrC,QAAY,IAAA,QAAA;MACRwB,sBAAA,CAAAzD,uBAAA,CAAwBX,KAAK,CAAG,EAAA;QAACsG,UAAU,WAAa;QAAAnG,IAAA,EAAMH;OAAM,CAAA;IAAA;IAI3F,OAAA,eAAAb,GAAA,CAAC0H;MAAetH,KAAO,EAAA0F,IAAA;MAAMC,KAAc;MAAA7D,QAAA,EAAU,CAAO,CAAA;MAAAwD,UAAA;MACzD3F;IAAA,CAAA,EADQwE,GAEX,CAAA;EAAA;EAIK,SAAAgC,UAAAA,CAAWT,IAAqC,EAAA6B,MAAA,EAAgBpD,GAAa,EAAA;IACpF,MAAM;QAACqD,OAAA;QAASvG,QAAU;QAAAwG;MAAA,IAAW/B,IAC/B;MAAAgC,IAAA,GAAOlD,UAAW,CAAArB,KAAA,CAAMlC,QAAQ,CAAK,IAAAuD,UAAA,CAAWhB,WAChD;MAAA7D,QAAA,GAAW+F,KAAK/F,QAAS,CAAA8F,GAAA,CAAI,CAAC0B,KAAA,EAAOC,UACzC,KAAA9B,UAAA,CAAW;QAACI,IAAA,EAAMyB,KAAO;QAAAxB,KAAA,EAAOyB,UAAY;QAAAtF,QAAA,EAAU,CAAM,CAAA;QAAAwD;MAAW,CAAA,CACzE,CAAA;IAEA,OAAIoC,IAAS,KAAAlD,UAAA,CAAWhB,WACtB,IAAAqB,sBAAA,CAAuB7D,kBAAmB,CAAAC,QAAQ,CAAG,EAAA;MAAC8F,QAAU,EAAA,MAAA;MAAQnG,IAAM,EAAAK;IAAA,CAAS,CAIvF,EAAA,eAAArB,GAAA,CAAC8H,IAAA,EAAA;MAECC,IAAA,EAAMC,gBAAgBlC,IAAI,CAAA;MAC1B1F,KAAO,EAAAwH,OAAA;MACPvG,QAAA;MACAwG,OAAA;MACAnC,UAAA;MAEC3F;IAAA,CAAA,EAPIwE,GAQP,CAAA;EAAA;EAIJ,SAASoC,WAAYA,CAAAb,IAAA,EAAyBC,KAAe,EAAAxB,GAAA,EAAarC,QAAmB,EAAA;IAE3F,MAAA+F,eAAA,GAAyBjB,cAAA,CAAe;QAAClB,IAAA;QAAMC,KAAO;QAAA7D,QAAA;QAAUwD;MAAU,CAAC,CACrE;MADA;QAACO;MAAkB,CAAA,GAAAgC,eAAA;MAATC,KAAA,GAAAb,wBAAA,CAAAY,eAAA,EAAAE,UAAA;MACVtH,KAAA,GAAQqH,KAAM,CAAApC,IAAA,CAAKjF,KAAS,IAAA,QAAA;MAG5BuH,KADJ,GAAA,CAAA,OAAOxD,UAAW,CAAAtB,KAAA,IAAU,UAAa,GAAAsB,UAAA,CAAWtB,KAAQ,GAAAsB,UAAA,CAAWtB,KAAM,CAAAzC,KAAK,MAC3D+D,UAAW,CAAAb,iBAAA;IAEpC,OAAIqE,UAAUxD,UAAW,CAAAb,iBAAA,IACvBkB,sBAAuB,CAAA3D,wBAAA,CAAyBT,KAAK,CAAG,EAAA;MACtDsG,QAAU,EAAA,YAAA;MACVnG,IAAM,EAAAH;IACP,CAAA,CAAA,EAGI,eAACb,GAAA,CAAAoI,KAAA,EAAA/D,aAAA,CAAAA,aAAA,KAAoB6D,KAAO;MAAA9H,KAAA,EAAO8H,KAAM,CAAApC,IAAA;MAAMJ;IAAA,IAAnCnB,GAA2D,CAAA;EAAA;EAGvE,SAAAsC,UAAAA,CAAWf,MAAuBvB,GAAa,EAAA;IACtD,IAAIuB,KAAKiC,IAAS,KAAA;AAAA,CAAM,EAAA;MACtB,MAAMM,YAAYzD,UAAW,CAAAlB,SAAA;MAC7B,OAAO2E,YAAY,eAACrI,GAAA,CAAAqI,SAAA,EAAe,CAAA,CAAA,EAAA9D,GAAK,CAAK,GAAA;AAAA,CAAA;IAAA;IAG/C,OAAOuB,IAAK,CAAAiC,IAAA;EAAA;EAGd,SAASjB,iBAAkBA,CAAAhB,IAAA,EAAmBC,KAAe,EAAAxB,GAAA,EAAarC,QAAmB,EAAA;IAC3F,MAAMoG,WAAc,GAAA;MAClBlI,KAAO,EAAA0F,IAAA;MACP5D,QAAA;MACA6D,KAAA;MACAL;IACF,CAAA;IAEuBT,sBAAA,CAAA/D,kBAAA,CAAmB4E,IAAK,CAAA1D,KAAK,CAAG,EAAA;MAAC+E,UAAU,OAAS;MAAAnG,IAAA,EAAM8E,IAAK,CAAA1D;KAAM,CAAA;IAE5F,MAAMmG,cAAc3D,UAAW,CAAAjB,WAAA;IAC/B,OAAQ,mBAAA4E,WAAA,EAAAlE,aAAA,KAA0BiE,WAAA,GAAT/D,GAAsB,CAAA;EAAA;EAGjD,SAASkC,iBAAkBA,CAAAX,IAAA,EAAmBC,KAAe,EAAAxB,GAAA,EAAarC,QAAmB,EAAA;IAC3F,MAAMoG,WAAc,GAAA;QAClBlI,KAAO,EAAA0F,IAAA;QACP5D,QAAA;QACA6D,KAAA;QACAL;MAGI,CAAA;MAAA8C,IAAA,GAAO5D,UAAW,CAAAvB,KAAA,CAAMyC,KAAK1D,KAAK,CAAA;IACjC,OAAAoG,IAAA,GAAQ,mBAAAA,IAAA,EAAAnE,aAAA,KAAmBiE,WAAA,GAAT/D,GAAsB,CAAK,GAAA,IAAA;EAAA;EAI/C,OAAAmB,UAAA;AACT,CAAA;AAEA,SAASsB,eAAehB,OAA2D,EAAA;EAC3E,MAAA;MAACF,IAAM;MAAAC,KAAA;MAAO7D,QAAU;MAAAwD;IAAA,IAAcM,OAEtC;IAAAjG,QAAA,GADO0I,cAAe,CAAA3C,IAAI,CACV,CAAAD,GAAA,CAAI,CAAC0B,KAAA,EAAOmB,CAChC,KAAAhD,UAAA,CAAW;MAACI,IAAA,EAAMyB,KAAO;MAAArF,QAAA,EAAU,CAAM,CAAA;MAAA6D,KAAA,EAAO2C,CAAG;MAAAhD;IAAW,CAAA,CAChE,CAAA;EAEO,OAAA;IACLO,IAAM,EAAAH,IAAA,CAAKG,IAAQ,IAAA,SAASF,KAAK,EAAA;IACjChG,QAAA;IACAgG,KAAA;IACA7D,QAAA;IACA4D;EACF,CAAA;AACF;AAEA,SAASZ,IAAOA,CAAA,EAAA,CAEhB;"}