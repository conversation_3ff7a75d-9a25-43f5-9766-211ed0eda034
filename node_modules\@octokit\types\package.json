{"name": "@octokit/types", "version": "13.10.0", "publishConfig": {"access": "public", "provenance": true}, "description": "Shared TypeScript definitions for Octokit projects", "dependencies": {"@octokit/openapi-types": "^24.2.0"}, "repository": "github:octokit/types.ts", "keywords": ["github", "api", "sdk", "toolkit", "typescript"], "author": "<PERSON> (https://twitter.com/gr2m)", "license": "MIT", "devDependencies": {"@octokit/tsconfig": "^4.0.0", "github-openapi-graphql-query": "^4.5.0", "handlebars": "^4.7.6", "npm-run-all2": "^7.0.0", "prettier": "^3.0.0", "semantic-release": "^24.0.0", "semantic-release-plugin-update-version-in-files": "^2.0.0", "sort-keys": "^5.0.0", "typedoc": "^0.26.0", "typescript": "^5.0.0"}, "octokit": {"openapi-version": "18.2.0"}, "files": ["dist-types/**"], "types": "dist-types/index.d.ts", "sideEffects": false}