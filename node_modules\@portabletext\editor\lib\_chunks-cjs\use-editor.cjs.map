{"version": 3, "file": "use-editor.cjs", "sources": ["../../src/internal-utils/global-scope.ts", "../../src/internal-utils/globally-scoped-context.ts", "../../src/editor/editor-context.tsx", "../../src/editor/use-editor.ts"], "sourcesContent": ["/**\n * Gets the global scope instance in a given environment.\n *\n * The strategy is to return the most modern, and if not, the most common:\n * - The `globalThis` variable is the modern approach to accessing the global scope\n * - The `window` variable is the global scope in a web browser\n * - The `self` variable is the global scope in workers and others\n * - The `global` variable is the global scope in Node.js\n */\nfunction getGlobalScope() {\n  if (typeof globalThis !== 'undefined') return globalThis\n  if (typeof window !== 'undefined') return window\n  if (typeof self !== 'undefined') return self\n  if (typeof global !== 'undefined') return global\n\n  throw new Error('@portabletext/editor: could not locate global scope')\n}\n\nexport const globalScope = getGlobalScope() as any\n", "import {createContext, type Context} from 'react'\nimport {globalScope} from './global-scope'\n\n/**\n * As `@portabletext/editor` is declared as a dependency, and may be\n * duplicated, sometimes across major versions it's critical that vital\n * React Contexts are shared even when there is a duplicate.\n *\n * We have to support a Sanity Plugin being able to call hooks like\n * `useEditor`, and then read the context setup by `sanity`, which calls\n * `EditorProvider`, even if the provider and hook are different instances in\n * memory.\n *\n * For this reason it's vital that all changes to globally scoped providers\n * remain fully backwards compatible.\n */\nexport function createGloballyScopedContext<\n  ContextType,\n  const T extends ContextType = ContextType,\n>(\n  /**\n   * Enforce that all Symbol.for keys used for globally scoped contexts have a predictable prefix\n   */\n  key: `@portabletext/editor/context/${string}`,\n  defaultValue: T,\n): Context<ContextType> {\n  const symbol = Symbol.for(key)\n\n  /**\n   * Prevent errors about re-renders on React SSR on Next.js App Router\n   */\n  if (typeof document === 'undefined') {\n    return createContext<ContextType>(defaultValue)\n  }\n\n  globalScope[symbol] = globalScope[symbol] ?? createContext<T>(defaultValue)\n\n  return globalScope[symbol]\n}\n", "import type {Editor} from '../editor'\nimport {createGloballyScopedContext} from '../internal-utils/globally-scoped-context'\n\nexport const EditorContext = createGloballyScopedContext<Editor | null>(\n  '@portabletext/editor/context/editor',\n  null,\n)\n", "import React from 'react'\nimport {EditorContext} from './editor-context'\n\n/**\n * @public\n * Get the current editor context from the `EditorProvider`.\n * Must be used inside the `EditorProvider` component.\n * @returns The current editor object.\n * @example\n * ```tsx\n * import { useEditor } from '@portabletext/editor'\n *\n * function MyComponent() {\n *  const editor = useEditor()\n * }\n * ```\n * @group Hooks\n */\nexport function useEditor() {\n  const editor = React.useContext(EditorContext)\n\n  if (!editor) {\n    throw new Error('No Editor set. Use EditorProvider to set one.')\n  }\n\n  return editor\n}\n"], "names": ["getGlobalScope", "globalThis", "window", "self", "global", "Error", "globalScope", "createGloballyScopedContext", "key", "defaultValue", "symbol", "Symbol", "for", "document", "createContext", "EditorContext", "useEditor", "editor", "React", "useContext"], "mappings": ";;;;;;AASA,SAASA,iBAAiB;AACxB,MAAI,OAAOC,aAAe,IAAa,QAAOA;AAC9C,MAAI,OAAOC,SAAW,IAAa,QAAOA;AAC1C,MAAI,OAAOC,OAAS,IAAa,QAAOA;AACxC,MAAI,OAAOC,SAAW,IAAa,QAAOA;AAE1C,QAAM,IAAIC,MAAM,qDAAqD;AACvE;AAEO,MAAMC,cAAcN,eAAAA;ACFpB,SAASO,4BAOdC,KACAC,cACsB;AACtB,QAAMC,SAASC,OAAOC,IAAIJ,GAAG;AAK7B,SAAI,OAAOK,WAAa,MACfC,MAAAA,cAA2BL,YAAY,KAGhDH,YAAYI,MAAM,IAAIJ,YAAYI,MAAM,KAAKI,MAAAA,cAAiBL,YAAY,GAEnEH,YAAYI,MAAM;AAC3B;ACnCO,MAAMK,gBAAgBR,4BAC3B,uCACA,IACF;ACYO,SAAAS,YAAA;AACL,QAAAC,SAAeC,eAAAA,QAAAC,WAAAJ,aAA8B;AAAC,MAAA,CAEzCE;AAAM,UAAA,IAAAZ,MACO,+CAA+C;AAAA,SAG1DY;AAAM;;;"}