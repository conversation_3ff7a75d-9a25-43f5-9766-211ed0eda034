{"name": "@portabletext/keyboard-shortcuts", "version": "1.1.1", "description": "Create platform-aware keyboard shortcuts with automatic detection of Apple vs non-Apple platforms", "keywords": ["portabletext", "keyboard-shortcuts"], "homepage": "https://portabletext.org", "bugs": {"url": "https://github.com/portabletext/editor/issues"}, "repository": {"type": "git", "url": "https://github.com/portabletext/editor.git", "directory": "packages/keyboard-shortcuts"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"source": "./src/index.ts", "import": "./dist/index.js", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["src", "dist"], "devDependencies": {"@sanity/pkg-utils": "^7.9.6", "vitest": "^3.2.4"}, "scripts": {"build": "pkg-utils build --strict --check --clean", "check:lint": "biome lint .", "check:types": "tsc", "check:types:watch": "tsc --watch", "clean": "del .turbo && del dist && del node_modules", "dev": "pkg-utils watch", "lint:fix": "biome lint --write .", "test": "vitest --run", "test:unit": "vitest --run --project unit", "test:unit:watch": "vitest --project unit", "test:watch": "vitest"}}