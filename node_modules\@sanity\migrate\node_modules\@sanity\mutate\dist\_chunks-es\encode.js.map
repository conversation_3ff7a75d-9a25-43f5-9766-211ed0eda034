{"version": 3, "file": "encode.js", "sources": ["../../src/encoders/sanity/decode.ts", "../../src/encoders/sanity/encode.ts"], "sourcesContent": ["import {type SetIfMissingOp, type SetOp} from '../../mutations/operations/types'\nimport {\n  type Mutation,\n  type NodePatch,\n  type SanityDocumentBase,\n} from '../../mutations/types'\nimport {parse as parsePath} from '../../path/parser/parse'\n\nexport type {Mutation, SanityDocumentBase}\n\nexport type SanityDiffMatchPatch = {\n  id: string\n  diffMatchPatch: {[path: string]: string}\n}\n\nexport type SanitySetPatch = {\n  id: string\n  set: {[path: string]: any}\n}\n\nexport type Insert = {\n  before?: string\n  after?: string\n  replace?: string\n  items: any[]\n}\n\nexport type SanityInsertPatch = {\n  id: string\n  insert: Insert\n}\n\nexport type SanityUnsetPatch = {\n  id: string\n  unset: string[]\n}\n\nexport type SanityIncPatch = {\n  id: string\n  inc: {[path: string]: number}\n}\n\nexport type SanityDecPatch = {\n  id: string\n  dec: {[path: string]: number}\n}\n\nexport type SanitySetIfMissingPatch = {\n  id: string\n  setIfMissing: {[path: string]: any}\n}\n\nexport type SanityPatch =\n  | SanitySetPatch\n  | SanityUnsetPatch\n  | SanityInsertPatch\n  | SanitySetIfMissingPatch\n  | SanityDiffMatchPatch\n  | SanityIncPatch\n  | SanityDecPatch\n\nexport type SanityCreateIfNotExistsMutation<Doc extends SanityDocumentBase> = {\n  createIfNotExists: Doc\n}\n\nexport type SanityCreateOrReplaceMutation<Doc extends SanityDocumentBase> = {\n  createOrReplace: Doc\n}\n\nexport type SanityCreateMutation<Doc extends SanityDocumentBase> = {\n  create: Doc\n}\n\nexport type SanityDeleteMutation = {\n  delete: {id: string}\n}\n\nexport type SanityPatchMutation = {\n  patch:\n    | SanitySetPatch\n    | SanitySetIfMissingPatch\n    | SanityDiffMatchPatch\n    | SanityInsertPatch\n    | SanityUnsetPatch\n}\n\nexport type SanityMutation<\n  Doc extends SanityDocumentBase = SanityDocumentBase,\n> =\n  | SanityCreateMutation<Doc>\n  | SanityCreateIfNotExistsMutation<Doc>\n  | SanityCreateOrReplaceMutation<Doc>\n  | SanityDeleteMutation\n  | SanityPatchMutation\n\nfunction isCreateIfNotExistsMutation<Doc extends SanityDocumentBase>(\n  sanityMutation: SanityMutation<Doc>,\n): sanityMutation is SanityCreateIfNotExistsMutation<Doc> {\n  return 'createIfNotExists' in sanityMutation\n}\n\nfunction isCreateOrReplaceMutation<Doc extends SanityDocumentBase>(\n  sanityMutation: SanityMutation<Doc>,\n): sanityMutation is SanityCreateOrReplaceMutation<Doc> {\n  return 'createOrReplace' in sanityMutation\n}\n\nfunction isCreateMutation<Doc extends SanityDocumentBase>(\n  sanityMutation: SanityMutation<Doc>,\n): sanityMutation is SanityCreateMutation<Doc> {\n  return 'create' in sanityMutation\n}\n\nfunction isDeleteMutation(\n  sanityMutation: SanityMutation<any>,\n): sanityMutation is SanityDeleteMutation {\n  return 'delete' in sanityMutation\n}\n\nfunction isPatchMutation(\n  sanityMutation: SanityMutation<any>,\n): sanityMutation is SanityPatchMutation {\n  return 'patch' in sanityMutation\n}\n\nfunction isSetPatch(sanityPatch: SanityPatch): sanityPatch is SanitySetPatch {\n  return 'set' in sanityPatch\n}\n\nfunction isSetIfMissingPatch(\n  sanityPatch: SanityPatch,\n): sanityPatch is SanitySetIfMissingPatch {\n  return 'setIfMissing' in sanityPatch\n}\n\nfunction isDiffMatchPatch(\n  sanityPatch: SanityPatch,\n): sanityPatch is SanityDiffMatchPatch {\n  return 'diffMatchPatch' in sanityPatch\n}\n\nfunction isUnsetPatch(\n  sanityPatch: SanityPatch,\n): sanityPatch is SanityUnsetPatch {\n  return 'unset' in sanityPatch\n}\n\nfunction isIncPatch(sanityPatch: SanityPatch): sanityPatch is SanityIncPatch {\n  return 'inc' in sanityPatch\n}\n\nfunction isDecPatch(sanityPatch: SanityPatch): sanityPatch is SanityDecPatch {\n  return 'inc' in sanityPatch\n}\n\nfunction isInsertPatch(\n  sanityPatch: SanityPatch,\n): sanityPatch is SanityInsertPatch {\n  return 'insert' in sanityPatch\n}\n\nexport function decodeAll<Doc extends SanityDocumentBase>(\n  sanityMutations: SanityMutation<Doc>[],\n) {\n  return sanityMutations.map(decodeMutation)\n}\n\nexport function decode<Doc extends SanityDocumentBase>(\n  encodedMutation: SanityMutation<Doc>,\n) {\n  return decodeMutation(encodedMutation)\n}\n\nfunction decodeMutation<Doc extends SanityDocumentBase>(\n  encodedMutation: SanityMutation<Doc>,\n): Mutation {\n  if (isCreateIfNotExistsMutation(encodedMutation)) {\n    return {\n      type: 'createIfNotExists',\n      document: encodedMutation.createIfNotExists,\n    }\n  }\n  if (isCreateOrReplaceMutation(encodedMutation)) {\n    return {\n      type: 'createOrReplace',\n      document: encodedMutation.createOrReplace,\n    }\n  }\n  if (isCreateMutation(encodedMutation)) {\n    return {type: 'create', document: encodedMutation.create}\n  }\n  if (isDeleteMutation(encodedMutation)) {\n    return {id: encodedMutation.delete.id, type: 'delete'}\n  }\n  if (isPatchMutation(encodedMutation)) {\n    return {\n      type: 'patch',\n      id: encodedMutation.patch.id,\n      patches: decodeNodePatches(encodedMutation.patch),\n    }\n  }\n  throw new Error(`Unknown mutation: ${JSON.stringify(encodedMutation)}`)\n}\n\nconst POSITION_KEYS = ['before', 'replace', 'after'] as const\n\nfunction getInsertPosition(insert: Insert) {\n  const positions = POSITION_KEYS.filter(k => k in insert)\n  if (positions.length > 1) {\n    throw new Error(\n      `Insert patch is ambiguous. Should only contain one of: ${POSITION_KEYS.join(\n        ', ',\n      )}, instead found ${positions.join(', ')}`,\n    )\n  }\n  return positions[0]\n}\n\nfunction decodeNodePatches<T>(patch: SanityPatch): NodePatch<any, any>[] {\n  // If multiple patches are included, then the order of execution is as follows\n  // set, setIfMissing, unset, inc, dec, insert.\n  // order is defined here: https://www.sanity.io/docs/http-mutations#2f480b2baca5\n  return [\n    ...getSetPatches(patch),\n    ...getSetIfMissingPatches(patch),\n    ...getUnsetPatches(patch),\n    ...getIncPatches(patch),\n    ...getDecPatches(patch),\n    ...getInsertPatches(patch),\n    ...getDiffMatchPatchPatches(patch),\n  ]\n\n  throw new Error(`Unknown patch: ${JSON.stringify(patch)}`)\n}\n\nfunction getSetPatches(patch: SanityPatch): NodePatch<any[], SetOp<any>>[] {\n  return isSetPatch(patch)\n    ? Object.keys(patch.set).map(path => ({\n        path: parsePath(path),\n        op: {type: 'set', value: patch.set[path]},\n      }))\n    : []\n}\n\nfunction getSetIfMissingPatches(\n  patch: SanityPatch,\n): NodePatch<any[], SetIfMissingOp<any>>[] {\n  return isSetIfMissingPatch(patch)\n    ? Object.keys(patch.setIfMissing).map(path => ({\n        path: parsePath(path),\n        op: {type: 'setIfMissing', value: patch.setIfMissing[path]},\n      }))\n    : []\n}\n\nfunction getDiffMatchPatchPatches(patch: SanityPatch) {\n  return isDiffMatchPatch(patch)\n    ? Object.keys(patch.diffMatchPatch).map(path => ({\n        path: parsePath(path),\n        op: {type: 'diffMatchPatch', value: patch.diffMatchPatch[path]},\n      }))\n    : []\n}\n\nfunction getUnsetPatches(patch: SanityPatch) {\n  return isUnsetPatch(patch)\n    ? patch.unset.map(path => ({\n        path: parsePath(path),\n        op: {type: 'unset'},\n      }))\n    : []\n}\n\nfunction getIncPatches(patch: SanityPatch) {\n  return isIncPatch(patch)\n    ? Object.keys(patch.inc).map(path => ({\n        path: parsePath(path),\n        op: {type: 'inc', amount: patch.inc[path]},\n      }))\n    : []\n}\n\nfunction getDecPatches(patch: SanityPatch) {\n  return isDecPatch(patch)\n    ? Object.keys(patch.dec).map(path => ({\n        path: parsePath(path),\n        op: {type: 'dec', amount: patch.dec[path]},\n      }))\n    : []\n}\n\nfunction getInsertPatches(patch: SanityPatch) {\n  if (!isInsertPatch(patch)) {\n    return []\n  }\n  const position = getInsertPosition(patch.insert)\n  if (!position) {\n    throw new Error('Insert patch missing position')\n  }\n\n  const path = parsePath(patch.insert[position]!)\n  const referenceItem = path.pop()\n\n  const op =\n    position === 'replace'\n      ? {\n          type: 'insert',\n          position: position,\n          referenceItem,\n          items: patch.insert.items,\n        }\n      : {\n          type: 'insert',\n          position: position,\n          referenceItem,\n          items: patch.insert.items,\n        }\n\n  return [{path, op}]\n}\n", "import {\n  type Mutation,\n  type NodePatch,\n  type Transaction,\n} from '../../mutations/types'\nimport {stringify as stringifyPath} from '../../path/parser/stringify'\n\nexport function encode(mutation: Mutation) {\n  return encodeMutation(mutation)\n}\n\nexport function encodeAll(mutations: Mutation[]) {\n  return mutations.flatMap(encode)\n}\n\nexport function encodeTransaction(transaction: Transaction) {\n  return {\n    transactionId: transaction.id,\n    mutations: encodeAll(transaction.mutations),\n  }\n}\n\nexport function encodeMutation(mutation: Mutation) {\n  if (\n    mutation.type === 'create' ||\n    mutation.type === 'createIfNotExists' ||\n    mutation.type === 'createOrReplace'\n  ) {\n    return {[mutation.type]: mutation.document}\n  }\n  if (mutation.type === 'delete') {\n    return {\n      delete: {id: mutation.id},\n    }\n  }\n  const ifRevisionID = mutation.options?.ifRevision\n  return mutation.patches.map(patch => {\n    return {\n      patch: {\n        id: mutation.id,\n        ...(ifRevisionID && {ifRevisionID}),\n        ...patchToSanity(patch),\n      },\n    }\n  })\n}\n\nfunction patchToSanity(patch: NodePatch) {\n  const {path, op} = patch\n  if (op.type === 'unset') {\n    return {unset: [stringifyPath(path)]}\n  }\n  if (op.type === 'insert') {\n    return {\n      insert: {\n        [op.position]: stringifyPath([...path, op.referenceItem]),\n        items: op.items,\n      },\n    }\n  }\n  if (op.type === 'diffMatchPatch') {\n    return {diffMatchPatch: {[stringifyPath(path)]: op.value}}\n  }\n  if (op.type === 'inc') {\n    return {inc: {[stringifyPath(path)]: op.amount}}\n  }\n  if (op.type === 'dec') {\n    return {dec: {[stringifyPath(path)]: op.amount}}\n  }\n  if (op.type === 'set' || op.type === 'setIfMissing') {\n    return {[op.type]: {[stringifyPath(path)]: op.value}}\n  }\n  if (op.type === 'truncate') {\n    const range = [\n      op.startIndex,\n      typeof op.endIndex === 'number' ? op.endIndex : '',\n    ].join(':')\n\n    return {unset: [`${stringifyPath(path)}[${range}]`]}\n  }\n  if (op.type === 'upsert') {\n    // note: upsert currently not supported by sanity, so will always insert at reference position\n    return {\n      unset: op.items.map(item =>\n        stringifyPath([...path, {_key: (item as any)._key}]),\n      ),\n      insert: {\n        [op.position]: stringifyPath([...path, op.referenceItem]),\n        items: op.items,\n      },\n    }\n  }\n  if (op.type === 'assign') {\n    return {\n      set: Object.fromEntries(\n        Object.keys(op.value).map(key => [\n          stringifyPath(path.concat(key)),\n          op.value[key as keyof typeof op.value],\n        ]),\n      ),\n    }\n  }\n  if (op.type === 'unassign') {\n    return {\n      unset: op.keys.map(key => stringifyPath(path.concat(key))),\n    }\n  }\n  if (op.type === 'replace') {\n    return {\n      insert: {\n        replace: stringifyPath(path.concat(op.referenceItem)),\n        items: op.items,\n      },\n    }\n  }\n  if (op.type === 'remove') {\n    return {\n      unset: [stringifyPath(path.concat(op.referenceItem))],\n    }\n  }\n  //@ts-expect-error all cases should be covered\n  throw new Error(`Unknown operation type ${op.type}`)\n}\n"], "names": ["parsePath", "stringifyPath"], "mappings": ";;AA+FA,SAAS,4BACP,gBACwD;AACxD,SAAO,uBAAuB;AAChC;AAEA,SAAS,0BACP,gBACsD;AACtD,SAAO,qBAAqB;AAC9B;AAEA,SAAS,iBACP,gBAC6C;AAC7C,SAAO,YAAY;AACrB;AAEA,SAAS,iBACP,gBACwC;AACxC,SAAO,YAAY;AACrB;AAEA,SAAS,gBACP,gBACuC;AACvC,SAAO,WAAW;AACpB;AAEA,SAAS,WAAW,aAAyD;AAC3E,SAAO,SAAS;AAClB;AAEA,SAAS,oBACP,aACwC;AACxC,SAAO,kBAAkB;AAC3B;AAEA,SAAS,iBACP,aACqC;AACrC,SAAO,oBAAoB;AAC7B;AAEA,SAAS,aACP,aACiC;AACjC,SAAO,WAAW;AACpB;AAEA,SAAS,WAAW,aAAyD;AAC3E,SAAO,SAAS;AAClB;AAEA,SAAS,WAAW,aAAyD;AAC3E,SAAO,SAAS;AAClB;AAEA,SAAS,cACP,aACkC;AAClC,SAAO,YAAY;AACrB;AAEO,SAAS,UACd,iBACA;AACO,SAAA,gBAAgB,IAAI,cAAc;AAC3C;AAEO,SAAS,OACd,iBACA;AACA,SAAO,eAAe,eAAe;AACvC;AAEA,SAAS,eACP,iBACU;AACV,MAAI,4BAA4B,eAAe;AACtC,WAAA;AAAA,MACL,MAAM;AAAA,MACN,UAAU,gBAAgB;AAAA,IAC5B;AAEF,MAAI,0BAA0B,eAAe;AACpC,WAAA;AAAA,MACL,MAAM;AAAA,MACN,UAAU,gBAAgB;AAAA,IAC5B;AAEF,MAAI,iBAAiB,eAAe;AAClC,WAAO,EAAC,MAAM,UAAU,UAAU,gBAAgB,OAAM;AAE1D,MAAI,iBAAiB,eAAe;AAClC,WAAO,EAAC,IAAI,gBAAgB,OAAO,IAAI,MAAM,SAAQ;AAEvD,MAAI,gBAAgB,eAAe;AAC1B,WAAA;AAAA,MACL,MAAM;AAAA,MACN,IAAI,gBAAgB,MAAM;AAAA,MAC1B,SAAS,kBAAkB,gBAAgB,KAAK;AAAA,IAClD;AAEF,QAAM,IAAI,MAAM,qBAAqB,KAAK,UAAU,eAAe,CAAC,EAAE;AACxE;AAEA,MAAM,gBAAgB,CAAC,UAAU,WAAW,OAAO;AAEnD,SAAS,kBAAkB,QAAgB;AACzC,QAAM,YAAY,cAAc,OAAO,CAAA,MAAK,KAAK,MAAM;AACvD,MAAI,UAAU,SAAS;AACrB,UAAM,IAAI;AAAA,MACR,0DAA0D,cAAc;AAAA,QACtE;AAAA,MACD,CAAA,mBAAmB,UAAU,KAAK,IAAI,CAAC;AAAA,IAC1C;AAEF,SAAO,UAAU,CAAC;AACpB;AAEA,SAAS,kBAAqB,OAA2C;AAIhE,SAAA;AAAA,IACL,GAAG,cAAc,KAAK;AAAA,IACtB,GAAG,uBAAuB,KAAK;AAAA,IAC/B,GAAG,gBAAgB,KAAK;AAAA,IACxB,GAAG,cAAc,KAAK;AAAA,IACtB,GAAG,cAAc,KAAK;AAAA,IACtB,GAAG,iBAAiB,KAAK;AAAA,IACzB,GAAG,yBAAyB,KAAK;AAAA,EACnC;AAGF;AAEA,SAAS,cAAc,OAAoD;AAClE,SAAA,WAAW,KAAK,IACnB,OAAO,KAAK,MAAM,GAAG,EAAE,IAAI,CAAS,UAAA;AAAA,IAClC,MAAMA,MAAU,IAAI;AAAA,IACpB,IAAI,EAAC,MAAM,OAAO,OAAO,MAAM,IAAI,IAAI,EAAC;AAAA,EAC1C,EAAE,IACF,CAAC;AACP;AAEA,SAAS,uBACP,OACyC;AAClC,SAAA,oBAAoB,KAAK,IAC5B,OAAO,KAAK,MAAM,YAAY,EAAE,IAAI,CAAS,UAAA;AAAA,IAC3C,MAAMA,MAAU,IAAI;AAAA,IACpB,IAAI,EAAC,MAAM,gBAAgB,OAAO,MAAM,aAAa,IAAI,EAAC;AAAA,EAC5D,EAAE,IACF,CAAC;AACP;AAEA,SAAS,yBAAyB,OAAoB;AAC7C,SAAA,iBAAiB,KAAK,IACzB,OAAO,KAAK,MAAM,cAAc,EAAE,IAAI,CAAS,UAAA;AAAA,IAC7C,MAAMA,MAAU,IAAI;AAAA,IACpB,IAAI,EAAC,MAAM,kBAAkB,OAAO,MAAM,eAAe,IAAI,EAAC;AAAA,EAChE,EAAE,IACF,CAAC;AACP;AAEA,SAAS,gBAAgB,OAAoB;AAC3C,SAAO,aAAa,KAAK,IACrB,MAAM,MAAM,IAAI,CAAS,UAAA;AAAA,IACvB,MAAMA,MAAU,IAAI;AAAA,IACpB,IAAI,EAAC,MAAM,QAAO;AAAA,EACpB,EAAE,IACF,CAAC;AACP;AAEA,SAAS,cAAc,OAAoB;AAClC,SAAA,WAAW,KAAK,IACnB,OAAO,KAAK,MAAM,GAAG,EAAE,IAAI,CAAS,UAAA;AAAA,IAClC,MAAMA,MAAU,IAAI;AAAA,IACpB,IAAI,EAAC,MAAM,OAAO,QAAQ,MAAM,IAAI,IAAI,EAAC;AAAA,EAC3C,EAAE,IACF,CAAC;AACP;AAEA,SAAS,cAAc,OAAoB;AAClC,SAAA,WAAW,KAAK,IACnB,OAAO,KAAK,MAAM,GAAG,EAAE,IAAI,CAAS,UAAA;AAAA,IAClC,MAAMA,MAAU,IAAI;AAAA,IACpB,IAAI,EAAC,MAAM,OAAO,QAAQ,MAAM,IAAI,IAAI,EAAC;AAAA,EAC3C,EAAE,IACF,CAAC;AACP;AAEA,SAAS,iBAAiB,OAAoB;AACxC,MAAA,CAAC,cAAc,KAAK;AACtB,WAAO,CAAC;AAEJ,QAAA,WAAW,kBAAkB,MAAM,MAAM;AAC/C,MAAI,CAAC;AACG,UAAA,IAAI,MAAM,+BAA+B;AAGjD,QAAM,OAAOA,MAAU,MAAM,OAAO,QAAQ,CAAE,GACxC,gBAAgB,KAAK,IAAA,GAErB,KACJ,aAAa,YACT;AAAA,IACE,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,OAAO,MAAM,OAAO;AAAA,EAAA,IAEtB;AAAA,IACE,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,OAAO,MAAM,OAAO;AAAA,EACtB;AAEN,SAAO,CAAC,EAAC,MAAM,IAAG;AACpB;ACxTO,SAAS,OAAO,UAAoB;AACzC,SAAO,eAAe,QAAQ;AAChC;AAEO,SAAS,UAAU,WAAuB;AACxC,SAAA,UAAU,QAAQ,MAAM;AACjC;AAEO,SAAS,kBAAkB,aAA0B;AACnD,SAAA;AAAA,IACL,eAAe,YAAY;AAAA,IAC3B,WAAW,UAAU,YAAY,SAAS;AAAA,EAC5C;AACF;AAEO,SAAS,eAAe,UAAoB;AACjD,MACE,SAAS,SAAS,YAClB,SAAS,SAAS,uBAClB,SAAS,SAAS;AAElB,WAAO,EAAC,CAAC,SAAS,IAAI,GAAG,SAAS,SAAQ;AAE5C,MAAI,SAAS,SAAS;AACb,WAAA;AAAA,MACL,QAAQ,EAAC,IAAI,SAAS,GAAE;AAAA,IAC1B;AAEI,QAAA,eAAe,SAAS,SAAS;AAChC,SAAA,SAAS,QAAQ,IAAI,CACnB,WAAA;AAAA,IACL,OAAO;AAAA,MACL,IAAI,SAAS;AAAA,MACb,GAAI,gBAAgB,EAAC,aAAY;AAAA,MACjC,GAAG,cAAc,KAAK;AAAA,IAAA;AAAA,EACxB,EAEH;AACH;AAEA,SAAS,cAAc,OAAkB;AACjC,QAAA,EAAC,MAAM,GAAA,IAAM;AACnB,MAAI,GAAG,SAAS;AACd,WAAO,EAAC,OAAO,CAACC,UAAc,IAAI,CAAC,EAAC;AAEtC,MAAI,GAAG,SAAS;AACP,WAAA;AAAA,MACL,QAAQ;AAAA,QACN,CAAC,GAAG,QAAQ,GAAGA,UAAc,CAAC,GAAG,MAAM,GAAG,aAAa,CAAC;AAAA,QACxD,OAAO,GAAG;AAAA,MAAA;AAAA,IAEd;AAEF,MAAI,GAAG,SAAS;AACP,WAAA,EAAC,gBAAgB,EAAC,CAACA,UAAc,IAAI,CAAC,GAAG,GAAG,QAAM;AAE3D,MAAI,GAAG,SAAS;AACP,WAAA,EAAC,KAAK,EAAC,CAACA,UAAc,IAAI,CAAC,GAAG,GAAG,SAAO;AAEjD,MAAI,GAAG,SAAS;AACP,WAAA,EAAC,KAAK,EAAC,CAACA,UAAc,IAAI,CAAC,GAAG,GAAG,SAAO;AAEjD,MAAI,GAAG,SAAS,SAAS,GAAG,SAAS;AACnC,WAAO,EAAC,CAAC,GAAG,IAAI,GAAG,EAAC,CAACA,UAAc,IAAI,CAAC,GAAG,GAAG,QAAM;AAElD,MAAA,GAAG,SAAS,YAAY;AAC1B,UAAM,QAAQ;AAAA,MACZ,GAAG;AAAA,MACH,OAAO,GAAG,YAAa,WAAW,GAAG,WAAW;AAAA,IAAA,EAChD,KAAK,GAAG;AAEH,WAAA,EAAC,OAAO,CAAC,GAAGA,UAAc,IAAI,CAAC,IAAI,KAAK,GAAG,EAAC;AAAA,EAAA;AAErD,MAAI,GAAG,SAAS;AAEP,WAAA;AAAA,MACL,OAAO,GAAG,MAAM;AAAA,QAAI,CAAA,SAClBA,UAAc,CAAC,GAAG,MAAM,EAAC,MAAO,KAAa,MAAK,CAAC;AAAA,MACrD;AAAA,MACA,QAAQ;AAAA,QACN,CAAC,GAAG,QAAQ,GAAGA,UAAc,CAAC,GAAG,MAAM,GAAG,aAAa,CAAC;AAAA,QACxD,OAAO,GAAG;AAAA,MAAA;AAAA,IAEd;AAEF,MAAI,GAAG,SAAS;AACP,WAAA;AAAA,MACL,KAAK,OAAO;AAAA,QACV,OAAO,KAAK,GAAG,KAAK,EAAE,IAAI,CAAO,QAAA;AAAA,UAC/BA,UAAc,KAAK,OAAO,GAAG,CAAC;AAAA,UAC9B,GAAG,MAAM,GAA4B;AAAA,QACtC,CAAA;AAAA,MAAA;AAAA,IAEL;AAEF,MAAI,GAAG,SAAS;AACP,WAAA;AAAA,MACL,OAAO,GAAG,KAAK,IAAI,CAAA,QAAOA,UAAc,KAAK,OAAO,GAAG,CAAC,CAAC;AAAA,IAC3D;AAEF,MAAI,GAAG,SAAS;AACP,WAAA;AAAA,MACL,QAAQ;AAAA,QACN,SAASA,UAAc,KAAK,OAAO,GAAG,aAAa,CAAC;AAAA,QACpD,OAAO,GAAG;AAAA,MAAA;AAAA,IAEd;AAEF,MAAI,GAAG,SAAS;AACP,WAAA;AAAA,MACL,OAAO,CAACA,UAAc,KAAK,OAAO,GAAG,aAAa,CAAC,CAAC;AAAA,IACtD;AAGF,QAAM,IAAI,MAAM,0BAA0B,GAAG,IAAI,EAAE;AACrD;"}