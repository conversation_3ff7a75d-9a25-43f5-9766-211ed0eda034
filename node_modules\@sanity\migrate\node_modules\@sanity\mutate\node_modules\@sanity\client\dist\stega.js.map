{"version": 3, "file": "stega.js", "sources": ["../src/stega/index.ts"], "sourcesContent": ["export * from '@sanity/client'\nimport {\n  createClient as originalCreateClient,\n  ObservableSanityClient,\n  requester as originalRequester,\n  SanityClient,\n} from '@sanity/client'\n\nexport {encodeIntoResult} from './encodeIntoResult'\nexport {stegaClean, vercelStegaCleanAll} from './stegaClean'\nexport {stegaEncodeSourceMap} from './stegaEncodeSourceMap'\nexport * from './types'\n\n/**\n * @deprecated -- Use `import {SanityClient} from '@sanity/client'` instead\n * @public\n */\nexport class SanityStegaClient extends SanityClient {}\n\n/**\n * @deprecated -- Use `import {ObservableSanityClient} from '@sanity/client'` instead\n * @public\n */\nexport class ObservableSanityStegaClient extends ObservableSanityClient {}\n\n/**\n * @deprecated -- Use `import {requester} from '@sanity/client'` instead\n * @public\n */\nexport const requester = originalRequester\n\n/**\n * @deprecated -- Use `import {createClient} from '@sanity/client'` instead\n * @public\n */\nexport const createClient = originalCreateClient\n"], "names": ["originalRequester", "originalCreateClient"], "mappings": ";;;;AAiBO,MAAM,0BAA0B,aAAa;AAAC;AAM9C,MAAM,oCAAoC,uBAAuB;AAAC;AAM5D,MAAA,YAAYA,aAMZ,eAAeC;"}