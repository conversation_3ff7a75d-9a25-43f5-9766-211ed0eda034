{"version": 3, "file": "index.cjs", "sources": ["../src/types.ts", "../src/types.portable-text.ts", "../src/util/resolveJsType.ts", "../../../node_modules/.pnpm/@vercel+stega@0.1.2/node_modules/@vercel/stega/dist/index.mjs", "../src/constants.ts", "../src/HtmlDeserializer/preprocessors/xpathResult.ts", "../src/HtmlDeserializer/preprocessors/gdocs.ts", "../src/HtmlDeserializer/preprocessors/html.ts", "../src/HtmlDeserializer/preprocessors/notion.ts", "../src/HtmlDeserializer/preprocessors/whitespace.ts", "../src/HtmlDeserializer/preprocessors/word.ts", "../src/HtmlDeserializer/preprocessors/index.ts", "../src/HtmlDeserializer/helpers.ts", "../src/HtmlDeserializer/rules/gdocs.ts", "../src/util/randomKey.ts", "../src/HtmlDeserializer/rules/whitespace-text-node.ts", "../src/HtmlDeserializer/rules/html.ts", "../src/HtmlDeserializer/rules/notion.ts", "../src/HtmlDeserializer/rules/word.ts", "../src/HtmlDeserializer/rules/index.ts", "../src/HtmlDeserializer/index.ts", "../src/util/normalizeBlock.ts", "../src/index.ts"], "sourcesContent": ["import type {PortableTextObject} from './types.portable-text'\n\n/**\n * @public\n */\nexport interface TypedObject {\n  _type: string\n  _key?: string\n}\n\n/**\n * @public\n */\nexport interface ArbitraryTypedObject extends TypedObject {\n  [key: string]: unknown\n}\n\nexport function isArbitraryTypedObject(\n  object: unknown,\n): object is ArbitraryTypedObject {\n  return isRecord(object) && typeof object._type === 'string'\n}\n\nfunction isRecord(value: unknown): value is Record<string, unknown> {\n  return !!value && (typeof value === 'object' || typeof value === 'function')\n}\n\nexport interface MinimalSpan {\n  _type: 'span'\n  _key?: string\n  text: string\n  marks?: string[]\n}\n\nexport interface MinimalBlock extends TypedObject {\n  _type: 'block'\n  children: TypedObject[]\n  markDefs?: TypedObject[]\n  style?: string\n  level?: number\n  listItem?: string\n}\n\nexport interface PlaceholderDecorator {\n  _type: '__decorator'\n  name: string\n  children: TypedObject[]\n}\n\nexport interface PlaceholderAnnotation {\n  _type: '__annotation'\n  markDef: PortableTextObject\n  children: TypedObject[]\n}\n\n/**\n * @public\n */\nexport type HtmlParser = (html: string) => Document\n\n/**\n * @public\n */\nexport type WhiteSpacePasteMode = 'preserve' | 'remove' | 'normalize'\n\n/**\n * @public\n */\nexport interface HtmlDeserializerOptions {\n  keyGenerator?: () => string\n  rules?: DeserializerRule[]\n  parseHtml?: HtmlParser\n  unstable_whitespaceOnPasteMode?: WhiteSpacePasteMode\n}\n\nexport interface HtmlPreprocessorOptions {\n  unstable_whitespaceOnPasteMode?: WhiteSpacePasteMode\n}\n\n/**\n * @public\n */\nexport interface DeserializerRule {\n  deserialize: (\n    el: Node,\n    next: (\n      elements: Node | Node[] | NodeList,\n    ) => TypedObject | TypedObject[] | undefined,\n    createBlock: (props: ArbitraryTypedObject) => {\n      _type: string\n      block: ArbitraryTypedObject\n    },\n  ) => TypedObject | TypedObject[] | undefined\n}\n", "import type {Schema} from '@portabletext/schema'\nimport {isArbitraryTypedObject} from './types'\n\n/**\n * @public\n */\nexport type PortableTextBlock = PortableTextTextBlock | PortableTextObject\n\n/**\n * @public\n */\nexport interface PortableTextTextBlock<\n  TChild = PortableTextSpan | PortableTextObject,\n> {\n  _type: string\n  _key: string\n  children: TChild[]\n  markDefs?: PortableTextObject[]\n  listItem?: string\n  style?: string\n  level?: number\n}\n\nexport function isTextBlock(\n  schema: Schema,\n  block: unknown,\n): block is PortableTextTextBlock {\n  if (!isArbitraryTypedObject(block)) {\n    return false\n  }\n\n  if (block._type !== schema.block.name) {\n    return false\n  }\n\n  if (!Array.isArray(block.children)) {\n    return false\n  }\n\n  return true\n}\n\n/**\n * @public\n */\nexport interface PortableTextSpan {\n  _key: string\n  _type: 'span'\n  text: string\n  marks?: string[]\n}\n\nexport function isSpan(\n  schema: Schema,\n  child: unknown,\n): child is PortableTextSpan {\n  if (!isArbitraryTypedObject(child)) {\n    return false\n  }\n\n  if (child._type !== schema.span.name) {\n    return false\n  }\n\n  if (typeof child.text !== 'string') {\n    return false\n  }\n\n  return true\n}\n\n/**\n * @public\n */\nexport interface PortableTextObject {\n  _type: string\n  _key: string\n  [other: string]: unknown\n}\n", "const objectToString = Object.prototype.toString\n\n// Copied from https://github.com/ForbesLindesay/type-of\n// but inlined to have fine grained control\nexport function resolveJsType(val: unknown) {\n  switch (objectToString.call(val)) {\n    case '[object Function]':\n      return 'function'\n    case '[object Date]':\n      return 'date'\n    case '[object RegExp]':\n      return 'regexp'\n    case '[object Arguments]':\n      return 'arguments'\n    case '[object Array]':\n      return 'array'\n    case '[object String]':\n      return 'string'\n    default:\n  }\n\n  if (val === null) {\n    return 'null'\n  }\n\n  if (val === undefined) {\n    return 'undefined'\n  }\n\n  if (\n    val &&\n    typeof val === 'object' &&\n    'nodeType' in val &&\n    (val as {nodeType: unknown}).nodeType === 1\n  ) {\n    return 'element'\n  }\n\n  if (val === Object(val)) {\n    return 'object'\n  }\n\n  return typeof val\n}\n", "var s={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},c={0:8203,1:8204,2:8205,3:65279},u=new Array(4).fill(String.fromCodePoint(c[0])).join(\"\"),m=String.fromCharCode(0);function E(t){let e=JSON.stringify(t);return`${u}${Array.from(e).map(r=>{let n=r.charCodeAt(0);if(n>255)throw new Error(`Only ASCII edit info can be encoded. Error attempting to encode ${e} on character ${r} (${n})`);return Array.from(n.toString(4).padStart(4,\"0\")).map(o=>String.fromCodePoint(c[o])).join(\"\")}).join(\"\")}`}function y(t){let e=JSON.stringify(t);return Array.from(e).map(r=>{let n=r.charCodeAt(0);if(n>255)throw new Error(`Only ASCII edit info can be encoded. Error attempting to encode ${e} on character ${r} (${n})`);return Array.from(n.toString(16).padStart(2,\"0\")).map(o=>String.fromCodePoint(s[o])).join(\"\")}).join(\"\")}function I(t){return!Number.isNaN(Number(t))||/[a-z]/i.test(t)&&!/\\d+(?:[-:\\/]\\d+){2}(?:T\\d+(?:[-:\\/]\\d+){1,2}(\\.\\d+)?Z?)?/.test(t)?!1:Boolean(Date.parse(t))}function T(t){try{new URL(t,t.startsWith(\"/\")?\"https://acme.com\":void 0)}catch{return!1}return!0}function C(t,e,r=\"auto\"){return r===!0||r===\"auto\"&&(I(t)||T(t))?t:`${t}${E(e)}`}var x=Object.fromEntries(Object.entries(c).map(t=>t.reverse())),g=Object.fromEntries(Object.entries(s).map(t=>t.reverse())),S=`${Object.values(s).map(t=>`\\\\u{${t.toString(16)}}`).join(\"\")}`,f=new RegExp(`[${S}]{4,}`,\"gu\");function G(t){let e=t.match(f);if(!!e)return h(e[0],!0)[0]}function $(t){let e=t.match(f);if(!!e)return e.map(r=>h(r)).flat()}function h(t,e=!1){let r=Array.from(t);if(r.length%2===0){if(r.length%4||!t.startsWith(u))return A(r,e)}else throw new Error(\"Encoded data has invalid length\");let n=[];for(let o=r.length*.25;o--;){let p=r.slice(o*4,o*4+4).map(d=>x[d.codePointAt(0)]).join(\"\");n.unshift(String.fromCharCode(parseInt(p,4)))}if(e){n.shift();let o=n.indexOf(m);return o===-1&&(o=n.length),[JSON.parse(n.slice(0,o).join(\"\"))]}return n.join(\"\").split(m).filter(Boolean).map(o=>JSON.parse(o))}function A(t,e){var d;let r=[];for(let i=t.length*.5;i--;){let a=`${g[t[i*2].codePointAt(0)]}${g[t[i*2+1].codePointAt(0)]}`;r.unshift(String.fromCharCode(parseInt(a,16)))}let n=[],o=[r.join(\"\")],p=10;for(;o.length;){let i=o.shift();try{if(n.push(JSON.parse(i)),e)return n}catch(a){if(!p--)throw a;let l=+((d=a.message.match(/\\sposition\\s(\\d+)$/))==null?void 0:d[1]);if(!l)throw a;o.unshift(i.substring(0,l),i.substring(l))}}return n}function _(t){var e;return{cleaned:t.replace(f,\"\"),encoded:((e=t.match(f))==null?void 0:e[0])||\"\"}}function O(t){return t&&JSON.parse(_(JSON.stringify(t)).cleaned)}export{f as VERCEL_STEGA_REGEX,y as legacyStegaEncode,O as vercelStegaClean,C as vercelStegaCombine,G as vercelStegaDecode,$ as vercelStegaDecodeAll,E as vercelStegaEncode,_ as vercelStegaSplit};\n", "import {uniq} from 'lodash'\n\nexport interface PartialBlock {\n  _type: string\n  markDefs: string[]\n  style: string\n  level?: number\n  listItem?: string\n}\n\nexport const PRESERVE_WHITESPACE_TAGS = ['pre', 'textarea', 'code']\n\nexport const BLOCK_DEFAULT_STYLE = 'normal'\n\nexport const DEFAULT_BLOCK: PartialBlock = Object.freeze({\n  _type: 'block',\n  markDefs: [],\n  style: BLOCK_DEFAULT_STYLE,\n})\n\nexport const DEFAULT_SPAN = Object.freeze({\n  _type: 'span',\n  marks: [] as string[],\n})\n\nexport const HTML_BLOCK_TAGS = {\n  p: DEFAULT_BLOCK,\n  blockquote: {...DEFAULT_BLOCK, style: 'blockquote'} as PartialBlock,\n}\n\nexport const HTML_SPAN_TAGS = {\n  span: {object: 'text'},\n}\n\nexport const HTML_LIST_CONTAINER_TAGS: Record<\n  string,\n  {object: null} | undefined\n> = {\n  ol: {object: null},\n  ul: {object: null},\n}\n\nexport const HTML_HEADER_TAGS: Record<string, PartialBlock | undefined> = {\n  h1: {...DEFAULT_BLOCK, style: 'h1'},\n  h2: {...DEFAULT_BLOCK, style: 'h2'},\n  h3: {...DEFAULT_BLOCK, style: 'h3'},\n  h4: {...DEFAULT_BLOCK, style: 'h4'},\n  h5: {...DEFAULT_BLOCK, style: 'h5'},\n  h6: {...DEFAULT_BLOCK, style: 'h6'},\n}\n\nexport const HTML_MISC_TAGS = {\n  br: {...DEFAULT_BLOCK, style: BLOCK_DEFAULT_STYLE} as PartialBlock,\n}\n\nexport const HTML_DECORATOR_TAGS: Record<string, string | undefined> = {\n  b: 'strong',\n  strong: 'strong',\n\n  i: 'em',\n  em: 'em',\n\n  u: 'underline',\n  s: 'strike-through',\n  strike: 'strike-through',\n  del: 'strike-through',\n\n  code: 'code',\n  sup: 'sup',\n  sub: 'sub',\n  ins: 'ins',\n  mark: 'mark',\n  small: 'small',\n}\n\nexport const HTML_LIST_ITEM_TAGS: Record<string, PartialBlock | undefined> = {\n  li: {\n    ...DEFAULT_BLOCK,\n    style: BLOCK_DEFAULT_STYLE,\n    level: 1,\n    listItem: 'bullet',\n  },\n}\n\nexport const ELEMENT_MAP = {\n  ...HTML_BLOCK_TAGS,\n  ...HTML_SPAN_TAGS,\n  ...HTML_LIST_CONTAINER_TAGS,\n  ...HTML_LIST_ITEM_TAGS,\n  ...HTML_HEADER_TAGS,\n  ...HTML_MISC_TAGS,\n}\n\nexport const DEFAULT_SUPPORTED_STYLES = uniq(\n  Object.values(ELEMENT_MAP)\n    .filter((tag): tag is PartialBlock => 'style' in tag)\n    .map((tag) => tag.style),\n)\n\nexport const DEFAULT_SUPPORTED_DECORATORS = uniq(\n  Object.values(HTML_DECORATOR_TAGS),\n)\n\nexport const DEFAULT_SUPPORTED_ANNOTATIONS = ['link']\n", "// We need this here if run server side\nexport const _XPathResult = {\n  ANY_TYPE: 0,\n  NUMBER_TYPE: 1,\n  STRING_TYPE: 2,\n  BOOLEAN_TYPE: 3,\n  UNORDERED_NODE_ITERATOR_TYPE: 4,\n  ORDERED_NODE_ITERATOR_TYPE: 5,\n  UNORDERED_NODE_SNAPSHOT_TYPE: 6,\n  ORDERED_NODE_SNAPSHOT_TYPE: 7,\n  ANY_UNORDERED_NODE_TYPE: 8,\n  FIRST_ORDERED_NODE_TYPE: 9,\n}\n", "import type {HtmlPreprocessorOptions} from '../../types'\nimport {normalizeWhitespace, removeAllWhitespace, tagName} from '../helpers'\nimport {_XPathResult} from './xpathResult'\n\nexport default (\n  _html: string,\n  doc: Document,\n  options: HtmlPreprocessorOptions,\n): Document => {\n  const whitespaceOnPasteMode =\n    options?.unstable_whitespaceOnPasteMode || 'preserve'\n  let gDocsRootOrSiblingNode = doc\n    .evaluate(\n      '//*[@id and contains(@id, \"docs-internal-guid\")]',\n      doc,\n      null,\n      _XPathResult.ORDERED_NODE_ITERATOR_TYPE,\n      null,\n    )\n    .iterateNext()\n\n  if (gDocsRootOrSiblingNode) {\n    const isWrappedRootTag = tagName(gDocsRootOrSiblingNode) === 'b'\n\n    // If this document isn't wrapped in a 'b' tag, then assume all siblings live on the root level\n    if (!isWrappedRootTag) {\n      gDocsRootOrSiblingNode = doc.body\n    }\n\n    switch (whitespaceOnPasteMode) {\n      case 'normalize':\n        // Keep only 1 empty block between content nodes\n        normalizeWhitespace(gDocsRootOrSiblingNode)\n        break\n      case 'remove':\n        // Remove all whitespace nodes\n        removeAllWhitespace(gDocsRootOrSiblingNode)\n        break\n      default:\n        break\n    }\n\n    // Tag every child with attribute 'is-google-docs' so that the GDocs rule-set can\n    // work exclusivly on these children\n    const childNodes = doc.evaluate(\n      '//*',\n      doc,\n      null,\n      _XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE,\n      null,\n    )\n\n    for (let i = childNodes.snapshotLength - 1; i >= 0; i--) {\n      const elm = childNodes.snapshotItem(i) as HTMLElement\n      elm?.setAttribute('data-is-google-docs', 'true')\n\n      if (\n        elm?.parentElement === gDocsRootOrSiblingNode ||\n        (!isWrappedRootTag && elm.parentElement === doc.body)\n      ) {\n        elm?.setAttribute('data-is-root-node', 'true')\n        tagName(elm)\n      }\n\n      // Handle checkmark lists - The first child of a list item is an image with a checkmark, and the serializer\n      // expects the first child to be the text node\n      if (\n        tagName(elm) === 'li' &&\n        elm.firstChild &&\n        tagName(elm?.firstChild) === 'img'\n      ) {\n        elm.removeChild(elm.firstChild)\n      }\n    }\n\n    // Remove that 'b' which Google Docs wraps the HTML content in\n    if (isWrappedRootTag) {\n      doc.body.firstElementChild?.replaceWith(\n        ...Array.from(gDocsRootOrSiblingNode.childNodes),\n      )\n    }\n\n    return doc\n  }\n  return doc\n}\n", "import {_XPathResult} from './xpathResult'\n\n// Remove this cruft from the document\nconst unwantedWordDocumentPaths = [\n  '/html/text()',\n  '/html/head/text()',\n  '/html/body/text()',\n  '/html/body/ul/text()',\n  '/html/body/ol/text()',\n  '//comment()',\n  '//style',\n  '//xml',\n  '//script',\n  '//meta',\n  '//link',\n]\n\nexport default (_html: string, doc: Document): Document => {\n  // Make sure text directly on the body is wrapped in spans.\n  // This mimics what the browser does before putting html on the clipboard,\n  // when used in a script context with JSDOM\n  const bodyTextNodes = doc.evaluate(\n    '/html/body/text()',\n    doc,\n    null,\n    _XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE,\n    null,\n  )\n\n  for (let i = bodyTextNodes.snapshotLength - 1; i >= 0; i--) {\n    const node = bodyTextNodes.snapshotItem(i) as HTMLElement\n    const text = node.textContent || ''\n    if (text.replace(/[^\\S\\n]+$/g, '')) {\n      const newNode = doc.createElement('span')\n      newNode.appendChild(doc.createTextNode(text))\n      node.parentNode?.replaceChild(newNode, node)\n    } else {\n      node.parentNode?.removeChild(node)\n    }\n  }\n\n  const unwantedNodes = doc.evaluate(\n    unwantedWordDocumentPaths.join('|'),\n    doc,\n    null,\n    _XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE,\n    null,\n  )\n  for (let i = unwantedNodes.snapshotLength - 1; i >= 0; i--) {\n    const unwanted = unwantedNodes.snapshotItem(i)\n    if (!unwanted) {\n      continue\n    }\n    unwanted.parentNode?.removeChild(unwanted)\n  }\n  return doc\n}\n", "import {_XPathResult} from './xpathResult'\n\nexport default (html: string, doc: Document): Document => {\n  const NOTION_REGEX = /<!-- notionvc:.*?-->/g\n\n  if (html.match(NOTION_REGEX)) {\n    // Tag every child with attribute 'is-notion' so that the Notion rule-set can\n    // work exclusivly on these children\n    const childNodes = doc.evaluate(\n      '//*',\n      doc,\n      null,\n      _XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE,\n      null,\n    )\n\n    for (let i = childNodes.snapshotLength - 1; i >= 0; i--) {\n      const elm = childNodes.snapshotItem(i) as HTMLElement\n      elm?.setAttribute('data-is-notion', 'true')\n    }\n\n    return doc\n  }\n  return doc\n}\n", "import {PRESERVE_WHITESPACE_TAGS} from '../../constants'\nimport {_XPathResult} from './xpathResult'\n\nexport default (_: string, doc: Document): Document => {\n  // Recursively process all nodes.\n  function processNode(node: Node) {\n    // If this is a text node and not inside a tag where whitespace should be preserved, process it.\n    if (\n      node.nodeType === _XPathResult.BOOLEAN_TYPE &&\n      !PRESERVE_WHITESPACE_TAGS.includes(\n        node.parentElement?.tagName.toLowerCase() || '',\n      )\n    ) {\n      node.textContent =\n        node.textContent\n          ?.replace(/\\s\\s+/g, ' ') // Remove multiple whitespace\n          .replace(/[\\r\\n]+/g, ' ') || '' // Replace newlines with spaces\n    }\n    // Otherwise, if this node has children, process them.\n    else {\n      for (let i = 0; i < node.childNodes.length; i++) {\n        processNode(node.childNodes[i])\n      }\n    }\n  }\n\n  // Process all nodes starting from the root.\n  processNode(doc.body)\n\n  return doc\n}\n", "import {_XPathResult} from './xpathResult'\n\nconst WORD_HTML_REGEX =\n  /(class=\"?Mso|style=(?:\"|')[^\"]*?\\bmso-|w:WordDocument|<o:\\w+>|<\\/font>)/\n\n// xPaths for elements that will be removed from the document\nconst unwantedPaths = [\n  '//o:p',\n  \"//span[@style='mso-list:Ignore']\",\n  \"//span[@style='mso-list: Ignore']\",\n]\n\n// xPaths for elements that needs to be remapped into other tags\nconst mappedPaths = [\n  \"//p[@class='MsoTocHeading']\",\n  \"//p[@class='MsoTitle']\",\n  \"//p[@class='MsoToaHeading']\",\n  \"//p[@class='MsoSubtitle']\",\n  \"//span[@class='MsoSubtleEmphasis']\",\n  \"//span[@class='MsoIntenseEmphasis']\",\n]\n\n// Which HTML element(s) to map the elements matching mappedPaths into\nconst elementMap: Record<string, string[] | undefined> = {\n  MsoTocHeading: ['h3'],\n  MsoTitle: ['h1'],\n  MsoToaHeading: ['h2'],\n  MsoSubtitle: ['h5'],\n  MsoSubtleEmphasis: ['span', 'em'],\n  MsoIntenseEmphasis: ['span', 'em', 'strong'],\n  // Remove cruft\n}\n\nfunction isWordHtml(html: string) {\n  return WORD_HTML_REGEX.test(html)\n}\n\nexport default (html: string, doc: Document): Document => {\n  if (!isWordHtml(html)) {\n    return doc\n  }\n\n  const unwantedNodes = doc.evaluate(\n    unwantedPaths.join('|'),\n    doc,\n    (prefix) => {\n      if (prefix === 'o') {\n        return 'urn:schemas-microsoft-com:office:office'\n      }\n      return null\n    },\n    _XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE,\n    null,\n  )\n\n  for (let i = unwantedNodes.snapshotLength - 1; i >= 0; i--) {\n    const unwanted = unwantedNodes.snapshotItem(i)\n    if (unwanted?.parentNode) {\n      unwanted.parentNode.removeChild(unwanted)\n    }\n  }\n\n  // Transform mapped elements into what they should be mapped to\n  const mappedElements = doc.evaluate(\n    mappedPaths.join('|'),\n    doc,\n    null,\n    _XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE,\n    null,\n  )\n  for (let i = mappedElements.snapshotLength - 1; i >= 0; i--) {\n    const mappedElm = mappedElements.snapshotItem(i) as HTMLElement\n    const tags = elementMap[mappedElm.className]\n    const text = doc.createTextNode(mappedElm.textContent || '')\n    if (!tags) {\n      continue\n    }\n\n    const parentElement = doc.createElement(tags[0])\n    let parent = parentElement\n    let child = parentElement\n    tags.slice(1).forEach((tag) => {\n      child = doc.createElement(tag)\n      parent.appendChild(child)\n      parent = child\n    })\n    child.appendChild(text)\n    mappedElm?.parentNode?.replaceChild(parentElement, mappedElm)\n  }\n\n  return doc\n}\n", "import preprocessGDocs from './gdocs'\nimport preprocessHTML from './html'\nimport preprocessNotion from './notion'\nimport preprocessWhitespace from './whitespace'\nimport preprocessWord from './word'\n\nexport default [\n  preprocessWhitespace,\n  preprocessNotion,\n  preprocessWord,\n  preprocessGDocs,\n  preprocessHTML,\n]\n", "import type {Schema} from '@portabletext/schema'\nimport {vercelStegaClean} from '@vercel/stega'\nimport {isEqual} from 'lodash'\nimport {DEFAULT_BLOCK} from '../constants'\nimport type {\n  HtmlParser,\n  HtmlPreprocessorOptions,\n  MinimalBlock,\n  MinimalSpan,\n  PlaceholderAnnotation,\n  PlaceholderDecorator,\n  TypedObject,\n} from '../types'\nimport {\n  isTextBlock,\n  type PortableTextObject,\n  type PortableTextTextBlock,\n} from '../types.portable-text'\nimport {resolveJsType} from '../util/resolveJsType'\nimport preprocessors from './preprocessors'\n\n/**\n * Utility function that always return a lowerCase version of the element.tagName\n *\n * @param el - Element to get tag name for\n * @returns Lowercase tagName for that element, or undefined if not an element\n */\nexport function tagName(el: HTMLElement | Node | null): string | undefined {\n  if (el && 'tagName' in el) {\n    return el.tagName.toLowerCase()\n  }\n\n  return undefined\n}\n\n// TODO: make this plugin-style\nexport function preprocess(\n  html: string,\n  parseHtml: HtmlParser,\n  options: HtmlPreprocessorOptions,\n): Document {\n  const cleanHTML = vercelStegaClean(html)\n  const doc = parseHtml(normalizeHtmlBeforePreprocess(cleanHTML))\n  preprocessors.forEach((processor) => {\n    processor(cleanHTML, doc, options)\n  })\n  return doc\n}\n\nfunction normalizeHtmlBeforePreprocess(html: string): string {\n  return html.trim()\n}\n\n/**\n * A default `parseHtml` function that returns the html using `DOMParser`.\n *\n * @returns HTML Parser based on `DOMParser`\n */\nexport function defaultParseHtml(): HtmlParser {\n  if (resolveJsType(DOMParser) === 'undefined') {\n    throw new Error(\n      'The native `DOMParser` global which the `Html` deserializer uses by ' +\n        'default is not present in this environment. ' +\n        'You must supply the `options.parseHtml` function instead.',\n    )\n  }\n  return (html) => {\n    return new DOMParser().parseFromString(html, 'text/html')\n  }\n}\n\nexport function flattenNestedBlocks(\n  schema: Schema,\n  blocks: TypedObject[],\n): TypedObject[] {\n  let depth = 0\n  const flattened: TypedObject[] = []\n  const traverse = (nodes: TypedObject[]) => {\n    const toRemove: TypedObject[] = []\n    nodes.forEach((node) => {\n      if (depth === 0) {\n        flattened.push(node)\n      }\n      if (isTextBlock(schema, node)) {\n        if (depth > 0) {\n          toRemove.push(node)\n          flattened.push(node)\n        }\n        depth++\n        traverse(node.children)\n      }\n      if (node._type === '__block') {\n        toRemove.push(node)\n        flattened.push((node as any).block)\n      }\n    })\n    toRemove.forEach((node) => {\n      nodes.splice(nodes.indexOf(node), 1)\n    })\n    depth--\n  }\n  traverse(blocks)\n  return flattened\n}\n\nfunction nextSpan(block: PortableTextTextBlock, index: number) {\n  const next = block.children[index + 1]\n  return next && next._type === 'span' ? next : null\n}\n\nfunction prevSpan(block: PortableTextTextBlock, index: number) {\n  const prev = block.children[index - 1]\n  return prev && prev._type === 'span' ? prev : null\n}\n\nfunction isWhiteSpaceChar(text: string) {\n  return ['\\xa0', ' '].includes(text)\n}\n\n/**\n * NOTE: _mutates_ passed blocks!\n *\n * @param blocks - Array of blocks to trim whitespace for\n * @returns\n */\nexport function trimWhitespace(\n  schema: Schema,\n  blocks: TypedObject[],\n): TypedObject[] {\n  blocks.forEach((block) => {\n    if (!isTextBlock(schema, block)) {\n      return\n    }\n\n    // eslint-disable-next-line complexity\n    block.children.forEach((child, index) => {\n      if (!isMinimalSpan(child)) {\n        return\n      }\n      const nextChild = nextSpan(block, index)\n      const prevChild = prevSpan(block, index)\n      if (index === 0) {\n        child.text = child.text.replace(/^[^\\S\\n]+/g, '')\n      }\n      if (index === block.children.length - 1) {\n        child.text = child.text.replace(/[^\\S\\n]+$/g, '')\n      }\n      if (\n        /\\s/.test(child.text.slice(Math.max(0, child.text.length - 1))) &&\n        nextChild &&\n        isMinimalSpan(nextChild) &&\n        /\\s/.test(nextChild.text.slice(0, 1))\n      ) {\n        child.text = child.text.replace(/[^\\S\\n]+$/g, '')\n      }\n      if (\n        /\\s/.test(child.text.slice(0, 1)) &&\n        prevChild &&\n        isMinimalSpan(prevChild) &&\n        /\\s/.test(prevChild.text.slice(Math.max(0, prevChild.text.length - 1)))\n      ) {\n        child.text = child.text.replace(/^[^\\S\\n]+/g, '')\n      }\n      if (!child.text) {\n        block.children.splice(index, 1)\n      }\n      if (\n        prevChild &&\n        isEqual(prevChild.marks, child.marks) &&\n        isWhiteSpaceChar(child.text)\n      ) {\n        prevChild.text += ' '\n        block.children.splice(index, 1)\n      } else if (\n        nextChild &&\n        isEqual(nextChild.marks, child.marks) &&\n        isWhiteSpaceChar(child.text)\n      ) {\n        nextChild.text = ` ${nextChild.text}`\n        block.children.splice(index, 1)\n      }\n    })\n  })\n\n  return blocks\n}\n\nexport function ensureRootIsBlocks(\n  schema: Schema,\n  blocks: TypedObject[],\n): TypedObject[] {\n  return blocks.reduce((memo, node, i, original) => {\n    if (node._type === 'block') {\n      memo.push(node)\n      return memo\n    }\n\n    if (node._type === '__block') {\n      memo.push((node as any).block)\n      return memo\n    }\n\n    const lastBlock = memo[memo.length - 1]\n    if (\n      i > 0 &&\n      !isTextBlock(schema, original[i - 1]) &&\n      isTextBlock(schema, lastBlock)\n    ) {\n      lastBlock.children.push(node as PortableTextObject)\n      return memo\n    }\n\n    const block = {\n      ...DEFAULT_BLOCK,\n      children: [node],\n    }\n\n    memo.push(block)\n    return memo\n  }, [] as TypedObject[])\n}\n\nexport function isNodeList(node: unknown): node is NodeList {\n  return Object.prototype.toString.call(node) === '[object NodeList]'\n}\n\nexport function isMinimalSpan(node: TypedObject): node is MinimalSpan {\n  return node._type === 'span'\n}\n\nexport function isMinimalBlock(node: TypedObject): node is MinimalBlock {\n  return node._type === 'block'\n}\n\nexport function isPlaceholderDecorator(\n  node: TypedObject,\n): node is PlaceholderDecorator {\n  return node._type === '__decorator'\n}\n\nexport function isPlaceholderAnnotation(\n  node: TypedObject,\n): node is PlaceholderAnnotation {\n  return node._type === '__annotation'\n}\n\nexport function isElement(node: Node): node is Element {\n  return node.nodeType === 1\n}\n\n/**\n * Helper to normalize whitespace to only 1 empty block between content nodes\n * @param node - Root node to process\n */\nexport function normalizeWhitespace(rootNode: Node) {\n  let emptyBlockCount = 0\n  let lastParent = null\n  const nodesToRemove: Node[] = []\n\n  for (let child = rootNode.firstChild; child; child = child.nextSibling) {\n    if (!isElement(child)) {\n      normalizeWhitespace(child)\n      emptyBlockCount = 0\n      continue\n    }\n\n    const elm = child as HTMLElement\n\n    if (isWhitespaceBlock(elm)) {\n      if (lastParent && elm.parentElement === lastParent) {\n        emptyBlockCount++\n        if (emptyBlockCount > 1) {\n          nodesToRemove.push(elm)\n        }\n      } else {\n        // Different parent, reset counter\n        emptyBlockCount = 1\n      }\n\n      lastParent = elm.parentElement\n    } else {\n      // Recurse into child nodes\n      normalizeWhitespace(child)\n      // Reset counter for siblings\n      emptyBlockCount = 0\n    }\n  }\n\n  // Remove marked nodes\n  nodesToRemove.forEach((node) => node.parentElement?.removeChild(node))\n}\n\n/**\n * Helper to remove all whitespace nodes\n * @param node - Root node to process\n */\nexport function removeAllWhitespace(rootNode: Node) {\n  const nodesToRemove: Node[] = []\n\n  function collectNodesToRemove(currentNode: Node) {\n    if (isElement(currentNode)) {\n      const elm = currentNode as HTMLElement\n\n      // Handle <br> tags that is between <p> tags\n      if (\n        tagName(elm) === 'br' &&\n        (tagName(elm.nextElementSibling) === 'p' ||\n          tagName(elm.previousElementSibling) === 'p')\n      ) {\n        nodesToRemove.push(elm)\n\n        return\n      }\n\n      // Handle empty blocks\n      if (\n        (tagName(elm) === 'p' || tagName(elm) === 'br') &&\n        elm?.firstChild?.textContent?.trim() === ''\n      ) {\n        nodesToRemove.push(elm)\n\n        return\n      }\n\n      // Recursively process child nodes\n      for (let child = elm.firstChild; child; child = child.nextSibling) {\n        collectNodesToRemove(child)\n      }\n    }\n  }\n\n  collectNodesToRemove(rootNode)\n\n  // Remove the collected nodes\n  nodesToRemove.forEach((node) => node.parentElement?.removeChild(node))\n}\n\nfunction isWhitespaceBlock(elm: HTMLElement): boolean {\n  return ['p', 'br'].includes(tagName(elm) || '') && !elm.textContent?.trim()\n}\n", "import type {Schema} from '@portabletext/schema'\nimport {\n  BLOCK_DEFAULT_STYLE,\n  DEFAULT_BLOCK,\n  DEFAULT_SPAN,\n  HTML_BLOCK_TAGS,\n  HTML_HEADER_TAGS,\n  HTML_LIST_CONTAINER_TAGS,\n} from '../../constants'\nimport type {DeserializerRule} from '../../types'\nimport {isElement, tagName} from '../helpers'\n\nconst LIST_CONTAINER_TAGS = Object.keys(HTML_LIST_CONTAINER_TAGS)\n\n// font-style:italic seems like the most important rule for italic / emphasis in their html\nfunction isEmphasis(el: Node): boolean {\n  const style = isElement(el) && el.getAttribute('style')\n  return /font-style\\s*:\\s*italic/.test(style || '')\n}\n\n// font-weight:700 seems like the most important rule for bold in their html\nfunction isStrong(el: Node): boolean {\n  const style = isElement(el) && el.getAttribute('style')\n  return /font-weight\\s*:\\s*700/.test(style || '')\n}\n\n// text-decoration seems like the most important rule for underline in their html\nfunction isUnderline(el: Node): boolean {\n  if (!isElement(el) || tagName(el.parentNode) === 'a') {\n    return false\n  }\n\n  const style = isElement(el) && el.getAttribute('style')\n\n  return /text-decoration\\s*:\\s*underline/.test(style || '')\n}\n\n// text-decoration seems like the most important rule for strike-through in their html\n// allows for line-through regex to be more lineient to allow for other text-decoration before or after\nfunction isStrikethrough(el: Node): boolean {\n  const style = isElement(el) && el.getAttribute('style')\n  return /text-decoration\\s*:\\s*(?:.*line-through.*;)/.test(style || '')\n}\n\n// Check for attribute given by the gdocs preprocessor\nfunction isGoogleDocs(el: Node): boolean {\n  return isElement(el) && Boolean(el.getAttribute('data-is-google-docs'))\n}\n\nfunction isRootNode(el: Node): boolean {\n  return isElement(el) && Boolean(el.getAttribute('data-is-root-node'))\n}\n\nfunction getListItemStyle(el: Node): 'bullet' | 'number' | undefined {\n  const parentTag = tagName(el.parentNode)\n  if (parentTag && !LIST_CONTAINER_TAGS.includes(parentTag)) {\n    return undefined\n  }\n  return tagName(el.parentNode) === 'ul' ? 'bullet' : 'number'\n}\n\nfunction getListItemLevel(el: Node): number {\n  let level = 0\n  if (tagName(el) === 'li') {\n    let parentNode = el.parentNode\n    while (parentNode) {\n      const parentTag = tagName(parentNode)\n      if (parentTag && LIST_CONTAINER_TAGS.includes(parentTag)) {\n        level++\n      }\n      parentNode = parentNode.parentNode\n    }\n  } else {\n    level = 1\n  }\n  return level\n}\n\nconst blocks: Record<string, {style: string} | undefined> = {\n  ...HTML_BLOCK_TAGS,\n  ...HTML_HEADER_TAGS,\n}\n\nfunction getBlockStyle(schema: Schema, el: Node): string {\n  const childTag = tagName(el.firstChild)\n  const block = childTag && blocks[childTag]\n  if (!block) {\n    return BLOCK_DEFAULT_STYLE\n  }\n  if (!schema.styles.some((style) => style.name === block.style)) {\n    return BLOCK_DEFAULT_STYLE\n  }\n  return block.style\n}\n\nexport default function createGDocsRules(schema: Schema): DeserializerRule[] {\n  return [\n    {\n      deserialize(el) {\n        if (isElement(el) && tagName(el) === 'span' && isGoogleDocs(el)) {\n          const span = {\n            ...DEFAULT_SPAN,\n            marks: [] as string[],\n            text: el.textContent,\n          }\n          if (isStrong(el)) {\n            span.marks.push('strong')\n          }\n          if (isUnderline(el)) {\n            span.marks.push('underline')\n          }\n          if (isStrikethrough(el)) {\n            span.marks.push('strike-through')\n          }\n          if (isEmphasis(el)) {\n            span.marks.push('em')\n          }\n          return span\n        }\n        return undefined\n      },\n    },\n    {\n      deserialize(el, next) {\n        if (tagName(el) === 'li' && isGoogleDocs(el)) {\n          return {\n            ...DEFAULT_BLOCK,\n            listItem: getListItemStyle(el),\n            level: getListItemLevel(el),\n            style: getBlockStyle(schema, el),\n            children: next(el.firstChild?.childNodes || []),\n          }\n        }\n        return undefined\n      },\n    },\n    {\n      deserialize(el) {\n        if (\n          tagName(el) === 'br' &&\n          isGoogleDocs(el) &&\n          isElement(el) &&\n          el.classList.contains('apple-interchange-newline')\n        ) {\n          return {\n            ...DEFAULT_SPAN,\n            text: '',\n          }\n        }\n\n        // BRs inside empty paragraphs\n        if (\n          tagName(el) === 'br' &&\n          isGoogleDocs(el) &&\n          isElement(el) &&\n          el?.parentNode?.textContent === ''\n        ) {\n          return {\n            ...DEFAULT_SPAN,\n            text: '',\n          }\n        }\n\n        // BRs on the root\n        if (\n          tagName(el) === 'br' &&\n          isGoogleDocs(el) &&\n          isElement(el) &&\n          isRootNode(el)\n        ) {\n          return {\n            ...DEFAULT_SPAN,\n            text: '',\n          }\n        }\n        return undefined\n      },\n    },\n  ]\n}\n", "import getRandomValues from 'get-random-values-esm'\n\nexport function keyGenerator() {\n  return randomKey(12)\n}\n\n// WHATWG crypto RNG - https://w3c.github.io/webcrypto/Overview.html\nfunction whatwgRNG(length = 16) {\n  const rnds8 = new Uint8Array(length)\n  getRandomValues(rnds8)\n  return rnds8\n}\n\nconst byteToHex: string[] = []\nfor (let i = 0; i < 256; ++i) {\n  byteToHex[i] = (i + 0x100).toString(16).slice(1)\n}\n\n/**\n * Generate a random key of the given length\n *\n * @param length - Length of string to generate\n * @returns A string of the given length\n * @public\n */\nexport function randomKey(length: number): string {\n  return whatwgRNG(length)\n    .reduce((str, n) => str + byteToHex[n], '')\n    .slice(0, length)\n}\n", "import {DEFAULT_SPAN} from '../../constants'\nimport type {DeserializerRule} from '../../types'\nimport {tagName} from '../helpers'\n\nexport const whitespaceTextNodeRule: DeserializerRule = {\n  deserialize(node) {\n    return node.nodeName === '#text' && isWhitespaceTextNode(node)\n      ? {\n          ...DEFAULT_SPAN,\n          marks: [],\n          text: (node.textContent ?? '').replace(/\\s\\s+/g, ' '),\n        }\n      : undefined\n  },\n}\n\nfunction isWhitespaceTextNode(node: Node) {\n  const isValidWhiteSpace =\n    node.nodeType === 3 &&\n    (node.textContent || '').replace(/[\\r\\n]/g, ' ').replace(/\\s\\s+/g, ' ') ===\n      ' ' &&\n    node.nextSibling &&\n    node.nextSibling.nodeType !== 3 &&\n    node.previousSibling &&\n    node.previousSibling.nodeType !== 3\n\n  return (\n    (isValidWhiteSpace || node.textContent !== ' ') &&\n    tagName(node.parentNode) !== 'body'\n  )\n}\n", "import type {Schema} from '@portabletext/schema'\nimport {\n  DEFAULT_BLOCK,\n  DEFAULT_SPAN,\n  HTML_BLOCK_TAGS,\n  HTML_DECORATOR_TAGS,\n  HTML_HEADER_TAGS,\n  HTML_LIST_CONTAINER_TAGS,\n  HTML_LIST_ITEM_TAGS,\n  HTML_SPAN_TAGS,\n  type PartialBlock,\n} from '../../constants'\nimport type {DeserializerRule} from '../../types'\nimport {keyGenerator} from '../../util/randomKey'\nimport {isElement, tagName} from '../helpers'\nimport {whitespaceTextNodeRule} from './whitespace-text-node'\n\nexport function resolveListItem(\n  schema: Schema,\n  listNodeTagName: string,\n): string | undefined {\n  if (\n    listNodeTagName === 'ul' &&\n    schema.lists.some((list) => list.name === 'bullet')\n  ) {\n    return 'bullet'\n  }\n  if (\n    listNodeTagName === 'ol' &&\n    schema.lists.some((list) => list.name === 'number')\n  ) {\n    return 'number'\n  }\n  return undefined\n}\n\nexport default function createHTMLRules(\n  schema: Schema,\n  options: {keyGenerator?: () => string},\n): DeserializerRule[] {\n  return [\n    whitespaceTextNodeRule,\n    {\n      // Pre element\n      deserialize(el) {\n        if (tagName(el) !== 'pre') {\n          return undefined\n        }\n\n        const isCodeEnabled = schema.styles.some(\n          (style) => style.name === 'code',\n        )\n\n        return {\n          _type: 'block',\n          style: 'normal',\n          markDefs: [],\n          children: [\n            {\n              ...DEFAULT_SPAN,\n              marks: isCodeEnabled ? ['code'] : [],\n              text: el.textContent || '',\n            },\n          ],\n        }\n      },\n    }, // Blockquote element\n    {\n      deserialize(el, next) {\n        if (tagName(el) !== 'blockquote') {\n          return undefined\n        }\n        const blocks: Record<string, PartialBlock | undefined> = {\n          ...HTML_BLOCK_TAGS,\n          ...HTML_HEADER_TAGS,\n        }\n        delete blocks.blockquote\n        const nonBlockquoteBlocks = Object.keys(blocks)\n\n        const children: HTMLElement[] = []\n\n        el.childNodes.forEach((node, index) => {\n          if (!el.ownerDocument) {\n            return\n          }\n\n          if (\n            node.nodeType === 1 &&\n            nonBlockquoteBlocks.includes(\n              (node as Element).localName.toLowerCase(),\n            )\n          ) {\n            const span = el.ownerDocument.createElement('span')\n\n            const previousChild = children[children.length - 1]\n\n            if (\n              previousChild &&\n              previousChild.nodeType === 3 &&\n              previousChild.textContent?.trim()\n            ) {\n              // Only prepend line break if the previous node is a non-empty\n              // text node.\n              span.appendChild(el.ownerDocument.createTextNode('\\r'))\n            }\n\n            node.childNodes.forEach((cn) => {\n              span.appendChild(cn.cloneNode(true))\n            })\n\n            if (index !== el.childNodes.length) {\n              // Only append line break if this is not the last child\n              span.appendChild(el.ownerDocument.createTextNode('\\r'))\n            }\n\n            children.push(span)\n          } else {\n            children.push(node as HTMLElement)\n          }\n        })\n\n        return {\n          _type: 'block',\n          style: 'blockquote',\n          markDefs: [],\n          children: next(children),\n        }\n      },\n    }, // Block elements\n    {\n      deserialize(el, next) {\n        const blocks: Record<string, PartialBlock | undefined> = {\n          ...HTML_BLOCK_TAGS,\n          ...HTML_HEADER_TAGS,\n        }\n        const tag = tagName(el)\n        let block = tag ? blocks[tag] : undefined\n        if (!block) {\n          return undefined\n        }\n        // Don't add blocks into list items\n        if (el.parentNode && tagName(el.parentNode) === 'li') {\n          return next(el.childNodes)\n        }\n        const blockStyle = block.style\n        // If style is not supported, return a defaultBlockType\n        if (!schema.styles.some((style) => style.name === blockStyle)) {\n          block = DEFAULT_BLOCK\n        }\n        return {\n          ...block,\n          children: next(el.childNodes),\n        }\n      },\n    }, // Ignore span tags\n    {\n      deserialize(el, next) {\n        const tag = tagName(el)\n        if (!tag || !(tag in HTML_SPAN_TAGS)) {\n          return undefined\n        }\n        return next(el.childNodes)\n      },\n    }, // Ignore div tags\n    {\n      deserialize(el, next) {\n        const div = tagName(el) === 'div'\n        if (!div) {\n          return undefined\n        }\n        return next(el.childNodes)\n      },\n    }, // Ignore list containers\n    {\n      deserialize(el, next) {\n        const tag = tagName(el)\n        if (!tag || !(tag in HTML_LIST_CONTAINER_TAGS)) {\n          return undefined\n        }\n        return next(el.childNodes)\n      },\n    }, // Deal with br's\n    {\n      deserialize(el) {\n        if (tagName(el) === 'br') {\n          return {\n            ...DEFAULT_SPAN,\n            text: '\\n',\n          }\n        }\n        return undefined\n      },\n    }, // Deal with list items\n    {\n      deserialize(el, next, block) {\n        const tag = tagName(el)\n        const listItem = tag ? HTML_LIST_ITEM_TAGS[tag] : undefined\n        const parentTag = tagName(el.parentNode) || ''\n        if (\n          !listItem ||\n          !el.parentNode ||\n          !HTML_LIST_CONTAINER_TAGS[parentTag]\n        ) {\n          return undefined\n        }\n        const enabledListItem = resolveListItem(schema, parentTag)\n        // If the list item style is not supported, return a new default block\n        if (!enabledListItem) {\n          return block({_type: 'block', children: next(el.childNodes)})\n        }\n        listItem.listItem = enabledListItem\n        return {\n          ...listItem,\n          children: next(el.childNodes),\n        }\n      },\n    }, // Deal with decorators - this is a limited set of known html elements that we know how to deserialize\n    {\n      deserialize(el, next) {\n        const decorator = HTML_DECORATOR_TAGS[tagName(el) || '']\n        if (\n          !decorator ||\n          !schema.decorators.some(\n            (decoratorType) => decoratorType.name === decorator,\n          )\n        ) {\n          return undefined\n        }\n        return {\n          _type: '__decorator',\n          name: decorator,\n          children: next(el.childNodes),\n        }\n      },\n    }, // Special case for hyperlinks, add annotation (if allowed by schema),\n    // If not supported just write out the link text and href in plain text.\n    {\n      deserialize(el, next) {\n        if (tagName(el) !== 'a') {\n          return undefined\n        }\n        const linkEnabled = schema.annotations.some(\n          (annotation) => annotation.name === 'link',\n        )\n        const href = isElement(el) && el.getAttribute('href')\n        if (!href) {\n          return next(el.childNodes)\n        }\n        if (linkEnabled) {\n          return {\n            _type: '__annotation',\n            markDef: {\n              _key: options.keyGenerator\n                ? options.keyGenerator()\n                : keyGenerator(),\n              _type: 'link',\n              href: href,\n            },\n            children: next(el.childNodes),\n          }\n        }\n        return (\n          el.appendChild(el.ownerDocument.createTextNode(` (${href})`)) &&\n          next(el.childNodes)\n        )\n      },\n    },\n  ]\n}\n", "import {DEFAULT_SPAN} from '../../constants'\nimport type {DeserializerRule} from '../../types'\nimport {isElement, tagName} from '../helpers'\n\n// font-style:italic seems like the most important rule for italic / emphasis in their html\nfunction isEmphasis(el: Node): boolean {\n  const style = isElement(el) && el.getAttribute('style')\n  return /font-style:italic/.test(style || '')\n}\n\n// font-weight:700 or 600 seems like the most important rule for bold in their html\nfunction isStrong(el: Node): boolean {\n  const style = isElement(el) && el.getAttribute('style')\n  return (\n    /font-weight:700/.test(style || '') || /font-weight:600/.test(style || '')\n  )\n}\n\n// text-decoration seems like the most important rule for underline in their html\nfunction isUnderline(el: Node): boolean {\n  const style = isElement(el) && el.getAttribute('style')\n  return /text-decoration:underline/.test(style || '')\n}\n\n// Check for attribute given by the Notion preprocessor\nfunction isNotion(el: Node): boolean {\n  return isElement(el) && Boolean(el.getAttribute('data-is-notion'))\n}\n\nexport default function createNotionRules(): DeserializerRule[] {\n  return [\n    {\n      deserialize(el) {\n        // Notion normally exports semantic HTML. However, if you copy a single block, the formatting will be inline styles\n        // This handles a limited set of styles\n        if (isElement(el) && tagName(el) === 'span' && isNotion(el)) {\n          const span = {\n            ...DEFAULT_SPAN,\n            marks: [] as string[],\n            text: el.textContent,\n          }\n          if (isStrong(el)) {\n            span.marks.push('strong')\n          }\n          if (isUnderline(el)) {\n            span.marks.push('underline')\n          }\n          if (isEmphasis(el)) {\n            span.marks.push('em')\n          }\n          return span\n        }\n        return undefined\n      },\n    },\n  ]\n}\n", "import {BLOCK_DEFAULT_STYLE, DEFAULT_BLOCK} from '../../constants'\nimport type {DeserializerRule} from '../../types'\nimport {isElement, tagName} from '../helpers'\n\nfunction getListItemStyle(el: Node): string | undefined {\n  const style = isElement(el) && el.getAttribute('style')\n  if (!style) {\n    return undefined\n  }\n\n  if (!style.match(/lfo\\d+/)) {\n    return undefined\n  }\n\n  return style.match('lfo1') ? 'bullet' : 'number'\n}\n\nfunction getListItemLevel(el: Node): number | undefined {\n  const style = isElement(el) && el.getAttribute('style')\n  if (!style) {\n    return undefined\n  }\n\n  const levelMatch = style.match(/level\\d+/)\n  if (!levelMatch) {\n    return undefined\n  }\n\n  const [level] = levelMatch[0].match(/\\d/) || []\n  const levelNum = level ? Number.parseInt(level, 10) : 1\n  return levelNum || 1\n}\n\nfunction isWordListElement(el: Node): boolean {\n  return isElement(el) && el.className\n    ? el.className === 'MsoListParagraphCxSpFirst' ||\n        el.className === 'MsoListParagraphCxSpMiddle' ||\n        el.className === 'MsoListParagraphCxSpLast'\n    : false\n}\n\nexport default function createWordRules(): DeserializerRule[] {\n  return [\n    {\n      deserialize(el, next) {\n        if (tagName(el) === 'p' && isWordListElement(el)) {\n          return {\n            ...DEFAULT_BLOCK,\n            listItem: getListItemStyle(el),\n            level: getListItemLevel(el),\n            style: BLOCK_DEFAULT_STYLE,\n            children: next(el.childNodes),\n          }\n        }\n        return undefined\n      },\n    },\n  ]\n}\n", "import type {Schema} from '@portabletext/schema'\nimport type {DeserializerRule} from '../../types'\nimport createGDocsRules from './gdocs'\nimport createHTMLRules from './html'\nimport createNotionRules from './notion'\nimport createWordRules from './word'\n\nexport function createRules(\n  schema: Schema,\n  options: {keyGenerator?: () => string},\n): DeserializerRule[] {\n  return [\n    ...createWordRules(),\n    ...createNotionRules(),\n    ...createGDocsRules(schema),\n    ...createHTMLRules(schema, options),\n  ]\n}\n", "import type {Schema} from '@portabletext/schema'\nimport {flatten} from 'lodash'\nimport type {\n  ArbitraryTypedObject,\n  DeserializerRule,\n  HtmlDeserializerOptions,\n  PlaceholderAnnotation,\n  PlaceholderDecorator,\n  TypedObject,\n} from '../types'\nimport {\n  isTextBlock,\n  type PortableTextBlock,\n  type PortableTextObject,\n} from '../types.portable-text'\nimport {resolveJsType} from '../util/resolveJsType'\nimport {\n  defaultParseHtml,\n  ensureRootIsBlocks,\n  flattenNestedBlocks,\n  isMinimalBlock,\n  isMinimalSpan,\n  isNodeList,\n  isPlaceholderAnnotation,\n  isPlaceholderDecorator,\n  preprocess,\n  tagName,\n  trimWhitespace,\n} from './helpers'\nimport {createRules} from './rules'\n\n/**\n * HTML Deserializer\n *\n */\nexport default class HtmlDeserializer {\n  schema: Schema\n  rules: DeserializerRule[]\n  parseHtml: (html: string) => HTMLElement\n  _markDefs: PortableTextObject[] = []\n\n  /**\n   * Create a new serializer respecting a Sanity block content type's schema\n   *\n   * @param blockContentType - Schema type for array containing _at least_ a block child type\n   * @param options - Options for the deserialization process\n   */\n  constructor(schema: Schema, options: HtmlDeserializerOptions = {}) {\n    const {rules = [], unstable_whitespaceOnPasteMode = 'preserve'} = options\n    const standardRules = createRules(schema, {\n      keyGenerator: options.keyGenerator,\n    })\n    this.schema = schema\n    this.rules = [...rules, ...standardRules]\n    const parseHtml = options.parseHtml || defaultParseHtml()\n    this.parseHtml = (html) => {\n      const doc = preprocess(html, parseHtml, {unstable_whitespaceOnPasteMode})\n      return doc.body\n    }\n  }\n\n  /**\n   * Deserialize HTML.\n   *\n   * @param html - The HTML to deserialize, as a string\n   * @returns Array of blocks - either portable text blocks or other allowed blocks\n   */\n  deserialize = (html: string): TypedObject[] => {\n    this._markDefs = []\n    const {parseHtml} = this\n    const fragment = parseHtml(html)\n    const children = Array.from(fragment.childNodes) as HTMLElement[]\n    // Ensure that there are no blocks within blocks, and trim whitespace\n    const blocks = trimWhitespace(\n      this.schema,\n      flattenNestedBlocks(\n        this.schema,\n        ensureRootIsBlocks(this.schema, this.deserializeElements(children)),\n      ),\n    )\n\n    if (this._markDefs.length > 0) {\n      blocks\n        .filter((block) => isTextBlock(this.schema, block))\n        .forEach((block) => {\n          block.markDefs = block.markDefs || []\n          block.markDefs = block.markDefs.concat(\n            this._markDefs.filter((def) => {\n              return flatten(\n                block.children.map((child) => child.marks || []),\n              ).includes(def._key)\n            }),\n          )\n        })\n    }\n\n    return blocks.map((block) => {\n      if (block._type === 'block') {\n        block._type = this.schema.block.name\n      }\n      return block\n    })\n  }\n\n  /**\n   * Deserialize an array of DOM elements.\n   *\n   * @param elements - Array of DOM elements to deserialize\n   * @returns\n   */\n  deserializeElements = (elements: Node[] = []): TypedObject[] => {\n    let nodes: TypedObject[] = []\n    elements.forEach((element) => {\n      nodes = nodes.concat(this.deserializeElement(element))\n    })\n    return nodes\n  }\n\n  /**\n   * Deserialize a DOM element\n   *\n   * @param element - Deserialize a DOM element\n   * @returns\n   */\n  deserializeElement = (element: Node): TypedObject | TypedObject[] => {\n    const next = (\n      elements: Node | Node[] | NodeList,\n    ): TypedObject | TypedObject[] | undefined => {\n      if (isNodeList(elements)) {\n        return this.deserializeElements(Array.from(elements))\n      }\n\n      if (Array.isArray(elements)) {\n        return this.deserializeElements(elements)\n      }\n\n      if (!elements) {\n        return undefined\n      }\n\n      return this.deserializeElement(elements)\n    }\n\n    const block = (props: ArbitraryTypedObject) => {\n      return {\n        _type: '__block',\n        block: props,\n      }\n    }\n\n    let node: TypedObject | Array<TypedObject> | undefined\n    for (let i = 0; i < this.rules.length; i++) {\n      const rule = this.rules[i]\n      if (!rule.deserialize) {\n        continue\n      }\n\n      const ret = rule.deserialize(element, next, block)\n      const type = resolveJsType(ret)\n\n      if (\n        type !== 'array' &&\n        type !== 'object' &&\n        type !== 'null' &&\n        type !== 'undefined'\n      ) {\n        throw new Error(\n          `A rule returned an invalid deserialized representation: \"${node}\".`,\n        )\n      }\n\n      if (ret === undefined) {\n        continue\n      } else if (ret === null) {\n        throw new Error('Deserializer rule returned `null`')\n      } else if (Array.isArray(ret)) {\n        node = ret\n      } else if (isPlaceholderDecorator(ret)) {\n        node = this.deserializeDecorator(ret)\n      } else if (isPlaceholderAnnotation(ret)) {\n        node = this.deserializeAnnotation(ret)\n      } else {\n        node = ret\n      }\n\n      // Set list level on list item\n      if (\n        ret &&\n        !Array.isArray(ret) &&\n        isMinimalBlock(ret) &&\n        'listItem' in ret\n      ) {\n        let parent = element.parentNode?.parentNode\n        while (parent && tagName(parent) === 'li') {\n          parent = parent.parentNode?.parentNode\n          ret.level = ret.level ? ret.level + 1 : 1\n        }\n      }\n\n      // Set newlines on spans orginating from a block element within a blockquote\n      if (\n        ret &&\n        !Array.isArray(ret) &&\n        isMinimalBlock(ret) &&\n        ret.style === 'blockquote'\n      ) {\n        ret.children.forEach((child, index) => {\n          if (isMinimalSpan(child) && child.text === '\\r') {\n            child.text = '\\n'\n            if (index === 0 || index === ret.children.length - 1) {\n              ret.children.splice(index, 1)\n            }\n          }\n        })\n      }\n      break\n    }\n\n    return node || next(element.childNodes) || []\n  }\n\n  /**\n   * Deserialize a `__decorator` type\n   * (an internal made up type to process decorators exclusively)\n   *\n   * @param decorator -\n   * @returns array of ...\n   */\n  deserializeDecorator = (decorator: PlaceholderDecorator): TypedObject[] => {\n    const {name} = decorator\n    const applyDecorator = (node: TypedObject) => {\n      if (isPlaceholderDecorator(node)) {\n        return this.deserializeDecorator(node)\n      } else if (isMinimalSpan(node)) {\n        node.marks = node.marks || []\n        if (node.text.trim()) {\n          // Only apply marks if this is an actual text\n          node.marks.unshift(name)\n        }\n      } else if (\n        'children' in node &&\n        Array.isArray((node as PortableTextBlock).children)\n      ) {\n        const block = node as any\n        block.children = block.children.map(applyDecorator)\n      }\n      return node\n    }\n    return decorator.children.reduce((children, node) => {\n      const ret = applyDecorator(node)\n      if (Array.isArray(ret)) {\n        return children.concat(ret)\n      }\n      children.push(ret)\n      return children\n    }, [] as TypedObject[])\n  }\n\n  /**\n   * Deserialize a `__annotation` object.\n   * (an internal made up type to process annotations exclusively)\n   *\n   * @param annotation -\n   * @returns Array of...\n   */\n  deserializeAnnotation = (\n    annotation: PlaceholderAnnotation,\n  ): TypedObject[] => {\n    const {markDef} = annotation\n    this._markDefs.push(markDef)\n    const applyAnnotation = (node: TypedObject) => {\n      if (isPlaceholderAnnotation(node)) {\n        return this.deserializeAnnotation(node)\n      } else if (isMinimalSpan(node)) {\n        node.marks = node.marks || []\n        if (node.text.trim()) {\n          // Only apply marks if this is an actual text\n          node.marks.unshift(markDef._key)\n        }\n      } else if (\n        'children' in node &&\n        Array.isArray((node as PortableTextBlock).children)\n      ) {\n        const block = node as any\n        block.children = block.children.map(applyAnnotation)\n      }\n      return node\n    }\n    return annotation.children.reduce((children, node) => {\n      const ret = applyAnnotation(node)\n      if (Array.isArray(ret)) {\n        return children.concat(ret)\n      }\n      children.push(ret)\n      return children\n    }, [] as TypedObject[])\n  }\n}\n", "import type {Schema} from '@portabletext/schema'\nimport {isEqual} from 'lodash'\nimport type {TypedObject} from '../types'\nimport {\n  isSpan,\n  type PortableTextSpan,\n  type PortableTextTextBlock,\n} from '../types.portable-text'\nimport {keyGenerator} from './randomKey'\n\n/**\n * Block normalization options\n *\n * @public\n */\nexport interface BlockNormalizationOptions {\n  /**\n   * Decorator names that are allowed within portable text blocks, eg `em`, `strong`\n   */\n  allowedDecorators?: string[]\n\n  /**\n   * Name of the portable text block type, if not `block`\n   */\n  blockTypeName?: string\n\n  /**\n   * Custom key generator function\n   */\n  keyGenerator?: () => string\n}\n\n/**\n * Normalizes a block by ensuring it has a `_key` property. If the block is a\n * portable text block, additional normalization is applied:\n *\n * - Ensures it has `children` and `markDefs` properties\n * - Ensures it has at least one child (adds an empty span if empty)\n * - Joins sibling spans that has the same marks\n * - Removes decorators that are not allowed according to the schema\n * - Removes marks that have no annotation definition\n *\n * @param node - The block to normalize\n * @param options - Options for normalization process. See {@link BlockNormalizationOptions}\n * @returns Normalized block\n * @public\n */\nexport function normalizeBlock(\n  node: TypedObject,\n  options: BlockNormalizationOptions = {},\n): Omit<\n  TypedObject | PortableTextTextBlock<TypedObject | PortableTextSpan>,\n  '_key'\n> & {\n  _key: string\n} {\n  const schema: Schema = {\n    block: {\n      name: options.blockTypeName || 'block',\n    },\n    span: {\n      name: 'span',\n    },\n    styles: [],\n    lists: [],\n    decorators: [],\n    annotations: [],\n    blockObjects: [],\n    inlineObjects: [],\n  }\n\n  if (node._type !== (options.blockTypeName || 'block')) {\n    return '_key' in node\n      ? (node as TypedObject & {_key: string})\n      : {\n          ...node,\n          _key: options.keyGenerator ? options.keyGenerator() : keyGenerator(),\n        }\n  }\n\n  const block: Omit<\n    PortableTextTextBlock<TypedObject | PortableTextSpan>,\n    'style'\n  > = {\n    _key: options.keyGenerator ? options.keyGenerator() : keyGenerator(),\n    children: [],\n    markDefs: [],\n    ...node,\n  }\n\n  const lastChild = block.children[block.children.length - 1]\n\n  if (!lastChild) {\n    // A block must at least have an empty span type child\n    block.children = [\n      {\n        _type: 'span',\n        _key: options.keyGenerator ? options.keyGenerator() : keyGenerator(),\n        text: '',\n        marks: [],\n      },\n    ]\n    return block\n  }\n\n  const usedMarkDefs: string[] = []\n  const allowedDecorators =\n    options.allowedDecorators && Array.isArray(options.allowedDecorators)\n      ? options.allowedDecorators\n      : false\n\n  block.children = block.children\n    .reduce(\n      (acc, child) => {\n        const previousChild = acc[acc.length - 1]\n        if (\n          previousChild &&\n          isSpan(schema, child) &&\n          isSpan(schema, previousChild) &&\n          isEqual(previousChild.marks, child.marks)\n        ) {\n          if (\n            lastChild &&\n            lastChild === child &&\n            child.text === '' &&\n            block.children.length > 1\n          ) {\n            return acc\n          }\n\n          previousChild.text += child.text\n          return acc\n        }\n        acc.push(child)\n        return acc\n      },\n      [] as (TypedObject | PortableTextSpan)[],\n    )\n    .map((child) => {\n      if (!child) {\n        throw new Error('missing child')\n      }\n\n      child._key = options.keyGenerator\n        ? options.keyGenerator()\n        : keyGenerator()\n\n      if (isSpan(schema, child)) {\n        if (!child.marks) {\n          child.marks = []\n        } else if (allowedDecorators) {\n          child.marks = child.marks.filter((mark) => {\n            const isAllowed = allowedDecorators.includes(mark)\n            const isUsed = block.markDefs?.some((def) => def._key === mark)\n            return isAllowed || isUsed\n          })\n        }\n\n        usedMarkDefs.push(...child.marks)\n      }\n\n      return child\n    })\n\n  // Remove leftover (unused) markDefs\n  block.markDefs = (block.markDefs || []).filter((markDef) =>\n    usedMarkDefs.includes(markDef._key),\n  )\n\n  return block\n}\n", "import {sanitySchemaToPortableTextSchema} from '@portabletext/sanity-bridge'\nimport type {Schema} from '@portabletext/schema'\nimport type {ArraySchemaType} from '@sanity/types'\nimport HtmlDeserializer from './HtmlDeserializer'\nimport type {HtmlDeserializerOptions, TypedObject} from './types'\nimport type {PortableTextTextBlock} from './types.portable-text'\nimport {normalizeBlock} from './util/normalizeBlock'\n\n/**\n * Convert HTML to blocks respecting the block content type's schema\n *\n * @param html - The HTML to convert to blocks\n * @param schemaType - A compiled version of the schema type for the block content\n * @param options - Options for deserializing HTML to blocks\n * @returns Array of blocks\n * @public\n */\nexport function htmlToBlocks(\n  html: string,\n  schemaType: ArraySchemaType | Schema,\n  options: HtmlDeserializerOptions = {},\n): (TypedObject | PortableTextTextBlock)[] {\n  const schema = isSanitySchema(schemaType)\n    ? sanitySchemaToPortableTextSchema(schemaType)\n    : schemaType\n\n  const deserializer = new HtmlDeserializer(schema, options)\n  return deserializer\n    .deserialize(html)\n    .map((block) => normalizeBlock(block, {keyGenerator: options.keyGenerator}))\n}\n\nexport type {ArbitraryTypedObject, DeserializerRule, HtmlParser} from './types'\nexport type {\n  PortableTextBlock,\n  PortableTextObject,\n  PortableTextSpan,\n  PortableTextTextBlock,\n} from './types.portable-text'\nexport type {BlockNormalizationOptions} from './util/normalizeBlock'\nexport {randomKey} from './util/randomKey'\nexport {normalizeBlock}\nexport type {HtmlDeserializerOptions, TypedObject}\n\nfunction isSanitySchema(\n  schema: ArraySchemaType | Schema,\n): schema is ArraySchemaType {\n  return schema.hasOwnProperty('jsonType')\n}\n"], "names": ["uniq", "_", "vercelStegaClean", "blocks", "isEqual", "isEmphasis", "isStrong", "isUnderline", "getListItemStyle", "getListItemLevel", "getRandomValues", "flatten", "sanitySchemaToPortableTextSchema"], "mappings": ";;;;;;;AAiBO,SAAS,uBACd,QACgC;AAChC,SAAO,SAAS,MAAM,KAAK,OAAO,OAAO,SAAU;AACrD;AAEA,SAAS,SAAS,OAAkD;AAClE,SAAO,CAAC,CAAC,UAAU,OAAO,SAAU,YAAY,OAAO,SAAU;AACnE;ACFO,SAAS,YACd,QACA,OACgC;AAShC,SARI,EAAA,CAAC,uBAAuB,KAAK,KAI7B,MAAM,UAAU,OAAO,MAAM,QAI7B,CAAC,MAAM,QAAQ,MAAM,QAAQ;AAKnC;AAYO,SAAS,OACd,QACA,OAC2B;AAS3B,SARI,EAAA,CAAC,uBAAuB,KAAK,KAI7B,MAAM,UAAU,OAAO,KAAK,QAI5B,OAAO,MAAM,QAAS;AAK5B;ACrEA,MAAM,iBAAiB,OAAO,UAAU;AAIjC,SAAS,cAAc,KAAc;AAC1C,UAAQ,eAAe,KAAK,GAAG,GAAA;AAAA,IAC7B,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,EACT;AAGF,SAAI,QAAQ,OACH,SAGL,QAAQ,SACH,cAIP,OACA,OAAO,OAAQ,YACf,cAAc,OACb,IAA4B,aAAa,IAEnC,YAGL,QAAQ,OAAO,GAAG,IACb,WAGF,OAAO;AAChB;AC3CG,IAAC,IAAE,EAAC,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,OAAM,GAAE,MAAK,GAAE,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,OAAM,GAAE,IAAE,EAAC,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK;AAAI,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE;AAAg/B,OAAO,YAAY,OAAO,QAAQ,CAAC,EAAE,IAAI,OAAG,EAAE,QAAO,CAAE,CAAC;AAAI,OAAO,YAAY,OAAO,QAAQ,CAAC,EAAE,IAAI,OAAG,EAAE,QAAO,CAAE,CAAC;AAAC,IAAC,IAAE,GAAG,OAAO,OAAO,CAAC,EAAE,IAAI,OAAG,OAAO,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,IAAG,IAAE,IAAI,OAAO,IAAI,CAAC,SAAQ,IAAI;AAAugC,SAAS,EAAE,GAAE;AAAC,MAAI;AAAE,SAAM,EAAC,SAAQ,EAAE,QAAQ,GAAE,EAAE,GAAE,WAAU,IAAE,EAAE,MAAM,CAAC,MAAI,OAAK,SAAO,EAAE,CAAC,MAAI,GAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,KAAG,KAAK,MAAM,EAAE,KAAK,UAAU,CAAC,CAAC,EAAE,OAAO;AAAC;ACU1kF,MAAM,2BAA2B,CAAC,OAAO,YAAY,MAAM,GAErD,sBAAsB,UAEtB,gBAA8B,OAAO,OAAO;AAAA,EACvD,OAAO;AAAA,EACP,UAAU,CAAA;AAAA,EACV,OAAO;AACT,CAAC,GAEY,eAAe,OAAO,OAAO;AAAA,EACxC,OAAO;AAAA,EACP,OAAO,CAAA;AACT,CAAC,GAEY,kBAAkB;AAAA,EAC7B,GAAG;AAAA,EACH,YAAY,EAAC,GAAG,eAAe,OAAO,aAAA;AACxC,GAEa,iBAAiB;AAAA,EAC5B,MAAM,EAAC,QAAQ,OAAA;AACjB,GAEa,2BAGT;AAAA,EACF,IAAI,EAAC,QAAQ,KAAA;AAAA,EACb,IAAI,EAAC,QAAQ,KAAA;AACf,GAEa,mBAA6D;AAAA,EACxE,IAAI,EAAC,GAAG,eAAe,OAAO,KAAA;AAAA,EAC9B,IAAI,EAAC,GAAG,eAAe,OAAO,KAAA;AAAA,EAC9B,IAAI,EAAC,GAAG,eAAe,OAAO,KAAA;AAAA,EAC9B,IAAI,EAAC,GAAG,eAAe,OAAO,KAAA;AAAA,EAC9B,IAAI,EAAC,GAAG,eAAe,OAAO,KAAA;AAAA,EAC9B,IAAI,EAAC,GAAG,eAAe,OAAO,KAAA;AAChC,GAEa,iBAAiB;AAAA,EAC5B,IAAI,EAAC,GAAG,eAAe,OAAO,oBAAA;AAChC,GAEa,sBAA0D;AAAA,EACrE,GAAG;AAAA,EACH,QAAQ;AAAA,EAER,GAAG;AAAA,EACH,IAAI;AAAA,EAEJ,GAAG;AAAA,EACH,GAAG;AAAA,EACH,QAAQ;AAAA,EACR,KAAK;AAAA,EAEL,MAAM;AAAA,EACN,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,OAAO;AACT,GAEa,sBAAgE;AAAA,EAC3E,IAAI;AAAA,IACF,GAAG;AAAA,IACH,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,EAAA;AAEd,GAEa,cAAc;AAAA,EACzB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AAEwCA,cAAAA;AAAAA,EACtC,OAAO,OAAO,WAAW,EACtB,OAAO,CAAC,QAA6B,WAAW,GAAG,EACnD,IAAI,CAAC,QAAQ,IAAI,KAAK;AAC3B;AAE4CA,cAAAA;AAAAA,EAC1C,OAAO,OAAO,mBAAmB;AACnC;ACpGO,MAAM,eAAe;AAAA,EAI1B,cAAc;AAAA,EAEd,4BAA4B;AAAA,EAC5B,8BAA8B;AAIhC;ACRA,IAAA,kBAAe,CACb,OACA,KACA,YACa;AACb,QAAM,wBACJ,SAAS,kCAAkC;AAC7C,MAAI,yBAAyB,IAC1B;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,EAAA,EAED,YAAA;AAEH,MAAI,wBAAwB;AAC1B,UAAM,mBAAmB,QAAQ,sBAAsB,MAAM;AAO7D,YAJK,qBACH,yBAAyB,IAAI,OAGvB,uBAAA;AAAA,MACN,KAAK;AAEH,4BAAoB,sBAAsB;AAC1C;AAAA,MACF,KAAK;AAEH,4BAAoB,sBAAsB;AAC1C;AAAA,IAEA;AAKJ,UAAM,aAAa,IAAI;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa;AAAA,MACb;AAAA,IAAA;AAGF,aAAS,IAAI,WAAW,iBAAiB,GAAG,KAAK,GAAG,KAAK;AACvD,YAAM,MAAM,WAAW,aAAa,CAAC;AACrC,WAAK,aAAa,uBAAuB,MAAM,IAG7C,KAAK,kBAAkB,0BACtB,CAAC,oBAAoB,IAAI,kBAAkB,IAAI,UAEhD,KAAK,aAAa,qBAAqB,MAAM,GAC7C,QAAQ,GAAG,IAMX,QAAQ,GAAG,MAAM,QACjB,IAAI,cACJ,QAAQ,KAAK,UAAU,MAAM,SAE7B,IAAI,YAAY,IAAI,UAAU;AAAA,IAElC;AAGA,WAAI,oBACF,IAAI,KAAK,mBAAmB;AAAA,MAC1B,GAAG,MAAM,KAAK,uBAAuB,UAAU;AAAA,IAAA,GAI5C;AAAA,EACT;AACA,SAAO;AACT;AClFA,MAAM,4BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAA,iBAAe,CAAC,OAAe,QAA4B;AAIzD,QAAM,gBAAgB,IAAI;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,EAAA;AAGF,WAAS,IAAI,cAAc,iBAAiB,GAAG,KAAK,GAAG,KAAK;AAC1D,UAAM,OAAO,cAAc,aAAa,CAAC,GACnC,OAAO,KAAK,eAAe;AACjC,QAAI,KAAK,QAAQ,cAAc,EAAE,GAAG;AAClC,YAAM,UAAU,IAAI,cAAc,MAAM;AACxC,cAAQ,YAAY,IAAI,eAAe,IAAI,CAAC,GAC5C,KAAK,YAAY,aAAa,SAAS,IAAI;AAAA,IAC7C;AACE,WAAK,YAAY,YAAY,IAAI;AAAA,EAErC;AAEA,QAAM,gBAAgB,IAAI;AAAA,IACxB,0BAA0B,KAAK,GAAG;AAAA,IAClC;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,EAAA;AAEF,WAAS,IAAI,cAAc,iBAAiB,GAAG,KAAK,GAAG,KAAK;AAC1D,UAAM,WAAW,cAAc,aAAa,CAAC;AACxC,gBAGL,SAAS,YAAY,YAAY,QAAQ;AAAA,EAC3C;AACA,SAAO;AACT,GCtDA,mBAAe,CAAC,MAAc,QAA4B;AACxD,QAAM,eAAe;AAErB,MAAI,KAAK,MAAM,YAAY,GAAG;AAG5B,UAAM,aAAa,IAAI;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa;AAAA,MACb;AAAA,IAAA;AAGF,aAAS,IAAI,WAAW,iBAAiB,GAAG,KAAK,GAAG;AACtC,iBAAW,aAAa,CAAC,GAChC,aAAa,kBAAkB,MAAM;AAG5C,WAAO;AAAA,EACT;AACA,SAAO;AACT,GCrBA,uBAAe,CAACC,IAAW,QAA4B;AAErD,WAAS,YAAY,MAAY;AAE/B,QACE,KAAK,aAAa,aAAa,gBAC/B,CAAC,yBAAyB;AAAA,MACxB,KAAK,eAAe,QAAQ,iBAAiB;AAAA,IAAA;AAG/C,WAAK,cACH,KAAK,aACD,QAAQ,UAAU,GAAG,EACtB,QAAQ,YAAY,GAAG,KAAK;AAAA;AAIjC,eAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ;AAC1C,oBAAY,KAAK,WAAW,CAAC,CAAC;AAAA,EAGpC;AAGA,SAAA,YAAY,IAAI,IAAI,GAEb;AACT;AC5BA,MAAM,kBACJ,2EAGI,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AACF,GAGM,cAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAGM,aAAmD;AAAA,EACvD,eAAe,CAAC,IAAI;AAAA,EACpB,UAAU,CAAC,IAAI;AAAA,EACf,eAAe,CAAC,IAAI;AAAA,EACpB,aAAa,CAAC,IAAI;AAAA,EAClB,mBAAmB,CAAC,QAAQ,IAAI;AAAA,EAChC,oBAAoB,CAAC,QAAQ,MAAM,QAAQ;AAAA;AAE7C;AAEA,SAAS,WAAW,MAAc;AAChC,SAAO,gBAAgB,KAAK,IAAI;AAClC;AAEA,IAAA,iBAAe,CAAC,MAAc,QAA4B;AACxD,MAAI,CAAC,WAAW,IAAI;AAClB,WAAO;AAGT,QAAM,gBAAgB,IAAI;AAAA,IACxB,cAAc,KAAK,GAAG;AAAA,IACtB;AAAA,IACA,CAAC,WACK,WAAW,MACN,4CAEF;AAAA,IAET,aAAa;AAAA,IACb;AAAA,EAAA;AAGF,WAAS,IAAI,cAAc,iBAAiB,GAAG,KAAK,GAAG,KAAK;AAC1D,UAAM,WAAW,cAAc,aAAa,CAAC;AACzC,cAAU,cACZ,SAAS,WAAW,YAAY,QAAQ;AAAA,EAE5C;AAGA,QAAM,iBAAiB,IAAI;AAAA,IACzB,YAAY,KAAK,GAAG;AAAA,IACpB;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,EAAA;AAEF,WAAS,IAAI,eAAe,iBAAiB,GAAG,KAAK,GAAG,KAAK;AAC3D,UAAM,YAAY,eAAe,aAAa,CAAC,GACzC,OAAO,WAAW,UAAU,SAAS,GACrC,OAAO,IAAI,eAAe,UAAU,eAAe,EAAE;AAC3D,QAAI,CAAC;AACH;AAGF,UAAM,gBAAgB,IAAI,cAAc,KAAK,CAAC,CAAC;AAC/C,QAAI,SAAS,eACT,QAAQ;AACZ,SAAK,MAAM,CAAC,EAAE,QAAQ,CAAC,QAAQ;AAC7B,cAAQ,IAAI,cAAc,GAAG,GAC7B,OAAO,YAAY,KAAK,GACxB,SAAS;AAAA,IACX,CAAC,GACD,MAAM,YAAY,IAAI,GACtB,WAAW,YAAY,aAAa,eAAe,SAAS;AAAA,EAC9D;AAEA,SAAO;AACT,GCrFA,gBAAe;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;ACeO,SAAS,QAAQ,IAAmD;AACzE,MAAI,MAAM,aAAa;AACrB,WAAO,GAAG,QAAQ,YAAA;AAItB;AAGO,SAAS,WACd,MACA,WACA,SACU;AACV,QAAM,YAAYC,EAAiB,IAAI,GACjC,MAAM,UAAU,8BAA8B,SAAS,CAAC;AAC9D,SAAA,cAAc,QAAQ,CAAC,cAAc;AACnC,cAAU,WAAW,KAAK,OAAO;AAAA,EACnC,CAAC,GACM;AACT;AAEA,SAAS,8BAA8B,MAAsB;AAC3D,SAAO,KAAK,KAAA;AACd;AAOO,SAAS,mBAA+B;AAC7C,MAAI,cAAc,SAAS,MAAM;AAC/B,UAAM,IAAI;AAAA,MACR;AAAA,IAAA;AAKJ,SAAO,CAAC,SACC,IAAI,YAAY,gBAAgB,MAAM,WAAW;AAE5D;AAEO,SAAS,oBACd,QACAC,SACe;AACf,MAAI,QAAQ;AACZ,QAAM,YAA2B,CAAA,GAC3B,WAAW,CAAC,UAAyB;AACzC,UAAM,WAA0B,CAAA;AAChC,UAAM,QAAQ,CAAC,SAAS;AAClB,gBAAU,KACZ,UAAU,KAAK,IAAI,GAEjB,YAAY,QAAQ,IAAI,MACtB,QAAQ,MACV,SAAS,KAAK,IAAI,GAClB,UAAU,KAAK,IAAI,IAErB,SACA,SAAS,KAAK,QAAQ,IAEpB,KAAK,UAAU,cACjB,SAAS,KAAK,IAAI,GAClB,UAAU,KAAM,KAAa,KAAK;AAAA,IAEtC,CAAC,GACD,SAAS,QAAQ,CAAC,SAAS;AACzB,YAAM,OAAO,MAAM,QAAQ,IAAI,GAAG,CAAC;AAAA,IACrC,CAAC,GACD;AAAA,EACF;AACA,SAAA,SAASA,OAAM,GACR;AACT;AAEA,SAAS,SAAS,OAA8B,OAAe;AAC7D,QAAM,OAAO,MAAM,SAAS,QAAQ,CAAC;AACrC,SAAO,QAAQ,KAAK,UAAU,SAAS,OAAO;AAChD;AAEA,SAAS,SAAS,OAA8B,OAAe;AAC7D,QAAM,OAAO,MAAM,SAAS,QAAQ,CAAC;AACrC,SAAO,QAAQ,KAAK,UAAU,SAAS,OAAO;AAChD;AAEA,SAAS,iBAAiB,MAAc;AACtC,SAAO,CAAC,QAAQ,GAAG,EAAE,SAAS,IAAI;AACpC;AAQO,SAAS,eACd,QACAA,SACe;AACf,SAAAA,QAAO,QAAQ,CAAC,UAAU;AACnB,gBAAY,QAAQ,KAAK,KAK9B,MAAM,SAAS,QAAQ,CAAC,OAAO,UAAU;AACvC,UAAI,CAAC,cAAc,KAAK;AACtB;AAEF,YAAM,YAAY,SAAS,OAAO,KAAK,GACjC,YAAY,SAAS,OAAO,KAAK;AACnC,gBAAU,MACZ,MAAM,OAAO,MAAM,KAAK,QAAQ,cAAc,EAAE,IAE9C,UAAU,MAAM,SAAS,SAAS,MACpC,MAAM,OAAO,MAAM,KAAK,QAAQ,cAAc,EAAE,IAGhD,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,KAC9D,aACA,cAAc,SAAS,KACvB,KAAK,KAAK,UAAU,KAAK,MAAM,GAAG,CAAC,CAAC,MAEpC,MAAM,OAAO,MAAM,KAAK,QAAQ,cAAc,EAAE,IAGhD,KAAK,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC,KAChC,aACA,cAAc,SAAS,KACvB,KAAK,KAAK,UAAU,KAAK,MAAM,KAAK,IAAI,GAAG,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,MAEtE,MAAM,OAAO,MAAM,KAAK,QAAQ,cAAc,EAAE,IAE7C,MAAM,QACT,MAAM,SAAS,OAAO,OAAO,CAAC,GAG9B,aACAC,iBAAAA,QAAQ,UAAU,OAAO,MAAM,KAAK,KACpC,iBAAiB,MAAM,IAAI,KAE3B,UAAU,QAAQ,KAClB,MAAM,SAAS,OAAO,OAAO,CAAC,KAE9B,aACAA,iBAAAA,QAAQ,UAAU,OAAO,MAAM,KAAK,KACpC,iBAAiB,MAAM,IAAI,MAE3B,UAAU,OAAO,IAAI,UAAU,IAAI,IACnC,MAAM,SAAS,OAAO,OAAO,CAAC;AAAA,IAElC,CAAC;AAAA,EACH,CAAC,GAEMD;AACT;AAEO,SAAS,mBACd,QACAA,SACe;AACf,SAAOA,QAAO,OAAO,CAAC,MAAM,MAAM,GAAG,aAAa;AAChD,QAAI,KAAK,UAAU;AACjB,aAAA,KAAK,KAAK,IAAI,GACP;AAGT,QAAI,KAAK,UAAU;AACjB,aAAA,KAAK,KAAM,KAAa,KAAK,GACtB;AAGT,UAAM,YAAY,KAAK,KAAK,SAAS,CAAC;AACtC,QACE,IAAI,KACJ,CAAC,YAAY,QAAQ,SAAS,IAAI,CAAC,CAAC,KACpC,YAAY,QAAQ,SAAS;AAE7B,aAAA,UAAU,SAAS,KAAK,IAA0B,GAC3C;AAGT,UAAM,QAAQ;AAAA,MACZ,GAAG;AAAA,MACH,UAAU,CAAC,IAAI;AAAA,IAAA;AAGjB,WAAA,KAAK,KAAK,KAAK,GACR;AAAA,EACT,GAAG,CAAA,CAAmB;AACxB;AAEO,SAAS,WAAW,MAAiC;AAC1D,SAAO,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM;AAClD;AAEO,SAAS,cAAc,MAAwC;AACpE,SAAO,KAAK,UAAU;AACxB;AAEO,SAAS,eAAe,MAAyC;AACtE,SAAO,KAAK,UAAU;AACxB;AAEO,SAAS,uBACd,MAC8B;AAC9B,SAAO,KAAK,UAAU;AACxB;AAEO,SAAS,wBACd,MAC+B;AAC/B,SAAO,KAAK,UAAU;AACxB;AAEO,SAAS,UAAU,MAA6B;AACrD,SAAO,KAAK,aAAa;AAC3B;AAMO,SAAS,oBAAoB,UAAgB;AAClD,MAAI,kBAAkB,GAClB,aAAa;AACjB,QAAM,gBAAwB,CAAA;AAE9B,WAAS,QAAQ,SAAS,YAAY,OAAO,QAAQ,MAAM,aAAa;AACtE,QAAI,CAAC,UAAU,KAAK,GAAG;AACrB,0BAAoB,KAAK,GACzB,kBAAkB;AAClB;AAAA,IACF;AAEA,UAAM,MAAM;AAER,sBAAkB,GAAG,KACnB,cAAc,IAAI,kBAAkB,cACtC,mBACI,kBAAkB,KACpB,cAAc,KAAK,GAAG,KAIxB,kBAAkB,GAGpB,aAAa,IAAI,kBAGjB,oBAAoB,KAAK,GAEzB,kBAAkB;AAAA,EAEtB;AAGA,gBAAc,QAAQ,CAAC,SAAS,KAAK,eAAe,YAAY,IAAI,CAAC;AACvE;AAMO,SAAS,oBAAoB,UAAgB;AAClD,QAAM,gBAAwB,CAAA;AAE9B,WAAS,qBAAqB,aAAmB;AAC/C,QAAI,UAAU,WAAW,GAAG;AAC1B,YAAM,MAAM;AAGZ,UACE,QAAQ,GAAG,MAAM,SAChB,QAAQ,IAAI,kBAAkB,MAAM,OACnC,QAAQ,IAAI,sBAAsB,MAAM,MAC1C;AACA,sBAAc,KAAK,GAAG;AAEtB;AAAA,MACF;AAGA,WACG,QAAQ,GAAG,MAAM,OAAO,QAAQ,GAAG,MAAM,SAC1C,KAAK,YAAY,aAAa,KAAA,MAAW,IACzC;AACA,sBAAc,KAAK,GAAG;AAEtB;AAAA,MACF;AAGA,eAAS,QAAQ,IAAI,YAAY,OAAO,QAAQ,MAAM;AACpD,6BAAqB,KAAK;AAAA,IAE9B;AAAA,EACF;AAEA,uBAAqB,QAAQ,GAG7B,cAAc,QAAQ,CAAC,SAAS,KAAK,eAAe,YAAY,IAAI,CAAC;AACvE;AAEA,SAAS,kBAAkB,KAA2B;AACpD,SAAO,CAAC,KAAK,IAAI,EAAE,SAAS,QAAQ,GAAG,KAAK,EAAE,KAAK,CAAC,IAAI,aAAa,KAAA;AACvE;ACvUA,MAAM,sBAAsB,OAAO,KAAK,wBAAwB;AAGhE,SAASE,aAAW,IAAmB;AACrC,QAAM,QAAQ,UAAU,EAAE,KAAK,GAAG,aAAa,OAAO;AACtD,SAAO,0BAA0B,KAAK,SAAS,EAAE;AACnD;AAGA,SAASC,WAAS,IAAmB;AACnC,QAAM,QAAQ,UAAU,EAAE,KAAK,GAAG,aAAa,OAAO;AACtD,SAAO,wBAAwB,KAAK,SAAS,EAAE;AACjD;AAGA,SAASC,cAAY,IAAmB;AACtC,MAAI,CAAC,UAAU,EAAE,KAAK,QAAQ,GAAG,UAAU,MAAM;AAC/C,WAAO;AAGT,QAAM,QAAQ,UAAU,EAAE,KAAK,GAAG,aAAa,OAAO;AAEtD,SAAO,kCAAkC,KAAK,SAAS,EAAE;AAC3D;AAIA,SAAS,gBAAgB,IAAmB;AAC1C,QAAM,QAAQ,UAAU,EAAE,KAAK,GAAG,aAAa,OAAO;AACtD,SAAO,8CAA8C,KAAK,SAAS,EAAE;AACvE;AAGA,SAAS,aAAa,IAAmB;AACvC,SAAO,UAAU,EAAE,KAAK,CAAA,CAAQ,GAAG,aAAa,qBAAqB;AACvE;AAEA,SAAS,WAAW,IAAmB;AACrC,SAAO,UAAU,EAAE,KAAK,CAAA,CAAQ,GAAG,aAAa,mBAAmB;AACrE;AAEA,SAASC,mBAAiB,IAA2C;AACnE,QAAM,YAAY,QAAQ,GAAG,UAAU;AACvC,MAAI,EAAA,aAAa,CAAC,oBAAoB,SAAS,SAAS;AAGxD,WAAO,QAAQ,GAAG,UAAU,MAAM,OAAO,WAAW;AACtD;AAEA,SAASC,mBAAiB,IAAkB;AAC1C,MAAI,QAAQ;AACZ,MAAI,QAAQ,EAAE,MAAM,MAAM;AACxB,QAAI,aAAa,GAAG;AACpB,WAAO,cAAY;AACjB,YAAM,YAAY,QAAQ,UAAU;AAChC,mBAAa,oBAAoB,SAAS,SAAS,KACrD,SAEF,aAAa,WAAW;AAAA,IAC1B;AAAA,EACF;AACE,YAAQ;AAEV,SAAO;AACT;AAEA,MAAM,SAAsD;AAAA,EAC1D,GAAG;AAAA,EACH,GAAG;AACL;AAEA,SAAS,cAAc,QAAgB,IAAkB;AACvD,QAAM,WAAW,QAAQ,GAAG,UAAU,GAChC,QAAQ,YAAY,OAAO,QAAQ;AACzC,SAAK,SAGA,OAAO,OAAO,KAAK,CAAC,UAAU,MAAM,SAAS,MAAM,KAAK,IAGtD,MAAM,QALJ;AAMX;AAEA,SAAwB,iBAAiB,QAAoC;AAC3E,SAAO;AAAA,IACL;AAAA,MACE,YAAY,IAAI;AACd,YAAI,UAAU,EAAE,KAAK,QAAQ,EAAE,MAAM,UAAU,aAAa,EAAE,GAAG;AAC/D,gBAAM,OAAO;AAAA,YACX,GAAG;AAAA,YACH,OAAO,CAAA;AAAA,YACP,MAAM,GAAG;AAAA,UAAA;AAEX,iBAAIH,WAAS,EAAE,KACb,KAAK,MAAM,KAAK,QAAQ,GAEtBC,cAAY,EAAE,KAChB,KAAK,MAAM,KAAK,WAAW,GAEzB,gBAAgB,EAAE,KACpB,KAAK,MAAM,KAAK,gBAAgB,GAE9BF,aAAW,EAAE,KACf,KAAK,MAAM,KAAK,IAAI,GAEf;AAAA,QACT;AAAA,MAEF;AAAA,IAAA;AAAA,IAEF;AAAA,MACE,YAAY,IAAI,MAAM;AACpB,YAAI,QAAQ,EAAE,MAAM,QAAQ,aAAa,EAAE;AACzC,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,UAAUG,mBAAiB,EAAE;AAAA,YAC7B,OAAOC,mBAAiB,EAAE;AAAA,YAC1B,OAAO,cAAc,QAAQ,EAAE;AAAA,YAC/B,UAAU,KAAK,GAAG,YAAY,cAAc,CAAA,CAAE;AAAA,UAAA;AAAA,MAIpD;AAAA,IAAA;AAAA,IAEF;AAAA,MACE,YAAY,IAAI;AACd,YACE,QAAQ,EAAE,MAAM,QAChB,aAAa,EAAE,KACf,UAAU,EAAE,KACZ,GAAG,UAAU,SAAS,2BAA2B;AAEjD,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,MAAM;AAAA,UAAA;AAKV,YACE,QAAQ,EAAE,MAAM,QAChB,aAAa,EAAE,KACf,UAAU,EAAE,KACZ,IAAI,YAAY,gBAAgB;AAEhC,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,MAAM;AAAA,UAAA;AAKV,YACE,QAAQ,EAAE,MAAM,QAChB,aAAa,EAAE,KACf,UAAU,EAAE,KACZ,WAAW,EAAE;AAEb,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,MAAM;AAAA,UAAA;AAAA,MAIZ;AAAA,IAAA;AAAA,EACF;AAEJ;ACjLO,SAAS,eAAe;AAC7B,SAAO,UAAU,EAAE;AACrB;AAGA,SAAS,UAAU,SAAS,IAAI;AAC9B,QAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,SAAAC,yBAAAA,QAAgB,KAAK,GACd;AACT;AAEA,MAAM,YAAsB,CAAA;AAC5B,SAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACzB,YAAU,CAAC,KAAK,IAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC;AAU1C,SAAS,UAAU,QAAwB;AAChD,SAAO,UAAU,MAAM,EACpB,OAAO,CAAC,KAAK,MAAM,MAAM,UAAU,CAAC,GAAG,EAAE,EACzC,MAAM,GAAG,MAAM;AACpB;ACzBO,MAAM,yBAA2C;AAAA,EACtD,YAAY,MAAM;AAChB,WAAO,KAAK,aAAa,WAAW,qBAAqB,IAAI,IACzD;AAAA,MACE,GAAG;AAAA,MACH,OAAO,CAAA;AAAA,MACP,OAAO,KAAK,eAAe,IAAI,QAAQ,UAAU,GAAG;AAAA,IAAA,IAEtD;AAAA,EACN;AACF;AAEA,SAAS,qBAAqB,MAAY;AAUxC,UARE,KAAK,aAAa,MACjB,KAAK,eAAe,IAAI,QAAQ,WAAW,GAAG,EAAE,QAAQ,UAAU,GAAG,MACpE,OACF,KAAK,eACL,KAAK,YAAY,aAAa,KAC9B,KAAK,mBACL,KAAK,gBAAgB,aAAa,KAGZ,KAAK,gBAAgB,QAC3C,QAAQ,KAAK,UAAU,MAAM;AAEjC;ACbO,SAAS,gBACd,QACA,iBACoB;AACpB,MACE,oBAAoB,QACpB,OAAO,MAAM,KAAK,CAAC,SAAS,KAAK,SAAS,QAAQ;AAElD,WAAO;AAET,MACE,oBAAoB,QACpB,OAAO,MAAM,KAAK,CAAC,SAAS,KAAK,SAAS,QAAQ;AAElD,WAAO;AAGX;AAEA,SAAwB,gBACtB,QACA,SACoB;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA;AAAA,MAEE,YAAY,IAAI;AACd,YAAI,QAAQ,EAAE,MAAM;AAClB;AAGF,cAAM,gBAAgB,OAAO,OAAO;AAAA,UAClC,CAAC,UAAU,MAAM,SAAS;AAAA,QAAA;AAG5B,eAAO;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU,CAAA;AAAA,UACV,UAAU;AAAA,YACR;AAAA,cACE,GAAG;AAAA,cACH,OAAO,gBAAgB,CAAC,MAAM,IAAI,CAAA;AAAA,cAClC,MAAM,GAAG,eAAe;AAAA,YAAA;AAAA,UAC1B;AAAA,QACF;AAAA,MAEJ;AAAA,IAAA;AAAA;AAAA,IAEF;AAAA,MACE,YAAY,IAAI,MAAM;AACpB,YAAI,QAAQ,EAAE,MAAM;AAClB;AAEF,cAAMP,UAAmD;AAAA,UACvD,GAAG;AAAA,UACH,GAAG;AAAA,QAAA;AAEL,eAAOA,QAAO;AACd,cAAM,sBAAsB,OAAO,KAAKA,OAAM,GAExC,WAA0B,CAAA;AAEhC,eAAA,GAAG,WAAW,QAAQ,CAAC,MAAM,UAAU;AACrC,cAAK,GAAG;AAIR,gBACE,KAAK,aAAa,KAClB,oBAAoB;AAAA,cACjB,KAAiB,UAAU,YAAA;AAAA,YAAY,GAE1C;AACA,oBAAM,OAAO,GAAG,cAAc,cAAc,MAAM,GAE5C,gBAAgB,SAAS,SAAS,SAAS,CAAC;AAGhD,+BACA,cAAc,aAAa,KAC3B,cAAc,aAAa,KAAA,KAI3B,KAAK,YAAY,GAAG,cAAc,eAAe,IAAI,CAAC,GAGxD,KAAK,WAAW,QAAQ,CAAC,OAAO;AAC9B,qBAAK,YAAY,GAAG,UAAU,EAAI,CAAC;AAAA,cACrC,CAAC,GAEG,UAAU,GAAG,WAAW,UAE1B,KAAK,YAAY,GAAG,cAAc,eAAe,IAAI,CAAC,GAGxD,SAAS,KAAK,IAAI;AAAA,YACpB;AACE,uBAAS,KAAK,IAAmB;AAAA,QAErC,CAAC,GAEM;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU,CAAA;AAAA,UACV,UAAU,KAAK,QAAQ;AAAA,QAAA;AAAA,MAE3B;AAAA,IAAA;AAAA;AAAA,IAEF;AAAA,MACE,YAAY,IAAI,MAAM;AACpB,cAAMA,UAAmD;AAAA,UACvD,GAAG;AAAA,UACH,GAAG;AAAA,QAAA,GAEC,MAAM,QAAQ,EAAE;AACtB,YAAI,QAAQ,MAAMA,QAAO,GAAG,IAAI;AAChC,YAAI,CAAC;AACH;AAGF,YAAI,GAAG,cAAc,QAAQ,GAAG,UAAU,MAAM;AAC9C,iBAAO,KAAK,GAAG,UAAU;AAE3B,cAAM,aAAa,MAAM;AAEzB,eAAK,OAAO,OAAO,KAAK,CAAC,UAAU,MAAM,SAAS,UAAU,MAC1D,QAAQ,gBAEH;AAAA,UACL,GAAG;AAAA,UACH,UAAU,KAAK,GAAG,UAAU;AAAA,QAAA;AAAA,MAEhC;AAAA,IAAA;AAAA;AAAA,IAEF;AAAA,MACE,YAAY,IAAI,MAAM;AACpB,cAAM,MAAM,QAAQ,EAAE;AACtB,YAAI,EAAA,CAAC,OAAO,EAAE,OAAO;AAGrB,iBAAO,KAAK,GAAG,UAAU;AAAA,MAC3B;AAAA,IAAA;AAAA;AAAA,IAEF;AAAA,MACE,YAAY,IAAI,MAAM;AAEpB,YADY,QAAQ,EAAE,MAAM;AAI5B,iBAAO,KAAK,GAAG,UAAU;AAAA,MAC3B;AAAA,IAAA;AAAA;AAAA,IAEF;AAAA,MACE,YAAY,IAAI,MAAM;AACpB,cAAM,MAAM,QAAQ,EAAE;AACtB,YAAI,EAAA,CAAC,OAAO,EAAE,OAAO;AAGrB,iBAAO,KAAK,GAAG,UAAU;AAAA,MAC3B;AAAA,IAAA;AAAA;AAAA,IAEF;AAAA,MACE,YAAY,IAAI;AACd,YAAI,QAAQ,EAAE,MAAM;AAClB,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,MAAM;AAAA;AAAA,UAAA;AAAA,MAIZ;AAAA,IAAA;AAAA;AAAA,IAEF;AAAA,MACE,YAAY,IAAI,MAAM,OAAO;AAC3B,cAAM,MAAM,QAAQ,EAAE,GAChB,WAAW,MAAM,oBAAoB,GAAG,IAAI,QAC5C,YAAY,QAAQ,GAAG,UAAU,KAAK;AAC5C,YACE,CAAC,YACD,CAAC,GAAG,cACJ,CAAC,yBAAyB,SAAS;AAEnC;AAEF,cAAM,kBAAkB,gBAAgB,QAAQ,SAAS;AAEzD,eAAK,mBAGL,SAAS,WAAW,iBACb;AAAA,UACL,GAAG;AAAA,UACH,UAAU,KAAK,GAAG,UAAU;AAAA,QAAA,KALrB,MAAM,EAAC,OAAO,SAAS,UAAU,KAAK,GAAG,UAAU,GAAE;AAAA,MAOhE;AAAA,IAAA;AAAA;AAAA,IAEF;AAAA,MACE,YAAY,IAAI,MAAM;AACpB,cAAM,YAAY,oBAAoB,QAAQ,EAAE,KAAK,EAAE;AACvD,YACE,EAAA,CAAC,aACD,CAAC,OAAO,WAAW;AAAA,UACjB,CAAC,kBAAkB,cAAc,SAAS;AAAA,QAAA;AAK9C,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU,KAAK,GAAG,UAAU;AAAA,UAAA;AAAA,MAEhC;AAAA,IAAA;AAAA;AAAA;AAAA,IAGF;AAAA,MACE,YAAY,IAAI,MAAM;AACpB,YAAI,QAAQ,EAAE,MAAM;AAClB;AAEF,cAAM,cAAc,OAAO,YAAY;AAAA,UACrC,CAAC,eAAe,WAAW,SAAS;AAAA,QAAA,GAEhC,OAAO,UAAU,EAAE,KAAK,GAAG,aAAa,MAAM;AACpD,eAAK,OAGD,cACK;AAAA,UACL,OAAO;AAAA,UACP,SAAS;AAAA,YACP,MAAM,QAAQ,eACV,QAAQ,aAAA,IACR,aAAA;AAAA,YACJ,OAAO;AAAA,YACP;AAAA,UAAA;AAAA,UAEF,UAAU,KAAK,GAAG,UAAU;AAAA,QAAA,IAI9B,GAAG,YAAY,GAAG,cAAc,eAAe,KAAK,IAAI,GAAG,CAAC,KAC5D,KAAK,GAAG,UAAU,IAjBX,KAAK,GAAG,UAAU;AAAA,MAmB7B;AAAA,IAAA;AAAA,EACF;AAEJ;ACvQA,SAAS,WAAW,IAAmB;AACrC,QAAM,QAAQ,UAAU,EAAE,KAAK,GAAG,aAAa,OAAO;AACtD,SAAO,oBAAoB,KAAK,SAAS,EAAE;AAC7C;AAGA,SAAS,SAAS,IAAmB;AACnC,QAAM,QAAQ,UAAU,EAAE,KAAK,GAAG,aAAa,OAAO;AACtD,SACE,kBAAkB,KAAK,SAAS,EAAE,KAAK,kBAAkB,KAAK,SAAS,EAAE;AAE7E;AAGA,SAAS,YAAY,IAAmB;AACtC,QAAM,QAAQ,UAAU,EAAE,KAAK,GAAG,aAAa,OAAO;AACtD,SAAO,4BAA4B,KAAK,SAAS,EAAE;AACrD;AAGA,SAAS,SAAS,IAAmB;AACnC,SAAO,UAAU,EAAE,KAAK,CAAA,CAAQ,GAAG,aAAa,gBAAgB;AAClE;AAEA,SAAwB,oBAAwC;AAC9D,SAAO;AAAA,IACL;AAAA,MACE,YAAY,IAAI;AAGd,YAAI,UAAU,EAAE,KAAK,QAAQ,EAAE,MAAM,UAAU,SAAS,EAAE,GAAG;AAC3D,gBAAM,OAAO;AAAA,YACX,GAAG;AAAA,YACH,OAAO,CAAA;AAAA,YACP,MAAM,GAAG;AAAA,UAAA;AAEX,iBAAI,SAAS,EAAE,KACb,KAAK,MAAM,KAAK,QAAQ,GAEtB,YAAY,EAAE,KAChB,KAAK,MAAM,KAAK,WAAW,GAEzB,WAAW,EAAE,KACf,KAAK,MAAM,KAAK,IAAI,GAEf;AAAA,QACT;AAAA,MAEF;AAAA,IAAA;AAAA,EACF;AAEJ;ACpDA,SAAS,iBAAiB,IAA8B;AACtD,QAAM,QAAQ,UAAU,EAAE,KAAK,GAAG,aAAa,OAAO;AACtD,MAAK,SAIA,MAAM,MAAM,QAAQ;AAIzB,WAAO,MAAM,MAAM,MAAM,IAAI,WAAW;AAC1C;AAEA,SAAS,iBAAiB,IAA8B;AACtD,QAAM,QAAQ,UAAU,EAAE,KAAK,GAAG,aAAa,OAAO;AACtD,MAAI,CAAC;AACH;AAGF,QAAM,aAAa,MAAM,MAAM,UAAU;AACzC,MAAI,CAAC;AACH;AAGF,QAAM,CAAC,KAAK,IAAI,WAAW,CAAC,EAAE,MAAM,IAAI,KAAK,CAAA;AAE7C,UADiB,QAAQ,OAAO,SAAS,OAAO,EAAE,IAAI,MACnC;AACrB;AAEA,SAAS,kBAAkB,IAAmB;AAC5C,SAAO,UAAU,EAAE,KAAK,GAAG,YACvB,GAAG,cAAc,+BACf,GAAG,cAAc,gCACjB,GAAG,cAAc,6BACnB;AACN;AAEA,SAAwB,kBAAsC;AAC5D,SAAO;AAAA,IACL;AAAA,MACE,YAAY,IAAI,MAAM;AACpB,YAAI,QAAQ,EAAE,MAAM,OAAO,kBAAkB,EAAE;AAC7C,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,UAAU,iBAAiB,EAAE;AAAA,YAC7B,OAAO,iBAAiB,EAAE;AAAA,YAC1B,OAAO;AAAA,YACP,UAAU,KAAK,GAAG,UAAU;AAAA,UAAA;AAAA,MAIlC;AAAA,IAAA;AAAA,EACF;AAEJ;ACnDO,SAAS,YACd,QACA,SACoB;AACpB,SAAO;AAAA,IACL,GAAG,gBAAA;AAAA,IACH,GAAG,kBAAA;AAAA,IACH,GAAG,iBAAiB,MAAM;AAAA,IAC1B,GAAG,gBAAgB,QAAQ,OAAO;AAAA,EAAA;AAEtC;ACkBA,MAAqB,iBAAiB;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAkC,CAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlC,YAAY,QAAgB,UAAmC,IAAI;AACjE,UAAM,EAAC,QAAQ,CAAA,GAAI,iCAAiC,eAAc,SAC5D,gBAAgB,YAAY,QAAQ;AAAA,MACxC,cAAc,QAAQ;AAAA,IAAA,CACvB;AACD,SAAK,SAAS,QACd,KAAK,QAAQ,CAAC,GAAG,OAAO,GAAG,aAAa;AACxC,UAAM,YAAY,QAAQ,aAAa,iBAAA;AACvC,SAAK,YAAY,CAAC,SACJ,WAAW,MAAM,WAAW,EAAC,+BAAA,CAA+B,EAC7D;AAAA,EAEf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,CAAC,SAAgC;AAC7C,SAAK,YAAY,CAAA;AACjB,UAAM,EAAC,UAAA,IAAa,MACd,WAAW,UAAU,IAAI,GACzB,WAAW,MAAM,KAAK,SAAS,UAAU,GAEzCA,UAAS;AAAA,MACb,KAAK;AAAA,MACL;AAAA,QACE,KAAK;AAAA,QACL,mBAAmB,KAAK,QAAQ,KAAK,oBAAoB,QAAQ,CAAC;AAAA,MAAA;AAAA,IACpE;AAGF,WAAI,KAAK,UAAU,SAAS,KAC1BA,QACG,OAAO,CAAC,UAAU,YAAY,KAAK,QAAQ,KAAK,CAAC,EACjD,QAAQ,CAAC,UAAU;AAClB,YAAM,WAAW,MAAM,YAAY,CAAA,GACnC,MAAM,WAAW,MAAM,SAAS;AAAA,QAC9B,KAAK,UAAU,OAAO,CAAC,QACdQ,iBAAAA;AAAAA,UACL,MAAM,SAAS,IAAI,CAAC,UAAU,MAAM,SAAS,CAAA,CAAE;AAAA,QAAA,EAC/C,SAAS,IAAI,IAAI,CACpB;AAAA,MAAA;AAAA,IAEL,CAAC,GAGER,QAAO,IAAI,CAAC,WACb,MAAM,UAAU,YAClB,MAAM,QAAQ,KAAK,OAAO,MAAM,OAE3B,MACR;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,CAAC,WAAmB,OAAsB;AAC9D,QAAI,QAAuB,CAAA;AAC3B,WAAA,SAAS,QAAQ,CAAC,YAAY;AAC5B,cAAQ,MAAM,OAAO,KAAK,mBAAmB,OAAO,CAAC;AAAA,IACvD,CAAC,GACM;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,CAAC,YAA+C;AACnE,UAAM,OAAO,CACX,aAC4C;AAC5C,UAAI,WAAW,QAAQ;AACrB,eAAO,KAAK,oBAAoB,MAAM,KAAK,QAAQ,CAAC;AAGtD,UAAI,MAAM,QAAQ,QAAQ;AACxB,eAAO,KAAK,oBAAoB,QAAQ;AAG1C,UAAK;AAIL,eAAO,KAAK,mBAAmB,QAAQ;AAAA,IACzC,GAEM,QAAQ,CAAC,WACN;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,IAAA;AAIX,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,YAAM,OAAO,KAAK,MAAM,CAAC;AACzB,UAAI,CAAC,KAAK;AACR;AAGF,YAAM,MAAM,KAAK,YAAY,SAAS,MAAM,KAAK,GAC3C,OAAO,cAAc,GAAG;AAE9B,UACE,SAAS,WACT,SAAS,YACT,SAAS,UACT,SAAS;AAET,cAAM,IAAI;AAAA,UACR,4DAA4D,IAAI;AAAA,QAAA;AAIpE,UAAI,QAAQ,QAEL;AAAA;AAAA,cAAI,QAAQ;AACjB,kBAAM,IAAI,MAAM,mCAAmC;AAC1C,gBAAM,QAAQ,GAAG,IAC1B,OAAO,MACE,uBAAuB,GAAG,IACnC,OAAO,KAAK,qBAAqB,GAAG,IAC3B,wBAAwB,GAAG,IACpC,OAAO,KAAK,sBAAsB,GAAG,IAErC,OAAO;AAAA,QAAA;AAIT,YACE,OACA,CAAC,MAAM,QAAQ,GAAG,KAClB,eAAe,GAAG,KAClB,cAAc,KACd;AACA,cAAI,SAAS,QAAQ,YAAY;AACjC,iBAAO,UAAU,QAAQ,MAAM,MAAM;AACnC,qBAAS,OAAO,YAAY,YAC5B,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI;AAAA,QAE5C;AAIE,eACA,CAAC,MAAM,QAAQ,GAAG,KAClB,eAAe,GAAG,KAClB,IAAI,UAAU,gBAEd,IAAI,SAAS,QAAQ,CAAC,OAAO,UAAU;AACjC,wBAAc,KAAK,KAAK,MAAM,SAAS,SACzC,MAAM,OAAO;AAAA,IACT,UAAU,KAAK,UAAU,IAAI,SAAS,SAAS,MACjD,IAAI,SAAS,OAAO,OAAO,CAAC;AAAA,QAGlC,CAAC;AAEH;AAAA,MAAA;AAAA,IACF;AAEA,WAAO,QAAQ,KAAK,QAAQ,UAAU,KAAK,CAAA;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,uBAAuB,CAAC,cAAmD;AACzE,UAAM,EAAC,KAAA,IAAQ,WACT,iBAAiB,CAAC,SAAsB;AAC5C,UAAI,uBAAuB,IAAI;AAC7B,eAAO,KAAK,qBAAqB,IAAI;AAChC,UAAI,cAAc,IAAI;AAC3B,aAAK,QAAQ,KAAK,SAAS,CAAA,GACvB,KAAK,KAAK,KAAA,KAEZ,KAAK,MAAM,QAAQ,IAAI;AAAA,eAGzB,cAAc,QACd,MAAM,QAAS,KAA2B,QAAQ,GAClD;AACA,cAAM,QAAQ;AACd,cAAM,WAAW,MAAM,SAAS,IAAI,cAAc;AAAA,MACpD;AACA,aAAO;AAAA,IACT;AACA,WAAO,UAAU,SAAS,OAAO,CAAC,UAAU,SAAS;AACnD,YAAM,MAAM,eAAe,IAAI;AAC/B,aAAI,MAAM,QAAQ,GAAG,IACZ,SAAS,OAAO,GAAG,KAE5B,SAAS,KAAK,GAAG,GACV;AAAA,IACT,GAAG,CAAA,CAAmB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,wBAAwB,CACtB,eACkB;AAClB,UAAM,EAAC,YAAW;AAClB,SAAK,UAAU,KAAK,OAAO;AAC3B,UAAM,kBAAkB,CAAC,SAAsB;AAC7C,UAAI,wBAAwB,IAAI;AAC9B,eAAO,KAAK,sBAAsB,IAAI;AACjC,UAAI,cAAc,IAAI;AAC3B,aAAK,QAAQ,KAAK,SAAS,CAAA,GACvB,KAAK,KAAK,KAAA,KAEZ,KAAK,MAAM,QAAQ,QAAQ,IAAI;AAAA,eAGjC,cAAc,QACd,MAAM,QAAS,KAA2B,QAAQ,GAClD;AACA,cAAM,QAAQ;AACd,cAAM,WAAW,MAAM,SAAS,IAAI,eAAe;AAAA,MACrD;AACA,aAAO;AAAA,IACT;AACA,WAAO,WAAW,SAAS,OAAO,CAAC,UAAU,SAAS;AACpD,YAAM,MAAM,gBAAgB,IAAI;AAChC,aAAI,MAAM,QAAQ,GAAG,IACZ,SAAS,OAAO,GAAG,KAE5B,SAAS,KAAK,GAAG,GACV;AAAA,IACT,GAAG,CAAA,CAAmB;AAAA,EACxB;AACF;AC1PO,SAAS,eACd,MACA,UAAqC,IAMrC;AACA,QAAM,SAAiB;AAAA,IAIrB,MAAM;AAAA,MACJ,MAAM;AAAA,IAAA;AAAA,EAQV;AAEA,MAAI,KAAK,WAAW,QAAQ,iBAAiB;AAC3C,WAAO,UAAU,OACZ,OACD;AAAA,MACE,GAAG;AAAA,MACH,MAAM,QAAQ,eAAe,QAAQ,aAAA,IAAiB,aAAA;AAAA,IAAa;AAI3E,QAAM,QAGF;AAAA,IACF,MAAM,QAAQ,eAAe,QAAQ,aAAA,IAAiB,aAAA;AAAA,IACtD,UAAU,CAAA;AAAA,IACV,UAAU,CAAA;AAAA,IACV,GAAG;AAAA,EAAA,GAGC,YAAY,MAAM,SAAS,MAAM,SAAS,SAAS,CAAC;AAE1D,MAAI,CAAC;AAEH,WAAA,MAAM,WAAW;AAAA,MACf;AAAA,QACE,OAAO;AAAA,QACP,MAAM,QAAQ,eAAe,QAAQ,aAAA,IAAiB,aAAA;AAAA,QACtD,MAAM;AAAA,QACN,OAAO,CAAA;AAAA,MAAC;AAAA,IACV,GAEK;AAGT,QAAM,eAAyB,CAAA,GACzB,oBACJ,QAAQ,qBAAqB,MAAM,QAAQ,QAAQ,iBAAiB,IAChE,QAAQ,oBACR;AAEN,SAAA,MAAM,WAAW,MAAM,SACpB;AAAA,IACC,CAAC,KAAK,UAAU;AACd,YAAM,gBAAgB,IAAI,IAAI,SAAS,CAAC;AACxC,aACE,iBACA,OAAO,QAAQ,KAAK,KACpB,OAAO,QAAQ,aAAa,KAC5BC,iBAAAA,QAAQ,cAAc,OAAO,MAAM,KAAK,KAGtC,aACA,cAAc,SACd,MAAM,SAAS,MACf,MAAM,SAAS,SAAS,MAK1B,cAAc,QAAQ,MAAM,OACrB,QAET,IAAI,KAAK,KAAK,GACP;AAAA,IACT;AAAA,IACA,CAAA;AAAA,EAAC,EAEF,IAAI,CAAC,UAAU;AACd,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,eAAe;AAGjC,WAAA,MAAM,OAAO,QAAQ,eACjB,QAAQ,iBACR,gBAEA,OAAO,QAAQ,KAAK,MACjB,MAAM,QAEA,sBACT,MAAM,QAAQ,MAAM,MAAM,OAAO,CAAC,SAAS;AACzC,YAAM,YAAY,kBAAkB,SAAS,IAAI,GAC3C,SAAS,MAAM,UAAU,KAAK,CAAC,QAAQ,IAAI,SAAS,IAAI;AAC9D,aAAO,aAAa;AAAA,IACtB,CAAC,KAND,MAAM,QAAQ,CAAA,GAShB,aAAa,KAAK,GAAG,MAAM,KAAK,IAG3B;AAAA,EACT,CAAC,GAGH,MAAM,YAAY,MAAM,YAAY,CAAA,GAAI;AAAA,IAAO,CAAC,YAC9C,aAAa,SAAS,QAAQ,IAAI;AAAA,EAAA,GAG7B;AACT;ACzJO,SAAS,aACd,MACA,YACA,UAAmC,CAAA,GACM;AACzC,QAAM,SAAS,eAAe,UAAU,IACpCQ,aAAAA,iCAAiC,UAAU,IAC3C;AAGJ,SADqB,IAAI,iBAAiB,QAAQ,OAAO,EAEtD,YAAY,IAAI,EAChB,IAAI,CAAC,UAAU,eAAe,OAAO,EAAC,cAAc,QAAQ,aAAA,CAAa,CAAC;AAC/E;AAcA,SAAS,eACP,QAC2B;AAC3B,SAAO,OAAO,eAAe,UAAU;AACzC;;;;", "x_google_ignoreList": [3]}