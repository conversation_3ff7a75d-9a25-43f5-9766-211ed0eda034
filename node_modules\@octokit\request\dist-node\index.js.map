{"version": 3, "sources": ["../dist-src/index.js", "../dist-src/version.js", "../dist-src/is-plain-object.js", "../dist-src/fetch-wrapper.js", "../dist-src/get-buffer-response.js", "../dist-src/with-defaults.js"], "sourcesContent": ["import { endpoint } from \"@octokit/endpoint\";\nimport { getUserAgent } from \"universal-user-agent\";\nimport { VERSION } from \"./version.js\";\nimport withDefaults from \"./with-defaults.js\";\nconst request = withDefaults(endpoint, {\n  headers: {\n    \"user-agent\": `octokit-request.js/${VERSION} ${getUserAgent()}`\n  }\n});\nexport {\n  request\n};\n", "const VERSION = \"8.4.1\";\nexport {\n  VERSION\n};\n", "function isPlainObject(value) {\n  if (typeof value !== \"object\" || value === null)\n    return false;\n  if (Object.prototype.toString.call(value) !== \"[object Object]\")\n    return false;\n  const proto = Object.getPrototypeOf(value);\n  if (proto === null)\n    return true;\n  const Ctor = Object.prototype.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n  return typeof Ctor === \"function\" && Ctor instanceof Ctor && Function.prototype.call(Ctor) === Function.prototype.call(value);\n}\nexport {\n  isPlainObject\n};\n", "import { isPlainObject } from \"./is-plain-object.js\";\nimport { RequestError } from \"@octokit/request-error\";\nimport getBuffer from \"./get-buffer-response.js\";\nfunction fetchWrapper(requestOptions) {\n  const log = requestOptions.request && requestOptions.request.log ? requestOptions.request.log : console;\n  const parseSuccessResponseBody = requestOptions.request?.parseSuccessResponseBody !== false;\n  if (isPlainObject(requestOptions.body) || Array.isArray(requestOptions.body)) {\n    requestOptions.body = JSON.stringify(requestOptions.body);\n  }\n  let headers = {};\n  let status;\n  let url;\n  let { fetch } = globalThis;\n  if (requestOptions.request?.fetch) {\n    fetch = requestOptions.request.fetch;\n  }\n  if (!fetch) {\n    throw new Error(\n      \"fetch is not set. Please pass a fetch implementation as new Octokit({ request: { fetch }}). Learn more at https://github.com/octokit/octokit.js/#fetch-missing\"\n    );\n  }\n  return fetch(requestOptions.url, {\n    method: requestOptions.method,\n    body: requestOptions.body,\n    redirect: requestOptions.request?.redirect,\n    headers: requestOptions.headers,\n    signal: requestOptions.request?.signal,\n    // duplex must be set if request.body is ReadableStream or Async Iterables.\n    // See https://fetch.spec.whatwg.org/#dom-requestinit-duplex.\n    ...requestOptions.body && { duplex: \"half\" }\n  }).then(async (response) => {\n    url = response.url;\n    status = response.status;\n    for (const keyAndValue of response.headers) {\n      headers[keyAndValue[0]] = keyAndValue[1];\n    }\n    if (\"deprecation\" in headers) {\n      const matches = headers.link && headers.link.match(/<([^<>]+)>; rel=\"deprecation\"/);\n      const deprecationLink = matches && matches.pop();\n      log.warn(\n        `[@octokit/request] \"${requestOptions.method} ${requestOptions.url}\" is deprecated. It is scheduled to be removed on ${headers.sunset}${deprecationLink ? `. See ${deprecationLink}` : \"\"}`\n      );\n    }\n    if (status === 204 || status === 205) {\n      return;\n    }\n    if (requestOptions.method === \"HEAD\") {\n      if (status < 400) {\n        return;\n      }\n      throw new RequestError(response.statusText, status, {\n        response: {\n          url,\n          status,\n          headers,\n          data: void 0\n        },\n        request: requestOptions\n      });\n    }\n    if (status === 304) {\n      throw new RequestError(\"Not modified\", status, {\n        response: {\n          url,\n          status,\n          headers,\n          data: await getResponseData(response)\n        },\n        request: requestOptions\n      });\n    }\n    if (status >= 400) {\n      const data = await getResponseData(response);\n      const error = new RequestError(toErrorMessage(data), status, {\n        response: {\n          url,\n          status,\n          headers,\n          data\n        },\n        request: requestOptions\n      });\n      throw error;\n    }\n    return parseSuccessResponseBody ? await getResponseData(response) : response.body;\n  }).then((data) => {\n    return {\n      status,\n      url,\n      headers,\n      data\n    };\n  }).catch((error) => {\n    if (error instanceof RequestError)\n      throw error;\n    else if (error.name === \"AbortError\")\n      throw error;\n    let message = error.message;\n    if (error.name === \"TypeError\" && \"cause\" in error) {\n      if (error.cause instanceof Error) {\n        message = error.cause.message;\n      } else if (typeof error.cause === \"string\") {\n        message = error.cause;\n      }\n    }\n    throw new RequestError(message, 500, {\n      request: requestOptions\n    });\n  });\n}\nasync function getResponseData(response) {\n  const contentType = response.headers.get(\"content-type\");\n  if (/application\\/json/.test(contentType)) {\n    return response.json().catch(() => response.text()).catch(() => \"\");\n  }\n  if (!contentType || /^text\\/|charset=utf-8$/.test(contentType)) {\n    return response.text();\n  }\n  return getBuffer(response);\n}\nfunction toErrorMessage(data) {\n  if (typeof data === \"string\")\n    return data;\n  let suffix;\n  if (\"documentation_url\" in data) {\n    suffix = ` - ${data.documentation_url}`;\n  } else {\n    suffix = \"\";\n  }\n  if (\"message\" in data) {\n    if (Array.isArray(data.errors)) {\n      return `${data.message}: ${data.errors.map(JSON.stringify).join(\", \")}${suffix}`;\n    }\n    return `${data.message}${suffix}`;\n  }\n  return `Unknown error: ${JSON.stringify(data)}`;\n}\nexport {\n  fetchWrapper as default\n};\n", "function getBufferResponse(response) {\n  return response.arrayBuffer();\n}\nexport {\n  getBufferResponse as default\n};\n", "import fetchWrapper from \"./fetch-wrapper.js\";\nfunction withDefaults(oldEndpoint, newDefaults) {\n  const endpoint = oldEndpoint.defaults(newDefaults);\n  const newApi = function(route, parameters) {\n    const endpointOptions = endpoint.merge(route, parameters);\n    if (!endpointOptions.request || !endpointOptions.request.hook) {\n      return fetchWrapper(endpoint.parse(endpointOptions));\n    }\n    const request = (route2, parameters2) => {\n      return fetchWrapper(\n        endpoint.parse(endpoint.merge(route2, parameters2))\n      );\n    };\n    Object.assign(request, {\n      endpoint,\n      defaults: withDefaults.bind(null, endpoint)\n    });\n    return endpointOptions.request.hook(request, endpointOptions);\n  };\n  return Object.assign(newApi, {\n    endpoint,\n    defaults: withDefaults.bind(null, endpoint)\n  });\n}\nexport {\n  withDefaults as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAAyB;AACzB,kCAA6B;;;ACD7B,IAAM,UAAU;;;ACAhB,SAAS,cAAc,OAAO;AAC5B,MAAI,OAAO,UAAU,YAAY,UAAU;AACzC,WAAO;AACT,MAAI,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAC5C,WAAO;AACT,QAAM,QAAQ,OAAO,eAAe,KAAK;AACzC,MAAI,UAAU;AACZ,WAAO;AACT,QAAM,OAAO,OAAO,UAAU,eAAe,KAAK,OAAO,aAAa,KAAK,MAAM;AACjF,SAAO,OAAO,SAAS,cAAc,gBAAgB,QAAQ,SAAS,UAAU,KAAK,IAAI,MAAM,SAAS,UAAU,KAAK,KAAK;AAC9H;;;ACTA,2BAA6B;;;ACD7B,SAAS,kBAAkB,UAAU;AACnC,SAAO,SAAS,YAAY;AAC9B;;;ADCA,SAAS,aAAa,gBAAgB;AAHtC;AAIE,QAAM,MAAM,eAAe,WAAW,eAAe,QAAQ,MAAM,eAAe,QAAQ,MAAM;AAChG,QAAM,6BAA2B,oBAAe,YAAf,mBAAwB,8BAA6B;AACtF,MAAI,cAAc,eAAe,IAAI,KAAK,MAAM,QAAQ,eAAe,IAAI,GAAG;AAC5E,mBAAe,OAAO,KAAK,UAAU,eAAe,IAAI;AAAA,EAC1D;AACA,MAAI,UAAU,CAAC;AACf,MAAI;AACJ,MAAI;AACJ,MAAI,EAAE,MAAM,IAAI;AAChB,OAAI,oBAAe,YAAf,mBAAwB,OAAO;AACjC,YAAQ,eAAe,QAAQ;AAAA,EACjC;AACA,MAAI,CAAC,OAAO;AACV,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO,MAAM,eAAe,KAAK;AAAA,IAC/B,QAAQ,eAAe;AAAA,IACvB,MAAM,eAAe;AAAA,IACrB,WAAU,oBAAe,YAAf,mBAAwB;AAAA,IAClC,SAAS,eAAe;AAAA,IACxB,SAAQ,oBAAe,YAAf,mBAAwB;AAAA;AAAA;AAAA,IAGhC,GAAG,eAAe,QAAQ,EAAE,QAAQ,OAAO;AAAA,EAC7C,CAAC,EAAE,KAAK,OAAO,aAAa;AAC1B,UAAM,SAAS;AACf,aAAS,SAAS;AAClB,eAAW,eAAe,SAAS,SAAS;AAC1C,cAAQ,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC;AAAA,IACzC;AACA,QAAI,iBAAiB,SAAS;AAC5B,YAAM,UAAU,QAAQ,QAAQ,QAAQ,KAAK,MAAM,+BAA+B;AAClF,YAAM,kBAAkB,WAAW,QAAQ,IAAI;AAC/C,UAAI;AAAA,QACF,uBAAuB,eAAe,MAAM,IAAI,eAAe,GAAG,qDAAqD,QAAQ,MAAM,GAAG,kBAAkB,SAAS,eAAe,KAAK,EAAE;AAAA,MAC3L;AAAA,IACF;AACA,QAAI,WAAW,OAAO,WAAW,KAAK;AACpC;AAAA,IACF;AACA,QAAI,eAAe,WAAW,QAAQ;AACpC,UAAI,SAAS,KAAK;AAChB;AAAA,MACF;AACA,YAAM,IAAI,kCAAa,SAAS,YAAY,QAAQ;AAAA,QAClD,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA,MAAM;AAAA,QACR;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,WAAW,KAAK;AAClB,YAAM,IAAI,kCAAa,gBAAgB,QAAQ;AAAA,QAC7C,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA,MAAM,MAAM,gBAAgB,QAAQ;AAAA,QACtC;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,UAAU,KAAK;AACjB,YAAM,OAAO,MAAM,gBAAgB,QAAQ;AAC3C,YAAM,QAAQ,IAAI,kCAAa,eAAe,IAAI,GAAG,QAAQ;AAAA,QAC3D,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AACD,YAAM;AAAA,IACR;AACA,WAAO,2BAA2B,MAAM,gBAAgB,QAAQ,IAAI,SAAS;AAAA,EAC/E,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC,EAAE,MAAM,CAAC,UAAU;AAClB,QAAI,iBAAiB;AACnB,YAAM;AAAA,aACC,MAAM,SAAS;AACtB,YAAM;AACR,QAAI,UAAU,MAAM;AACpB,QAAI,MAAM,SAAS,eAAe,WAAW,OAAO;AAClD,UAAI,MAAM,iBAAiB,OAAO;AAChC,kBAAU,MAAM,MAAM;AAAA,MACxB,WAAW,OAAO,MAAM,UAAU,UAAU;AAC1C,kBAAU,MAAM;AAAA,MAClB;AAAA,IACF;AACA,UAAM,IAAI,kCAAa,SAAS,KAAK;AAAA,MACnC,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC;AACH;AACA,eAAe,gBAAgB,UAAU;AACvC,QAAM,cAAc,SAAS,QAAQ,IAAI,cAAc;AACvD,MAAI,oBAAoB,KAAK,WAAW,GAAG;AACzC,WAAO,SAAS,KAAK,EAAE,MAAM,MAAM,SAAS,KAAK,CAAC,EAAE,MAAM,MAAM,EAAE;AAAA,EACpE;AACA,MAAI,CAAC,eAAe,yBAAyB,KAAK,WAAW,GAAG;AAC9D,WAAO,SAAS,KAAK;AAAA,EACvB;AACA,SAAO,kBAAU,QAAQ;AAC3B;AACA,SAAS,eAAe,MAAM;AAC5B,MAAI,OAAO,SAAS;AAClB,WAAO;AACT,MAAI;AACJ,MAAI,uBAAuB,MAAM;AAC/B,aAAS,MAAM,KAAK,iBAAiB;AAAA,EACvC,OAAO;AACL,aAAS;AAAA,EACX;AACA,MAAI,aAAa,MAAM;AACrB,QAAI,MAAM,QAAQ,KAAK,MAAM,GAAG;AAC9B,aAAO,GAAG,KAAK,OAAO,KAAK,KAAK,OAAO,IAAI,KAAK,SAAS,EAAE,KAAK,IAAI,CAAC,GAAG,MAAM;AAAA,IAChF;AACA,WAAO,GAAG,KAAK,OAAO,GAAG,MAAM;AAAA,EACjC;AACA,SAAO,kBAAkB,KAAK,UAAU,IAAI,CAAC;AAC/C;;;AEvIA,SAAS,aAAa,aAAa,aAAa;AAC9C,QAAMA,YAAW,YAAY,SAAS,WAAW;AACjD,QAAM,SAAS,SAAS,OAAO,YAAY;AACzC,UAAM,kBAAkBA,UAAS,MAAM,OAAO,UAAU;AACxD,QAAI,CAAC,gBAAgB,WAAW,CAAC,gBAAgB,QAAQ,MAAM;AAC7D,aAAO,aAAaA,UAAS,MAAM,eAAe,CAAC;AAAA,IACrD;AACA,UAAMC,WAAU,CAAC,QAAQ,gBAAgB;AACvC,aAAO;AAAA,QACLD,UAAS,MAAMA,UAAS,MAAM,QAAQ,WAAW,CAAC;AAAA,MACpD;AAAA,IACF;AACA,WAAO,OAAOC,UAAS;AAAA,MACrB,UAAAD;AAAA,MACA,UAAU,aAAa,KAAK,MAAMA,SAAQ;AAAA,IAC5C,CAAC;AACD,WAAO,gBAAgB,QAAQ,KAAKC,UAAS,eAAe;AAAA,EAC9D;AACA,SAAO,OAAO,OAAO,QAAQ;AAAA,IAC3B,UAAAD;AAAA,IACA,UAAU,aAAa,KAAK,MAAMA,SAAQ;AAAA,EAC5C,CAAC;AACH;;;ALnBA,IAAM,UAAU,aAAa,0BAAU;AAAA,EACrC,SAAS;AAAA,IACP,cAAc,sBAAsB,OAAO,QAAI,0CAAa,CAAC;AAAA,EAC/D;AACF,CAAC;", "names": ["endpoint", "request"]}