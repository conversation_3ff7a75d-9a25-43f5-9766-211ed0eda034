{"version": 3, "file": "index.js", "sources": ["../../src/behaviors/behavior.types.action.ts", "../../src/behaviors/behavior.types.behavior.ts"], "sourcesContent": ["import type {EditorDom} from '../editor/editor-dom'\nimport type {EditorSnapshot} from '../editor/editor-snapshot'\nimport type {PickFromUnion} from '../type-utils'\nimport type {\n  CustomBehaviorEvent,\n  NativeBehaviorEvent,\n  SyntheticBehaviorEvent,\n} from './behavior.types.event'\n\n/**\n * @beta\n */\nexport type BehaviorAction =\n  | {\n      type: 'execute'\n      event: SyntheticBehaviorEvent\n    }\n  | {\n      type: 'forward'\n      event: NativeBehaviorEvent | SyntheticBehaviorEvent | CustomBehaviorEvent\n    }\n  | {\n      type: 'raise'\n      event: SyntheticBehaviorEvent | CustomBehaviorEvent\n    }\n  | {\n      type: 'effect'\n      effect: () => void\n    }\n\n/**\n * @beta\n */\nexport function execute(\n  event: SyntheticBehaviorEvent,\n): PickFromUnion<BehaviorAction, 'type', 'execute'> {\n  return {type: 'execute', event}\n}\n\n/**\n * @beta\n */\nexport function forward(\n  event: NativeBehaviorEvent | SyntheticBehaviorEvent | CustomBehaviorEvent,\n): PickFromUnion<BehaviorAction, 'type', 'forward'> {\n  return {type: 'forward', event}\n}\n\n/**\n * @beta\n */\nexport function raise(\n  event: SyntheticBehaviorEvent | CustomBehaviorEvent,\n): PickFromUnion<BehaviorAction, 'type', 'raise'> {\n  return {type: 'raise', event}\n}\n\n/**\n * @beta\n */\nexport function effect(\n  effect: () => void,\n): PickFromUnion<BehaviorAction, 'type', 'effect'> {\n  return {type: 'effect', effect}\n}\n\n/**\n * @beta\n */\nexport type BehaviorActionSet<TBehaviorEvent, TGuardResponse> = (\n  payload: {\n    snapshot: EditorSnapshot\n    event: TBehaviorEvent\n    dom: EditorDom\n  },\n  guardResponse: TGuardResponse,\n) => Array<BehaviorAction>\n", "import type {BehaviorActionSet} from './behavior.types.action'\nimport type {\n  BehaviorEvent,\n  BehaviorEventTypeNamespace,\n  CustomBehaviorEvent,\n  ResolveBehaviorEvent,\n} from './behavior.types.event'\nimport type {BehaviorGuard} from './behavior.types.guard'\n\n/**\n * @beta\n */\nexport type Behavior<\n  TBehaviorEventType extends\n    | '*'\n    | `${BehaviorEventTypeNamespace}.*`\n    | BehaviorEvent['type'] =\n    | '*'\n    | `${BehaviorEventTypeNamespace}.*`\n    | BehaviorEvent['type'],\n  TGuardResponse = true,\n  TBehaviorEvent extends\n    ResolveBehaviorEvent<TBehaviorEventType> = ResolveBehaviorEvent<TBehaviorEventType>,\n> = {\n  /**\n   * Editor Event that triggers this Behavior.\n   */\n  on: TBehaviorEventType\n  /**\n   * Predicate function that determines if the Behavior should be executed.\n   * Returning a non-nullable value from the guard will pass the value to the\n   * actions and execute them.\n   */\n  guard?: BehaviorGuard<TBehaviorEvent, TGuardResponse>\n  /**\n   * Array of Behavior Action sets.\n   * Each set represents a step in the history stack.\n   */\n  actions: Array<BehaviorActionSet<TBehaviorEvent, TGuardResponse>>\n}\n\n/**\n * @beta\n *\n * @example\n *\n * ```tsx\n * const noLowerCaseA = defineBehavior({\n *   on: 'insert.text',\n *   guard: ({event, snapshot}) => event.text === 'a',\n *   actions: [({event, snapshot}) => [{type: 'insert.text', text: 'A'}]],\n * })\n * ```\n *\n */\nexport function defineBehavior<\n  TPayload extends Record<string, unknown>,\n  TBehaviorEventType extends\n    | '*'\n    | `${BehaviorEventTypeNamespace}.*`\n    | BehaviorEvent['type'] = CustomBehaviorEvent['type'],\n  TGuardResponse = true,\n>(\n  behavior: Behavior<\n    TBehaviorEventType,\n    TGuardResponse,\n    ResolveBehaviorEvent<TBehaviorEventType, TPayload>\n  >,\n): Behavior\nexport function defineBehavior<\n  TPayload extends never = never,\n  TBehaviorEventType extends\n    | '*'\n    | `${BehaviorEventTypeNamespace}.*`\n    | BehaviorEvent['type'] = BehaviorEvent['type'],\n  TGuardResponse = true,\n  TBehaviorEvent extends ResolveBehaviorEvent<\n    TBehaviorEventType,\n    TPayload\n  > = ResolveBehaviorEvent<TBehaviorEventType, TPayload>,\n>(\n  behavior: Behavior<TBehaviorEventType, TGuardResponse, TBehaviorEvent>,\n): Behavior {\n  return behavior as unknown as Behavior\n}\n"], "names": ["execute", "event", "type", "forward", "raise", "effect", "define<PERSON>eh<PERSON>or", "behavior"], "mappings": "AAiCO,SAASA,QACdC,OACkD;AAClD,SAAO;AAAA,IAACC,MAAM;AAAA,IAAWD;AAAAA,EAAAA;AAC3B;AAKO,SAASE,QACdF,OACkD;AAClD,SAAO;AAAA,IAACC,MAAM;AAAA,IAAWD;AAAAA,EAAAA;AAC3B;AAKO,SAASG,MACdH,OACgD;AAChD,SAAO;AAAA,IAACC,MAAM;AAAA,IAASD;AAAAA,EAAAA;AACzB;AAKO,SAASI,OACdA,SACiD;AACjD,SAAO;AAAA,IAACH,MAAM;AAAA,IAAUG,QAAAA;AAAAA,EAAAA;AAC1B;ACKO,SAASC,eAYdC,UACU;AACV,SAAOA;AACT;"}