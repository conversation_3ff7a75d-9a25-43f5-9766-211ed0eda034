{"version": 3, "file": "_path.cjs", "sources": ["../src/path/utils/normalize.ts"], "sourcesContent": ["import {parse} from '../parser/parse'\nimport {type Path} from '../types'\n\nexport function normalize(path: string | Readonly<Path>): Readonly<Path> {\n  return typeof path === 'string' ? parse(path) : path\n}\n"], "names": ["parse"], "mappings": ";;;AAGO,SAAS,UAAU,MAA+C;AACvE,SAAO,OAAO,QAAS,WAAWA,MAAAA,MAAM,IAAI,IAAI;AAClD;;;;;;;;;;;;;"}