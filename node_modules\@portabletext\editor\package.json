{"name": "@portabletext/editor", "version": "2.3.8", "description": "Portable Text Editor made in React", "keywords": ["sanity", "realtime", "content", "portable-text-editor", "structure", "collaborative", "editor", "text", "portable-text"], "homepage": "https://www.sanity.io/", "bugs": {"url": "https://github.com/portabletext/editor/issues"}, "repository": {"type": "git", "url": "git+https://github.com/portabletext/editor.git", "directory": "packages/editor"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"source": "./src/index.ts", "import": "./lib/index.js", "require": "./lib/index.cjs", "default": "./lib/index.js"}, "./behaviors": {"source": "./src/behaviors/_exports/index.ts", "import": "./lib/behaviors/index.js", "require": "./lib/behaviors/index.cjs", "default": "./lib/behaviors/index.js"}, "./plugins": {"source": "./src/plugins/index.ts", "import": "./lib/plugins/index.js", "require": "./lib/plugins/index.cjs", "default": "./lib/plugins/index.js"}, "./selectors": {"source": "./src/selectors/_exports/index.ts", "import": "./lib/selectors/index.js", "require": "./lib/selectors/index.cjs", "default": "./lib/selectors/index.js"}, "./utils": {"source": "./src/utils/_exports/index.ts", "import": "./lib/utils/index.js", "require": "./lib/utils/index.cjs", "default": "./lib/utils/index.js"}, "./package.json": "./package.json"}, "main": "./lib/index.cjs", "module": "./lib/index.js", "types": "./lib/index.d.ts", "files": ["lib", "src"], "dependencies": {"@portabletext/to-html": "^2.0.14", "@xstate/react": "^6.0.0", "debug": "^4.4.1", "get-random-values-esm": "^1.0.2", "immer": "^10.1.1", "lodash": "^4.17.21", "lodash.startcase": "^4.4.0", "react-compiler-runtime": "19.1.0-rc.2", "slate": "0.118.0", "slate-dom": "^0.117.4", "slate-react": "0.117.4", "xstate": "^5.20.2", "@portabletext/block-tools": "^3.2.1", "@portabletext/keyboard-shortcuts": "^1.1.1", "@portabletext/patches": "^1.1.6", "@portabletext/schema": "^1.0.0"}, "devDependencies": {"@sanity/diff-match-patch": "^3.2.0", "@sanity/pkg-utils": "^7.11.1", "@sanity/schema": "^4.4.1", "@sanity/types": "^4.4.1", "@testing-library/react": "^16.3.0", "@types/debug": "^4.1.12", "@types/lodash": "^4.17.16", "@types/lodash.startcase": "^4.4.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "@vitejs/plugin-react": "^4.7.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-istanbul": "^3.2.4", "babel-plugin-react-compiler": "19.1.0-rc.2", "eslint": "8.57.1", "eslint-plugin-react-hooks": "6.0.0-rc.1", "jsdom": "^26.0.0", "react": "^19.1.1", "react-dom": "^19.1.1", "rxjs": "^7.8.2", "typescript": "5.9.2", "vite": "^7.0.6", "vitest": "^3.2.4", "vitest-browser-react": "^1.0.1", "@portabletext/sanity-bridge": "1.1.2", "racejar": "1.2.12"}, "peerDependencies": {"@sanity/schema": "^4.4.1", "@sanity/types": "^4.4.1", "react": "^18.3 || ^19", "rxjs": "^7.8.2", "@portabletext/sanity-bridge": "^1.1.2"}, "engines": {"node": ">=20.19 <22 || >=22.12"}, "publishConfig": {"access": "public"}, "scripts": {"build": "pkg-utils build --strict --check --clean", "check:lint": "biome lint .", "check:react-compiler": "eslint --cache --no-inline-config --no-eslintrc --ignore-pattern '**/__tests__/**' --ext .cjs,.mjs,.js,.jsx,.ts,.tsx --parser @typescript-eslint/parser --plugin react-hooks --rule 'react-hooks/react-compiler: [error]' --rule 'react-hooks/rules-of-hooks: [error]' --rule 'react-hooks/exhaustive-deps: [error]' src", "check:types": "tsc", "check:types:watch": "tsc --watch", "clean": "del .turbo && del lib && del node_modules", "dev": "pkg-utils watch", "lint:fix": "biome lint --write .", "test": "vitest --run", "test:browser": "vitest --run --project browser", "test:browser:chromium": "vitest --run --project \"browser (chromium)\"", "test:browser:chromium:watch": "vitest --project \"browser (chromium)\"", "test:browser:firefox": "vitest --run --project \"browser (firefox)\"", "test:browser:firefox:watch": "vitest --project \"browser (firefox)\"", "test:browser:watch": "vitest --project browser", "test:browser:webkit": "vitest --run --project \"browser (webkit)\"", "test:browser:webkit:watch": "vitest --project \"browser (webkit)\"", "test:e2e:chromium": "vitest --run --project \"browser.bak (chromium)\"", "test:e2e:chromium:watch": "vitest --project \"browser.bak (chromium)\"", "test:unit": "vitest --run --project unit", "test:unit:watch": "vitest --project unit", "test:watch": "vitest"}}