{"version": 3, "file": "index.js", "sources": ["../../src/plugins/plugin.behavior.tsx", "../../src/internal-utils/get-text-to-emphasize.ts", "../../src/behaviors/behavior.decorator-pair.ts", "../../src/plugins/plugin.decorator-shortcut.ts", "../../src/plugins/plugin.editor-ref.tsx", "../../src/plugins/plugin.event-listener.tsx", "../../src/behaviors/behavior.markdown.ts", "../../src/plugins/plugin.markdown.tsx", "../../src/plugins/plugin.one-line.tsx"], "sourcesContent": ["import {useEffect} from 'react'\nimport type {Behavior} from '../behaviors'\nimport {useEditor} from '../editor/use-editor'\n\n/**\n * @beta\n */\nexport function BehaviorPlugin(props: {behaviors: Array<Behavior>}) {\n  const editor = useEditor()\n\n  useEffect(() => {\n    const unregisterBehaviors = props.behaviors.map((behavior) =>\n      editor.registerBehavior({behavior}),\n    )\n\n    return () => {\n      unregisterBehaviors.forEach((unregister) => unregister())\n    }\n  }, [editor, props.behaviors])\n\n  return null\n}\n", "export function createPairRegex(char: string, amount: number) {\n  // Negative lookbehind: Ensures that the matched sequence is not preceded by the same character\n  const prePrefix = `(?<!\\\\${char})`\n\n  // Repeats the character `amount` times\n  const prefix = `\\\\${char}`.repeat(Math.max(amount, 1))\n\n  // Negative lookahead: Ensures that the opening pair (**, *, etc.) is not followed by a space\n  const postPrefix = `(?!\\\\s)`\n\n  // Captures the content inside the pair\n  const content = `([^${char}\\\\n]+?)`\n\n  // Negative lookbehind: Ensures that the content is not followed by a space\n  const preSuffix = `(?<!\\\\s)`\n\n  // Repeats the character `amount` times\n  const suffix = `\\\\${char}`.repeat(Math.max(amount, 1))\n\n  // Negative lookahead: Ensures that the matched sequence is not followed by the same character\n  const postSuffix = `(?!\\\\${char})`\n\n  return `${prePrefix}${prefix}${postPrefix}${content}${preSuffix}${suffix}${postSuffix}`\n}\n\nconst italicRegex = new RegExp(\n  `(${createPairRegex('*', 1)}|${createPairRegex('_', 1)})$`,\n)\n\nconst boldRegex = new RegExp(\n  `(${createPairRegex('*', 2)}|${createPairRegex('_', 2)})$`,\n)\n\nexport function getTextToItalic(text: string) {\n  return text.match(italicRegex)?.at(0)\n}\n\nexport function getTextToBold(text: string) {\n  return text.match(boldRegex)?.at(0)\n}\n", "import type {EditorSchema} from '../editor/editor-schema'\nimport {createPairRegex} from '../internal-utils/get-text-to-emphasize'\nimport * as selectors from '../selectors'\nimport type {BlockOffset} from '../types/block-offset'\nimport * as utils from '../utils'\nimport {effect, execute} from './behavior.types.action'\nimport {defineBehavior} from './behavior.types.behavior'\n\nexport function createDecoratorPairBehavior(config: {\n  decorator: ({schema}: {schema: EditorSchema}) => string | undefined\n  pair: {char: string; amount: number}\n  onDecorate: (offset: BlockOffset) => void\n}) {\n  if (config.pair.amount < 1) {\n    console.warn(\n      `The amount of characters in the pair should be greater than 0`,\n    )\n  }\n\n  const pairRegex = createPairRegex(config.pair.char, config.pair.amount)\n  const regEx = new RegExp(`(${pairRegex})$`)\n\n  return defineBehavior({\n    on: 'insert.text',\n    guard: ({snapshot, event}) => {\n      if (config.pair.amount < 1) {\n        return false\n      }\n\n      const decorator = config.decorator({schema: snapshot.context.schema})\n\n      if (decorator === undefined) {\n        return false\n      }\n\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n      const selectionStartPoint = selectors.getSelectionStartPoint(snapshot)\n      const selectionStartOffset = selectionStartPoint\n        ? utils.spanSelectionPointToBlockOffset({\n            context: {\n              schema: snapshot.context.schema,\n              value: snapshot.context.value,\n            },\n            selectionPoint: selectionStartPoint,\n          })\n        : undefined\n\n      if (!focusTextBlock || !selectionStartOffset) {\n        return false\n      }\n\n      const textBefore = selectors.getBlockTextBefore(snapshot)\n      const newText = `${textBefore}${event.text}`\n      const textToDecorate = newText.match(regEx)?.at(0)\n\n      if (textToDecorate === undefined) {\n        return false\n      }\n\n      const prefixOffsets = {\n        anchor: {\n          path: focusTextBlock.path,\n          // Example: \"foo **bar**\".length - \"**bar**\".length = 4\n          offset: newText.length - textToDecorate.length,\n        },\n        focus: {\n          path: focusTextBlock.path,\n          // Example: \"foo **bar**\".length - \"**bar**\".length + \"*\".length * 2 = 6\n          offset:\n            newText.length -\n            textToDecorate.length +\n            config.pair.char.length * config.pair.amount,\n        },\n      }\n\n      const suffixOffsets = {\n        anchor: {\n          path: focusTextBlock.path,\n          // Example: \"foo **bar*|\" (10) + \"*\".length - 2 = 9\n          offset:\n            selectionStartOffset.offset +\n            event.text.length -\n            config.pair.char.length * config.pair.amount,\n        },\n        focus: {\n          path: focusTextBlock.path,\n          // Example: \"foo **bar*|\" (10) + \"*\".length = 11\n          offset: selectionStartOffset.offset + event.text.length,\n        },\n      }\n\n      // If the prefix is more than one character, then we need to check if\n      // there is an inline object inside it\n      if (prefixOffsets.focus.offset - prefixOffsets.anchor.offset > 1) {\n        const prefixSelection = utils.blockOffsetsToSelection({\n          context: snapshot.context,\n          offsets: prefixOffsets,\n        })\n        const inlineObjectBeforePrefixFocus = selectors.getPreviousInlineObject(\n          {\n            ...snapshot,\n            context: {\n              ...snapshot.context,\n              selection: prefixSelection\n                ? {\n                    anchor: prefixSelection.focus,\n                    focus: prefixSelection.focus,\n                  }\n                : null,\n            },\n          },\n        )\n        const inlineObjectBeforePrefixFocusOffset =\n          inlineObjectBeforePrefixFocus\n            ? utils.childSelectionPointToBlockOffset({\n                context: {\n                  schema: snapshot.context.schema,\n                  value: snapshot.context.value,\n                },\n                selectionPoint: {\n                  path: inlineObjectBeforePrefixFocus.path,\n                  offset: 0,\n                },\n              })\n            : undefined\n\n        if (\n          inlineObjectBeforePrefixFocusOffset &&\n          inlineObjectBeforePrefixFocusOffset.offset >\n            prefixOffsets.anchor.offset &&\n          inlineObjectBeforePrefixFocusOffset.offset <\n            prefixOffsets.focus.offset\n        ) {\n          return false\n        }\n      }\n\n      // If the suffix is more than one character, then we need to check if\n      // there is an inline object inside it\n      if (suffixOffsets.focus.offset - suffixOffsets.anchor.offset > 1) {\n        const previousInlineObject = selectors.getPreviousInlineObject(snapshot)\n        const previousInlineObjectOffset = previousInlineObject\n          ? utils.childSelectionPointToBlockOffset({\n              context: {\n                schema: snapshot.context.schema,\n                value: snapshot.context.value,\n              },\n              selectionPoint: {\n                path: previousInlineObject.path,\n                offset: 0,\n              },\n            })\n          : undefined\n\n        if (\n          previousInlineObjectOffset &&\n          previousInlineObjectOffset.offset > suffixOffsets.anchor.offset &&\n          previousInlineObjectOffset.offset < suffixOffsets.focus.offset\n        ) {\n          return false\n        }\n      }\n\n      return {\n        prefixOffsets,\n        suffixOffsets,\n        decorator,\n      }\n    },\n    actions: [\n      // Insert the text as usual in its own undo step\n      ({event}) => [execute(event)],\n      (_, {prefixOffsets, suffixOffsets, decorator}) => [\n        // Decorate the text between the prefix and suffix\n        execute({\n          type: 'decorator.add',\n          decorator,\n          at: {\n            anchor: prefixOffsets.focus,\n            focus: suffixOffsets.anchor,\n          },\n        }),\n        // Delete the suffix\n        execute({\n          type: 'delete.text',\n          at: suffixOffsets,\n        }),\n        // Delete the prefix\n        execute({\n          type: 'delete.text',\n          at: prefixOffsets,\n        }),\n        // Toggle the decorator off so the next inserted text isn't emphasized\n        execute({\n          type: 'decorator.remove',\n          decorator,\n        }),\n        effect(() => {\n          config.onDecorate({\n            ...suffixOffsets.anchor,\n            offset:\n              suffixOffsets.anchor.offset -\n              (prefixOffsets.focus.offset - prefixOffsets.anchor.offset),\n          })\n        }),\n      ],\n    ],\n  })\n}\n", "import {useActorRef} from '@xstate/react'\nimport {isEqual} from 'lodash'\nimport {\n  assign,\n  fromCallback,\n  setup,\n  type AnyEventObject,\n  type CallbackLogicFunction,\n} from 'xstate'\nimport {createDecoratorPairBehavior} from '../behaviors/behavior.decorator-pair'\nimport {effect, execute, forward} from '../behaviors/behavior.types.action'\nimport {defineBehavior} from '../behaviors/behavior.types.behavior'\nimport type {Editor} from '../editor'\nimport type {EditorSchema} from '../editor/editor-schema'\nimport {useEditor} from '../editor/use-editor'\nimport type {BlockOffset} from '../types/block-offset'\nimport * as utils from '../utils'\n\n/**\n * @beta\n * @deprecated Install the plugin from `@portabletext/plugin-character-pair-decorator`\n */\nexport function DecoratorShortcutPlugin(config: {\n  decorator: ({schema}: {schema: EditorSchema}) => string | undefined\n  pair: {char: string; amount: number}\n}) {\n  const editor = useEditor()\n\n  useActorRef(decoratorPairMachine, {\n    input: {\n      editor,\n      decorator: config.decorator,\n      pair: config.pair,\n    },\n  })\n\n  return null\n}\n\ntype MarkdownEmphasisEvent =\n  | {\n      type: 'emphasis.add'\n      blockOffset: BlockOffset\n    }\n  | {\n      type: 'selection'\n      blockOffsets?: {\n        anchor: BlockOffset\n        focus: BlockOffset\n      }\n    }\n  | {\n      type: 'delete.backward'\n    }\n\nconst emphasisListener: CallbackLogicFunction<\n  AnyEventObject,\n  MarkdownEmphasisEvent,\n  {\n    decorator: ({schema}: {schema: EditorSchema}) => string | undefined\n    editor: Editor\n    pair: {char: string; amount: number}\n  }\n> = ({sendBack, input}) => {\n  const unregister = input.editor.registerBehavior({\n    behavior: createDecoratorPairBehavior({\n      decorator: input.decorator,\n      pair: input.pair,\n      onDecorate: (offset) => {\n        sendBack({type: 'emphasis.add', blockOffset: offset})\n      },\n    }),\n  })\n\n  return unregister\n}\n\nconst selectionListenerCallback: CallbackLogicFunction<\n  AnyEventObject,\n  MarkdownEmphasisEvent,\n  {editor: Editor}\n> = ({sendBack, input}) => {\n  const unregister = input.editor.registerBehavior({\n    behavior: defineBehavior({\n      on: 'select',\n      guard: ({snapshot, event}) => {\n        if (!event.at) {\n          return {blockOffsets: undefined}\n        }\n\n        const anchor = utils.spanSelectionPointToBlockOffset({\n          context: snapshot.context,\n          selectionPoint: event.at.anchor,\n        })\n        const focus = utils.spanSelectionPointToBlockOffset({\n          context: snapshot.context,\n          selectionPoint: event.at.focus,\n        })\n\n        if (!anchor || !focus) {\n          return {blockOffsets: undefined}\n        }\n\n        return {\n          blockOffsets: {\n            anchor,\n            focus,\n          },\n        }\n      },\n      actions: [\n        ({event}, {blockOffsets}) => [\n          {\n            type: 'effect',\n            effect: () => {\n              sendBack({type: 'selection', blockOffsets})\n            },\n          },\n          forward(event),\n        ],\n      ],\n    }),\n  })\n\n  return unregister\n}\n\nconst deleteBackwardListenerCallback: CallbackLogicFunction<\n  AnyEventObject,\n  MarkdownEmphasisEvent,\n  {editor: Editor}\n> = ({sendBack, input}) => {\n  const unregister = input.editor.registerBehavior({\n    behavior: defineBehavior({\n      on: 'delete.backward',\n      actions: [\n        () => [\n          execute({\n            type: 'history.undo',\n          }),\n          effect(() => {\n            sendBack({type: 'delete.backward'})\n          }),\n        ],\n      ],\n    }),\n  })\n\n  return unregister\n}\n\nconst decoratorPairMachine = setup({\n  types: {\n    context: {} as {\n      decorator: ({schema}: {schema: EditorSchema}) => string | undefined\n      editor: Editor\n      offsetAfterEmphasis?: BlockOffset\n      pair: {char: string; amount: number}\n    },\n    input: {} as {\n      decorator: ({schema}: {schema: EditorSchema}) => string | undefined\n      editor: Editor\n      pair: {char: string; amount: number}\n    },\n    events: {} as MarkdownEmphasisEvent,\n  },\n  actors: {\n    'emphasis listener': fromCallback(emphasisListener),\n    'delete.backward listener': fromCallback(deleteBackwardListenerCallback),\n    'selection listener': fromCallback(selectionListenerCallback),\n  },\n}).createMachine({\n  id: 'decorator pair',\n  context: ({input}) => ({\n    decorator: input.decorator,\n    editor: input.editor,\n    pair: input.pair,\n  }),\n  initial: 'idle',\n  states: {\n    'idle': {\n      invoke: [\n        {\n          src: 'emphasis listener',\n          input: ({context}) => ({\n            decorator: context.decorator,\n            editor: context.editor,\n            pair: context.pair,\n          }),\n        },\n      ],\n      on: {\n        'emphasis.add': {\n          target: 'emphasis added',\n          actions: assign({\n            offsetAfterEmphasis: ({event}) => event.blockOffset,\n          }),\n        },\n      },\n    },\n    'emphasis added': {\n      exit: [\n        assign({\n          offsetAfterEmphasis: undefined,\n        }),\n      ],\n      invoke: [\n        {\n          src: 'selection listener',\n          input: ({context}) => ({editor: context.editor}),\n        },\n        {\n          src: 'delete.backward listener',\n          input: ({context}) => ({editor: context.editor}),\n        },\n      ],\n      on: {\n        'selection': {\n          target: 'idle',\n          guard: ({context, event}) => {\n            const selectionChanged = !isEqual(\n              {\n                anchor: context.offsetAfterEmphasis,\n                focus: context.offsetAfterEmphasis,\n              },\n              event.blockOffsets,\n            )\n\n            return selectionChanged\n          },\n        },\n        'delete.backward': {\n          target: 'idle',\n        },\n      },\n    },\n  },\n})\n", "import React from 'react'\nimport type {Editor} from '../editor'\nimport {useEditor} from '../editor/use-editor'\n\n/**\n * @beta\n */\nexport const EditorRefPlugin = React.forwardRef<Editor | null>((_, ref) => {\n  const editor = useEditor()\n\n  const portableTextEditorRef = React.useRef(editor)\n\n  React.useImperativeHandle(ref, () => portableTextEditorRef.current, [])\n\n  return null\n})\nEditorRefPlugin.displayName = 'EditorRefPlugin'\n", "import {useEffect} from 'react'\nimport type {EditorEmittedEvent} from '../editor/relay-machine'\nimport {useEditor} from '../editor/use-editor'\n\n/**\n * @public\n * Listen for events emitted by the editor. Must be used inside `EditorProvider`. Events available include:\n *  - 'blurred'\n *  - 'done loading'\n *  - 'editable'\n *  - 'error'\n *  - 'focused'\n *  - 'invalid value'\n *  - 'loading'\n *  - 'mutation'\n *  - 'patch'\n *  - 'read only'\n *  - 'ready'\n *  - 'selection'\n *  - 'value changed'\n *\n * @example\n * Listen and log events.\n * ```tsx\n * import {EditorProvider} from '@portabletext/editor'\n * import {EventListenerPlugin} from '@portabletext/editor/plugins'\n *\n * function MyComponent() {\n *  return (\n *  <EditorProvider>\n *   <EventListenerPlugin\n *    on={(event) => {\n *     console.log(event)\n *    }\n *   } />\n *   { ... }\n * </EditorProvider>\n *  )\n * }\n * ```\n * @example\n * Handle events when there is a mutation.\n * ```tsx\n * <EventListenerPlugin\n *  on={(event) => {\n *    if (event.type === 'mutation') {\n *      console.log('Value changed:', event.snapshot)\n *    }\n *  }}\n * />\n * ```\n * @group Components\n */\nexport function EventListenerPlugin(props: {\n  on: (event: EditorEmittedEvent) => void\n}) {\n  const editor = useEditor()\n\n  useEffect(() => {\n    const subscription = editor.on('*', props.on)\n\n    return () => {\n      subscription.unsubscribe()\n    }\n  }, [editor, props.on])\n\n  return null\n}\n", "import type {EditorSchema} from '../editor/editor-schema'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport * as selectors from '../selectors'\nimport {spanSelectionPointToBlockOffset} from '../utils/util.block-offset'\nimport {getTextBlockText} from '../utils/util.get-text-block-text'\nimport {execute} from './behavior.types.action'\nimport {defineBehavior} from './behavior.types.behavior'\n\nexport type MarkdownBehaviorsConfig = {\n  horizontalRuleObject?: (context: {\n    schema: EditorSchema\n  }) => {name: string; value?: {[prop: string]: unknown}} | undefined\n  defaultStyle?: (context: {schema: EditorSchema}) => string | undefined\n  headingStyle?: (context: {\n    schema: EditorSchema\n    level: number\n  }) => string | undefined\n  blockquoteStyle?: (context: {schema: EditorSchema}) => string | undefined\n  unorderedListStyle?: (context: {schema: EditorSchema}) => string | undefined\n  orderedListStyle?: (context: {schema: EditorSchema}) => string | undefined\n}\n\nexport function createMarkdownBehaviors(config: MarkdownBehaviorsConfig) {\n  const automaticBlockquoteOnSpace = defineBehavior({\n    on: 'insert.text',\n    guard: ({snapshot, event}) => {\n      const isSpace = event.text === ' '\n\n      if (!isSpace) {\n        return false\n      }\n\n      const selectionCollapsed = selectors.isSelectionCollapsed(snapshot)\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n      const focusSpan = selectors.getFocusSpan(snapshot)\n\n      if (!selectionCollapsed || !focusTextBlock || !focusSpan) {\n        return false\n      }\n\n      const previousInlineObject = selectors.getPreviousInlineObject(snapshot)\n      const blockOffset = spanSelectionPointToBlockOffset({\n        context: snapshot.context,\n        selectionPoint: {\n          path: [\n            {_key: focusTextBlock.node._key},\n            'children',\n            {_key: focusSpan.node._key},\n          ],\n          offset: snapshot.context.selection?.focus.offset ?? 0,\n        },\n      })\n\n      if (previousInlineObject || !blockOffset) {\n        return false\n      }\n\n      const blockText = getTextBlockText(focusTextBlock.node)\n      const caretAtTheEndOfQuote = blockOffset.offset === 1\n      const looksLikeMarkdownQuote = /^>/.test(blockText)\n      const blockquoteStyle = config.blockquoteStyle?.(snapshot.context)\n\n      if (\n        caretAtTheEndOfQuote &&\n        looksLikeMarkdownQuote &&\n        blockquoteStyle !== undefined\n      ) {\n        return {focusTextBlock, style: blockquoteStyle}\n      }\n\n      return false\n    },\n    actions: [\n      () => [\n        execute({\n          type: 'insert.text',\n          text: ' ',\n        }),\n      ],\n      (_, {focusTextBlock, style}) => [\n        execute({\n          type: 'block.unset',\n          props: ['listItem', 'level'],\n          at: focusTextBlock.path,\n        }),\n        execute({\n          type: 'block.set',\n          props: {style},\n          at: focusTextBlock.path,\n        }),\n        execute({\n          type: 'delete.text',\n          at: {\n            anchor: {\n              path: focusTextBlock.path,\n              offset: 0,\n            },\n            focus: {\n              path: focusTextBlock.path,\n              offset: 2,\n            },\n          },\n        }),\n      ],\n    ],\n  })\n  const automaticHr = defineBehavior({\n    on: 'insert.text',\n    guard: ({snapshot, event}) => {\n      const hrCharacter =\n        event.text === '-'\n          ? '-'\n          : event.text === '*'\n            ? '*'\n            : event.text === '_'\n              ? '_'\n              : undefined\n\n      if (hrCharacter === undefined) {\n        return false\n      }\n\n      const hrObject = config.horizontalRuleObject?.(snapshot.context)\n      const focusBlock = selectors.getFocusTextBlock(snapshot)\n      const selectionCollapsed = selectors.isSelectionCollapsed(snapshot)\n\n      if (!hrObject || !focusBlock || !selectionCollapsed) {\n        return false\n      }\n\n      const previousInlineObject = selectors.getPreviousInlineObject(snapshot)\n      const textBefore = selectors.getBlockTextBefore(snapshot)\n      const hrBlockOffsets = {\n        anchor: {\n          path: focusBlock.path,\n          offset: 0,\n        },\n        focus: {\n          path: focusBlock.path,\n          offset: 3,\n        },\n      }\n\n      if (\n        !previousInlineObject &&\n        textBefore === `${hrCharacter}${hrCharacter}`\n      ) {\n        return {hrObject, focusBlock, hrCharacter, hrBlockOffsets}\n      }\n\n      return false\n    },\n    actions: [\n      (_, {hrCharacter}) => [\n        execute({\n          type: 'insert.text',\n          text: hrCharacter,\n        }),\n      ],\n      (_, {hrObject, hrBlockOffsets}) => [\n        execute({\n          type: 'insert.block',\n          placement: 'before',\n          block: {\n            _type: hrObject.name,\n            ...(hrObject.value ?? {}),\n          },\n        }),\n        execute({\n          type: 'delete.text',\n          at: hrBlockOffsets,\n        }),\n      ],\n    ],\n  })\n  const automaticHrOnPaste = defineBehavior({\n    on: 'clipboard.paste',\n    guard: ({snapshot, event}) => {\n      const text = event.originEvent.dataTransfer.getData('text/plain')\n      const hrRegExp = /^(---)$|(___)$|(\\*\\*\\*)$/\n      const hrCharacters = text.match(hrRegExp)?.[0]\n      const hrObject = config.horizontalRuleObject?.(snapshot.context)\n      const focusBlock = selectors.getFocusBlock(snapshot)\n\n      if (!hrCharacters || !hrObject || !focusBlock) {\n        return false\n      }\n\n      return {hrCharacters, hrObject, focusBlock}\n    },\n    actions: [\n      (_, {hrCharacters}) => [\n        execute({\n          type: 'insert.text',\n          text: hrCharacters,\n        }),\n      ],\n      ({snapshot}, {hrObject, focusBlock}) =>\n        isTextBlock(snapshot.context, focusBlock.node)\n          ? [\n              execute({\n                type: 'insert.block',\n                block: {\n                  _type: snapshot.context.schema.block.name,\n                  children: focusBlock.node.children,\n                },\n                placement: 'after',\n              }),\n              execute({\n                type: 'insert.block',\n                block: {\n                  _type: hrObject.name,\n                  ...(hrObject.value ?? {}),\n                },\n                placement: 'after',\n              }),\n              execute({\n                type: 'delete.block',\n                at: focusBlock.path,\n              }),\n            ]\n          : [\n              execute({\n                type: 'insert.block',\n                block: {\n                  _type: hrObject.name,\n                  ...(hrObject.value ?? {}),\n                },\n                placement: 'after',\n              }),\n            ],\n    ],\n  })\n  const automaticHeadingOnSpace = defineBehavior({\n    on: 'insert.text',\n    guard: ({snapshot, event}) => {\n      const isSpace = event.text === ' '\n\n      if (!isSpace) {\n        return false\n      }\n\n      const selectionCollapsed = selectors.isSelectionCollapsed(snapshot)\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n      const focusSpan = selectors.getFocusSpan(snapshot)\n\n      if (!selectionCollapsed || !focusTextBlock || !focusSpan) {\n        return false\n      }\n\n      const blockOffset = spanSelectionPointToBlockOffset({\n        context: snapshot.context,\n        selectionPoint: {\n          path: [\n            {_key: focusTextBlock.node._key},\n            'children',\n            {_key: focusSpan.node._key},\n          ],\n          offset: snapshot.context.selection?.focus.offset ?? 0,\n        },\n      })\n\n      if (!blockOffset) {\n        return false\n      }\n\n      const previousInlineObject = selectors.getPreviousInlineObject(snapshot)\n      const blockText = getTextBlockText(focusTextBlock.node)\n      const markdownHeadingSearch = /^#+/.exec(blockText)\n      const level = markdownHeadingSearch\n        ? markdownHeadingSearch[0].length\n        : undefined\n      const caretAtTheEndOfHeading = blockOffset.offset === level\n\n      if (previousInlineObject || !caretAtTheEndOfHeading) {\n        return false\n      }\n\n      const style =\n        level !== undefined\n          ? config.headingStyle?.({schema: snapshot.context.schema, level})\n          : undefined\n\n      if (level !== undefined && style !== undefined) {\n        return {\n          focusTextBlock,\n          style: style,\n          level,\n        }\n      }\n\n      return false\n    },\n    actions: [\n      ({event}) => [execute(event)],\n      (_, {focusTextBlock, style, level}) => [\n        execute({\n          type: 'block.unset',\n          props: ['listItem', 'level'],\n          at: focusTextBlock.path,\n        }),\n        execute({\n          type: 'block.set',\n          props: {style},\n          at: focusTextBlock.path,\n        }),\n        execute({\n          type: 'delete.text',\n          at: {\n            anchor: {\n              path: focusTextBlock.path,\n              offset: 0,\n            },\n            focus: {\n              path: focusTextBlock.path,\n              offset: level + 1,\n            },\n          },\n        }),\n      ],\n    ],\n  })\n  const clearStyleOnBackspace = defineBehavior({\n    on: 'delete.backward',\n    guard: ({snapshot}) => {\n      const selectionCollapsed = selectors.isSelectionCollapsed(snapshot)\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n      const focusSpan = selectors.getFocusSpan(snapshot)\n\n      if (!selectionCollapsed || !focusTextBlock || !focusSpan) {\n        return false\n      }\n\n      const atTheBeginningOfBLock =\n        focusTextBlock.node.children[0]._key === focusSpan.node._key &&\n        snapshot.context.selection?.focus.offset === 0\n\n      const defaultStyle = config.defaultStyle?.(snapshot.context)\n\n      if (\n        atTheBeginningOfBLock &&\n        defaultStyle &&\n        focusTextBlock.node.style !== defaultStyle\n      ) {\n        return {defaultStyle, focusTextBlock}\n      }\n\n      return false\n    },\n    actions: [\n      (_, {defaultStyle, focusTextBlock}) => [\n        execute({\n          type: 'block.set',\n          props: {style: defaultStyle},\n          at: focusTextBlock.path,\n        }),\n      ],\n    ],\n  })\n  const automaticListOnSpace = defineBehavior({\n    on: 'insert.text',\n    guard: ({snapshot, event}) => {\n      const isSpace = event.text === ' '\n\n      if (!isSpace) {\n        return false\n      }\n\n      const selectionCollapsed = selectors.isSelectionCollapsed(snapshot)\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n      const focusSpan = selectors.getFocusSpan(snapshot)\n\n      if (!selectionCollapsed || !focusTextBlock || !focusSpan) {\n        return false\n      }\n\n      const previousInlineObject = selectors.getPreviousInlineObject(snapshot)\n      const blockOffset = spanSelectionPointToBlockOffset({\n        context: snapshot.context,\n        selectionPoint: {\n          path: [\n            {_key: focusTextBlock.node._key},\n            'children',\n            {_key: focusSpan.node._key},\n          ],\n          offset: snapshot.context.selection?.focus.offset ?? 0,\n        },\n      })\n\n      if (previousInlineObject || !blockOffset) {\n        return false\n      }\n\n      const blockText = getTextBlockText(focusTextBlock.node)\n      const defaultStyle = config.defaultStyle?.(snapshot.context)\n      const looksLikeUnorderedList = /^(-|\\*)/.test(blockText)\n      const unorderedListStyle = config.unorderedListStyle?.(snapshot.context)\n      const caretAtTheEndOfUnorderedList = blockOffset.offset === 1\n\n      if (\n        defaultStyle &&\n        caretAtTheEndOfUnorderedList &&\n        looksLikeUnorderedList &&\n        unorderedListStyle !== undefined\n      ) {\n        return {\n          focusTextBlock,\n          listItem: unorderedListStyle,\n          listItemLength: 1,\n          style: defaultStyle,\n        }\n      }\n\n      const looksLikeOrderedList = /^1\\./.test(blockText)\n      const orderedListStyle = config.orderedListStyle?.(snapshot.context)\n      const caretAtTheEndOfOrderedList = blockOffset.offset === 2\n\n      if (\n        defaultStyle &&\n        caretAtTheEndOfOrderedList &&\n        looksLikeOrderedList &&\n        orderedListStyle !== undefined\n      ) {\n        return {\n          focusTextBlock,\n          listItem: orderedListStyle,\n          listItemLength: 2,\n          style: defaultStyle,\n        }\n      }\n\n      return false\n    },\n    actions: [\n      ({event}) => [execute(event)],\n      (_, {focusTextBlock, style, listItem, listItemLength}) => [\n        execute({\n          type: 'block.set',\n          props: {\n            listItem,\n            level: 1,\n            style,\n          },\n          at: focusTextBlock.path,\n        }),\n        execute({\n          type: 'delete.text',\n          at: {\n            anchor: {\n              path: focusTextBlock.path,\n              offset: 0,\n            },\n            focus: {\n              path: focusTextBlock.path,\n              offset: listItemLength + 1,\n            },\n          },\n        }),\n      ],\n    ],\n  })\n\n  const markdownBehaviors = [\n    automaticBlockquoteOnSpace,\n    automaticHeadingOnSpace,\n    automaticHr,\n    automaticHrOnPaste,\n    clearStyleOnBackspace,\n    automaticListOnSpace,\n  ]\n\n  return markdownBehaviors\n}\n", "import {useEffect} from 'react'\nimport {\n  createMarkdownBehaviors,\n  type MarkdownBehaviorsConfig,\n} from '../behaviors/behavior.markdown'\nimport type {EditorSchema} from '../editor/editor-schema'\nimport {useEditor} from '../editor/use-editor'\nimport {DecoratorShortcutPlugin} from './plugin.decorator-shortcut'\n\n/**\n * @beta\n */\nexport type MarkdownPluginConfig = MarkdownBehaviorsConfig & {\n  boldDecorator?: ({schema}: {schema: EditorSchema}) => string | undefined\n  codeDecorator?: ({schema}: {schema: EditorSchema}) => string | undefined\n  italicDecorator?: ({schema}: {schema: EditorSchema}) => string | undefined\n  strikeThroughDecorator?: ({\n    schema,\n  }: {\n    schema: EditorSchema\n  }) => string | undefined\n}\n\n/**\n * @beta\n * Add markdown behaviors for common markdown actions such as converting ### to headings, --- to HRs, and more.\n *\n * @example\n * Configure the bundled markdown behaviors\n * ```ts\n * import {EditorProvider} from '@portabletext/editor'\n * import {MarkdownPlugin} from '@portabletext/editor/plugins'\n *\n * function App() {\n *   return (\n *    <EditorProvider>\n *      <MarkdownPlugin\n *        config={{\n *          boldDecorator: ({schema}) =>\n *            schema.decorators.find((decorator) => decorator.value === 'strong')?.value,\n *          codeDecorator: ({schema}) =>\n *            schema.decorators.find((decorator) => decorator.value === 'code')?.value,\n *          italicDecorator: ({schema}) =>\n *            schema.decorators.find((decorator) => decorator.value === 'em')?.value,\n *          strikeThroughDecorator: ({schema}) =>\n *            schema.decorators.find((decorator) => decorator.value === 'strike-through')?.value,\n *          horizontalRuleObject: ({schema}) => {\n *            const name = schema.blockObjects.find(\n *              (object) => object.name === 'break',\n *            )?.name\n *            return name ? {name} : undefined\n *          },\n *          defaultStyle: ({schema}) => schema.styles[0].value,\n *          headingStyle: ({schema, level}) =>\n *            schema.styles.find((style) => style.value === `h${level}`)\n *              ?.value,\n *          blockquoteStyle: ({schema}) =>\n *            schema.styles.find((style) => style.value === 'blockquote')\n *              ?.value,\n *          unorderedListStyle: ({schema}) =>\n *            schema.lists.find((list) => list.value === 'bullet')?.value,\n *          orderedListStyle: ({schema}) =>\n *            schema.lists.find((list) => list.value === 'number')?.value,\n *        }}\n *      />\n *      {...}\n *    </EditorProvider>\n *  )\n * }\n * ```\n *\n * @deprecated Install the plugin from `@portabletext/plugin-markdown-shortcuts`\n */\nexport function MarkdownPlugin(props: {config: MarkdownPluginConfig}) {\n  const editor = useEditor()\n\n  useEffect(() => {\n    const behaviors = createMarkdownBehaviors(props.config)\n\n    const unregisterBehaviors = behaviors.map((behavior) =>\n      editor.registerBehavior({behavior}),\n    )\n\n    return () => {\n      for (const unregisterBehavior of unregisterBehaviors) {\n        unregisterBehavior()\n      }\n    }\n  }, [editor, props.config])\n\n  return (\n    <>\n      {props.config.boldDecorator ? (\n        <>\n          <DecoratorShortcutPlugin\n            decorator={props.config.boldDecorator}\n            pair={{char: '*', amount: 2}}\n          />\n          <DecoratorShortcutPlugin\n            decorator={props.config.boldDecorator}\n            pair={{char: '_', amount: 2}}\n          />\n        </>\n      ) : null}\n      {props.config.codeDecorator ? (\n        <DecoratorShortcutPlugin\n          decorator={props.config.codeDecorator}\n          pair={{char: '`', amount: 1}}\n        />\n      ) : null}\n      {props.config.italicDecorator ? (\n        <>\n          <DecoratorShortcutPlugin\n            decorator={props.config.italicDecorator}\n            pair={{char: '*', amount: 1}}\n          />\n          <DecoratorShortcutPlugin\n            decorator={props.config.italicDecorator}\n            pair={{char: '_', amount: 1}}\n          />\n        </>\n      ) : null}\n      {props.config.strikeThroughDecorator ? (\n        <DecoratorShortcutPlugin\n          decorator={props.config.strikeThroughDecorator}\n          pair={{char: '~', amount: 2}}\n        />\n      ) : null}\n    </>\n  )\n}\n", "import {defineBehavior, execute, raise} from '../behaviors'\nimport * as selectors from '../selectors'\nimport * as utils from '../utils'\nimport {BehaviorPlugin} from './plugin.behavior'\n\nconst oneLineBehaviors = [\n  /**\n   * Hitting Enter on an expanded selection should just delete that selection\n   * without causing a line break.\n   */\n  defineBehavior({\n    on: 'insert.break',\n    guard: ({snapshot}) =>\n      snapshot.context.selection && selectors.isSelectionExpanded(snapshot)\n        ? {selection: snapshot.context.selection}\n        : false,\n    actions: [(_, {selection}) => [execute({type: 'delete', at: selection})]],\n  }),\n  /**\n   * All other cases of `insert.break` should be aborted.\n   */\n  defineBehavior({\n    on: 'insert.break',\n    actions: [],\n  }),\n  /**\n   * `insert.block` `before` or `after` is not allowed in a one-line editor.\n   */\n  defineBehavior({\n    on: 'insert.block',\n    guard: ({event}) =>\n      event.placement === 'before' || event.placement === 'after',\n    actions: [],\n  }),\n  /**\n   * An ordinary `insert.block` is acceptable if it's a text block. In that\n   * case it will get merged into the existing text block.\n   */\n  defineBehavior({\n    on: 'insert.block',\n    guard: ({snapshot, event}) => {\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n\n      if (\n        !focusTextBlock ||\n        !utils.isTextBlock(snapshot.context, event.block)\n      ) {\n        return false\n      }\n\n      return true\n    },\n    actions: [\n      ({event}) => [\n        execute({\n          type: 'insert.block',\n          block: event.block,\n          placement: 'auto',\n          select: 'end',\n        }),\n      ],\n    ],\n  }),\n  /**\n   * Fallback Behavior to avoid `insert.block` in case the Behaviors above all\n   * end up with a falsy guard.\n   */\n  defineBehavior({\n    on: 'insert.block',\n    actions: [],\n  }),\n  /**\n   * If multiple blocks are inserted, then the non-text blocks are filtered out\n   * and the text blocks are merged into one block\n   */\n  defineBehavior({\n    on: 'insert.blocks',\n    guard: ({snapshot, event}) => {\n      const textBlocks = event.blocks.filter((block) =>\n        utils.isTextBlock(snapshot.context, block),\n      )\n\n      if (textBlocks.length === 0) {\n        return false\n      }\n\n      return textBlocks.reduce((targetBlock, incomingBlock) => {\n        return utils.mergeTextBlocks({\n          context: snapshot.context,\n          targetBlock,\n          incomingBlock,\n        })\n      })\n    },\n    actions: [\n      // `insert.block` is raised so the Behavior above can handle the\n      // insertion\n      (_, block) => [raise({type: 'insert.block', block, placement: 'auto'})],\n    ],\n  }),\n  /**\n   * Fallback Behavior to avoid `insert.blocks` in case the Behavior above\n   * ends up with a falsy guard.\n   */\n  defineBehavior({\n    on: 'insert.blocks',\n    actions: [],\n  }),\n]\n\n/**\n * @beta\n * Restrict the editor to one line. The plugin takes care of blocking\n * `insert.break` events and smart handling of other `insert.*` events.\n *\n * Place it with as high priority as possible to make sure other plugins don't\n * overwrite `insert.*` events before this plugin gets a chance to do so.\n *\n * @deprecated Install the plugin from `@portabletext/plugin-one-line`\n */\nexport function OneLinePlugin() {\n  return <BehaviorPlugin behaviors={oneLineBehaviors} />\n}\n"], "names": ["BehaviorPlugin", "props", "$", "_c", "editor", "useEditor", "t0", "t1", "behaviors", "unregisterBeh<PERSON>ors", "map", "behavior", "registerBehavior", "for<PERSON>ach", "_temp", "useEffect", "unregister", "createPairRegex", "char", "amount", "prePrefix", "prefix", "repeat", "Math", "max", "postPrefix", "content", "preSuffix", "suffix", "postSuffix", "createDecoratorPairBehavior", "config", "pair", "console", "warn", "pairRegex", "regEx", "RegExp", "define<PERSON>eh<PERSON>or", "on", "guard", "snapshot", "event", "decorator", "schema", "context", "undefined", "focusTextBlock", "selectors", "selectionStartPoint", "selectionStartOffset", "utils", "value", "selectionPoint", "newText", "text", "textToDecorate", "match", "at", "prefixOffsets", "anchor", "path", "offset", "length", "focus", "suffixOffsets", "prefixSelection", "offsets", "inlineObjectBeforePrefixFocus", "selection", "inlineObjectBeforePrefixFocusOffset", "previousInlineObject", "previousInlineObjectOffset", "actions", "execute", "_", "type", "effect", "onDecorate", "DecoratorShortcutPlugin", "input", "useActorRef", "decoratorPairMachine", "emphasisListener", "sendBack", "blockOffset", "selectionListenerCallback", "blockOffsets", "forward", "deleteBackwardListenerCallback", "setup", "types", "events", "actors", "fromCallback", "createMachine", "id", "initial", "states", "invoke", "src", "target", "assign", "offsetAfterEmphasis", "exit", "isEqual", "EditorRefPlugin", "React", "forwardRef", "ref", "portableTextEditorRef", "useRef", "Symbol", "for", "current", "useImperativeHandle", "displayName", "EventListenerPlugin", "subscription", "unsubscribe", "createMarkdownBehaviors", "automaticBlockquoteOnSpace", "selectionCollapsed", "focusSpan", "spanSelectionPointToBlockOffset", "_key", "node", "blockText", "getTextBlockText", "caretAtTheEndOfQuote", "looksLikeMarkdownQuote", "test", "blockquoteStyle", "style", "automaticHr", "hrCharacter", "hrObject", "horizontalRuleObject", "focusBlock", "textBefore", "hrBlockOffsets", "placement", "block", "_type", "name", "automaticHrOnPaste", "originEvent", "dataTransfer", "getData", "hrRegExp", "hrCharacters", "isTextBlock", "children", "automaticHeadingOnSpace", "markdownHeadingSearch", "exec", "level", "caretAtTheEndOfHeading", "headingStyle", "clearStyleOnBackspace", "atTheBeginningOfBLock", "defaultStyle", "automaticListOnSpace", "looksLikeUnorderedList", "unorderedListStyle", "caretAtTheEndOfUnorderedList", "listItem", "listItemLength", "looksLikeOrderedList", "orderedListStyle", "caretAtTheEndOfOrderedList", "<PERSON>downP<PERSON>in", "unregister<PERSON><PERSON><PERSON><PERSON>", "t2", "boldDecorator", "t3", "codeDecorator", "t4", "italicDecorator", "t5", "strikeThroughDecorator", "t6", "oneLineBehaviors", "select", "textBlocks", "blocks", "filter", "reduce", "targetBlock", "incomingBlock", "raise", "OneLinePlugin"], "mappings": ";;;;;;;;;;;;;AAOO,SAAAA,eAAAC,OAAA;AAAA,QAAAC,IAAAC,EAAA,CAAA,GACLC,SAAeC,UAAAA;AAAW,MAAAC,IAAAC;AAAA,SAAAL,SAAAE,UAAAF,EAAA,CAAA,MAAAD,MAAAO,aAEhBF,KAAAA,MAAA;AACR,UAAAG,sBAA4BR,MAAKO,UAAAE,IAAAC,CAAAA,aAC/BP,OAAMQ,iBAAA;AAAA,MAAAD;AAAAA,IAAAA,CAA4B,CACpC;AAAC,WAAA,MAAA;AAGCF,0BAAmBI,QAAAC,KAAqC;AAAA,IAAC;AAAA,EAAA,GAE1DP,KAAA,CAACH,QAAQH,MAAKO,SAAA,GAAWN,OAAAE,QAAAF,EAAA,CAAA,IAAAD,MAAAO,WAAAN,OAAAI,IAAAJ,OAAAK,OAAAD,KAAAJ,EAAA,CAAA,GAAAK,KAAAL,EAAA,CAAA,IAR5Ba,UAAUT,IAQPC,EAAyB,GAAC;AAAA;AAXxB,SAAAO,MAAAE,YAAA;AAAA,SAS2CA,WAAAA;AAAY;AChBvD,SAASC,gBAAgBC,MAAcC,QAAgB;AAE5D,QAAMC,YAAY,SAASF,IAAI,KAGzBG,SAAS,KAAKH,IAAI,GAAGI,OAAOC,KAAKC,IAAIL,QAAQ,CAAC,CAAC,GAG/CM,aAAa,WAGbC,UAAU,MAAMR,IAAI,WAGpBS,YAAY,YAGZC,SAAS,KAAKV,IAAI,GAAGI,OAAOC,KAAKC,IAAIL,QAAQ,CAAC,CAAC,GAG/CU,aAAa,QAAQX,IAAI;AAE/B,SAAO,GAAGE,SAAS,GAAGC,MAAM,GAAGI,UAAU,GAAGC,OAAO,GAAGC,SAAS,GAAGC,MAAM,GAAGC,UAAU;AACvF;ACfO,SAASC,4BAA4BC,QAIzC;AACGA,SAAOC,KAAKb,SAAS,KACvBc,QAAQC,KACN,+DACF;AAGF,QAAMC,YAAYlB,gBAAgBc,OAAOC,KAAKd,MAAMa,OAAOC,KAAKb,MAAM,GAChEiB,QAAQ,IAAIC,OAAO,IAAIF,SAAS,IAAI;AAE1C,SAAOG,eAAe;AAAA,IACpBC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAC5B,UAAIX,OAAOC,KAAKb,SAAS;AACvB,eAAO;AAGT,YAAMwB,YAAYZ,OAAOY,UAAU;AAAA,QAACC,QAAQH,SAASI,QAAQD;AAAAA,MAAAA,CAAO;AAEpE,UAAID,cAAcG;AAChB,eAAO;AAGT,YAAMC,iBAAiBC,kBAA4BP,QAAQ,GACrDQ,sBAAsBD,uBAAiCP,QAAQ,GAC/DS,uBAAuBD,sBACzBE,gCAAsC;AAAA,QACpCN,SAAS;AAAA,UACPD,QAAQH,SAASI,QAAQD;AAAAA,UACzBQ,OAAOX,SAASI,QAAQO;AAAAA,QAAAA;AAAAA,QAE1BC,gBAAgBJ;AAAAA,MAAAA,CACjB,IACDH;AAEJ,UAAI,CAACC,kBAAkB,CAACG;AACtB,eAAO;AAIT,YAAMI,UAAU,GADGN,mBAA6BP,QAAQ,CAC3B,GAAGC,MAAMa,IAAI,IACpCC,iBAAiBF,QAAQG,MAAMrB,KAAK,GAAGsB,GAAG,CAAC;AAEjD,UAAIF,mBAAmBV;AACrB,eAAO;AAGT,YAAMa,gBAAgB;AAAA,QACpBC,QAAQ;AAAA,UACNC,MAAMd,eAAec;AAAAA;AAAAA,UAErBC,QAAQR,QAAQS,SAASP,eAAeO;AAAAA,QAAAA;AAAAA,QAE1CC,OAAO;AAAA,UACLH,MAAMd,eAAec;AAAAA;AAAAA,UAErBC,QACER,QAAQS,SACRP,eAAeO,SACfhC,OAAOC,KAAKd,KAAK6C,SAAShC,OAAOC,KAAKb;AAAAA,QAAAA;AAAAA,MAC1C,GAGI8C,gBAAgB;AAAA,QACpBL,QAAQ;AAAA,UACNC,MAAMd,eAAec;AAAAA;AAAAA,UAErBC,QACEZ,qBAAqBY,SACrBpB,MAAMa,KAAKQ,SACXhC,OAAOC,KAAKd,KAAK6C,SAAShC,OAAOC,KAAKb;AAAAA,QAAAA;AAAAA,QAE1C6C,OAAO;AAAA,UACLH,MAAMd,eAAec;AAAAA;AAAAA,UAErBC,QAAQZ,qBAAqBY,SAASpB,MAAMa,KAAKQ;AAAAA,QAAAA;AAAAA,MACnD;AAKF,UAAIJ,cAAcK,MAAMF,SAASH,cAAcC,OAAOE,SAAS,GAAG;AAChE,cAAMI,kBAAkBf,wBAA8B;AAAA,UACpDN,SAASJ,SAASI;AAAAA,UAClBsB,SAASR;AAAAA,QAAAA,CACV,GACKS,gCAAgCpB,wBACpC;AAAA,UACE,GAAGP;AAAAA,UACHI,SAAS;AAAA,YACP,GAAGJ,SAASI;AAAAA,YACZwB,WAAWH,kBACP;AAAA,cACEN,QAAQM,gBAAgBF;AAAAA,cACxBA,OAAOE,gBAAgBF;AAAAA,YAAAA,IAEzB;AAAA,UAAA;AAAA,QACN,CAEJ,GACMM,sCACJF,gCACIjB,iCAAuC;AAAA,UACrCN,SAAS;AAAA,YACPD,QAAQH,SAASI,QAAQD;AAAAA,YACzBQ,OAAOX,SAASI,QAAQO;AAAAA,UAAAA;AAAAA,UAE1BC,gBAAgB;AAAA,YACdQ,MAAMO,8BAA8BP;AAAAA,YACpCC,QAAQ;AAAA,UAAA;AAAA,QACV,CACD,IACDhB;AAEN,YACEwB,uCACAA,oCAAoCR,SAClCH,cAAcC,OAAOE,UACvBQ,oCAAoCR,SAClCH,cAAcK,MAAMF;AAEtB,iBAAO;AAAA,MAEX;AAIA,UAAIG,cAAcD,MAAMF,SAASG,cAAcL,OAAOE,SAAS,GAAG;AAChE,cAAMS,uBAAuBvB,wBAAkCP,QAAQ,GACjE+B,6BAA6BD,uBAC/BpB,iCAAuC;AAAA,UACrCN,SAAS;AAAA,YACPD,QAAQH,SAASI,QAAQD;AAAAA,YACzBQ,OAAOX,SAASI,QAAQO;AAAAA,UAAAA;AAAAA,UAE1BC,gBAAgB;AAAA,YACdQ,MAAMU,qBAAqBV;AAAAA,YAC3BC,QAAQ;AAAA,UAAA;AAAA,QACV,CACD,IACDhB;AAEJ,YACE0B,8BACAA,2BAA2BV,SAASG,cAAcL,OAAOE,UACzDU,2BAA2BV,SAASG,cAAcD,MAAMF;AAExD,iBAAO;AAAA,MAEX;AAEA,aAAO;AAAA,QACLH;AAAAA,QACAM;AAAAA,QACAtB;AAAAA,MAAAA;AAAAA,IAEJ;AAAA,IACA8B,SAAS;AAAA;AAAA,MAEP,CAAC;AAAA,QAAC/B;AAAAA,MAAAA,MAAW,CAACgC,QAAQhC,KAAK,CAAC;AAAA,MAC5B,CAACiC,GAAG;AAAA,QAAChB;AAAAA,QAAeM;AAAAA,QAAetB;AAAAA,MAAAA,MAAe;AAAA;AAAA,QAEhD+B,QAAQ;AAAA,UACNE,MAAM;AAAA,UACNjC;AAAAA,UACAe,IAAI;AAAA,YACFE,QAAQD,cAAcK;AAAAA,YACtBA,OAAOC,cAAcL;AAAAA,UAAAA;AAAAA,QACvB,CACD;AAAA;AAAA,QAEDc,QAAQ;AAAA,UACNE,MAAM;AAAA,UACNlB,IAAIO;AAAAA,QAAAA,CACL;AAAA;AAAA,QAEDS,QAAQ;AAAA,UACNE,MAAM;AAAA,UACNlB,IAAIC;AAAAA,QAAAA,CACL;AAAA;AAAA,QAEDe,QAAQ;AAAA,UACNE,MAAM;AAAA,UACNjC;AAAAA,QAAAA,CACD;AAAA,QACDkC,OAAO,MAAM;AACX9C,iBAAO+C,WAAW;AAAA,YAChB,GAAGb,cAAcL;AAAAA,YACjBE,QACEG,cAAcL,OAAOE,UACpBH,cAAcK,MAAMF,SAASH,cAAcC,OAAOE;AAAAA,UAAAA,CACtD;AAAA,QACH,CAAC;AAAA,MAAA;AAAA,IAAC;AAAA,EACH,CAEJ;AACH;AC1LO,SAAAiB,wBAAAhD,QAAA;AAAA,QAAA7B,IAAAC,EAAA,CAAA,GAILC,SAAeC,UAAAA;AAAW,MAAAC;AAAA,SAAAJ,EAAA,CAAA,MAAA6B,OAAAY,aAAAzC,EAAA,CAAA,MAAA6B,OAAAC,QAAA9B,SAAAE,UAEQE,KAAA;AAAA,IAAA0E,OAAA;AAAA,MAAA5E;AAAAA,MAAAuC,WAGnBZ,OAAMY;AAAAA,MAAAX,MACXD,OAAMC;AAAAA,IAAAA;AAAAA,EAAA,GAEf9B,EAAA,CAAA,IAAA6B,OAAAY,WAAAzC,EAAA,CAAA,IAAA6B,OAAAC,MAAA9B,OAAAE,QAAAF,OAAAI,MAAAA,KAAAJ,EAAA,CAAA,GAND+E,YAAAC,sBAAkC5E,EAMjC,GAAC;AAAA;AAqBJ,MAAM6E,mBAQFA,CAAC;AAAA,EAACC;AAAAA,EAAUJ;AAAK,MACAA,MAAM5E,OAAOQ,iBAAiB;AAAA,EAC/CD,UAAUmB,4BAA4B;AAAA,IACpCa,WAAWqC,MAAMrC;AAAAA,IACjBX,MAAMgD,MAAMhD;AAAAA,IACZ8C,YAAahB,CAAAA,WAAW;AACtBsB,eAAS;AAAA,QAACR,MAAM;AAAA,QAAgBS,aAAavB;AAAAA,MAAAA,CAAO;AAAA,IACtD;AAAA,EAAA,CACD;AACH,CAAC,GAKGwB,4BAIFA,CAAC;AAAA,EAACF;AAAAA,EAAUJ;AAAK,MACAA,MAAM5E,OAAOQ,iBAAiB;AAAA,EAC/CD,UAAU2B,eAAe;AAAA,IACvBC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAC5B,UAAI,CAACA,MAAMgB;AACT,eAAO;AAAA,UAAC6B,cAAczC;AAAAA,QAAAA;AAGxB,YAAMc,SAAST,gCAAsC;AAAA,QACnDN,SAASJ,SAASI;AAAAA,QAClBQ,gBAAgBX,MAAMgB,GAAGE;AAAAA,MAAAA,CAC1B,GACKI,QAAQb,gCAAsC;AAAA,QAClDN,SAASJ,SAASI;AAAAA,QAClBQ,gBAAgBX,MAAMgB,GAAGM;AAAAA,MAAAA,CAC1B;AAED,aAAI,CAACJ,UAAU,CAACI,QACP;AAAA,QAACuB,cAAczC;AAAAA,MAAAA,IAGjB;AAAA,QACLyC,cAAc;AAAA,UACZ3B;AAAAA,UACAI;AAAAA,QAAAA;AAAAA,MACF;AAAA,IAEJ;AAAA,IACAS,SAAS,CACP,CAAC;AAAA,MAAC/B;AAAAA,IAAAA,GAAQ;AAAA,MAAC6C;AAAAA,IAAAA,MAAkB,CAC3B;AAAA,MACEX,MAAM;AAAA,MACNC,QAAQA,MAAM;AACZO,iBAAS;AAAA,UAACR,MAAM;AAAA,UAAaW;AAAAA,QAAAA,CAAa;AAAA,MAC5C;AAAA,IAAA,GAEFC,QAAQ9C,KAAK,CAAC,CACf;AAAA,EAAA,CAEJ;AACH,CAAC,GAKG+C,iCAIFA,CAAC;AAAA,EAACL;AAAAA,EAAUJ;AAAK,MACAA,MAAM5E,OAAOQ,iBAAiB;AAAA,EAC/CD,UAAU2B,eAAe;AAAA,IACvBC,IAAI;AAAA,IACJkC,SAAS,CACP,MAAM,CACJC,QAAQ;AAAA,MACNE,MAAM;AAAA,IAAA,CACP,GACDC,OAAO,MAAM;AACXO,eAAS;AAAA,QAACR,MAAM;AAAA,MAAA,CAAkB;AAAA,IACpC,CAAC,CAAC,CACH;AAAA,EAAA,CAEJ;AACH,CAAC,GAKGM,uBAAuBQ,MAAM;AAAA,EACjCC,OAAO;AAAA,IACL9C,SAAS,CAAA;AAAA,IAMTmC,OAAO,CAAA;AAAA,IAKPY,QAAQ,CAAA;AAAA,EAAC;AAAA,EAEXC,QAAQ;AAAA,IACN,qBAAqBC,aAAaX,gBAAgB;AAAA,IAClD,4BAA4BW,aAAaL,8BAA8B;AAAA,IACvE,sBAAsBK,aAAaR,yBAAyB;AAAA,EAAA;AAEhE,CAAC,EAAES,cAAc;AAAA,EACfC,IAAI;AAAA,EACJnD,SAASA,CAAC;AAAA,IAACmC;AAAAA,EAAAA,OAAY;AAAA,IACrBrC,WAAWqC,MAAMrC;AAAAA,IACjBvC,QAAQ4E,MAAM5E;AAAAA,IACd4B,MAAMgD,MAAMhD;AAAAA,EAAAA;AAAAA,EAEdiE,SAAS;AAAA,EACTC,QAAQ;AAAA,IACN,MAAQ;AAAA,MACNC,QAAQ,CACN;AAAA,QACEC,KAAK;AAAA,QACLpB,OAAOA,CAAC;AAAA,UAACnC;AAAAA,QAAAA,OAAc;AAAA,UACrBF,WAAWE,QAAQF;AAAAA,UACnBvC,QAAQyC,QAAQzC;AAAAA,UAChB4B,MAAMa,QAAQb;AAAAA,QAAAA;AAAAA,MAChB,CACD;AAAA,MAEHO,IAAI;AAAA,QACF,gBAAgB;AAAA,UACd8D,QAAQ;AAAA,UACR5B,SAAS6B,OAAO;AAAA,YACdC,qBAAqBA,CAAC;AAAA,cAAC7D;AAAAA,YAAAA,MAAWA,MAAM2C;AAAAA,UAAAA,CACzC;AAAA,QAAA;AAAA,MACH;AAAA,IACF;AAAA,IAEF,kBAAkB;AAAA,MAChBmB,MAAM,CACJF,OAAO;AAAA,QACLC,qBAAqBzD;AAAAA,MAAAA,CACtB,CAAC;AAAA,MAEJqD,QAAQ,CACN;AAAA,QACEC,KAAK;AAAA,QACLpB,OAAOA,CAAC;AAAA,UAACnC;AAAAA,QAAAA,OAAc;AAAA,UAACzC,QAAQyC,QAAQzC;AAAAA,QAAAA;AAAAA,MAAM,GAEhD;AAAA,QACEgG,KAAK;AAAA,QACLpB,OAAOA,CAAC;AAAA,UAACnC;AAAAA,QAAAA,OAAc;AAAA,UAACzC,QAAQyC,QAAQzC;AAAAA,QAAAA;AAAAA,MAAM,CAC/C;AAAA,MAEHmC,IAAI;AAAA,QACF,WAAa;AAAA,UACX8D,QAAQ;AAAA,UACR7D,OAAOA,CAAC;AAAA,YAACK;AAAAA,YAASH;AAAAA,UAAAA,MACS,CAAC+D,QACxB;AAAA,YACE7C,QAAQf,QAAQ0D;AAAAA,YAChBvC,OAAOnB,QAAQ0D;AAAAA,UAAAA,GAEjB7D,MAAM6C,YACR;AAAA,QAAA;AAAA,QAKJ,mBAAmB;AAAA,UACjBc,QAAQ;AAAA,QAAA;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAEJ,CAAC,GCtOYK,kBAAkBC,MAAMC,WAA0B,CAAAjC,GAAAkC,QAAA;AAAA,QAAA3G,IAAAC,EAAA,CAAA,GAC7DC,SAAeC,aAEfyG,wBAA8BH,MAAAI,OAAa3G,MAAM;AAAC,MAAAE,IAAAC;AAAA,SAAAL,EAAA,CAAA,MAAA8G,OAAAC,IAAA,2BAAA,KAEnB3G,KAAAA,MAAMwG,sBAAqBI,SAAU3G,KAAA,CAAA,GAAEL,OAAAI,IAAAJ,OAAAK,OAAAD,KAAAJ,EAAA,CAAA,GAAAK,KAAAL,EAAA,CAAA,IAAtEyG,MAAAQ,oBAA0BN,KAAKvG,IAAqCC,EAAE,GAAC;AAAA,CAGxE;AACDmG,gBAAgBU,cAAc;ACqCvB,SAAAC,oBAAApH,OAAA;AAAA,QAAAC,IAAAC,EAAA,CAAA,GAGLC,SAAeC,UAAAA;AAAW,MAAAC,IAAAC;AAAA,SAAAL,SAAAE,UAAAF,EAAA,CAAA,MAAAD,MAAAsC,MAEhBjC,KAAAA,MAAA;AACR,UAAAgH,eAAqBlH,OAAMmC,GAAI,KAAKtC,MAAKsC,EAAG;AAAC,WAAA,MAAA;AAG3C+E,mBAAYC,YAAAA;AAAAA,IAAc;AAAA,EAAA,GAE3BhH,KAAA,CAACH,QAAQH,MAAKsC,EAAA,GAAIrC,OAAAE,QAAAF,EAAA,CAAA,IAAAD,MAAAsC,IAAArC,OAAAI,IAAAJ,OAAAK,OAAAD,KAAAJ,EAAA,CAAA,GAAAK,KAAAL,EAAA,CAAA,IANrBa,UAAUT,IAMPC,EAAkB,GAAC;AAAA;AC1CjB,SAASiH,wBAAwBzF,QAAiC;AACvE,QAAM0F,6BAA6BnF,eAAe;AAAA,IAChDC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAG5B,UAFgBA,MAAMa,SAAS;AAG7B,eAAO;AAGT,YAAMmE,qBAAqB1E,qBAA+BP,QAAQ,GAC5DM,iBAAiBC,kBAA4BP,QAAQ,GACrDkF,YAAY3E,aAAuBP,QAAQ;AAEjD,UAAI,CAACiF,sBAAsB,CAAC3E,kBAAkB,CAAC4E;AAC7C,eAAO;AAGT,YAAMpD,uBAAuBvB,wBAAkCP,QAAQ,GACjE4C,cAAcuC,gCAAgC;AAAA,QAClD/E,SAASJ,SAASI;AAAAA,QAClBQ,gBAAgB;AAAA,UACdQ,MAAM,CACJ;AAAA,YAACgE,MAAM9E,eAAe+E,KAAKD;AAAAA,UAAAA,GAC3B,YACA;AAAA,YAACA,MAAMF,UAAUG,KAAKD;AAAAA,UAAAA,CAAK;AAAA,UAE7B/D,QAAQrB,SAASI,QAAQwB,WAAWL,MAAMF,UAAU;AAAA,QAAA;AAAA,MACtD,CACD;AAED,UAAIS,wBAAwB,CAACc;AAC3B,eAAO;AAGT,YAAM0C,YAAYC,iBAAiBjF,eAAe+E,IAAI,GAChDG,uBAAuB5C,YAAYvB,WAAW,GAC9CoE,yBAAyB,KAAKC,KAAKJ,SAAS,GAC5CK,kBAAkBrG,OAAOqG,kBAAkB3F,SAASI,OAAO;AAEjE,aACEoF,wBACAC,0BACAE,oBAAoBtF,SAEb;AAAA,QAACC;AAAAA,QAAgBsF,OAAOD;AAAAA,MAAAA,IAG1B;AAAA,IACT;AAAA,IACA3D,SAAS,CACP,MAAM,CACJC,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNrB,MAAM;AAAA,IAAA,CACP,CAAC,GAEJ,CAACoB,GAAG;AAAA,MAAC5B;AAAAA,MAAgBsF;AAAAA,IAAAA,MAAW,CAC9B3D,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO,CAAC,YAAY,OAAO;AAAA,MAC3ByD,IAAIX,eAAec;AAAAA,IAAAA,CACpB,GACDa,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO;AAAA,QAACoI;AAAAA,MAAAA;AAAAA,MACR3E,IAAIX,eAAec;AAAAA,IAAAA,CACpB,GACDa,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNlB,IAAI;AAAA,QACFE,QAAQ;AAAA,UACNC,MAAMd,eAAec;AAAAA,UACrBC,QAAQ;AAAA,QAAA;AAAA,QAEVE,OAAO;AAAA,UACLH,MAAMd,eAAec;AAAAA,UACrBC,QAAQ;AAAA,QAAA;AAAA,MACV;AAAA,IACF,CACD,CAAC,CACH;AAAA,EAAA,CAEJ,GACKwE,cAAchG,eAAe;AAAA,IACjCC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAC5B,YAAM6F,cACJ7F,MAAMa,SAAS,MACX,MACAb,MAAMa,SAAS,MACb,MACAb,MAAMa,SAAS,MACb,MACAT;AAEV,UAAIyF,gBAAgBzF;AAClB,eAAO;AAGT,YAAM0F,WAAWzG,OAAO0G,uBAAuBhG,SAASI,OAAO,GACzD6F,aAAa1F,kBAA4BP,QAAQ,GACjDiF,qBAAqB1E,qBAA+BP,QAAQ;AAElE,UAAI,CAAC+F,YAAY,CAACE,cAAc,CAAChB;AAC/B,eAAO;AAGT,YAAMnD,uBAAuBvB,wBAAkCP,QAAQ,GACjEkG,aAAa3F,mBAA6BP,QAAQ,GAClDmG,iBAAiB;AAAA,QACrBhF,QAAQ;AAAA,UACNC,MAAM6E,WAAW7E;AAAAA,UACjBC,QAAQ;AAAA,QAAA;AAAA,QAEVE,OAAO;AAAA,UACLH,MAAM6E,WAAW7E;AAAAA,UACjBC,QAAQ;AAAA,QAAA;AAAA,MACV;AAGF,aACE,CAACS,wBACDoE,eAAe,GAAGJ,WAAW,GAAGA,WAAW,KAEpC;AAAA,QAACC;AAAAA,QAAUE;AAAAA,QAAYH;AAAAA,QAAaK;AAAAA,MAAAA,IAGtC;AAAA,IACT;AAAA,IACAnE,SAAS,CACP,CAACE,GAAG;AAAA,MAAC4D;AAAAA,IAAAA,MAAiB,CACpB7D,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNrB,MAAMgF;AAAAA,IAAAA,CACP,CAAC,GAEJ,CAAC5D,GAAG;AAAA,MAAC6D;AAAAA,MAAUI;AAAAA,IAAAA,MAAoB,CACjClE,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNiE,WAAW;AAAA,MACXC,OAAO;AAAA,QACLC,OAAOP,SAASQ;AAAAA,QAChB,GAAIR,SAASpF,SAAS,CAAA;AAAA,MAAC;AAAA,IACzB,CACD,GACDsB,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNlB,IAAIkF;AAAAA,IAAAA,CACL,CAAC,CACH;AAAA,EAAA,CAEJ,GACKK,qBAAqB3G,eAAe;AAAA,IACxCC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAC5B,YAAMa,OAAOb,MAAMwG,YAAYC,aAAaC,QAAQ,YAAY,GAC1DC,WAAW,4BACXC,eAAe/F,KAAKE,MAAM4F,QAAQ,IAAI,CAAC,GACvCb,WAAWzG,OAAO0G,uBAAuBhG,SAASI,OAAO,GACzD6F,aAAa1F,cAAwBP,QAAQ;AAEnD,aAAI,CAAC6G,gBAAgB,CAACd,YAAY,CAACE,aAC1B,KAGF;AAAA,QAACY;AAAAA,QAAcd;AAAAA,QAAUE;AAAAA,MAAAA;AAAAA,IAClC;AAAA,IACAjE,SAAS,CACP,CAACE,GAAG;AAAA,MAAC2E;AAAAA,IAAAA,MAAkB,CACrB5E,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNrB,MAAM+F;AAAAA,IAAAA,CACP,CAAC,GAEJ,CAAC;AAAA,MAAC7G;AAAAA,IAAAA,GAAW;AAAA,MAAC+F;AAAAA,MAAUE;AAAAA,IAAAA,MACtBa,YAAY9G,SAASI,SAAS6F,WAAWZ,IAAI,IACzC,CACEpD,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNkE,OAAO;AAAA,QACLC,OAAOtG,SAASI,QAAQD,OAAOkG,MAAME;AAAAA,QACrCQ,UAAUd,WAAWZ,KAAK0B;AAAAA,MAAAA;AAAAA,MAE5BX,WAAW;AAAA,IAAA,CACZ,GACDnE,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNkE,OAAO;AAAA,QACLC,OAAOP,SAASQ;AAAAA,QAChB,GAAIR,SAASpF,SAAS,CAAA;AAAA,MAAC;AAAA,MAEzByF,WAAW;AAAA,IAAA,CACZ,GACDnE,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNlB,IAAIgF,WAAW7E;AAAAA,IAAAA,CAChB,CAAC,IAEJ,CACEa,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNkE,OAAO;AAAA,QACLC,OAAOP,SAASQ;AAAAA,QAChB,GAAIR,SAASpF,SAAS,CAAA;AAAA,MAAC;AAAA,MAEzByF,WAAW;AAAA,IAAA,CACZ,CAAC,CACH;AAAA,EAAA,CAEV,GACKY,0BAA0BnH,eAAe;AAAA,IAC7CC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAG5B,UAFgBA,MAAMa,SAAS;AAG7B,eAAO;AAGT,YAAMmE,qBAAqB1E,qBAA+BP,QAAQ,GAC5DM,iBAAiBC,kBAA4BP,QAAQ,GACrDkF,YAAY3E,aAAuBP,QAAQ;AAEjD,UAAI,CAACiF,sBAAsB,CAAC3E,kBAAkB,CAAC4E;AAC7C,eAAO;AAGT,YAAMtC,cAAcuC,gCAAgC;AAAA,QAClD/E,SAASJ,SAASI;AAAAA,QAClBQ,gBAAgB;AAAA,UACdQ,MAAM,CACJ;AAAA,YAACgE,MAAM9E,eAAe+E,KAAKD;AAAAA,UAAAA,GAC3B,YACA;AAAA,YAACA,MAAMF,UAAUG,KAAKD;AAAAA,UAAAA,CAAK;AAAA,UAE7B/D,QAAQrB,SAASI,QAAQwB,WAAWL,MAAMF,UAAU;AAAA,QAAA;AAAA,MACtD,CACD;AAED,UAAI,CAACuB;AACH,eAAO;AAGT,YAAMd,uBAAuBvB,wBAAkCP,QAAQ,GACjEsF,YAAYC,iBAAiBjF,eAAe+E,IAAI,GAChD4B,wBAAwB,MAAMC,KAAK5B,SAAS,GAC5C6B,QAAQF,wBACVA,sBAAsB,CAAC,EAAE3F,SACzBjB,QACE+G,yBAAyBxE,YAAYvB,WAAW8F;AAEtD,UAAIrF,wBAAwB,CAACsF;AAC3B,eAAO;AAGT,YAAMxB,QACJuB,UAAU9G,SACNf,OAAO+H,eAAe;AAAA,QAAClH,QAAQH,SAASI,QAAQD;AAAAA,QAAQgH;AAAAA,MAAAA,CAAM,IAC9D9G;AAEN,aAAI8G,UAAU9G,UAAauF,UAAUvF,SAC5B;AAAA,QACLC;AAAAA,QACAsF;AAAAA,QACAuB;AAAAA,MAAAA,IAIG;AAAA,IACT;AAAA,IACAnF,SAAS,CACP,CAAC;AAAA,MAAC/B;AAAAA,IAAAA,MAAW,CAACgC,QAAQhC,KAAK,CAAC,GAC5B,CAACiC,GAAG;AAAA,MAAC5B;AAAAA,MAAgBsF;AAAAA,MAAOuB;AAAAA,IAAAA,MAAW,CACrClF,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO,CAAC,YAAY,OAAO;AAAA,MAC3ByD,IAAIX,eAAec;AAAAA,IAAAA,CACpB,GACDa,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO;AAAA,QAACoI;AAAAA,MAAAA;AAAAA,MACR3E,IAAIX,eAAec;AAAAA,IAAAA,CACpB,GACDa,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNlB,IAAI;AAAA,QACFE,QAAQ;AAAA,UACNC,MAAMd,eAAec;AAAAA,UACrBC,QAAQ;AAAA,QAAA;AAAA,QAEVE,OAAO;AAAA,UACLH,MAAMd,eAAec;AAAAA,UACrBC,QAAQ8F,QAAQ;AAAA,QAAA;AAAA,MAClB;AAAA,IACF,CACD,CAAC,CACH;AAAA,EAAA,CAEJ,GACKG,wBAAwBzH,eAAe;AAAA,IAC3CC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,IAAAA,MAAc;AACrB,YAAMiF,qBAAqB1E,qBAA+BP,QAAQ,GAC5DM,iBAAiBC,kBAA4BP,QAAQ,GACrDkF,YAAY3E,aAAuBP,QAAQ;AAEjD,UAAI,CAACiF,sBAAsB,CAAC3E,kBAAkB,CAAC4E;AAC7C,eAAO;AAGT,YAAMqC,wBACJjH,eAAe+E,KAAK0B,SAAS,CAAC,EAAE3B,SAASF,UAAUG,KAAKD,QACxDpF,SAASI,QAAQwB,WAAWL,MAAMF,WAAW,GAEzCmG,eAAelI,OAAOkI,eAAexH,SAASI,OAAO;AAE3D,aACEmH,yBACAC,gBACAlH,eAAe+E,KAAKO,UAAU4B,eAEvB;AAAA,QAACA;AAAAA,QAAclH;AAAAA,MAAAA,IAGjB;AAAA,IACT;AAAA,IACA0B,SAAS,CACP,CAACE,GAAG;AAAA,MAACsF;AAAAA,MAAclH;AAAAA,IAAAA,MAAoB,CACrC2B,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO;AAAA,QAACoI,OAAO4B;AAAAA,MAAAA;AAAAA,MACfvG,IAAIX,eAAec;AAAAA,IAAAA,CACpB,CAAC,CACH;AAAA,EAAA,CAEJ,GACKqG,uBAAuB5H,eAAe;AAAA,IAC1CC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAG5B,UAFgBA,MAAMa,SAAS;AAG7B,eAAO;AAGT,YAAMmE,qBAAqB1E,qBAA+BP,QAAQ,GAC5DM,iBAAiBC,kBAA4BP,QAAQ,GACrDkF,YAAY3E,aAAuBP,QAAQ;AAEjD,UAAI,CAACiF,sBAAsB,CAAC3E,kBAAkB,CAAC4E;AAC7C,eAAO;AAGT,YAAMpD,uBAAuBvB,wBAAkCP,QAAQ,GACjE4C,cAAcuC,gCAAgC;AAAA,QAClD/E,SAASJ,SAASI;AAAAA,QAClBQ,gBAAgB;AAAA,UACdQ,MAAM,CACJ;AAAA,YAACgE,MAAM9E,eAAe+E,KAAKD;AAAAA,UAAAA,GAC3B,YACA;AAAA,YAACA,MAAMF,UAAUG,KAAKD;AAAAA,UAAAA,CAAK;AAAA,UAE7B/D,QAAQrB,SAASI,QAAQwB,WAAWL,MAAMF,UAAU;AAAA,QAAA;AAAA,MACtD,CACD;AAED,UAAIS,wBAAwB,CAACc;AAC3B,eAAO;AAGT,YAAM0C,YAAYC,iBAAiBjF,eAAe+E,IAAI,GAChDmC,eAAelI,OAAOkI,eAAexH,SAASI,OAAO,GACrDsH,yBAAyB,UAAUhC,KAAKJ,SAAS,GACjDqC,qBAAqBrI,OAAOqI,qBAAqB3H,SAASI,OAAO,GACjEwH,+BAA+BhF,YAAYvB,WAAW;AAE5D,UACEmG,gBACAI,gCACAF,0BACAC,uBAAuBtH;AAEvB,eAAO;AAAA,UACLC;AAAAA,UACAuH,UAAUF;AAAAA,UACVG,gBAAgB;AAAA,UAChBlC,OAAO4B;AAAAA,QAAAA;AAIX,YAAMO,uBAAuB,OAAOrC,KAAKJ,SAAS,GAC5C0C,mBAAmB1I,OAAO0I,mBAAmBhI,SAASI,OAAO,GAC7D6H,6BAA6BrF,YAAYvB,WAAW;AAE1D,aACEmG,gBACAS,8BACAF,wBACAC,qBAAqB3H,SAEd;AAAA,QACLC;AAAAA,QACAuH,UAAUG;AAAAA,QACVF,gBAAgB;AAAA,QAChBlC,OAAO4B;AAAAA,MAAAA,IAIJ;AAAA,IACT;AAAA,IACAxF,SAAS,CACP,CAAC;AAAA,MAAC/B;AAAAA,IAAAA,MAAW,CAACgC,QAAQhC,KAAK,CAAC,GAC5B,CAACiC,GAAG;AAAA,MAAC5B;AAAAA,MAAgBsF;AAAAA,MAAOiC;AAAAA,MAAUC;AAAAA,IAAAA,MAAoB,CACxD7F,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO;AAAA,QACLqK;AAAAA,QACAV,OAAO;AAAA,QACPvB;AAAAA,MAAAA;AAAAA,MAEF3E,IAAIX,eAAec;AAAAA,IAAAA,CACpB,GACDa,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNlB,IAAI;AAAA,QACFE,QAAQ;AAAA,UACNC,MAAMd,eAAec;AAAAA,UACrBC,QAAQ;AAAA,QAAA;AAAA,QAEVE,OAAO;AAAA,UACLH,MAAMd,eAAec;AAAAA,UACrBC,QAAQyG,iBAAiB;AAAA,QAAA;AAAA,MAC3B;AAAA,IACF,CACD,CAAC,CACH;AAAA,EAAA,CAEJ;AAWD,SAT0B,CACxB9C,4BACAgC,yBACAnB,aACAW,oBACAc,uBACAG,oBAAoB;AAIxB;AC/YO,SAAAS,eAAA1K,OAAA;AAAA,QAAAC,IAAAC,EAAA,EAAA,GACLC,SAAeC,UAAAA;AAAW,MAAAC,IAAAC;AAAAL,WAAAE,UAAAF,EAAA,CAAA,MAAAD,MAAA8B,UAEhBzB,KAAAA,MAAA;AAGR,UAAAG,sBAFkB+G,wBAAwBvH,MAAK8B,MAAO,EAEjBrB,IAAAC,CAAAA,aACnCP,OAAMQ,iBAAA;AAAA,MAAAD;AAAAA,IAAAA,CAA4B,CACpC;AAAC,WAAA,MAAA;AAAA,iBAGMiK,sBAA4BnK;AAC/BmK,2BAAAA;AAAAA,IAAoB;AAAA,EAAA,GAGvBrK,KAAA,CAACH,QAAQH,MAAK8B,MAAA,GAAQ7B,OAAAE,QAAAF,EAAA,CAAA,IAAAD,MAAA8B,QAAA7B,OAAAI,IAAAJ,OAAAK,OAAAD,KAAAJ,EAAA,CAAA,GAAAK,KAAAL,EAAA,CAAA,IAZzBa,UAAUT,IAYPC,EAAsB;AAAC,MAAAsK;AAAA3K,IAAA,CAAA,MAAAD,MAAA8B,OAAA+I,iBAIrBD,KAAA5K,MAAK8B,OAAA+I,gBAAA,qBAAA,UAAA,EAEF,UAAA;AAAA,IAAA,oBAAC,yBAAA,EACY,WAAA7K,MAAK8B,OAAA+I,eACV,MAAA;AAAA,MAAA5J,MAAO;AAAA,MAAGC,QAAA;AAAA,IAAA,GAAY;AAAA,wBAE7B,yBAAA,EACY,WAAAlB,MAAK8B,OAAA+I,eACV,MAAA;AAAA,MAAA5J,MAAO;AAAA,MAAGC,QAAA;AAAA,IAAA,EAAA;KAChB,IAAA,MAEEjB,EAAA,CAAA,IAAAD,MAAA8B,OAAA+I,eAAA5K,OAAA2K,MAAAA,KAAA3K,EAAA,CAAA;AAAA,MAAA6K;AAAA7K,IAAA,CAAA,MAAAD,MAAA8B,OAAAiJ,iBACPD,KAAA9K,MAAK8B,OAAAiJ,oCACH,yBAAA,EACY,WAAA/K,MAAK8B,OAAAiJ,eACV,MAAA;AAAA,IAAA9J,MAAO;AAAA,IAAGC,QAAA;AAAA,EAAA,EAAA,KAChB,MACIjB,EAAA,CAAA,IAAAD,MAAA8B,OAAAiJ,eAAA9K,OAAA6K,MAAAA,KAAA7K,EAAA,CAAA;AAAA,MAAA+K;AAAA/K,IAAA,CAAA,MAAAD,MAAA8B,OAAAmJ,mBACPD,KAAAhL,MAAK8B,OAAAmJ,kBAAA,qBAAA,UAAA,EAEF,UAAA;AAAA,IAAA,oBAAC,yBAAA,EACY,WAAAjL,MAAK8B,OAAAmJ,iBACV,MAAA;AAAA,MAAAhK,MAAO;AAAA,MAAGC,QAAA;AAAA,IAAA,GAAY;AAAA,wBAE7B,yBAAA,EACY,WAAAlB,MAAK8B,OAAAmJ,iBACV,MAAA;AAAA,MAAAhK,MAAO;AAAA,MAAGC,QAAA;AAAA,IAAA,EAAA;KAChB,IAAA,MAEEjB,EAAA,CAAA,IAAAD,MAAA8B,OAAAmJ,iBAAAhL,OAAA+K,MAAAA,KAAA/K,EAAA,CAAA;AAAA,MAAAiL;AAAAjL,IAAA,EAAA,MAAAD,MAAA8B,OAAAqJ,0BACPD,KAAAlL,MAAK8B,OAAAqJ,6CACH,yBAAA,EACY,WAAAnL,MAAK8B,OAAAqJ,wBACV,MAAA;AAAA,IAAAlK,MAAO;AAAA,IAAGC,QAAA;AAAA,EAAA,EAAA,KAChB,MACIjB,EAAA,EAAA,IAAAD,MAAA8B,OAAAqJ,wBAAAlL,QAAAiL,MAAAA,KAAAjL,EAAA,EAAA;AAAA,MAAAmL;AAAA,SAAAnL,EAAA,EAAA,MAAA2K,MAAA3K,EAAA,EAAA,MAAA6K,MAAA7K,EAAA,EAAA,MAAA+K,MAAA/K,UAAAiL,MApCVE,KAAA,qBAAA,UAAA,EACGR,UAAAA;AAAAA,IAAAA;AAAAA,IAYAE;AAAAA,IAMAE;AAAAA,IAYAE;AAAAA,EAAAA,EAAAA,CAKO,GACPjL,QAAA2K,IAAA3K,QAAA6K,IAAA7K,QAAA+K,IAAA/K,QAAAiL,IAAAjL,QAAAmL,MAAAA,KAAAnL,EAAA,EAAA,GArCHmL;AAqCG;AC3HP,MAAMC,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvBhJ,eAAe;AAAA,IACbC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,IAAAA,MACPA,SAASI,QAAQwB,aAAarB,oBAA8BP,QAAQ,IAChE;AAAA,MAAC4B,WAAW5B,SAASI,QAAQwB;AAAAA,IAAAA,IAC7B;AAAA,IACNI,SAAS,CAAC,CAACE,GAAG;AAAA,MAACN;AAAAA,IAAAA,MAAe,CAACK,QAAQ;AAAA,MAACE,MAAM;AAAA,MAAUlB,IAAIW;AAAAA,IAAAA,CAAU,CAAC,CAAC;AAAA,EAAA,CACzE;AAAA;AAAA;AAAA;AAAA,EAID/B,eAAe;AAAA,IACbC,IAAI;AAAA,IACJkC,SAAS,CAAA;AAAA,EAAA,CACV;AAAA;AAAA;AAAA;AAAA,EAIDnC,eAAe;AAAA,IACbC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACE;AAAAA,IAAAA,MACPA,MAAMmG,cAAc,YAAYnG,MAAMmG,cAAc;AAAA,IACtDpE,SAAS,CAAA;AAAA,EAAA,CACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAKDnC,eAAe;AAAA,IACbC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAIf,EAAA,CAHqBM,kBAA4BP,QAAQ,KAIzD,CAACU,cAAkBV,SAASI,SAASH,MAAMoG,KAAK;AAAA,IAOpDrE,SAAS,CACP,CAAC;AAAA,MAAC/B;AAAAA,IAAAA,MAAW,CACXgC,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNkE,OAAOpG,MAAMoG;AAAAA,MACbD,WAAW;AAAA,MACX0C,QAAQ;AAAA,IAAA,CACT,CAAC,CACH;AAAA,EAAA,CAEJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKDjJ,eAAe;AAAA,IACbC,IAAI;AAAA,IACJkC,SAAS,CAAA;AAAA,EAAA,CACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAKDnC,eAAe;AAAA,IACbC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAC5B,YAAM8I,aAAa9I,MAAM+I,OAAOC,OAAQ5C,CAAAA,UACtC3F,cAAkBV,SAASI,SAASiG,KAAK,CAC3C;AAEA,aAAI0C,WAAWzH,WAAW,IACjB,KAGFyH,WAAWG,OAAO,CAACC,aAAaC,kBAC9B1I,gBAAsB;AAAA,QAC3BN,SAASJ,SAASI;AAAAA,QAClB+I;AAAAA,QACAC;AAAAA,MAAAA,CACD,CACF;AAAA,IACH;AAAA,IACApH,SAAS;AAAA;AAAA;AAAA,MAGP,CAACE,GAAGmE,UAAU,CAACgD,MAAM;AAAA,QAAClH,MAAM;AAAA,QAAgBkE;AAAAA,QAAOD,WAAW;AAAA,MAAA,CAAO,CAAC;AAAA,IAAA;AAAA,EAAC,CAE1E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKDvG,eAAe;AAAA,IACbC,IAAI;AAAA,IACJkC,SAAS,CAAA;AAAA,EAAA,CACV;AAAC;AAaG,SAAAsH,gBAAA;AAAA,QAAA7L,IAAAC,EAAA,CAAA;AAAA,MAAAG;AAAA,SAAAJ,EAAA,CAAA,MAAA8G,OAAAC,IAAA,2BAAA,KACE3G,yBAAC,gBAAA,EAA0BgL,WAAAA,iBAAAA,CAAgB,GAAIpL,OAAAI,MAAAA,KAAAJ,EAAA,CAAA,GAA/CI;AAA+C;"}