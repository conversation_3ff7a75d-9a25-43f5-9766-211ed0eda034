{"version": 3, "file": "stringify.cjs", "sources": ["../../src/path/utils/predicates.ts", "../../src/path/parser/stringify.ts"], "sourcesContent": ["import {type KeyedPathElement, type Path, type PathElement} from '../types'\n\nfunction safeGetElementAt<T>(array: T[] | readonly T[], index: number): T {\n  if (index < 0 || index >= array.length) {\n    throw new Error('Index out of bounds')\n  }\n  return array[index]!\n}\n\nexport function startsWith(parentPath: Path, path: Path): boolean {\n  return (\n    parentPath.length <= path.length &&\n    parentPath.every((segment, i) =>\n      isElementEqual(segment, safeGetElementAt(path, i)),\n    )\n  )\n}\n\nexport function isEqual(path: Path, otherPath: Path): boolean {\n  return (\n    path.length === otherPath.length &&\n    path.every((segment, i) =>\n      isElementEqual(segment, safeGetElementAt(path, i)),\n    )\n  )\n}\n\nexport function isElementEqual(\n  segmentA: PathElement,\n  segmentB: PathElement,\n): boolean {\n  if (isKeyElement(segmentA) && isKeyElement(segmentB)) {\n    return segmentA._key === segmentB._key\n  }\n\n  if (isIndexElement(segmentA)) {\n    return Number(segmentA) === Number(segmentB)\n  }\n\n  return segmentA === segmentB\n}\n\nexport function isKeyElement(\n  segment: PathElement,\n): segment is KeyedPathElement {\n  return typeof (segment as any)?._key === 'string'\n}\nexport function isIndexElement(segment: PathElement): segment is number {\n  return typeof segment === 'number'\n}\n\nexport function isKeyedElement(\n  element: PathElement,\n): element is KeyedPathElement {\n  return (\n    typeof element === 'object' &&\n    '_key' in element &&\n    typeof element._key === 'string'\n  )\n}\nexport function isArrayElement(\n  element: PathElement,\n): element is KeyedPathElement | number {\n  return typeof element === 'number' || isKeyedElement(element)\n}\n\nexport function isPropertyElement(element: PathElement): element is string {\n  return typeof element === 'string'\n}\n", "import {type Path, type PathElement} from '../types'\nimport {isKeyedElement} from '../utils/predicates'\n\nconst IS_DOTTABLE = /^[a-z_$]+/\n\nfunction stringifySegment(segment: PathElement, hasLeading: boolean): string {\n  if (Array.isArray(segment)) {\n    return `[${segment[0]}:${segment[1] || ''}]`\n  }\n  const type = typeof segment\n\n  const isNumber = type === 'number'\n\n  if (isNumber) {\n    return `[${segment}]`\n  }\n\n  if (isKeyedElement(segment)) {\n    return `[_key==${JSON.stringify(segment._key)}]`\n  }\n\n  if (typeof segment === 'string' && IS_DOTTABLE.test(segment)) {\n    return hasLeading ? segment : `.${segment}`\n  }\n\n  return `['${segment}']`\n}\n\nexport function stringify(pathArray: Path): string {\n  return pathArray\n    .map((segment, i) => stringifySegment(segment, i === 0))\n    .join('')\n}\n"], "names": [], "mappings": ";AAEA,SAAS,iBAAoB,OAA2B,OAAkB;AACpE,MAAA,QAAQ,KAAK,SAAS,MAAM;AACxB,UAAA,IAAI,MAAM,qBAAqB;AAEvC,SAAO,MAAM,KAAK;AACpB;AAEgB,SAAA,WAAW,YAAkB,MAAqB;AAChE,SACE,WAAW,UAAU,KAAK,UAC1B,WAAW;AAAA,IAAM,CAAC,SAAS,MACzB,eAAe,SAAS,iBAAiB,MAAM,CAAC,CAAC;AAAA,EACnD;AAEJ;AAEgB,SAAA,QAAQ,MAAY,WAA0B;AAC5D,SACE,KAAK,WAAW,UAAU,UAC1B,KAAK;AAAA,IAAM,CAAC,SAAS,MACnB,eAAe,SAAS,iBAAiB,MAAM,CAAC,CAAC;AAAA,EACnD;AAEJ;AAEgB,SAAA,eACd,UACA,UACS;AACT,SAAI,aAAa,QAAQ,KAAK,aAAa,QAAQ,IAC1C,SAAS,SAAS,SAAS,OAGhC,eAAe,QAAQ,IAClB,OAAO,QAAQ,MAAM,OAAO,QAAQ,IAGtC,aAAa;AACtB;AAEO,SAAS,aACd,SAC6B;AACtB,SAAA,OAAQ,SAAiB,QAAS;AAC3C;AACO,SAAS,eAAe,SAAyC;AACtE,SAAO,OAAO,WAAY;AAC5B;AAEO,SAAS,eACd,SAC6B;AAC7B,SACE,OAAO,WAAY,YACnB,UAAU,WACV,OAAO,QAAQ,QAAS;AAE5B;AACO,SAAS,eACd,SACsC;AACtC,SAAO,OAAO,WAAY,YAAY,eAAe,OAAO;AAC9D;AAEO,SAAS,kBAAkB,SAAyC;AACzE,SAAO,OAAO,WAAY;AAC5B;ACjEA,MAAM,cAAc;AAEpB,SAAS,iBAAiB,SAAsB,YAA6B;AAC3E,SAAI,MAAM,QAAQ,OAAO,IAChB,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,KAAK,EAAE,MAE9B,OAAO,WAEM,WAGjB,IAAI,OAAO,MAGhB,eAAe,OAAO,IACjB,UAAU,KAAK,UAAU,QAAQ,IAAI,CAAC,MAG3C,OAAO,WAAY,YAAY,YAAY,KAAK,OAAO,IAClD,aAAa,UAAU,IAAI,OAAO,KAGpC,KAAK,OAAO;AACrB;AAEO,SAAS,UAAU,WAAyB;AACjD,SAAO,UACJ,IAAI,CAAC,SAAS,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,EACtD,KAAK,EAAE;AACZ;;;;;;;;;;"}