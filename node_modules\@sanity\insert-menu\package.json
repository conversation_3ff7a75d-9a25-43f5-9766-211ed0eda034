{"name": "@sanity/insert-menu", "version": "2.0.1", "description": "", "keywords": [], "homepage": "https://github.com/sanity-io/visual-editing/tree/main/packages/insert-menu#readme", "bugs": {"url": "https://github.com/sanity-io/visual-editing/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/sanity-io/visual-editing.git", "directory": "packages/insert-menu"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"source": "./src/index.ts", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "src"], "browserslist": "extends @sanity/browserslist-config", "dependencies": {"@sanity/icons": "^3.7.4", "@sanity/ui": "^3.0.0", "lodash": "^4.17.21", "react-compiler-runtime": "19.1.0-rc.2"}, "devDependencies": {"@sanity/pkg-utils": "^7.9.8", "@sanity/types": "^4.2.0", "@sanity/ui-workshop": "^3.1.2", "@types/lodash": "^4.17.20", "@types/react": "^19.1.8", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "babel-plugin-react-compiler": "19.1.0-rc.2", "eslint": "^8.57.1", "eslint-plugin-import": "^2.32.0", "lint-staged": "^15.2.10", "react": "^19.1.0", "react-dom": "^19.1.0", "react-is": "^19.1.0", "typescript": "5.8.3"}, "peerDependencies": {"@sanity/types": "*", "react": "^18.3 || >=19.0.0-rc", "react-dom": "^18.3 || >=19.0.0-rc", "react-is": "^18.3 || >=19.0.0-rc"}, "engines": {"node": ">=20.19"}, "scripts": {"build": "pkg build --strict --check --clean", "dev": "pkg build --strict", "dev:workshop": "workshop dev", "lint": "eslint --cache .", "ts:check": "tsc --noEmit"}}