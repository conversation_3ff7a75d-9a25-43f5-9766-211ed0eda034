{"name": "@portabletext/react", "version": "3.2.1", "description": "Render Portable Text with React", "keywords": ["portable-text"], "homepage": "https://github.com/portabletext/react-portabletext#readme", "bugs": {"url": "https://github.com/portabletext/react-portabletext/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/portabletext/react-portabletext.git"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"source": "./src/index.ts", "import": "./dist/index.js", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "!dist/stats.html", "src"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*": ["prettier --write --cache --ignore-unknown"]}, "browserslist": "extends @sanity/browserslist-config", "prettier": {"bracketSpacing": false, "plugins": ["prettier-plugin-package<PERSON><PERSON>"], "printWidth": 100, "semi": false, "singleQuote": true}, "eslintConfig": {"parserOptions": {"ecmaFeatures": {"modules": true}, "ecmaVersion": 9, "sourceType": "module"}, "plugins": ["react-compiler"], "extends": ["sanity", "sanity/react", "sanity/typescript", "prettier"], "rules": {"react-compiler/react-compiler": "error", "react/prop-types": "off"}, "ignorePatterns": ["dist/**/"]}, "dependencies": {"@portabletext/toolkit": "^2.0.17", "@portabletext/types": "^2.0.13"}, "devDependencies": {"@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@sanity/pkg-utils": "^6.11.14", "@sanity/ui": "^2.9.1", "@types/leaflet": "^1.9.15", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@types/refractor": "^3.4.1", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.3.4", "babel-plugin-react-compiler": "19.0.0-beta-df7b47d-20241124", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "esbuild": "^0.24.0", "esbuild-register": "^3.6.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-config-sanity": "^7.1.3", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-compiler": "19.0.0-beta-df7b47d-20241124", "eslint-plugin-react-hooks": "^5.1.0", "husky": "^8.0.3", "leaflet": "^1.9.4", "npm-run-all2": "^5.0.2", "prettier": "^3.4.2", "prettier-plugin-packagejson": "^2.5.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-is": "^19.0.0", "react-leaflet": "^4.2.1", "react-refractor": "^2.2.0", "refractor": "^4.8.1", "rimraf": "^5.0.1", "rollup-plugin-visualizer": "^5.12.0", "styled-components": "^6.1.13", "typescript": "^5.7.2", "vite": "^5.4.11", "vitest": "^1.6.0"}, "peerDependencies": {"react": "^17 || ^18 || >=19.0.0-0"}, "engines": {"node": "^14.13.1 || >=16.0.0"}, "publishConfig": {"access": "public"}, "scripts": {"build": "pkg-utils build --strict --check --clean", "build:demo": "vite build demo --config=./vite.config.demo.ts --base=/react-portabletext/", "clean": "rimraf dist coverage demo/dist .nyc_output", "dev": "vite demo", "format": "prettier --write --cache --ignore-unknown .", "lint": "eslint .", "start": "vite demo", "test": "vitest", "type-check": "tsc --noEmit"}}