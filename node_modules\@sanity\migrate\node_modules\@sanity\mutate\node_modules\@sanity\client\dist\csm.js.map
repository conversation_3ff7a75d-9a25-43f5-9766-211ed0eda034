{"version": 3, "file": "csm.js", "sources": ["../src/csm/resolvePerspectives.ts", "../src/csm/createSourceDocumentResolver.ts", "../src/csm/applySourceDocuments.ts", "../src/csm/resolvedKeyedSourcePath.ts", "../src/csm/resolveEditUrl.ts"], "sourcesContent": ["import {validateApiPerspective} from '../config'\nimport type {StackablePerspective} from '../types'\nimport type {ClientPerspective} from './types'\n\n/**\n * This resolves the perspectives to how documents should be resolved when applying optimistic updates,\n * like in `applySourceDocuments`.\n * @internal\n */\nexport function resolvePerspectives(\n  perspective: Exclude<ClientPerspective, 'raw'>,\n): ('published' | 'drafts' | StackablePerspective)[] {\n  validateApiPerspective(perspective)\n\n  if (Array.isArray(perspective)) {\n    if (!perspective.includes('published')) {\n      return [...perspective, 'published']\n    }\n    return perspective\n  }\n  switch (perspective) {\n    case 'previewDrafts':\n    case 'drafts':\n      return ['drafts', 'published']\n    case 'published':\n    default:\n      return ['published']\n  }\n}\n", "import {getDraftId, getPublishedId, getVersionId} from './draftUtils'\nimport {resolvePerspectives} from './resolvePerspectives'\nimport type {ClientPerspective, ContentSourceMapDocuments, SanityDocument} from './types'\n\n/** @internal */\nexport type ResolvedDocument = Partial<SanityDocument> &\n  Required<Pick<SanityDocument, '_id' | '_type'>>\n\n/** @internal */\nexport type MatchedDocument = Partial<SanityDocument> &\n  Required<Pick<SanityDocument, '_id' | '_type' | '_originalId'>>\n\n/** @internal */\nexport function createSourceDocumentResolver(\n  getCachedDocument: (\n    sourceDocument: ContentSourceMapDocuments[number],\n  ) => ResolvedDocument | null | undefined,\n  _perspective: Exclude<ClientPerspective, 'raw'>,\n) {\n  const perspectives = resolvePerspectives(_perspective)\n  function findDocument(sourceDocument: ContentSourceMapDocuments[number]) {\n    for (const perspective of perspectives) {\n      let match: ReturnType<typeof getCachedDocument> = null\n      if (perspective.startsWith('r')) {\n        match = getCachedDocument({\n          ...sourceDocument,\n          _id: getVersionId(sourceDocument._id, perspective),\n        })\n      }\n      if (perspective === 'drafts') {\n        match = getCachedDocument({\n          ...sourceDocument,\n          _id: getDraftId(sourceDocument._id),\n        })\n      }\n      if (perspective === 'published') {\n        match = getCachedDocument({\n          ...sourceDocument,\n          _id: getPublishedId(sourceDocument._id),\n        })\n      }\n      if (match) {\n        return {...match, _id: getPublishedId(match._id), _originalId: match._id}\n      }\n    }\n    return null\n  }\n  // define resolver that loops over source documents and perspectives\n  return function resolveSourceDocument(\n    sourceDocument: ContentSourceMapDocuments[number],\n  ): MatchedDocument | null {\n    return findDocument(sourceDocument)\n  }\n}\n", "import {createSourceDocumentResolver} from './createSourceDocumentResolver'\nimport {parseJsonPath} from './jsonPath'\nimport {resolveMapping} from './resolveMapping'\nimport * as paths from './studioPath'\nimport type {\n  Any,\n  ApplySourceDocumentsUpdateFunction,\n  ClientPerspective,\n  ContentSourceMap,\n  ContentSourceMapDocuments,\n  Path,\n  SanityDocument,\n} from './types'\nimport {walkMap} from './walkMap'\n\n/**\n * Optimistically applies source documents to a result, using the content source map to trace fields.\n * Can be used to apply mutations to documents being edited in a Studio, or any mutation on Content Lake, to a result with extremely low latency.\n * @alpha\n */\nexport function applySourceDocuments<Result = unknown>(\n  result: Result,\n  resultSourceMap: ContentSourceMap | undefined,\n  getCachedDocument: (\n    sourceDocument: ContentSourceMapDocuments[number],\n  ) =>\n    | (Partial<SanityDocument> & Required<Pick<SanityDocument, '_id' | '_type'>>)\n    | null\n    | undefined,\n  updateFn: ApplySourceDocumentsUpdateFunction,\n  perspective: Exclude<ClientPerspective, 'raw'>,\n): Result {\n  if (!resultSourceMap) return result\n\n  const resolveDocument = createSourceDocumentResolver(getCachedDocument, perspective)\n  const cachedDocuments = resultSourceMap.documents?.map?.(resolveDocument) || []\n\n  return walkMap(JSON.parse(JSON.stringify(result)), (value, path) => {\n    const resolveMappingResult = resolveMapping(path, resultSourceMap)\n    if (!resolveMappingResult) {\n      return value\n    }\n\n    const {mapping, pathSuffix} = resolveMappingResult\n    if (mapping.type !== 'value') {\n      return value\n    }\n\n    if (mapping.source.type !== 'documentValue') {\n      return value\n    }\n\n    const sourceDocument = resultSourceMap.documents[mapping.source.document]\n    const sourcePath = resultSourceMap.paths[mapping.source.path]\n\n    if (sourceDocument) {\n      const parsedPath = parseJsonPath(sourcePath + pathSuffix)\n      const stringifiedPath = paths.toString(parsedPath as Path)\n      const cachedDocument = cachedDocuments[mapping.source.document]\n\n      if (!cachedDocument) {\n        return value\n      }\n\n      const changedValue = cachedDocument\n        ? paths.get<Result[keyof Result]>(cachedDocument, stringifiedPath, value)\n        : value\n      return value === changedValue\n        ? value\n        : updateFn<Result[keyof Result]>(changedValue as Any, {\n            cachedDocument,\n            previousValue: value as Result[keyof Result],\n            sourceDocument,\n            sourcePath: parsedPath,\n          })\n    }\n\n    return value\n  }) as Result\n}\n", "import {jsonPath, parseJsonPath} from './jsonPath'\nimport type {ContentSourceMapParsedPath} from './types'\n\n/**\n * @internal\n */\nexport function resolvedKeyedSourcePath(options: {\n  keyedResultPath: ContentSourceMapParsedPath\n  pathSuffix?: string\n  sourceBasePath: string\n}): ContentSourceMapParsedPath {\n  const {keyedResultPath, pathSuffix, sourceBasePath} = options\n\n  const inferredResultPath = pathSuffix === undefined ? [] : parseJsonPath(pathSuffix)\n\n  const inferredPath = keyedResultPath.slice(keyedResultPath.length - inferredResultPath.length)\n\n  const inferredPathSuffix = inferredPath.length ? jsonPath(inferredPath).slice(1) : ''\n\n  return parseJsonPath(sourceBasePath + inferredPathSuffix)\n}\n", "import {createEditUrl} from './createEditUrl'\nimport {studioPathToJsonPath} from './jsonPath'\nimport {resolveEditInfo} from './resolveEditInfo'\nimport type {ResolveEditUrlOptions} from './types'\n\n/** @alpha */\nexport function resolveEditUrl(\n  options: ResolveEditUrlOptions,\n): ReturnType<typeof createEditUrl> | undefined {\n  const {resultSourceMap, studioUrl} = options\n  const resultPath = studioPathToJsonPath(options.resultPath)\n\n  const editInfo = resolveEditInfo({\n    resultPath,\n    resultSourceMap,\n    studioUrl,\n  })\n  if (!editInfo) {\n    return undefined\n  }\n\n  return createEditUrl(editInfo)\n}\n"], "names": ["paths.toString", "paths.get"], "mappings": ";;;AASO,SAAS,oBACd,aACmD;AAGnD,MAFA,uBAAuB,WAAW,GAE9B,MAAM,QAAQ,WAAW;AACtB,WAAA,YAAY,SAAS,WAAW,IAG9B,cAFE,CAAC,GAAG,aAAa,WAAW;AAIvC,UAAQ,aAAa;AAAA,IACnB,KAAK;AAAA,IACL,KAAK;AACI,aAAA,CAAC,UAAU,WAAW;AAAA,IAC/B,KAAK;AAAA,IACL;AACE,aAAO,CAAC,WAAW;AAAA,EAAA;AAEzB;ACfgB,SAAA,6BACd,mBAGA,cACA;AACM,QAAA,eAAe,oBAAoB,YAAY;AACrD,WAAS,aAAa,gBAAmD;AACvE,eAAW,eAAe,cAAc;AACtC,UAAI,QAA8C;AAmBlD,UAlBI,YAAY,WAAW,GAAG,MAC5B,QAAQ,kBAAkB;AAAA,QACxB,GAAG;AAAA,QACH,KAAK,aAAa,eAAe,KAAK,WAAW;AAAA,MAClD,CAAA,IAEC,gBAAgB,aAClB,QAAQ,kBAAkB;AAAA,QACxB,GAAG;AAAA,QACH,KAAK,WAAW,eAAe,GAAG;AAAA,MACnC,CAAA,IAEC,gBAAgB,gBAClB,QAAQ,kBAAkB;AAAA,QACxB,GAAG;AAAA,QACH,KAAK,eAAe,eAAe,GAAG;AAAA,MACvC,CAAA,IAEC;AACK,eAAA,EAAC,GAAG,OAAO,KAAK,eAAe,MAAM,GAAG,GAAG,aAAa,MAAM,IAAG;AAAA,IAAA;AAGrE,WAAA;AAAA,EAAA;AAGT,SAAO,SACL,gBACwB;AACxB,WAAO,aAAa,cAAc;AAAA,EACpC;AACF;ACjCO,SAAS,qBACd,QACA,iBACA,mBAMA,UACA,aACQ;AACJ,MAAA,CAAC,gBAAwB,QAAA;AAEvB,QAAA,kBAAkB,6BAA6B,mBAAmB,WAAW,GAC7E,kBAAkB,gBAAgB,WAAW,MAAM,eAAe,KAAK,CAAC;AAEvE,SAAA,QAAQ,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC,GAAG,CAAC,OAAO,SAAS;AAC5D,UAAA,uBAAuB,eAAe,MAAM,eAAe;AACjE,QAAI,CAAC;AACI,aAAA;AAGH,UAAA,EAAC,SAAS,WAAA,IAAc;AAK9B,QAJI,QAAQ,SAAS,WAIjB,QAAQ,OAAO,SAAS;AACnB,aAAA;AAGT,UAAM,iBAAiB,gBAAgB,UAAU,QAAQ,OAAO,QAAQ,GAClE,aAAa,gBAAgB,MAAM,QAAQ,OAAO,IAAI;AAE5D,QAAI,gBAAgB;AAClB,YAAM,aAAa,cAAc,aAAa,UAAU,GAClD,kBAAkBA,SAAe,UAAkB,GACnD,iBAAiB,gBAAgB,QAAQ,OAAO,QAAQ;AAE9D,UAAI,CAAC;AACI,eAAA;AAGT,YAAM,eAAe,iBACjBC,IAAgC,gBAAgB,iBAAiB,KAAK,IACtE;AACJ,aAAO,UAAU,eACb,QACA,SAA+B,cAAqB;AAAA,QAClD;AAAA,QACA,eAAe;AAAA,QACf;AAAA,QACA,YAAY;AAAA,MAAA,CACb;AAAA,IAAA;AAGA,WAAA;AAAA,EAAA,CACR;AACH;ACzEO,SAAS,wBAAwB,SAIT;AAC7B,QAAM,EAAC,iBAAiB,YAAY,eAAA,IAAkB,SAEhD,qBAAqB,eAAe,SAAY,CAAK,IAAA,cAAc,UAAU,GAE7E,eAAe,gBAAgB,MAAM,gBAAgB,SAAS,mBAAmB,MAAM,GAEvF,qBAAqB,aAAa,SAAS,SAAS,YAAY,EAAE,MAAM,CAAC,IAAI;AAE5E,SAAA,cAAc,iBAAiB,kBAAkB;AAC1D;ACdO,SAAS,eACd,SAC8C;AACxC,QAAA,EAAC,iBAAiB,UAAa,IAAA,SAC/B,aAAa,qBAAqB,QAAQ,UAAU,GAEpD,WAAW,gBAAgB;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,EAAA,CACD;AACI,MAAA;AAIL,WAAO,cAAc,QAAQ;AAC/B;"}