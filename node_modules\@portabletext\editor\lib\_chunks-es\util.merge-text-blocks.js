import { isTypedObject, parseBlock } from "./util.slice-blocks.js";
function isTextBlock(context, block) {
  return isTypedObject(block) && block._type === context.schema.block.name;
}
function mergeTextBlocks({
  context,
  targetBlock,
  incomingBlock
}) {
  const parsedIncomingBlock = parseBlock({
    context,
    block: incomingBlock,
    options: {
      refreshKeys: !1,
      validateFields: !1
    }
  });
  return !parsedIncomingBlock || !isTextBlock(context, parsedIncomingBlock) ? targetBlock : {
    ...targetBlock,
    children: [...targetBlock.children, ...parsedIncomingBlock.children],
    markDefs: [...targetBlock.markDefs ?? [], ...parsedIncomingBlock.markDefs ?? []]
  };
}
export {
  isTextBlock,
  mergeTextBlocks
};
//# sourceMappingURL=util.merge-text-blocks.js.map
