{"version": 3, "file": "index.js", "sources": ["../src/encoders/compact/decode.ts", "../src/encoders/compact/encode.ts", "../src/mutations/creators.ts", "../src/mutations/operations/creators.ts", "../src/encoders/form-compat/encode.ts", "../src/formatters/compact.ts", "../src/mutations/autoKeys.ts"], "sourcesContent": ["import {\n  type Mutation,\n  type PatchMutation,\n  type SanityDocumentBase,\n} from '../../mutations/types'\nimport {type Index, type KeyedPathElement} from '../../path'\nimport {parse as parsePath} from '../../path/parser/parse'\nimport {\n  type CompactMutation,\n  type CompactPatchMutation,\n  type CreateIfNotExistsMutation,\n  type CreateMutation,\n  type CreateOrReplaceMutation,\n  type DeleteMutation,\n  type ItemRef,\n} from './types'\n\nexport {Mutation, SanityDocumentBase}\n\nexport function decode<Doc extends SanityDocumentBase>(\n  mutations: CompactMutation<Doc>[],\n): Mutation[] {\n  return mutations.map(decodeMutation)\n}\n\nexport function decodeMutation<Doc extends SanityDocumentBase>(\n  mutation: CompactMutation<Doc>,\n): Mutation {\n  const [type] = mutation\n  if (type === 'delete') {\n    const [, id] = mutation as DeleteMutation\n    return {id, type}\n  } else if (type === 'create') {\n    const [, document] = mutation as CreateMutation<Doc>\n    return {type, document}\n  } else if (type === 'createIfNotExists') {\n    const [, document] = mutation as CreateIfNotExistsMutation<Doc>\n    return {type, document}\n  } else if (type === 'createOrReplace') {\n    const [, document] = mutation as CreateOrReplaceMutation<Doc>\n    return {type, document}\n  } else if (type === 'patch') {\n    return decodePatchMutation(mutation)\n  }\n  throw new Error(`Unrecognized mutation: ${JSON.stringify(mutation)}`)\n}\n\nfunction decodePatchMutation(mutation: CompactPatchMutation): PatchMutation {\n  const [, type, id, serializedPath, , revisionId] = mutation\n\n  const path = parsePath(serializedPath)\n  if (type === 'dec' || type === 'inc') {\n    const [, , , , [amount]] = mutation\n    return {\n      type: 'patch',\n      id,\n      patches: [{path, op: {type: 'inc', amount}}],\n      ...createOpts(revisionId),\n    }\n  }\n  if (type === 'unset') {\n    return {\n      type: 'patch',\n      id,\n      patches: [{path, op: {type: 'unset'}}],\n      ...createOpts(revisionId),\n    }\n  }\n  if (type === 'insert') {\n    const [, , , , [position, ref, items]] = mutation\n    return {\n      type: 'patch',\n      id,\n      patches: [\n        {\n          path,\n          op: {\n            type: 'insert',\n            position,\n            items,\n            referenceItem: typeof ref === 'string' ? {_key: ref} : ref,\n          },\n        },\n      ],\n      ...createOpts(revisionId),\n    }\n  }\n  if (type === 'set') {\n    const [, , , , [value]] = mutation\n    return {\n      type: 'patch',\n      id,\n      patches: [{path, op: {type: 'set', value}}],\n      ...createOpts(revisionId),\n    }\n  }\n  if (type === 'setIfMissing') {\n    const [, , , , [value]] = mutation\n    return {\n      type: 'patch',\n      id,\n      patches: [{path, op: {type: 'setIfMissing', value}}],\n      ...createOpts(revisionId),\n    }\n  }\n  if (type === 'diffMatchPatch') {\n    const [, , , , [value]] = mutation\n    return {\n      type: 'patch',\n      id,\n      patches: [{path, op: {type: 'diffMatchPatch', value}}],\n      ...createOpts(revisionId),\n    }\n  }\n  if (type === 'truncate') {\n    const [, , , , [startIndex, endIndex]] = mutation\n\n    return {\n      type: 'patch',\n      id,\n      patches: [{path, op: {type: 'truncate', startIndex, endIndex}}],\n      ...createOpts(revisionId),\n    }\n  }\n  if (type === 'assign') {\n    const [, , , , [value]] = mutation\n    return {\n      type: 'patch',\n      id,\n      patches: [{path, op: {type: 'assign', value}}],\n      ...createOpts(revisionId),\n    }\n  }\n  if (type === 'replace') {\n    const [, , , , [ref, items]] = mutation\n    return {\n      type: 'patch',\n      id,\n      patches: [\n        {path, op: {type: 'replace', items, referenceItem: decodeItemRef(ref)}},\n      ],\n      ...createOpts(revisionId),\n    }\n  }\n  if (type === 'upsert') {\n    const [, , , , [position, referenceItem, items]] = mutation\n    return {\n      type: 'patch',\n      id,\n      patches: [\n        {\n          path,\n          op: {\n            type: 'upsert',\n            items,\n            referenceItem: decodeItemRef(referenceItem),\n            position,\n          },\n        },\n      ],\n      ...createOpts(revisionId),\n    }\n  }\n  throw new Error(`Invalid mutation type: ${type}`)\n}\n\nfunction decodeItemRef(ref: ItemRef): Index | KeyedPathElement {\n  return typeof ref === 'string' ? {_key: ref} : ref\n}\n\nfunction createOpts(revisionId: undefined | string) {\n  return revisionId ? {options: {ifRevision: revisionId}} : null\n}\n", "// An example of a compact transport/serialization format\nimport {\n  type Mutation,\n  type NodePatch,\n  type SanityDocumentBase,\n} from '../../mutations/types'\nimport {type Index, type KeyedPathElement} from '../../path'\nimport {stringify as stringifyPath} from '../../path/parser/stringify'\nimport {\n  type CompactMutation,\n  type CompactPatchMutation,\n  type ItemRef,\n} from './types'\n\nexport function encode<Doc extends SanityDocumentBase>(\n  mutations: Mutation[],\n): CompactMutation<Doc>[] {\n  return mutations.flatMap(m => encodeMutation<Doc>(m))\n}\n\nfunction encodeItemRef(ref: Index | KeyedPathElement): ItemRef {\n  return typeof ref === 'number' ? ref : ref._key\n}\n\nfunction encodeMutation<Doc extends SanityDocumentBase>(\n  mutation: Mutation,\n): CompactMutation<Doc>[] {\n  if (\n    mutation.type === 'create' ||\n    mutation.type === 'createIfNotExists' ||\n    mutation.type === 'createOrReplace'\n  ) {\n    return [[mutation.type, mutation.document]]\n  }\n  if (mutation.type === 'delete') {\n    return [['delete', mutation.id]]\n  }\n  if (mutation.type === 'patch') {\n    return mutation.patches.map(patch =>\n      maybeAddRevision(\n        mutation.options?.ifRevision,\n        encodePatchMutation(mutation.id, patch),\n      ),\n    )\n  }\n\n  //@ts-expect-error - all cases are covered\n  throw new Error(`Invalid mutation type: ${mutation.type}`)\n}\n\nfunction encodePatchMutation(\n  id: string,\n  patch: NodePatch<any>,\n): CompactPatchMutation {\n  const {op} = patch\n  const path = stringifyPath(patch.path)\n  if (op.type === 'unset') {\n    return ['patch', 'unset', id, path, []]\n  }\n  if (op.type === 'diffMatchPatch') {\n    return ['patch', 'diffMatchPatch', id, path, [op.value]]\n  }\n  if (op.type === 'inc' || op.type === 'dec') {\n    return ['patch', op.type, id, path, [op.amount]]\n  }\n  if (op.type === 'set') {\n    return ['patch', op.type, id, path, [op.value]]\n  }\n  if (op.type === 'setIfMissing') {\n    return ['patch', op.type, id, path, [op.value]]\n  }\n  if (op.type === 'insert') {\n    return [\n      'patch',\n      'insert',\n      id,\n      path,\n      [op.position, encodeItemRef(op.referenceItem), op.items],\n    ]\n  }\n  if (op.type === 'upsert') {\n    return [\n      'patch',\n      'upsert',\n      id,\n      path,\n      [op.position, encodeItemRef(op.referenceItem), op.items],\n    ]\n  }\n  if (op.type === 'assign') {\n    return ['patch', 'assign', id, path, [op.value]]\n  }\n  if (op.type === 'unassign') {\n    return ['patch', 'assign', id, path, [op.keys]]\n  }\n  if (op.type === 'replace') {\n    return [\n      'patch',\n      'replace',\n      id,\n      path,\n      [encodeItemRef(op.referenceItem), op.items],\n    ]\n  }\n  if (op.type === 'truncate') {\n    return ['patch', 'truncate', id, path, [op.startIndex, op.endIndex]]\n  }\n  if (op.type === 'remove') {\n    return ['patch', 'remove', id, path, [encodeItemRef(op.referenceItem)]]\n  }\n  // @ts-expect-error all cases are covered\n  throw new Error(`Invalid operation type: ${op.type}`)\n}\n\nfunction maybeAddRevision<T extends CompactPatchMutation>(\n  revision: string | undefined,\n  mut: T,\n): T {\n  const [mutType, patchType, id, path, args] = mut\n  return (revision ? [mutType, patchType, id, path, args, revision] : mut) as T\n}\n", "import {parse, type Path, type SafePath} from '../path'\nimport {arrify} from '../utils/arrify'\nimport {\n  type NormalizeReadOnlyArray,\n  type Optional,\n  type Tuplify,\n} from '../utils/typeUtils'\nimport {type Operation} from './operations/types'\nimport {\n  type CreateIfNotExistsMutation,\n  type CreateMutation,\n  type CreateOrReplaceMutation,\n  type DeleteMutation,\n  type NodePatch,\n  type NodePatchList,\n  type PatchMutation,\n  type PatchOptions,\n  type SanityDocumentBase,\n} from './types'\n\nexport function create<Doc extends Optional<SanityDocumentBase, '_id'>>(\n  document: Doc,\n): CreateMutation<Doc> {\n  return {type: 'create', document}\n}\n\nexport function patch<P extends NodePatchList | NodePatch>(\n  id: string,\n  patches: P,\n  options?: PatchOptions,\n): PatchMutation<NormalizeReadOnlyArray<Tuplify<P>>> {\n  return {\n    type: 'patch',\n    id,\n    patches: arrify(patches) as any,\n    ...(options ? {options} : {}),\n  }\n}\n\nexport function at<const P extends Path, O extends Operation>(\n  path: P,\n  operation: O,\n): NodePatch<NormalizeReadOnlyArray<P>, O>\n\nexport function at<const P extends string, O extends Operation>(\n  path: P,\n  operation: O,\n): NodePatch<SafePath<P>, O>\n\nexport function at<O extends Operation>(\n  path: Path | string,\n  operation: O,\n): NodePatch<Path, O> {\n  return {\n    path: typeof path === 'string' ? parse(path) : path,\n    op: operation,\n  }\n}\n\nexport function createIfNotExists<Doc extends SanityDocumentBase>(\n  document: Doc,\n): CreateIfNotExistsMutation<Doc> {\n  return {type: 'createIfNotExists', document}\n}\n\nexport function createOrReplace<Doc extends SanityDocumentBase>(\n  document: Doc,\n): CreateOrReplaceMutation<Doc> {\n  return {type: 'createOrReplace', document}\n}\n\nexport function delete_(id: string): DeleteMutation {\n  return {type: 'delete', id}\n}\n\nexport const del = delete_\nexport const destroy = delete_\n", "import {arrify} from '../../utils/arrify'\nimport {\n  type AnyArray,\n  type ArrayElement,\n  type NormalizeReadOnlyArray,\n} from '../../utils/typeUtils'\nimport {\n  type AssignOp,\n  type DecOp,\n  type DiffMatchPatchOp,\n  type IncOp,\n  type Index,\n  type InsertOp,\n  type KeyedPathElement,\n  type RelativePosition,\n  type RemoveOp,\n  type ReplaceOp,\n  type SetIfMissingOp,\n  type SetOp,\n  type TruncateOp,\n  type UnassignOp,\n  type UnsetOp,\n  type UpsertOp,\n} from './types'\n\nexport const set = <const T>(value: T): SetOp<T> => ({type: 'set', value})\n\nexport const assign = <const T extends {[K in string]: unknown}>(\n  value: T,\n): AssignOp<T> => ({\n  type: 'assign',\n  value,\n})\n\nexport const unassign = <const K extends readonly string[]>(\n  keys: K,\n): UnassignOp<K> => ({\n  type: 'unassign',\n  keys,\n})\n\nexport const setIfMissing = <const T>(value: T): SetIfMissingOp<T> => ({\n  type: 'setIfMissing',\n  value,\n})\n\nexport const unset = (): UnsetOp => ({type: 'unset'})\n\nexport const inc = <const N extends number = 1>(\n  amount: N = 1 as N,\n): IncOp<N> => ({\n  type: 'inc',\n  amount,\n})\n\nexport const dec = <const N extends number = 1>(\n  amount: N = 1 as N,\n): DecOp<N> => ({\n  type: 'dec',\n  amount,\n})\n\nexport const diffMatchPatch = (value: string): DiffMatchPatchOp => ({\n  type: 'diffMatchPatch',\n  value,\n})\n\nexport function insert<\n  const Items extends AnyArray<unknown>,\n  const Pos extends RelativePosition,\n  const ReferenceItem extends Index | KeyedPathElement,\n>(\n  items: Items | ArrayElement<Items>,\n  position: Pos,\n  indexOrReferenceItem: ReferenceItem,\n): InsertOp<NormalizeReadOnlyArray<Items>, Pos, ReferenceItem> {\n  return {\n    type: 'insert',\n    referenceItem: indexOrReferenceItem,\n    position,\n    items: arrify(items) as any,\n  }\n}\n\nexport function append<const Items extends AnyArray<unknown>>(\n  items: Items | ArrayElement<Items>,\n) {\n  return insert(items, 'after', -1)\n}\n\nexport function prepend<const Items extends AnyArray<unknown>>(\n  items: Items | ArrayElement<Items>,\n) {\n  return insert(items, 'before', 0)\n}\n\nexport function insertBefore<\n  const Items extends AnyArray<unknown>,\n  const ReferenceItem extends Index | KeyedPathElement,\n>(items: Items | ArrayElement<Items>, indexOrReferenceItem: ReferenceItem) {\n  return insert(items, 'before', indexOrReferenceItem)\n}\n\nexport const insertAfter = <\n  const Items extends AnyArray<unknown>,\n  const ReferenceItem extends Index | KeyedPathElement,\n>(\n  items: Items | ArrayElement<Items>,\n  indexOrReferenceItem: ReferenceItem,\n) => {\n  return insert(items, 'after', indexOrReferenceItem)\n}\n\nexport function truncate(startIndex: number, endIndex?: number): TruncateOp {\n  return {\n    type: 'truncate',\n    startIndex,\n    endIndex,\n  }\n}\n\n/*\n  Use this when you know the ref Items already exists\n */\nexport function replace<\n  Items extends any[],\n  ReferenceItem extends Index | KeyedPathElement,\n>(\n  items: Items | ArrayElement<Items>,\n  referenceItem: ReferenceItem,\n): ReplaceOp<Items, ReferenceItem> {\n  return {\n    type: 'replace',\n    referenceItem,\n    items: arrify(items) as Items,\n  }\n}\n\n/*\n  Remove an item from an array by either key or index\n */\nexport function remove<ReferenceItem extends Index | KeyedPathElement>(\n  referenceItem: ReferenceItem,\n): RemoveOp<ReferenceItem> {\n  return {\n    type: 'remove',\n    referenceItem,\n  }\n}\n\n/*\nuse this when the reference Items may or may not exist\n */\nexport function upsert<\n  const Items extends AnyArray<unknown>,\n  const Pos extends RelativePosition,\n  const ReferenceItem extends Index | KeyedPathElement,\n>(\n  items: Items | ArrayElement<Items>,\n  position: Pos,\n  referenceItem: ReferenceItem,\n): UpsertOp<Items, Pos, ReferenceItem> {\n  return {\n    type: 'upsert',\n    items: arrify(items) as Items,\n    referenceItem,\n    position,\n  }\n}\n", "import {at} from '../../mutations/creators'\nimport {\n  diffMatchPatch,\n  insert,\n  set,\n  setIfMissing,\n  unset,\n} from '../../mutations/operations/creators'\nimport {type NodePatch} from '../../mutations/types'\nimport {type KeyedPathElement} from '../../path'\nimport {\n  type CompatPath,\n  type FormPatchLike,\n  type FormPatchPath,\n} from './form-patch-types'\n\nfunction assertCompatible(formPatchPath: FormPatchPath): CompatPath {\n  if (formPatchPath.length === 0) {\n    return formPatchPath as never[]\n  }\n  for (const element of formPatchPath) {\n    if (Array.isArray(element)) {\n      throw new Error('Form patch paths cannot include arrays')\n    }\n  }\n  return formPatchPath as CompatPath\n}\n\n/**\n * Convert a Sanity form patch (ie emitted from an input component) to a {@link NodePatch}\n * Note the lack of encodeMutation here. Sanity forms never emit *mutations*, only patches\n * @param patches - Array of {@link FormPatchLike}\n * @internal\n */\nexport function encodePatches(patches: Form<PERSON>atchLike[]): NodePatch[] {\n  return patches.map(formPatch => {\n    const path = assertCompatible(formPatch.path)\n    if (formPatch.type === 'unset') {\n      return at(path, unset())\n    }\n    if (formPatch.type === 'set') {\n      return at(path, set(formPatch.value))\n    }\n    if (formPatch.type === 'setIfMissing') {\n      return at(path, setIfMissing(formPatch.value))\n    }\n    if (formPatch.type === 'insert') {\n      const arrayPath = path.slice(0, -1)\n      const itemRef = formPatch.path[formPatch.path.length - 1]\n      return at(\n        arrayPath,\n        insert(\n          formPatch.items,\n          formPatch.position,\n          itemRef as number | KeyedPathElement,\n        ),\n      )\n    }\n    if (formPatch.type === 'diffMatchPatch') {\n      return at(path, diffMatchPatch(formPatch.value))\n    }\n    // @ts-expect-error - should be exhaustive\n    throw new Error(`Unknown patch type ${formPatch.type}`)\n  })\n}\n", "// An example of a compact formatter\n\nimport {\n  type Mutation,\n  type NodePatch,\n  type SanityDocumentBase,\n} from '../mutations/types'\nimport {type Index, type KeyedPathElement, stringify} from '../path'\n\nexport type ItemRef = string | number\n\nexport function format<Doc extends SanityDocumentBase>(\n  mutations: Mutation[],\n): string {\n  return mutations.flatMap(m => encodeMutation<Doc>(m)).join('\\n')\n}\n\nfunction encodeItemRef(ref: Index | KeyedPathElement): ItemRef {\n  return typeof ref === 'number' ? ref : ref._key\n}\n\nfunction encodeMutation<Doc extends SanityDocumentBase>(\n  mutation: Mutation,\n): string {\n  if (\n    mutation.type === 'create' ||\n    mutation.type === 'createIfNotExists' ||\n    mutation.type === 'createOrReplace'\n  ) {\n    return [mutation.type, ': ', JSON.stringify(mutation.document)].join('')\n  }\n  if (mutation.type === 'delete') {\n    return ['delete ', mutation.id].join(': ')\n  }\n  if (mutation.type === 'patch') {\n    const ifRevision = mutation.options?.ifRevision\n    return [\n      'patch',\n      ' ',\n      `id=${mutation.id}`,\n      ifRevision ? ` (if revision==${ifRevision})` : '',\n      ':\\n',\n      mutation.patches\n        .map(nodePatch => `  ${formatPatchMutation(nodePatch)}`)\n        .join('\\n'),\n    ].join('')\n  }\n\n  //@ts-expect-error - all cases are covered\n  throw new Error(`Invalid mutation type: ${mutation.type}`)\n}\n\nfunction formatPatchMutation(patch: NodePatch<any>): string {\n  const {op} = patch\n  const path = stringify(patch.path)\n  if (op.type === 'unset') {\n    return [path, 'unset()'].join(': ')\n  }\n  if (op.type === 'diffMatchPatch') {\n    return [path, `diffMatchPatch(${op.value})`].join(': ')\n  }\n  if (op.type === 'inc' || op.type === 'dec') {\n    return [path, `${op.type}(${op.amount})`].join(': ')\n  }\n  if (op.type === 'set' || op.type === 'setIfMissing') {\n    return [path, `${op.type}(${JSON.stringify(op.value)})`].join(': ')\n  }\n  if (op.type === 'assign') {\n    return [path, `${op.type}(${JSON.stringify(op.value)})`].join(': ')\n  }\n  if (op.type === 'unassign') {\n    return [path, `${op.type}(${JSON.stringify(op.keys)})`].join(': ')\n  }\n  if (op.type === 'insert' || op.type === 'upsert') {\n    return [\n      path,\n      `${op.type}(${op.position}, ${encodeItemRef(\n        op.referenceItem,\n      )}, ${JSON.stringify(op.items)})`,\n    ].join(': ')\n  }\n  if (op.type === 'replace') {\n    return [\n      path,\n      `replace(${encodeItemRef(op.referenceItem)}, ${JSON.stringify(\n        op.items,\n      )})`,\n    ].join(': ')\n  }\n  if (op.type === 'truncate') {\n    return [path, `truncate(${op.startIndex}, ${op.endIndex}`].join(': ')\n  }\n  if (op.type === 'remove') {\n    return [path, `remove(${encodeItemRef(op.referenceItem)})`].join(': ')\n  }\n  // @ts-expect-error all cases are covered\n  throw new Error(`Invalid operation type: ${op.type}`)\n}\n", "import {type Index, type KeyedPathElement} from '../path'\nimport {isObject} from '../utils/isObject'\nimport {\n  insert as _insert,\n  replace as _replace,\n  upsert as _upsert,\n} from './operations/creators'\nimport {type RelativePosition} from './operations/types'\n\nexport function autoKeys<Item>(generateKey: (item: Item) => string) {\n  const ensureKeys = createEnsureKeys(generateKey)\n\n  const insert = <\n    Pos extends RelativePosition,\n    Ref extends Index | KeyedPathElement,\n  >(\n    position: Pos,\n    referenceItem: Ref,\n    items: Item[],\n  ) => _insert(ensureKeys(items), position, referenceItem)\n  const upsert = <\n    Pos extends RelativePosition,\n    ReferenceItem extends Index | KeyedPathElement,\n  >(\n    items: Item[],\n    position: Pos,\n    referenceItem: ReferenceItem,\n  ) => _upsert(ensureKeys(items), position, referenceItem)\n\n  const replace = <\n    Pos extends RelativePosition,\n    ReferenceItem extends Index | KeyedPathElement,\n  >(\n    items: Item[],\n    position: Pos,\n    referenceItem: ReferenceItem,\n  ) => _replace(ensureKeys(items), referenceItem)\n\n  const insertBefore = <Ref extends Index | KeyedPathElement>(\n    ref: Ref,\n    items: Item[],\n  ) => insert('before', ref, items)\n\n  const prepend = (items: Item[]) => insertBefore(0, items)\n\n  const insertAfter = <Ref extends Index | KeyedPathElement>(\n    ref: Ref,\n    items: Item[],\n  ) => insert('after', ref, items)\n\n  const append = (items: Item[]) => insert('after', -1, items)\n\n  return {insert, upsert, replace, insertBefore, prepend, insertAfter, append}\n}\n\nfunction hasKey<T extends object>(item: T): item is T & {_key: string} {\n  return '_key' in item\n}\n\nfunction createEnsureKeys<T>(generateKey: (item: T) => string) {\n  return (array: T[]): T[] => {\n    let didModify = false\n    const withKeys = array.map(item => {\n      if (needsKey(item)) {\n        didModify = true\n        return {...item, _key: generateKey(item)}\n      }\n      return item\n    })\n    return didModify ? withKeys : array\n  }\n}\n\nfunction needsKey(arrayItem: any): arrayItem is object {\n  return isObject(arrayItem) && !hasKey(arrayItem)\n}\n"], "names": ["parsePath", "encodeMutation", "encodeItemRef", "patch", "stringifyPath", "insert", "_insert", "upsert", "_upsert", "replace", "_replace", "insertBefore"], "mappings": ";;;;;AAmBO,SAAS,OACd,WACY;AACL,SAAA,UAAU,IAAI,cAAc;AACrC;AAEO,SAAS,eACd,UACU;AACJ,QAAA,CAAC,IAAI,IAAI;AACf,MAAI,SAAS,UAAU;AACf,UAAA,CAAA,EAAG,EAAE,IAAI;AACR,WAAA,EAAC,IAAI,KAAI;AAAA,EAAA,WACP,SAAS,UAAU;AACtB,UAAA,CAAA,EAAG,QAAQ,IAAI;AACd,WAAA,EAAC,MAAM,SAAQ;AAAA,EAAA,WACb,SAAS,qBAAqB;AACjC,UAAA,CAAA,EAAG,QAAQ,IAAI;AACd,WAAA,EAAC,MAAM,SAAQ;AAAA,EAAA,WACb,SAAS,mBAAmB;AAC/B,UAAA,CAAA,EAAG,QAAQ,IAAI;AACd,WAAA,EAAC,MAAM,SAAQ;AAAA,EAAA,WACb,SAAS;AAClB,WAAO,oBAAoB,QAAQ;AAErC,QAAM,IAAI,MAAM,0BAA0B,KAAK,UAAU,QAAQ,CAAC,EAAE;AACtE;AAEA,SAAS,oBAAoB,UAA+C;AACpE,QAAA,CAAG,EAAA,MAAM,IAAI,gBAAkB,EAAA,UAAU,IAAI,UAE7C,OAAOA,MAAU,cAAc;AACjC,MAAA,SAAS,SAAS,SAAS,OAAO;AAC9B,UAAA,CAAS,EAAA,EAAA,EAAA,EAAA,CAAC,MAAM,CAAC,IAAI;AACpB,WAAA;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,SAAS,CAAC,EAAC,MAAM,IAAI,EAAC,MAAM,OAAO,OAAM,GAAE;AAAA,MAC3C,GAAG,WAAW,UAAU;AAAA,IAC1B;AAAA,EAAA;AAEF,MAAI,SAAS;AACJ,WAAA;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,SAAS,CAAC,EAAC,MAAM,IAAI,EAAC,MAAM,QAAO,GAAE;AAAA,MACrC,GAAG,WAAW,UAAU;AAAA,IAC1B;AAEF,MAAI,SAAS,UAAU;AACf,UAAA,CAAS,EAAA,EAAA,EAAA,EAAA,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI;AAClC,WAAA;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,SAAS;AAAA,QACP;AAAA,UACE;AAAA,UACA,IAAI;AAAA,YACF,MAAM;AAAA,YACN;AAAA,YACA;AAAA,YACA,eAAe,OAAO,OAAQ,WAAW,EAAC,MAAM,QAAO;AAAA,UAAA;AAAA,QACzD;AAAA,MAEJ;AAAA,MACA,GAAG,WAAW,UAAU;AAAA,IAC1B;AAAA,EAAA;AAEF,MAAI,SAAS,OAAO;AACZ,UAAA,CAAS,EAAA,EAAA,EAAA,EAAA,CAAC,KAAK,CAAC,IAAI;AACnB,WAAA;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,SAAS,CAAC,EAAC,MAAM,IAAI,EAAC,MAAM,OAAO,MAAK,GAAE;AAAA,MAC1C,GAAG,WAAW,UAAU;AAAA,IAC1B;AAAA,EAAA;AAEF,MAAI,SAAS,gBAAgB;AACrB,UAAA,CAAS,EAAA,EAAA,EAAA,EAAA,CAAC,KAAK,CAAC,IAAI;AACnB,WAAA;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,SAAS,CAAC,EAAC,MAAM,IAAI,EAAC,MAAM,gBAAgB,MAAK,GAAE;AAAA,MACnD,GAAG,WAAW,UAAU;AAAA,IAC1B;AAAA,EAAA;AAEF,MAAI,SAAS,kBAAkB;AACvB,UAAA,CAAS,EAAA,EAAA,EAAA,EAAA,CAAC,KAAK,CAAC,IAAI;AACnB,WAAA;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,SAAS,CAAC,EAAC,MAAM,IAAI,EAAC,MAAM,kBAAkB,MAAK,GAAE;AAAA,MACrD,GAAG,WAAW,UAAU;AAAA,IAC1B;AAAA,EAAA;AAEF,MAAI,SAAS,YAAY;AACjB,UAAA,SAAS,CAAC,YAAY,QAAQ,CAAC,IAAI;AAElC,WAAA;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,SAAS,CAAC,EAAC,MAAM,IAAI,EAAC,MAAM,YAAY,YAAY,SAAQ,GAAE;AAAA,MAC9D,GAAG,WAAW,UAAU;AAAA,IAC1B;AAAA,EAAA;AAEF,MAAI,SAAS,UAAU;AACf,UAAA,CAAS,EAAA,EAAA,EAAA,EAAA,CAAC,KAAK,CAAC,IAAI;AACnB,WAAA;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,SAAS,CAAC,EAAC,MAAM,IAAI,EAAC,MAAM,UAAU,MAAK,GAAE;AAAA,MAC7C,GAAG,WAAW,UAAU;AAAA,IAC1B;AAAA,EAAA;AAEF,MAAI,SAAS,WAAW;AAChB,UAAA,SAAS,CAAC,KAAK,KAAK,CAAC,IAAI;AACxB,WAAA;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,SAAS;AAAA,QACP,EAAC,MAAM,IAAI,EAAC,MAAM,WAAW,OAAO,eAAe,cAAc,GAAG,EAAE,EAAA;AAAA,MACxE;AAAA,MACA,GAAG,WAAW,UAAU;AAAA,IAC1B;AAAA,EAAA;AAEF,MAAI,SAAS,UAAU;AACf,UAAA,CAAS,EAAA,EAAA,EAAA,EAAA,CAAC,UAAU,eAAe,KAAK,CAAC,IAAI;AAC5C,WAAA;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,SAAS;AAAA,QACP;AAAA,UACE;AAAA,UACA,IAAI;AAAA,YACF,MAAM;AAAA,YACN;AAAA,YACA,eAAe,cAAc,aAAa;AAAA,YAC1C;AAAA,UAAA;AAAA,QACF;AAAA,MAEJ;AAAA,MACA,GAAG,WAAW,UAAU;AAAA,IAC1B;AAAA,EAAA;AAEF,QAAM,IAAI,MAAM,0BAA0B,IAAI,EAAE;AAClD;AAEA,SAAS,cAAc,KAAwC;AAC7D,SAAO,OAAO,OAAQ,WAAW,EAAC,MAAM,IAAO,IAAA;AACjD;AAEA,SAAS,WAAW,YAAgC;AAClD,SAAO,aAAa,EAAC,SAAS,EAAC,YAAY,iBAAe;AAC5D;AC9JO,SAAS,OACd,WACwB;AACxB,SAAO,UAAU,QAAQ,CAAK,MAAAC,iBAAoB,CAAC,CAAC;AACtD;AAEA,SAASC,gBAAc,KAAwC;AAC7D,SAAO,OAAO,OAAQ,WAAW,MAAM,IAAI;AAC7C;AAEA,SAASD,iBACP,UACwB;AACxB,MACE,SAAS,SAAS,YAClB,SAAS,SAAS,uBAClB,SAAS,SAAS;AAElB,WAAO,CAAC,CAAC,SAAS,MAAM,SAAS,QAAQ,CAAC;AAE5C,MAAI,SAAS,SAAS;AACpB,WAAO,CAAC,CAAC,UAAU,SAAS,EAAE,CAAC;AAEjC,MAAI,SAAS,SAAS;AACpB,WAAO,SAAS,QAAQ;AAAA,MAAI,CAC1BE,WAAA;AAAA,QACE,SAAS,SAAS;AAAA,QAClB,oBAAoB,SAAS,IAAIA,MAAK;AAAA,MAAA;AAAA,IAE1C;AAIF,QAAM,IAAI,MAAM,0BAA0B,SAAS,IAAI,EAAE;AAC3D;AAEA,SAAS,oBACP,IACAA,QACsB;AACtB,QAAM,EAAC,GAAE,IAAIA,QACP,OAAOC,UAAcD,OAAM,IAAI;AACrC,MAAI,GAAG,SAAS;AACd,WAAO,CAAC,SAAS,SAAS,IAAI,MAAM,CAAA,CAAE;AAExC,MAAI,GAAG,SAAS;AACP,WAAA,CAAC,SAAS,kBAAkB,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC;AAEzD,MAAI,GAAG,SAAS,SAAS,GAAG,SAAS;AAC5B,WAAA,CAAC,SAAS,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC;AAEjD,MAAI,GAAG,SAAS;AACP,WAAA,CAAC,SAAS,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC;AAEhD,MAAI,GAAG,SAAS;AACP,WAAA,CAAC,SAAS,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC;AAEhD,MAAI,GAAG,SAAS;AACP,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,GAAG,UAAUD,gBAAc,GAAG,aAAa,GAAG,GAAG,KAAK;AAAA,IACzD;AAEF,MAAI,GAAG,SAAS;AACP,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,GAAG,UAAUA,gBAAc,GAAG,aAAa,GAAG,GAAG,KAAK;AAAA,IACzD;AAEF,MAAI,GAAG,SAAS;AACP,WAAA,CAAC,SAAS,UAAU,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC;AAEjD,MAAI,GAAG,SAAS;AACP,WAAA,CAAC,SAAS,UAAU,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC;AAEhD,MAAI,GAAG,SAAS;AACP,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAACA,gBAAc,GAAG,aAAa,GAAG,GAAG,KAAK;AAAA,IAC5C;AAEF,MAAI,GAAG,SAAS;AACP,WAAA,CAAC,SAAS,YAAY,IAAI,MAAM,CAAC,GAAG,YAAY,GAAG,QAAQ,CAAC;AAErE,MAAI,GAAG,SAAS;AACP,WAAA,CAAC,SAAS,UAAU,IAAI,MAAM,CAACA,gBAAc,GAAG,aAAa,CAAC,CAAC;AAGxE,QAAM,IAAI,MAAM,2BAA2B,GAAG,IAAI,EAAE;AACtD;AAEA,SAAS,iBACP,UACA,KACG;AACH,QAAM,CAAC,SAAS,WAAW,IAAI,MAAM,IAAI,IAAI;AACrC,SAAA,WAAW,CAAC,SAAS,WAAW,IAAI,MAAM,MAAM,QAAQ,IAAI;AACtE;;;;;;ACpGO,SAAS,OACd,UACqB;AACd,SAAA,EAAC,MAAM,UAAU,SAAQ;AAClC;AAEgB,SAAA,MACd,IACA,SACA,SACmD;AAC5C,SAAA;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,SAAS,OAAO,OAAO;AAAA,IACvB,GAAI,UAAU,EAAC,YAAW,CAAA;AAAA,EAC5B;AACF;AAYgB,SAAA,GACd,MACA,WACoB;AACb,SAAA;AAAA,IACL,MAAM,OAAO,QAAS,WAAW,MAAM,IAAI,IAAI;AAAA,IAC/C,IAAI;AAAA,EACN;AACF;AAEO,SAAS,kBACd,UACgC;AACzB,SAAA,EAAC,MAAM,qBAAqB,SAAQ;AAC7C;AAEO,SAAS,gBACd,UAC8B;AACvB,SAAA,EAAC,MAAM,mBAAmB,SAAQ;AAC3C;AAEO,SAAS,QAAQ,IAA4B;AAC3C,SAAA,EAAC,MAAM,UAAU,GAAE;AAC5B;AAEa,MAAA,MAAM,SACN,UAAU,SCnDV,MAAM,CAAU,WAAwB,EAAC,MAAM,OAAO,MAAK,IAE3D,SAAS,CACpB,WACiB;AAAA,EACjB,MAAM;AAAA,EACN;AACF,IAEa,WAAW,CACtB,UACmB;AAAA,EACnB,MAAM;AAAA,EACN;AACF,IAEa,eAAe,CAAU,WAAiC;AAAA,EACrE,MAAM;AAAA,EACN;AACF,IAEa,QAAQ,OAAgB,EAAC,MAAM,YAE/B,MAAM,CACjB,SAAY,OACE;AAAA,EACd,MAAM;AAAA,EACN;AACF,IAEa,MAAM,CACjB,SAAY,OACE;AAAA,EACd,MAAM;AAAA,EACN;AACF,IAEa,iBAAiB,CAAC,WAAqC;AAAA,EAClE,MAAM;AAAA,EACN;AACF;AAEgB,SAAA,OAKd,OACA,UACA,sBAC6D;AACtD,SAAA;AAAA,IACL,MAAM;AAAA,IACN,eAAe;AAAA,IACf;AAAA,IACA,OAAO,OAAO,KAAK;AAAA,EACrB;AACF;AAEO,SAAS,OACd,OACA;AACO,SAAA,OAAO,OAAO,SAAS,EAAE;AAClC;AAEO,SAAS,QACd,OACA;AACO,SAAA,OAAO,OAAO,UAAU,CAAC;AAClC;AAEgB,SAAA,aAGd,OAAoC,sBAAqC;AAClE,SAAA,OAAO,OAAO,UAAU,oBAAoB;AACrD;AAEO,MAAM,cAAc,CAIzB,OACA,yBAEO,OAAO,OAAO,SAAS,oBAAoB;AAGpC,SAAA,SAAS,YAAoB,UAA+B;AACnE,SAAA;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;AAKgB,SAAA,QAId,OACA,eACiC;AAC1B,SAAA;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,OAAO,OAAO,KAAK;AAAA,EACrB;AACF;AAKO,SAAS,OACd,eACyB;AAClB,SAAA;AAAA,IACL,MAAM;AAAA,IACN;AAAA,EACF;AACF;AAKgB,SAAA,OAKd,OACA,UACA,eACqC;AAC9B,SAAA;AAAA,IACL,MAAM;AAAA,IACN,OAAO,OAAO,KAAK;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AACF;ACxJA,SAAS,iBAAiB,eAA0C;AAClE,MAAI,cAAc,WAAW;AACpB,WAAA;AAET,aAAW,WAAW;AAChB,QAAA,MAAM,QAAQ,OAAO;AACjB,YAAA,IAAI,MAAM,wCAAwC;AAGrD,SAAA;AACT;AAQO,SAAS,cAAc,SAAuC;AAC5D,SAAA,QAAQ,IAAI,CAAa,cAAA;AACxB,UAAA,OAAO,iBAAiB,UAAU,IAAI;AAC5C,QAAI,UAAU,SAAS;AACd,aAAA,GAAG,MAAM,OAAO;AAEzB,QAAI,UAAU,SAAS;AACrB,aAAO,GAAG,MAAM,IAAI,UAAU,KAAK,CAAC;AAEtC,QAAI,UAAU,SAAS;AACrB,aAAO,GAAG,MAAM,aAAa,UAAU,KAAK,CAAC;AAE3C,QAAA,UAAU,SAAS,UAAU;AAC/B,YAAM,YAAY,KAAK,MAAM,GAAG,EAAE,GAC5B,UAAU,UAAU,KAAK,UAAU,KAAK,SAAS,CAAC;AACjD,aAAA;AAAA,QACL;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,UAAU;AAAA,UACV;AAAA,QAAA;AAAA,MAEJ;AAAA,IAAA;AAEF,QAAI,UAAU,SAAS;AACrB,aAAO,GAAG,MAAM,eAAe,UAAU,KAAK,CAAC;AAGjD,UAAM,IAAI,MAAM,sBAAsB,UAAU,IAAI,EAAE;AAAA,EAAA,CACvD;AACH;;;;;;;;;;;;;ACrDO,SAAS,OACd,WACQ;AACR,SAAO,UAAU,QAAQ,CAAA,MAAK,eAAoB,CAAC,CAAC,EAAE,KAAK;AAAA,CAAI;AACjE;AAEA,SAAS,cAAc,KAAwC;AAC7D,SAAO,OAAO,OAAQ,WAAW,MAAM,IAAI;AAC7C;AAEA,SAAS,eACP,UACQ;AACR,MACE,SAAS,SAAS,YAClB,SAAS,SAAS,uBAClB,SAAS,SAAS;AAEX,WAAA,CAAC,SAAS,MAAM,MAAM,KAAK,UAAU,SAAS,QAAQ,CAAC,EAAE,KAAK,EAAE;AAEzE,MAAI,SAAS,SAAS;AACpB,WAAO,CAAC,WAAW,SAAS,EAAE,EAAE,KAAK,IAAI;AAEvC,MAAA,SAAS,SAAS,SAAS;AACvB,UAAA,aAAa,SAAS,SAAS;AAC9B,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM,SAAS,EAAE;AAAA,MACjB,aAAa,kBAAkB,UAAU,MAAM;AAAA,MAC/C;AAAA;AAAA,MACA,SAAS,QACN,IAAI,CAAa,cAAA,KAAK,oBAAoB,SAAS,CAAC,EAAE,EACtD,KAAK;AAAA,CAAI;AAAA,IAAA,EACZ,KAAK,EAAE;AAAA,EAAA;AAIX,QAAM,IAAI,MAAM,0BAA0B,SAAS,IAAI,EAAE;AAC3D;AAEA,SAAS,oBAAoBC,QAA+B;AAC1D,QAAM,EAAC,GAAE,IAAIA,QACP,OAAO,UAAUA,OAAM,IAAI;AACjC,MAAI,GAAG,SAAS;AACd,WAAO,CAAC,MAAM,SAAS,EAAE,KAAK,IAAI;AAEpC,MAAI,GAAG,SAAS;AACP,WAAA,CAAC,MAAM,kBAAkB,GAAG,KAAK,GAAG,EAAE,KAAK,IAAI;AAExD,MAAI,GAAG,SAAS,SAAS,GAAG,SAAS;AAC5B,WAAA,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,GAAG,MAAM,GAAG,EAAE,KAAK,IAAI;AAErD,MAAI,GAAG,SAAS,SAAS,GAAG,SAAS;AACnC,WAAO,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,UAAU,GAAG,KAAK,CAAC,GAAG,EAAE,KAAK,IAAI;AAEpE,MAAI,GAAG,SAAS;AACd,WAAO,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,UAAU,GAAG,KAAK,CAAC,GAAG,EAAE,KAAK,IAAI;AAEpE,MAAI,GAAG,SAAS;AACd,WAAO,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI;AAEnE,MAAI,GAAG,SAAS,YAAY,GAAG,SAAS;AAC/B,WAAA;AAAA,MACL;AAAA,MACA,GAAG,GAAG,IAAI,IAAI,GAAG,QAAQ,KAAK;AAAA,QAC5B,GAAG;AAAA,MAAA,CACJ,KAAK,KAAK,UAAU,GAAG,KAAK,CAAC;AAAA,IAAA,EAC9B,KAAK,IAAI;AAEb,MAAI,GAAG,SAAS;AACP,WAAA;AAAA,MACL;AAAA,MACA,WAAW,cAAc,GAAG,aAAa,CAAC,KAAK,KAAK;AAAA,QAClD,GAAG;AAAA,MAAA,CACJ;AAAA,IAAA,EACD,KAAK,IAAI;AAEb,MAAI,GAAG,SAAS;AACP,WAAA,CAAC,MAAM,YAAY,GAAG,UAAU,KAAK,GAAG,QAAQ,EAAE,EAAE,KAAK,IAAI;AAEtE,MAAI,GAAG,SAAS;AACP,WAAA,CAAC,MAAM,UAAU,cAAc,GAAG,aAAa,CAAC,GAAG,EAAE,KAAK,IAAI;AAGvE,QAAM,IAAI,MAAM,2BAA2B,GAAG,IAAI,EAAE;AACtD;;;;;ACxFO,SAAS,SAAe,aAAqC;AAC5D,QAAA,aAAa,iBAAiB,WAAW,GAEzCE,WAAS,CAIb,UACA,eACA,UACGC,OAAQ,WAAW,KAAK,GAAG,UAAU,aAAa,GACjDC,WAAS,CAIb,OACA,UACA,kBACGC,OAAQ,WAAW,KAAK,GAAG,UAAU,aAAa,GAEjDC,YAAU,CAId,OACA,UACA,kBACGC,QAAS,WAAW,KAAK,GAAG,aAAa,GAExCC,gBAAe,CACnB,KACA,UACGN,SAAO,UAAU,KAAK,KAAK;AAWhC,SAAO,UAACA,UAAA,QAAQE,UAAQE,SAAAA,WAAS,cAAAE,eAAc,SAT/B,CAAC,UAAkBA,cAAa,GAAG,KAAK,GASA,aAPpC,CAClB,KACA,UACGN,SAAO,SAAS,KAAK,KAAK,GAIsC,QAFtD,CAAC,UAAkBA,SAAO,SAAS,IAAI,KAAK,EAEgB;AAC7E;AAEA,SAAS,OAAyB,MAAqC;AACrE,SAAO,UAAU;AACnB;AAEA,SAAS,iBAAoB,aAAkC;AAC7D,SAAO,CAAC,UAAoB;AAC1B,QAAI,YAAY;AAChB,UAAM,WAAW,MAAM,IAAI,CACrB,SAAA,SAAS,IAAI,KACf,YAAY,IACL,EAAC,GAAG,MAAM,MAAM,YAAY,IAAI,OAElC,IACR;AACD,WAAO,YAAY,WAAW;AAAA,EAChC;AACF;AAEA,SAAS,SAAS,WAAqC;AACrD,SAAO,SAAS,SAAS,KAAK,CAAC,OAAO,SAAS;AACjD;"}