import type { RequestHeaders } from "./RequestHeaders";
import type { RequestMethod } from "./RequestMethod";
import type { RequestRequestOptions } from "./RequestRequestOptions";
import type { Url } from "./Url";
/**
 * Generic request options as they are returned by the `endpoint()` method
 */
export type RequestOptions = {
    method: RequestMethod;
    url: Url;
    headers: RequestHeaders;
    body?: any;
    request?: RequestRequestOptions;
};
