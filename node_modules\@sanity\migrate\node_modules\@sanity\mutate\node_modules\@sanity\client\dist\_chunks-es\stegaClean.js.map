{"version": 3, "file": "stegaClean.js", "sources": ["../../node_modules/@vercel/stega/dist/index.mjs", "../../src/stega/stegaClean.ts"], "sourcesContent": ["var s={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},c={0:8203,1:8204,2:8205,3:65279},u=new Array(4).fill(String.fromCodePoint(c[0])).join(\"\"),m=String.fromCharCode(0);function E(t){let e=JSON.stringify(t);return`${u}${Array.from(e).map(r=>{let n=r.charCodeAt(0);if(n>255)throw new Error(`Only ASCII edit info can be encoded. Error attempting to encode ${e} on character ${r} (${n})`);return Array.from(n.toString(4).padStart(4,\"0\")).map(o=>String.fromCodePoint(c[o])).join(\"\")}).join(\"\")}`}function y(t){let e=JSON.stringify(t);return Array.from(e).map(r=>{let n=r.charCodeAt(0);if(n>255)throw new Error(`Only ASCII edit info can be encoded. Error attempting to encode ${e} on character ${r} (${n})`);return Array.from(n.toString(16).padStart(2,\"0\")).map(o=>String.fromCodePoint(s[o])).join(\"\")}).join(\"\")}function I(t){return!Number.isNaN(Number(t))||/[a-z]/i.test(t)&&!/\\d+(?:[-:\\/]\\d+){2}(?:T\\d+(?:[-:\\/]\\d+){1,2}(\\.\\d+)?Z?)?/.test(t)?!1:Boolean(Date.parse(t))}function T(t){try{new URL(t,t.startsWith(\"/\")?\"https://acme.com\":void 0)}catch{return!1}return!0}function C(t,e,r=\"auto\"){return r===!0||r===\"auto\"&&(I(t)||T(t))?t:`${t}${E(e)}`}var x=Object.fromEntries(Object.entries(c).map(t=>t.reverse())),g=Object.fromEntries(Object.entries(s).map(t=>t.reverse())),S=`${Object.values(s).map(t=>`\\\\u{${t.toString(16)}}`).join(\"\")}`,f=new RegExp(`[${S}]{4,}`,\"gu\");function G(t){let e=t.match(f);if(!!e)return h(e[0],!0)[0]}function $(t){let e=t.match(f);if(!!e)return e.map(r=>h(r)).flat()}function h(t,e=!1){let r=Array.from(t);if(r.length%2===0){if(r.length%4||!t.startsWith(u))return A(r,e)}else throw new Error(\"Encoded data has invalid length\");let n=[];for(let o=r.length*.25;o--;){let p=r.slice(o*4,o*4+4).map(d=>x[d.codePointAt(0)]).join(\"\");n.unshift(String.fromCharCode(parseInt(p,4)))}if(e){n.shift();let o=n.indexOf(m);return o===-1&&(o=n.length),[JSON.parse(n.slice(0,o).join(\"\"))]}return n.join(\"\").split(m).filter(Boolean).map(o=>JSON.parse(o))}function A(t,e){var d;let r=[];for(let i=t.length*.5;i--;){let a=`${g[t[i*2].codePointAt(0)]}${g[t[i*2+1].codePointAt(0)]}`;r.unshift(String.fromCharCode(parseInt(a,16)))}let n=[],o=[r.join(\"\")],p=10;for(;o.length;){let i=o.shift();try{if(n.push(JSON.parse(i)),e)return n}catch(a){if(!p--)throw a;let l=+((d=a.message.match(/\\sposition\\s(\\d+)$/))==null?void 0:d[1]);if(!l)throw a;o.unshift(i.substring(0,l),i.substring(l))}}return n}function _(t){var e;return{cleaned:t.replace(f,\"\"),encoded:((e=t.match(f))==null?void 0:e[0])||\"\"}}function O(t){return t&&JSON.parse(_(JSON.stringify(t)).cleaned)}export{f as VERCEL_STEGA_REGEX,y as legacyStegaEncode,O as vercelStegaClean,C as vercelStegaCombine,G as vercelStegaDecode,$ as vercelStegaDecodeAll,E as vercelStegaEncode,_ as vercelStegaSplit};\n", "import {vercelStegaClean} from '@vercel/stega'\n\n/**\n * Can take a `result` JSON from a `const {result} = client.fetch(query, params, {filterResponse: false})`\n * and remove all stega-encoded data from it.\n * @public\n */\nexport function stegaClean<Result = unknown>(result: Result): Result {\n  return vercelStegaClean<Result>(result)\n}\n\n/**\n * Can take a `result` JSON from a `const {result} = client.fetch(query, params, {filterResponse: false})`\n * and remove all stega-encoded data from it.\n * @alpha\n * @deprecated Use `stegaClean` instead\n */\nexport const vercelStegaCleanAll = stegaClean\n"], "names": ["vercelStegaClean"], "mappings": "AAAG,IAAC,IAAE,EAAC,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,OAAM,GAAE,MAAK,GAAE,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,OAAM,GAAE,IAAE,EAAC,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,IAAE,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE;AAA2B,SAAS,EAAE,GAAE;AAAC,MAAI,IAAE,KAAK,UAAU,CAAC;AAAE,SAAM,GAAG,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE,IAAI,OAAG;AAAC,QAAI,IAAE,EAAE,WAAW,CAAC;AAAE,QAAG,IAAE,IAAI,OAAM,IAAI,MAAM,mEAAmE,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG;AAAE,WAAO,MAAM,KAAK,EAAE,SAAS,CAAC,EAAE,SAAS,GAAE,GAAG,CAAC,EAAE,IAAI,OAAG,OAAO,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE;AAAA,EAAC,CAAC,EAAE,KAAK,EAAE,CAAC;AAAE;AAA6T,SAAS,EAAE,GAAE;AAAC,SAAM,CAAC,OAAO,MAAM,OAAO,CAAC,CAAC,KAAG,SAAS,KAAK,CAAC,KAAG,CAAC,2DAA2D,KAAK,CAAC,IAAE,KAAG,EAAQ,KAAK,MAAM,CAAC;AAAE;AAAC,SAAS,EAAE,GAAE;AAAC,MAAG;AAAC,QAAI,IAAI,GAAE,EAAE,WAAW,GAAG,IAAE,qBAAmB,MAAM;AAAA,EAAC,QAAM;AAAC,WAAQ;AAAA,EAAA;AAAC,SAAM;AAAE;AAAC,SAAS,EAAE,GAAE,GAAE,IAAE,QAAO;AAAC,SAAO,MAAI,MAAI,MAAI,WAAS,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,IAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;AAAE;AAAO,OAAO,YAAY,OAAO,QAAQ,CAAC,EAAE,IAAI,OAAG,EAAE,QAAO,CAAE,CAAC;AAAI,OAAO,YAAY,OAAO,QAAQ,CAAC,EAAE,IAAI,OAAG,EAAE,QAAS,CAAA,CAAC;AAAC,IAAC,IAAE,GAAG,OAAO,OAAO,CAAC,EAAE,IAAI,OAAG,OAAO,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,IAAG,IAAE,IAAI,OAAO,IAAI,CAAC,SAAQ,IAAI;AAAugC,SAAS,EAAE,GAAE;AAAC,MAAI;AAAE,SAAM,EAAC,SAAQ,EAAE,QAAQ,GAAE,EAAE,GAAE,WAAU,IAAE,EAAE,MAAM,CAAC,MAAI,OAAK,SAAO,EAAE,CAAC,MAAI,GAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,KAAG,KAAK,MAAM,EAAE,KAAK,UAAU,CAAC,CAAC,EAAE,OAAO;AAAC;ACO1kF,SAAS,WAA6B,QAAwB;AACnE,SAAOA,EAAyB,MAAM;AACxC;AAQO,MAAM,sBAAsB;", "x_google_ignoreList": [0]}