import { Patch, Patch as Patch$1 } from "@portabletext/patches";
import * as _sanity_types5 from "@sanity/types";
import { ArrayDefinition, ArraySchemaType, BlockDecoratorDefinition, BlockListDefinition, BlockStyleDefinition, ObjectSchemaType, Path, PortableTextBlock, PortableTextBlock as PortableTextBlock$1, PortableTextChild, PortableTextChild as PortableTextChild$1, PortableTextListBlock, PortableTextObject, PortableTextObject as PortableTextObject$1, PortableTextSpan, PortableTextSpan as PortableTextSpan$1, PortableTextTextBlock, PortableTextTextBlock as PortableTextTextBlock$1, TypedObject } from "@sanity/types";
import * as _portabletext_schema5 from "@portabletext/schema";
import { AnnotationDefinition, AnnotationSchemaType, BaseDefinition, BlockObjectDefinition, BlockObjectSchemaType, DecoratorDefinition, DecoratorSchemaType, FieldDefinition, InlineObjectDefinition, InlineObjectSchemaType, ListDefinition, ListSchemaType, Schema, SchemaDefinition, SchemaDefinition as SchemaDefinition$1, StyleDefinition, StyleSchemaType, defineSchema } from "@portabletext/schema";
import * as xstate227 from "xstate";
import { ActorRef, ActorRefFrom, EventObject, Snapshot } from "xstate";
import { BaseRange, Descendant, Operation } from "slate";
import * as react20 from "react";
import React$1, { BaseSyntheticEvent, ClipboardEvent, Component, FocusEvent, JSX, KeyboardEvent as KeyboardEvent$1, MutableRefObject, PropsWithChildren, ReactElement, RefObject, TextareaHTMLAttributes } from "react";
import * as xstate_guards12 from "xstate/guards";
import { Observable, Subject } from "rxjs";
import { DOMNode } from "slate-dom";
import { ReactEditor } from "slate-react";
/**
 * @internal
 */
type PickFromUnion<TUnion, TTagKey extends keyof TUnion, TPickedTags extends TUnion[TTagKey]> = TUnion extends Record<TTagKey, TPickedTags> ? TUnion : never;
/**
 * @internal
 */

type NamespaceEvent<TEvent, TNamespace extends string> = TEvent extends {
  type: infer TEventType;
} ? { [K in keyof TEvent]: K extends 'type' ? `${TNamespace}.${TEventType & string}` : TEvent[K] } : never;
type StrictExtract<T, U extends T> = U;
type TextBlockWithOptionalKey = Omit<PortableTextTextBlock, '_key'> & {
  _key?: PortableTextTextBlock['_key'];
};
type ObjectBlockWithOptionalKey = Omit<PortableTextObject, '_key'> & {
  _key?: PortableTextObject['_key'];
};
type BlockWithOptionalKey = TextBlockWithOptionalKey | ObjectBlockWithOptionalKey;
type MIMEType = `${string}/${string}`;
type EditorPriority = {
  id: string;
  name?: string;
  reference?: {
    priority: EditorPriority;
    importance: 'higher' | 'lower';
  };
};
type BehaviorConfig = {
  behavior: Behavior;
  priority: EditorPriority;
};
/**
 * @public
 */
type AddedAnnotationPaths = {
  /**
   * @deprecated An annotation may be applied to multiple blocks, resulting
   * in multiple `markDef`'s being created. Use `markDefPaths` instead.
   */
  markDefPath: Path;
  markDefPaths: Array<Path>;
  /**
   * @deprecated Does not return anything meaningful since an annotation
   * can span multiple blocks and spans. If references the span closest
   * to the focus point of the selection.
   */
  spanPath: Path;
};
/**
 * @public
 */
type EditorEmittedEvent = {
  type: 'blurred';
  event: FocusEvent<HTMLDivElement, Element>;
} | {
  /**
   * @deprecated Will be removed in the next major version
   */
  type: 'done loading';
} | {
  type: 'editable';
} | ErrorEvent | {
  type: 'focused';
  event: FocusEvent<HTMLDivElement, Element>;
} | {
  type: 'invalid value';
  resolution: InvalidValueResolution | null;
  value: Array<PortableTextBlock> | undefined;
} | {
  /**
   * @deprecated Will be removed in the next major version
   */
  type: 'loading';
} | MutationEvent | PatchEvent | {
  type: 'read only';
} | {
  type: 'ready';
} | {
  type: 'selection';
  selection: EditorSelection;
} | {
  type: 'value changed';
  value: Array<PortableTextBlock> | undefined;
};
/**
 * @deprecated The event is no longer emitted
 */
type ErrorEvent = {
  type: 'error';
  name: string;
  description: string;
  data: unknown;
};
/**
 * @public
 */
type MutationEvent = {
  type: 'mutation';
  patches: Array<Patch>;
  /**
   * @deprecated Use `value` instead
   */
  snapshot: Array<PortableTextBlock> | undefined;
  value: Array<PortableTextBlock> | undefined;
};
type PatchEvent = {
  type: 'patch';
  patch: Patch;
};
type SlateEditor = {
  instance: PortableTextSlateEditor;
  initialValue: Array<Descendant>;
};
/**
 * @public
 */
type EditorSchema = Schema;
type InternalEditor = Editor & {
  _internal: {
    editable: EditableAPI;
    editorActor: EditorActor;
    slateEditor: SlateEditor;
  };
};
/**
 * Props for the PortableTextEditor component
 *
 * @public
 * @deprecated Use `EditorProvider` instead
 */
type PortableTextEditorProps<TEditor extends InternalEditor | undefined = undefined> = PropsWithChildren<TEditor extends InternalEditor ? {
  /**
   * @internal
   */
  editor: TEditor;
} : {
  editor?: undefined;
  /**
   * Function that gets called when the editor changes the value
   */
  onChange: (change: EditorChange) => void;
  /**
   * Schema type for the portable text field
   */
  schemaType: ArraySchemaType<PortableTextBlock> | ArrayDefinition;
  /**
   * Maximum number of blocks to allow within the editor
   */
  maxBlocks?: number | string;
  /**
   * Function used to generate keys for array items (`_key`)
   */
  keyGenerator?: () => string;
  /**
   * Observable of local and remote patches for the edited value.
   */
  patches$?: PatchObservable;
  /**
   * Backward compatibility (renamed to patches$).
   */
  incomingPatches$?: PatchObservable;
  /**
   * Whether or not the editor should be in read-only mode
   */
  readOnly?: boolean;
  /**
   * The current value of the portable text field
   */
  value?: PortableTextBlock[];
  /**
   * A ref to the editor instance
   */
  editorRef?: MutableRefObject<PortableTextEditor | null>;
}>;
/**
 * The main Portable Text Editor component.
 * @public
 * @deprecated Use `EditorProvider` instead
 */
declare class PortableTextEditor extends Component<PortableTextEditorProps<InternalEditor | undefined>> {
  static displayName: string;
  /**
   * An observable of all the editor changes.
   */
  change$: EditorChanges;
  /**
   * A lookup table for all the relevant schema types for this portable text type.
   */
  schemaTypes: PortableTextMemberSchemaTypes;
  /**
   * The editor instance
   */
  private editor;
  private editable;
  private actors?;
  private subscriptions;
  private unsubscribers;
  constructor(props: PortableTextEditorProps);
  componentDidMount(): void;
  componentDidUpdate(prevProps: PortableTextEditorProps): void;
  componentWillUnmount(): void;
  setEditable: (editable: EditableAPI) => void;
  render(): react20.JSX.Element;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isActive = useEditorSelector(editor, selectors.getActiveAnnotations)
   * ```
   */
  static activeAnnotations: (editor: PortableTextEditor) => PortableTextObject[];
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isActive = useEditorSelector(editor, selectors.isActiveAnnotation(...))
   * ```
   */
  static isAnnotationActive: (editor: PortableTextEditor, annotationType: PortableTextObject["_type"]) => boolean;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'annotation.add',
   *  annotation: {
   *    name: '...',
   *    value: {...},
   *  }
   * })
   * ```
   */
  static addAnnotation: <TSchemaType extends {
    name: string;
  }>(editor: PortableTextEditor, type: TSchemaType, value?: {
    [prop: string]: unknown;
  }) => AddedAnnotationPaths | undefined;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'blur',
   * })
   * ```
   */
  static blur: (editor: PortableTextEditor) => void;
  static delete: (editor: PortableTextEditor, selection: EditorSelection, options?: EditableAPIDeleteOptions) => void;
  static findDOMNode: (editor: PortableTextEditor, element: PortableTextBlock | PortableTextChild) => Node | undefined;
  static findByPath: (editor: PortableTextEditor, path: Path) => [_sanity_types5.PortableTextTextBlock<PortableTextObject | _sanity_types5.PortableTextSpan> | PortableTextObject | _sanity_types5.PortableTextSpan | undefined, Path | undefined];
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'focus',
   * })
   * ```
   */
  static focus: (editor: PortableTextEditor) => void;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const focusBlock = useEditorSelector(editor, selectors.getFocusBlock)
   * ```
   */
  static focusBlock: (editor: PortableTextEditor) => PortableTextBlock | undefined;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const focusChild = useEditorSelector(editor, selectors.getFocusChild)
   * ```
   */
  static focusChild: (editor: PortableTextEditor) => PortableTextChild | undefined;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const selection = useEditorSelector(editor, selectors.getSelection)
   * ```
   */
  static getSelection: (editor: PortableTextEditor) => EditorSelection;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const value = useEditorSelector(editor, selectors.getValue)
   * ```
   */
  static getValue: (editor: PortableTextEditor) => PortableTextBlock[] | undefined;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isActive = useEditorSelector(editor, selectors.isActiveStyle(...))
   * ```
   */
  static hasBlockStyle: (editor: PortableTextEditor, blockStyle: string) => boolean;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isActive = useEditorSelector(editor, selectors.isActiveListItem(...))
   * ```
   */
  static hasListStyle: (editor: PortableTextEditor, listStyle: string) => boolean;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isSelectionCollapsed = useEditorSelector(editor, selectors.isSelectionCollapsed)
   * ```
   */
  static isCollapsedSelection: (editor: PortableTextEditor) => boolean;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isSelectionExpanded = useEditorSelector(editor, selectors.isSelectionExpanded)
   * ```
   */
  static isExpandedSelection: (editor: PortableTextEditor) => boolean;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isActive = useEditorSelector(editor, selectors.isActiveDecorator(...))
   * ```
   */
  static isMarkActive: (editor: PortableTextEditor, mark: string) => boolean;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'insert.span',
   *  text: '...',
   *  annotations: [{name: '...', value: {...}}],
   *  decorators: ['...'],
   * })
   * editor.send({
   *  type: 'insert.inline object',
   *  inlineObject: {
   *    name: '...',
   *    value: {...},
   *  },
   * })
   * ```
   */
  static insertChild: <TSchemaType extends {
    name: string;
  }>(editor: PortableTextEditor, type: TSchemaType, value?: {
    [prop: string]: unknown;
  }) => Path | undefined;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'insert.block object',
   *  blockObject: {
   *    name: '...',
   *    value: {...},
   *  },
   *  placement: 'auto' | 'after' | 'before',
   * })
   * ```
   */
  static insertBlock: <TSchemaType extends {
    name: string;
  }>(editor: PortableTextEditor, type: TSchemaType, value?: {
    [prop: string]: unknown;
  }) => Path | undefined;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'insert.break',
   * })
   * ```
   */
  static insertBreak: (editor: PortableTextEditor) => void;
  static isVoid: (editor: PortableTextEditor, element: PortableTextBlock | PortableTextChild) => boolean;
  static isObjectPath: (_editor: PortableTextEditor, path: Path) => boolean;
  static marks: (editor: PortableTextEditor) => string[];
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'select',
   *  selection: {...},
   * })
   * ```
   */
  static select: (editor: PortableTextEditor, selection: EditorSelection | null) => void;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'annotation.remove',
   *  annotation: {
   *    name: '...',
   *  },
   * })
   * ```
   */
  static removeAnnotation: <TSchemaType extends {
    name: string;
  }>(editor: PortableTextEditor, type: TSchemaType) => void;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'style.toggle',
   *  style: '...',
   * })
   * ```
   */
  static toggleBlockStyle: (editor: PortableTextEditor, blockStyle: string) => void;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'list item.toggle',
   *  listItem: '...',
   * })
   * ```
   */
  static toggleList: (editor: PortableTextEditor, listStyle: string) => void;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'decorator.toggle',
   *  decorator: '...',
   * })
   * ```
   */
  static toggleMark: (editor: PortableTextEditor, mark: string) => void;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const selectedSlice = useEditorSelector(editor, selectors.getSelectedSlice)
   * ```
   */
  static getFragment: (editor: PortableTextEditor) => PortableTextBlock[] | undefined;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *   type: 'history.undo',
   * })
   * ```
   */
  static undo: (editor: PortableTextEditor) => void;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *   type: 'history.redo',
   * })
   * ```
   */
  static redo: (editor: PortableTextEditor) => void;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isOverlapping = useEditorSelector(editor, selectors.isOverlappingSelection(selectionB))
   * ```
   */
  static isSelectionsOverlapping: (editor: PortableTextEditor, selectionA: EditorSelection, selectionB: EditorSelection) => boolean;
}
/**
 * @beta
 */
type HotkeyOptions = {
  marks?: Record<string, string>;
  custom?: Record<string, (event: BaseSyntheticEvent, editor: PortableTextEditor) => void>;
};
/**
 * @public
 */
type PortableTextEditableProps = Omit<TextareaHTMLAttributes<HTMLDivElement>, 'onPaste' | 'onCopy' | 'onBeforeInput'> & {
  hotkeys?: HotkeyOptions;
  onBeforeInput?: (event: InputEvent) => void;
  onPaste?: OnPasteFn;
  onCopy?: OnCopyFn;
  ref: MutableRefObject<HTMLDivElement | null>;
  rangeDecorations?: RangeDecoration[];
  renderAnnotation?: RenderAnnotationFunction;
  renderBlock?: RenderBlockFunction;
  renderChild?: RenderChildFunction;
  renderDecorator?: RenderDecoratorFunction;
  renderListItem?: RenderListItemFunction;
  renderPlaceholder?: RenderPlaceholderFunction;
  renderStyle?: RenderStyleFunction;
  scrollSelectionIntoView?: ScrollSelectionIntoViewFunction;
  selection?: EditorSelection;
  spellCheck?: boolean;
};
/**
 * @public
 *
 *
 * The core component that renders the editor. Must be placed within the {@link EditorProvider} component.
 *
 * @example
 * ```tsx
 * import { PortableTextEditable, EditorProvider } from '@portabletext/editor'
 *
 * function MyComponent() {
 *  return (
 *   <EditorProvider>
 *    <PortableTextEditable />
 *  </EditorProvider>
 *  )
 * }
 * ```
 * @group Components
 */
declare const PortableTextEditable: react20.ForwardRefExoticComponent<Omit<PortableTextEditableProps, "ref"> & react20.RefAttributes<Omit<HTMLDivElement, "as" | "onPaste" | "onBeforeInput">>>;
type DecoratedRange = BaseRange & {
  rangeDecoration: RangeDecoration;
};
/**
 * @public
 */
type BlockPath = [{
  _key: string;
}];
/**
 * @public
 */
type AnnotationPath = [{
  _key: string;
}, 'markDefs', {
  _key: string;
}];
/**
 * @public
 */
type ChildPath = [{
  _key: string;
}, 'children', {
  _key: string;
}];
/** @beta */
interface EditableAPIDeleteOptions {
  mode?: 'blocks' | 'children' | 'selected';
}
/** @beta */
interface EditableAPI {
  activeAnnotations: () => PortableTextObject[];
  isAnnotationActive: (annotationType: PortableTextObject['_type']) => boolean;
  addAnnotation: <TSchemaType extends {
    name: string;
  }>(type: TSchemaType, value?: {
    [prop: string]: unknown;
  }) => {
    markDefPath: Path;
    markDefPaths: Array<Path>;
    spanPath: Path;
  } | undefined;
  blur: () => void;
  delete: (selection: EditorSelection, options?: EditableAPIDeleteOptions) => void;
  findByPath: (path: Path) => [PortableTextBlock | PortableTextChild | undefined, Path | undefined];
  findDOMNode: (element: PortableTextBlock | PortableTextChild) => DOMNode | undefined;
  focus: () => void;
  focusBlock: () => PortableTextBlock | undefined;
  focusChild: () => PortableTextChild | undefined;
  getSelection: () => EditorSelection;
  getFragment: () => PortableTextBlock[] | undefined;
  getValue: () => PortableTextBlock[] | undefined;
  hasBlockStyle: (style: string) => boolean;
  hasListStyle: (listStyle: string) => boolean;
  insertBlock: <TSchemaType extends {
    name: string;
  }>(type: TSchemaType, value?: {
    [prop: string]: unknown;
  }) => Path;
  insertChild: <TSchemaType extends {
    name: string;
  }>(type: TSchemaType, value?: {
    [prop: string]: unknown;
  }) => Path;
  insertBreak: () => void;
  isCollapsedSelection: () => boolean;
  isExpandedSelection: () => boolean;
  isMarkActive: (mark: string) => boolean;
  isSelectionsOverlapping: (selectionA: EditorSelection, selectionB: EditorSelection) => boolean;
  isVoid: (element: PortableTextBlock | PortableTextChild) => boolean;
  marks: () => string[];
  redo: () => void;
  removeAnnotation: <TSchemaType extends {
    name: string;
  }>(type: TSchemaType) => void;
  select: (selection: EditorSelection) => void;
  toggleBlockStyle: (blockStyle: string) => void;
  toggleList: (listStyle: string) => void;
  toggleMark: (mark: string) => void;
  undo: () => void;
}
type HistoryItem = {
  operations: Operation[];
  timestamp: Date;
};
interface History {
  redos: HistoryItem[];
  undos: HistoryItem[];
}
/** @public */
type EditorSelectionPoint = {
  path: Path;
  offset: number;
};
/** @public */
type EditorSelection = {
  anchor: EditorSelectionPoint;
  focus: EditorSelectionPoint;
  backward?: boolean;
} | null;
interface PortableTextSlateEditor extends ReactEditor {
  _key: 'editor';
  _type: 'editor';
  createPlaceholderBlock: () => Descendant;
  editable: EditableAPI;
  history: History;
  insertPortableTextData: (data: DataTransfer) => boolean;
  insertTextOrHTMLData: (data: DataTransfer) => boolean;
  isTextBlock: (value: unknown) => value is PortableTextTextBlock;
  isTextSpan: (value: unknown) => value is PortableTextSpan;
  isListBlock: (value: unknown) => value is PortableTextListBlock;
  value: Array<PortableTextBlock>;
  decoratedRanges: Array<DecoratedRange>;
  decoratorState: Record<string, boolean | undefined>;
  blockIndexMap: Map<string, number>;
  listIndexMap: Map<string, number>;
  /**
   * Use hotkeys
   */
  pteWithHotKeys: (event: KeyboardEvent$1<HTMLDivElement>) => void;
  /**
   * Helper function that creates a text block
   */
  pteCreateTextBlock: (options: {
    decorators: Array<string>;
    listItem?: string;
    level?: number;
  }) => Descendant;
  /**
   * Undo
   */
  undo: () => void;
  /**
   * Redo
   */
  redo: () => void;
}
/**
 * The editor has mutated it's content.
 * @beta */
type MutationChange = {
  type: 'mutation';
  patches: Patch[];
  snapshot: PortableTextBlock[] | undefined;
};
/**
 * The editor has produced a patch
 * @beta */
type PatchChange = {
  type: 'patch';
  patch: Patch;
};
/**
 * The editor has received a new (props) value
 * @beta */
type ValueChange = {
  type: 'value';
  value: PortableTextBlock[] | undefined;
};
/**
 * The editor has a new selection
 * @beta */
type SelectionChange = {
  type: 'selection';
  selection: EditorSelection;
};
/**
 * The editor received focus
 * @beta */
type FocusChange = {
  type: 'focus';
  event: FocusEvent<HTMLDivElement, Element>;
};
/**
 * @beta
 * @deprecated Use `'patch'` changes instead
 */
type UnsetChange = {
  type: 'unset';
  previousValue: PortableTextBlock[];
};
/**
 * The editor blurred
 * @beta */
type BlurChange = {
  type: 'blur';
  event: FocusEvent<HTMLDivElement, Element>;
};
/**
 * The editor is currently loading something
 * Could be used to show a spinner etc.
 * @beta
 * @deprecated Will be removed in the next major version
 */
type LoadingChange = {
  type: 'loading';
  isLoading: boolean;
};
/**
 * The editor content is ready to be edited by the user
 * @beta */
type ReadyChange = {
  type: 'ready';
};
/**
 * The editor produced an error
 * @beta
 * @deprecated The change is no longer emitted
 * */
type ErrorChange = {
  type: 'error';
  name: string;
  level: 'warning' | 'error';
  description: string;
  data?: unknown;
};
/**
 * The editor has invalid data in the value that can be resolved by the user
 * @beta */
type InvalidValueResolution = {
  autoResolve?: boolean;
  patches: Patch[];
  description: string;
  action: string;
  item: PortableTextBlock[] | PortableTextBlock | PortableTextChild | undefined;
  /**
   * i18n keys for the description and action
   *
   * These are in addition to the description and action properties, to decouple the editor from
   * the i18n system, and allow usage without it. The i18n keys take precedence over the
   * description and action properties, if i18n framework is available.
   */
  i18n: {
    description: `inputs.portable-text.invalid-value.${Lowercase<string>}.description`;
    action: `inputs.portable-text.invalid-value.${Lowercase<string>}.action`;
    values?: Record<string, string | number | string[]>;
  };
};
/**
 * The editor has an invalid value
 * @beta */
type InvalidValue = {
  type: 'invalidValue';
  resolution: InvalidValueResolution | null;
  value: PortableTextBlock[] | undefined;
};
/**
 * The editor performed a undo history step
 * @beta
 * @deprecated The change is no longer emitted
 *  */
type UndoChange = {
  type: 'undo';
  patches: Patch[];
  timestamp: Date;
};
/**
 * The editor performed redo history step
 * @beta
 * @deprecated The change is no longer emitted
 *  */
type RedoChange = {
  type: 'redo';
  patches: Patch[];
  timestamp: Date;
};
/**
 * The editor was either connected or disconnected to the network
 * To show out of sync warnings etc when in collaborative mode.
 * @beta
 * @deprecated The change is no longer emitted
 *  */
type ConnectionChange = {
  type: 'connection';
  value: 'online' | 'offline';
};
/**
 * When the editor changes, it will emit a change item describing the change
 * @beta */
type EditorChange = BlurChange | ConnectionChange | ErrorChange | FocusChange | InvalidValue | LoadingChange | MutationChange | PatchChange | ReadyChange | RedoChange | SelectionChange | UndoChange | UnsetChange | ValueChange;
/**
 * @beta
 */
type EditorChanges = Subject<EditorChange>;
/** @beta */
type OnPasteResult = {
  insert?: TypedObject[];
  path?: Path;
} | undefined;
/**
 * @beta
 */
type OnPasteResultOrPromise = OnPasteResult | Promise<OnPasteResult>;
/** @beta */
interface PasteData {
  event: ClipboardEvent;
  path: Path;
  schemaTypes: PortableTextMemberSchemaTypes;
  value: PortableTextBlock[] | undefined;
}
/**
 * @beta
 * It is encouraged not to return `Promise<undefined>` from the `OnPasteFn` as
 * a mechanism to fall back to the native paste behaviour. This doesn't work in
 * all cases. Always return plain `undefined` if possible.
 **/
type OnPasteFn = (data: PasteData) => OnPasteResultOrPromise;
/** @beta */
type OnBeforeInputFn = (event: InputEvent) => void;
/** @beta */
type OnCopyFn = (event: ClipboardEvent<HTMLDivElement | HTMLSpanElement>) => undefined | unknown;
/** @beta */
type PatchObservable = Observable<{
  patches: Patch[];
  snapshot: PortableTextBlock[] | undefined;
}>;
/** @beta */
interface BlockRenderProps {
  children: ReactElement<any>;
  editorElementRef: RefObject<HTMLElement | null>;
  focused: boolean;
  level?: number;
  listItem?: string;
  path: BlockPath;
  selected: boolean;
  style?: string;
  schemaType: ObjectSchemaType;
  /** @deprecated Use `schemaType` instead */
  type: ObjectSchemaType;
  value: PortableTextBlock;
}
/** @beta */
interface BlockChildRenderProps {
  annotations: PortableTextObject[];
  children: ReactElement<any>;
  editorElementRef: RefObject<HTMLElement | null>;
  focused: boolean;
  path: Path;
  selected: boolean;
  schemaType: ObjectSchemaType;
  /** @deprecated Use `schemaType` instead */
  type: ObjectSchemaType;
  value: PortableTextChild;
}
/** @beta */
interface BlockAnnotationRenderProps {
  block: PortableTextBlock;
  children: ReactElement<any>;
  editorElementRef: RefObject<HTMLElement | null>;
  focused: boolean;
  path: Path;
  schemaType: ObjectSchemaType;
  selected: boolean;
  /** @deprecated Use `schemaType` instead */
  type: ObjectSchemaType;
  value: PortableTextObject;
}
/** @beta */
interface BlockDecoratorRenderProps {
  children: ReactElement<any>;
  editorElementRef: RefObject<HTMLElement | null>;
  focused: boolean;
  path: Path;
  schemaType: BlockDecoratorDefinition;
  selected: boolean;
  /** @deprecated Use `schemaType` instead */
  type: BlockDecoratorDefinition;
  value: string;
}
/** @beta */
interface BlockListItemRenderProps {
  block: PortableTextTextBlock;
  children: ReactElement<any>;
  editorElementRef: RefObject<HTMLElement | null>;
  focused: boolean;
  level: number;
  path: Path;
  schemaType: BlockListDefinition;
  selected: boolean;
  value: string;
}
/** @beta */
type RenderBlockFunction = (props: BlockRenderProps) => JSX.Element;
/** @beta */
type RenderChildFunction = (props: BlockChildRenderProps) => JSX.Element;
/** @beta */
type RenderEditableFunction = (props: PortableTextEditableProps) => JSX.Element;
/** @beta */
type RenderAnnotationFunction = (props: BlockAnnotationRenderProps) => JSX.Element;
/** @beta */
type RenderPlaceholderFunction = () => React.ReactNode;
/** @beta */
type RenderStyleFunction = (props: BlockStyleRenderProps) => JSX.Element;
/** @beta */
interface BlockStyleRenderProps {
  block: PortableTextTextBlock;
  children: ReactElement<any>;
  editorElementRef: RefObject<HTMLElement | null>;
  focused: boolean;
  path: Path;
  selected: boolean;
  schemaType: BlockStyleDefinition;
  value: string;
}
/** @beta */
type RenderListItemFunction = (props: BlockListItemRenderProps) => JSX.Element;
/** @beta */
type RenderDecoratorFunction = (props: BlockDecoratorRenderProps) => JSX.Element;
/** @beta */
type ScrollSelectionIntoViewFunction = (editor: PortableTextEditor, domRange: globalThis.Range) => void;
/**
 * Parameters for the callback that will be called for a RangeDecoration's onMoved.
 * @alpha */
interface RangeDecorationOnMovedDetails {
  rangeDecoration: RangeDecoration;
  newSelection: EditorSelection;
  origin: 'remote' | 'local';
}
/**
 * A range decoration is a UI affordance that wraps a given selection range in the editor
 * with a custom component. This can be used to highlight search results,
 * mark validation errors on specific words, draw user presence and similar.
 * @alpha */
interface RangeDecoration {
  /**
   * A component for rendering the range decoration.
   * The component will receive the children (text) of the range decoration as its children.
   *
   * @example
   * ```ts
   * (rangeComponentProps: PropsWithChildren) => (
   *    <SearchResultHighlight>
   *      {rangeComponentProps.children}
   *    </SearchResultHighlight>
   *  )
   * ```
   */
  component: (props: PropsWithChildren) => ReactElement<any>;
  /**
   * The editor content selection range
   */
  selection: EditorSelection;
  /**
   * A optional callback that will be called when the range decoration potentially moves according to user edits.
   */
  onMoved?: (details: RangeDecorationOnMovedDetails) => void;
  /**
   * A custom payload that can be set on the range decoration
   */
  payload?: Record<string, unknown>;
}
/** @beta */
type PortableTextMemberSchemaTypes = {
  annotations: (ObjectSchemaType & {
    i18nTitleKey?: string;
  })[];
  block: ObjectSchemaType;
  blockObjects: ObjectSchemaType[];
  decorators: BlockDecoratorDefinition[];
  inlineObjects: ObjectSchemaType[];
  portableText: ArraySchemaType<PortableTextBlock>;
  span: ObjectSchemaType;
  styles: BlockStyleDefinition[];
  lists: BlockListDefinition[];
};
/**
 * @public
 */
type EditorContext = {
  converters: Array<Converter>;
  keyGenerator: () => string;
  readOnly: boolean;
  schema: EditorSchema;
  selection: EditorSelection;
  value: Array<PortableTextBlock>;
};
/**
 * @public
 */
type EditorSnapshot = {
  context: EditorContext;
  blockIndexMap: Map<string, number>;
  /**
   * @beta
   * Subject to change
   */
  decoratorState: Record<string, boolean | undefined>;
};
type Converter<TMIMEType extends MIMEType = MIMEType> = {
  mimeType: TMIMEType;
  serialize: Serializer<TMIMEType>;
  deserialize: Deserializer<TMIMEType>;
};
type ConverterEvent<TMIMEType extends MIMEType = MIMEType> = {
  type: 'serialize';
  originEvent: 'clipboard.copy' | 'clipboard.cut' | 'drag.dragstart';
} | {
  type: 'serialization.failure';
  mimeType: TMIMEType;
  originEvent: 'clipboard.copy' | 'clipboard.cut' | 'drag.dragstart';
  reason: string;
} | {
  type: 'serialization.success';
  data: string;
  mimeType: TMIMEType;
  originEvent: 'clipboard.copy' | 'clipboard.cut' | 'drag.dragstart';
} | {
  type: 'deserialize';
  data: string;
} | {
  type: 'deserialization.failure';
  mimeType: TMIMEType;
  reason: string;
} | {
  type: 'deserialization.success';
  data: Array<PortableTextBlock>;
  mimeType: TMIMEType;
};
type Serializer<TMIMEType extends MIMEType> = ({
  snapshot,
  event
}: {
  snapshot: EditorSnapshot;
  event: PickFromUnion<ConverterEvent<TMIMEType>, 'type', 'serialize'>;
}) => PickFromUnion<ConverterEvent<TMIMEType>, 'type', 'serialization.success' | 'serialization.failure'>;
type Deserializer<TMIMEType extends MIMEType> = ({
  snapshot,
  event
}: {
  snapshot: EditorSnapshot;
  event: PickFromUnion<ConverterEvent<TMIMEType>, 'type', 'deserialize'>;
}) => PickFromUnion<ConverterEvent<TMIMEType>, 'type', 'deserialization.success' | 'deserialization.failure'>;
/**
 * @public
 */
type PatchesEvent = {
  type: 'patches';
  patches: Array<Patch>;
  snapshot: Array<PortableTextBlock> | undefined;
};
/**
 * @public
 */
type ExternalEditorEvent = {
  type: 'update readOnly';
  readOnly: boolean;
} | {
  type: 'update maxBlocks';
  maxBlocks: number | undefined;
} | PatchesEvent;
type InternalPatchEvent = NamespaceEvent<PatchEvent, 'internal'> & {
  operationId?: string;
  value: Array<PortableTextBlock>;
};
/**
 * @internal
 */
type EditorActor = ActorRefFrom<typeof editorMachine>;
/**
 * @internal
 */

/**
 * @internal
 */
declare const editorMachine: xstate227.StateMachine<{
  behaviors: Set<BehaviorConfig>;
  behaviorsSorted: boolean;
  converters: Set<Converter>;
  getLegacySchema: () => PortableTextMemberSchemaTypes;
  keyGenerator: () => string;
  pendingEvents: Array<InternalPatchEvent | MutationEvent>;
  pendingIncomingPatchesEvents: Array<PatchesEvent>;
  schema: EditorSchema;
  initialReadOnly: boolean;
  maxBlocks: number | undefined;
  selection: EditorSelection;
  initialValue: Array<PortableTextBlock> | undefined;
  internalDrag?: {
    origin: Pick<EventPosition, "selection">;
  };
  dragGhost?: HTMLElement;
  slateEditor?: PortableTextSlateEditor;
}, InternalPatchEvent | MutationEvent | PatchesEvent | {
  type: "update readOnly";
  readOnly: boolean;
} | {
  type: "update maxBlocks";
  maxBlocks: number | undefined;
} | {
  type: "add behavior";
  behaviorConfig: BehaviorConfig;
} | {
  type: "remove behavior";
  behaviorConfig: BehaviorConfig;
} | {
  type: "blur";
  editor: PortableTextSlateEditor;
} | {
  type: "focus";
  editor: PortableTextSlateEditor;
} | {
  type: "normalizing";
} | {
  type: "update selection";
  selection: EditorSelection;
} | {
  type: "done normalizing";
} | {
  type: "done syncing value";
} | {
  type: "syncing value";
} | {
  type: "behavior event";
  behaviorEvent: BehaviorEvent;
  editor: PortableTextSlateEditor;
  nativeEvent?: {
    preventDefault: () => void;
  };
} | {
  type: "set drag ghost";
  ghost: HTMLElement;
} | {
  type: "dragstart";
  ghost?: HTMLElement;
  origin: Pick<EventPosition, "selection">;
} | {
  type: "dragend";
} | {
  type: "drop";
}, {}, never, xstate227.Values<{
  "add behavior to context": {
    type: "add behavior to context";
    params: xstate227.NonReducibleUnknown;
  };
  "remove behavior from context": {
    type: "remove behavior from context";
    params: xstate227.NonReducibleUnknown;
  };
  "emit patch event": {
    type: "emit patch event";
    params: xstate227.NonReducibleUnknown;
  };
  "emit mutation event": {
    type: "emit mutation event";
    params: xstate227.NonReducibleUnknown;
  };
  "emit read only": {
    type: "emit read only";
    params: xstate227.NonReducibleUnknown;
  };
  "emit editable": {
    type: "emit editable";
    params: xstate227.NonReducibleUnknown;
  };
  "defer event": {
    type: "defer event";
    params: xstate227.NonReducibleUnknown;
  };
  "emit pending events": {
    type: "emit pending events";
    params: xstate227.NonReducibleUnknown;
  };
  "emit ready": {
    type: "emit ready";
    params: xstate227.NonReducibleUnknown;
  };
  "clear pending events": {
    type: "clear pending events";
    params: xstate227.NonReducibleUnknown;
  };
  "defer incoming patches": {
    type: "defer incoming patches";
    params: xstate227.NonReducibleUnknown;
  };
  "emit pending incoming patches": {
    type: "emit pending incoming patches";
    params: xstate227.NonReducibleUnknown;
  };
  "clear pending incoming patches": {
    type: "clear pending incoming patches";
    params: xstate227.NonReducibleUnknown;
  };
  "handle blur": {
    type: "handle blur";
    params: unknown;
  };
  "handle focus": {
    type: "handle focus";
    params: unknown;
  };
  "handle behavior event": {
    type: "handle behavior event";
    params: unknown;
  };
  "sort behaviors": {
    type: "sort behaviors";
    params: xstate227.NonReducibleUnknown;
  };
}>, {
  type: "slate is busy";
  params: unknown;
}, never, {
  "edit mode": {
    editable: "dragging internally" | "idle" | {
      focusing: "checking if busy" | "busy";
    };
  } | {
    "read only": "read only" | "determine initial edit mode";
  };
  setup: "setting up" | {
    "set up": {
      "value sync": "syncing value" | "idle";
      writing: "dirty" | {
        pristine: "normalizing" | "idle";
      };
    };
  };
}, "dragging internally", {
  converters?: Array<Converter>;
  getLegacySchema: () => PortableTextMemberSchemaTypes;
  keyGenerator: () => string;
  maxBlocks?: number;
  readOnly?: boolean;
  schema: EditorSchema;
  initialValue?: Array<PortableTextBlock>;
}, xstate227.NonReducibleUnknown, InternalPatchEvent | MutationEvent | PatchesEvent | {
  type: "blurred";
  event: react20.FocusEvent<HTMLDivElement, Element>;
} | {
  type: "done loading";
} | {
  type: "editable";
} | {
  type: "error";
  name: string;
  description: string;
  data: unknown;
} | {
  type: "focused";
  event: react20.FocusEvent<HTMLDivElement, Element>;
} | {
  type: "invalid value";
  resolution: InvalidValueResolution | null;
  value: Array<PortableTextBlock> | undefined;
} | {
  type: "loading";
} | {
  type: "read only";
} | {
  type: "ready";
} | {
  type: "selection";
  selection: EditorSelection;
} | {
  type: "value changed";
  value: Array<PortableTextBlock> | undefined;
}, xstate227.MetaObject, {
  readonly id: "editor";
  readonly context: ({
    input
  }: {
    spawn: {
      <TSrc extends never>(logic: TSrc, ...[options]: never): xstate227.ActorRefFromLogic<never>;
      <TLogic extends xstate227.AnyActorLogic>(src: TLogic, ...[options]: xstate227.ConditionalRequired<[options?: ({
        id?: never;
        systemId?: string;
        input?: xstate227.InputFrom<TLogic> | undefined;
        syncSnapshot?: boolean;
      } & { [K in xstate227.RequiredLogicInput<TLogic>]: unknown }) | undefined], xstate227.IsNotNever<xstate227.RequiredLogicInput<TLogic>>>): xstate227.ActorRefFromLogic<TLogic>;
    };
    input: {
      converters?: Array<Converter>;
      getLegacySchema: () => PortableTextMemberSchemaTypes;
      keyGenerator: () => string;
      maxBlocks?: number;
      readOnly?: boolean;
      schema: EditorSchema;
      initialValue?: Array<PortableTextBlock>;
    };
    self: xstate227.ActorRef<xstate227.MachineSnapshot<{
      behaviors: Set<BehaviorConfig>;
      behaviorsSorted: boolean;
      converters: Set<Converter>;
      getLegacySchema: () => PortableTextMemberSchemaTypes;
      keyGenerator: () => string;
      pendingEvents: Array<InternalPatchEvent | MutationEvent>;
      pendingIncomingPatchesEvents: Array<PatchesEvent>;
      schema: EditorSchema;
      initialReadOnly: boolean;
      maxBlocks: number | undefined;
      selection: EditorSelection;
      initialValue: Array<PortableTextBlock> | undefined;
      internalDrag?: {
        origin: Pick<EventPosition, "selection">;
      };
      dragGhost?: HTMLElement;
      slateEditor?: PortableTextSlateEditor;
    }, InternalPatchEvent | MutationEvent | PatchesEvent | {
      type: "update readOnly";
      readOnly: boolean;
    } | {
      type: "update maxBlocks";
      maxBlocks: number | undefined;
    } | {
      type: "add behavior";
      behaviorConfig: BehaviorConfig;
    } | {
      type: "remove behavior";
      behaviorConfig: BehaviorConfig;
    } | {
      type: "blur";
      editor: PortableTextSlateEditor;
    } | {
      type: "focus";
      editor: PortableTextSlateEditor;
    } | {
      type: "normalizing";
    } | {
      type: "update selection";
      selection: EditorSelection;
    } | {
      type: "done normalizing";
    } | {
      type: "done syncing value";
    } | {
      type: "syncing value";
    } | {
      type: "behavior event";
      behaviorEvent: BehaviorEvent;
      editor: PortableTextSlateEditor;
      nativeEvent?: {
        preventDefault: () => void;
      };
    } | {
      type: "set drag ghost";
      ghost: HTMLElement;
    } | {
      type: "dragstart";
      ghost?: HTMLElement;
      origin: Pick<EventPosition, "selection">;
    } | {
      type: "dragend";
    } | {
      type: "drop";
    }, Record<string, xstate227.AnyActorRef | undefined>, xstate227.StateValue, string, unknown, any, any>, InternalPatchEvent | MutationEvent | PatchesEvent | {
      type: "update readOnly";
      readOnly: boolean;
    } | {
      type: "update maxBlocks";
      maxBlocks: number | undefined;
    } | {
      type: "add behavior";
      behaviorConfig: BehaviorConfig;
    } | {
      type: "remove behavior";
      behaviorConfig: BehaviorConfig;
    } | {
      type: "blur";
      editor: PortableTextSlateEditor;
    } | {
      type: "focus";
      editor: PortableTextSlateEditor;
    } | {
      type: "normalizing";
    } | {
      type: "update selection";
      selection: EditorSelection;
    } | {
      type: "done normalizing";
    } | {
      type: "done syncing value";
    } | {
      type: "syncing value";
    } | {
      type: "behavior event";
      behaviorEvent: BehaviorEvent;
      editor: PortableTextSlateEditor;
      nativeEvent?: {
        preventDefault: () => void;
      };
    } | {
      type: "set drag ghost";
      ghost: HTMLElement;
    } | {
      type: "dragstart";
      ghost?: HTMLElement;
      origin: Pick<EventPosition, "selection">;
    } | {
      type: "dragend";
    } | {
      type: "drop";
    }, xstate227.AnyEventObject>;
  }) => {
    behaviors: Set<{
      behavior: Behavior<"*" | "split" | `custom.${string}` | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle" | "clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click" | "style.*" | "history.*" | "split.*" | "delete.*" | "select.*" | "deserialize.*" | "serialize.*" | "annotation.*" | "block.*" | "child.*" | "decorator.*" | "insert.*" | "move.*" | "deserialization.*" | "list item.*" | "serialization.*" | "clipboard.*" | "drag.*" | "keyboard.*" | "mouse.*", true, {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "annotation.add">;
        annotation: {
          name: string;
          value: {
            [prop: string]: unknown;
          };
        };
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "annotation.remove">;
        annotation: {
          name: string;
        };
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "block.set">;
        at: BlockPath;
        props: Record<string, unknown>;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "block.unset">;
        at: BlockPath;
        props: Array<string>;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "child.set">;
        at: ChildPath;
        props: {
          [prop: string]: unknown;
        };
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "child.unset">;
        at: ChildPath;
        props: Array<string>;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "decorator.add">;
        decorator: string;
        at?: {
          anchor: BlockOffset;
          focus: BlockOffset;
        };
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "decorator.remove">;
        decorator: string;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "delete">;
        at: NonNullable<EditorSelection>;
        direction?: "backward" | "forward";
        unit?: "character" | "word" | "line" | "block";
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "history.redo">;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "history.undo">;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "insert.inline object">;
        inlineObject: {
          name: string;
          value?: {
            [prop: string]: unknown;
          };
        };
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "insert.block">;
        block: BlockWithOptionalKey;
        placement: InsertPlacement;
        select?: "start" | "end" | "none";
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "insert.span">;
        text: string;
        annotations?: Array<{
          name: string;
          value: {
            [prop: string]: unknown;
          };
        }>;
        decorators?: Array<string>;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "insert.text">;
        text: string;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "move.backward">;
        distance: number;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "move.block">;
        at: BlockPath;
        to: BlockPath;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "move.forward">;
        distance: number;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "select">;
        at: EditorSelection;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "annotation.set">;
        at: AnnotationPath;
        props: Record<string, unknown>;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "annotation.toggle">;
        annotation: {
          name: string;
          value: {
            [prop: string]: unknown;
          };
        };
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "decorator.toggle">;
        decorator: string;
        at?: {
          anchor: BlockOffset;
          focus: BlockOffset;
        };
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "delete.backward">;
        unit: "character" | "word" | "line" | "block";
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "delete.block">;
        at: BlockPath;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "delete.child">;
        at: ChildPath;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "delete.forward">;
        unit: "character" | "word" | "line" | "block";
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "delete.text">;
        at: {
          anchor: BlockOffset;
          focus: BlockOffset;
        };
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "deserialize">;
        originEvent: PickFromUnion<NativeBehaviorEvent, "type", "drag.drop" | "clipboard.paste"> | InputBehaviorEvent;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "serialize">;
        originEvent: PickFromUnion<NativeBehaviorEvent, "type", "clipboard.copy" | "clipboard.cut" | "drag.dragstart">;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "deserialization.success">;
        mimeType: MIMEType;
        data: Array<PortableTextBlock>;
        originEvent: PickFromUnion<NativeBehaviorEvent, "type", "drag.drop" | "clipboard.paste"> | InputBehaviorEvent;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "deserialization.failure">;
        mimeType: MIMEType;
        reason: string;
        originEvent: PickFromUnion<NativeBehaviorEvent, "type", "drag.drop" | "clipboard.paste"> | InputBehaviorEvent;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "serialization.success">;
        mimeType: MIMEType;
        data: string;
        originEvent: PickFromUnion<NativeBehaviorEvent, "type", "clipboard.copy" | "clipboard.cut" | "drag.dragstart">;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "serialization.failure">;
        mimeType: MIMEType;
        reason: string;
        originEvent: PickFromUnion<NativeBehaviorEvent, "type", "clipboard.copy" | "clipboard.cut" | "drag.dragstart">;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "insert.blocks">;
        blocks: Array<BlockWithOptionalKey>;
        placement: InsertPlacement;
        select?: "start" | "end" | "none";
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "insert.break">;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "insert.soft break">;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "list item.add">;
        listItem: string;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "list item.remove">;
        listItem: string;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "list item.toggle">;
        listItem: string;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "move.block down">;
        at: BlockPath;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "move.block up">;
        at: BlockPath;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "select.previous block">;
        select?: "start" | "end";
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "select.next block">;
        select?: "start" | "end";
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "split">;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "style.add">;
        style: string;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "style.remove">;
        style: string;
      } | {
        type: StrictExtract<"split" | "annotation.add" | "annotation.remove" | "block.set" | "block.unset" | "child.set" | "child.unset" | "decorator.add" | "decorator.remove" | "delete" | "history.redo" | "history.undo" | "insert.inline object" | "insert.block" | "insert.span" | "insert.text" | "move.backward" | "move.block" | "move.forward" | "select" | "annotation.set" | "annotation.toggle" | "decorator.toggle" | "delete.backward" | "delete.block" | "delete.child" | "delete.forward" | "delete.text" | "deserialize" | "deserialization.success" | "deserialization.failure" | "insert.blocks" | "insert.break" | "insert.soft break" | "list item.add" | "list item.remove" | "list item.toggle" | "move.block down" | "move.block up" | "select.previous block" | "select.next block" | "serialize" | "serialization.success" | "serialization.failure" | "style.add" | "style.remove" | "style.toggle", "style.toggle">;
        style: string;
      } | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "clipboard.copy">;
        originEvent: {
          dataTransfer: DataTransfer;
        };
        position: Pick<EventPosition, "selection">;
      } | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "clipboard.cut">;
        originEvent: {
          dataTransfer: DataTransfer;
        };
        position: Pick<EventPosition, "selection">;
      } | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "clipboard.paste">;
        originEvent: {
          dataTransfer: DataTransfer;
        };
        position: Pick<EventPosition, "selection">;
      } | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "drag.dragstart">;
        originEvent: {
          clientX: number;
          clientY: number;
          dataTransfer: DataTransfer;
        };
        position: Pick<EventPosition, "selection">;
      } | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "drag.drag">;
        originEvent: {
          dataTransfer: DataTransfer;
        };
      } | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "drag.dragend">;
        originEvent: {
          dataTransfer: DataTransfer;
        };
      } | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "drag.dragenter">;
        originEvent: {
          dataTransfer: DataTransfer;
        };
        position: EventPosition;
      } | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "drag.dragover">;
        originEvent: {
          dataTransfer: DataTransfer;
        };
        dragOrigin?: Pick<EventPosition, "selection">;
        position: EventPosition;
      } | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "drag.drop">;
        originEvent: {
          dataTransfer: DataTransfer;
        };
        dragOrigin?: Pick<EventPosition, "selection">;
        position: EventPosition;
      } | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "drag.dragleave">;
        originEvent: {
          dataTransfer: DataTransfer;
        };
      } | InputBehaviorEvent | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "keyboard.keydown">;
        originEvent: Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">;
      } | {
        type: StrictExtract<"clipboard.copy" | "clipboard.cut" | "clipboard.paste" | "drag.dragstart" | "drag.drag" | "drag.dragend" | "drag.dragenter" | "drag.dragover" | "drag.dragleave" | "drag.drop" | "input.*" | "keyboard.keydown" | "keyboard.keyup" | "mouse.click", "keyboard.keyup">;
        originEvent: Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">;
      } | MouseBehaviorEvent | CustomBehaviorEvent<Record<string, unknown>, string, `custom.${string}`>>;
      priority: EditorPriority;
    }>;
    behaviorsSorted: false;
    converters: Set<Converter>;
    getLegacySchema: () => PortableTextMemberSchemaTypes;
    keyGenerator: () => string;
    pendingEvents: never[];
    pendingIncomingPatchesEvents: never[];
    schema: _portabletext_schema5.Schema;
    selection: null;
    initialReadOnly: boolean;
    maxBlocks: number | undefined;
    initialValue: PortableTextBlock[] | undefined;
  };
  readonly on: {
    readonly 'add behavior': {
      readonly actions: "add behavior to context";
    };
    readonly 'remove behavior': {
      readonly actions: "remove behavior from context";
    };
    readonly 'update maxBlocks': {
      readonly actions: xstate227.ActionFunction<{
        behaviors: Set<BehaviorConfig>;
        behaviorsSorted: boolean;
        converters: Set<Converter>;
        getLegacySchema: () => PortableTextMemberSchemaTypes;
        keyGenerator: () => string;
        pendingEvents: Array<InternalPatchEvent | MutationEvent>;
        pendingIncomingPatchesEvents: Array<PatchesEvent>;
        schema: EditorSchema;
        initialReadOnly: boolean;
        maxBlocks: number | undefined;
        selection: EditorSelection;
        initialValue: Array<PortableTextBlock> | undefined;
        internalDrag?: {
          origin: Pick<EventPosition, "selection">;
        };
        dragGhost?: HTMLElement;
        slateEditor?: PortableTextSlateEditor;
      }, {
        type: "update maxBlocks";
        maxBlocks: number | undefined;
      }, InternalPatchEvent | MutationEvent | PatchesEvent | {
        type: "update readOnly";
        readOnly: boolean;
      } | {
        type: "update maxBlocks";
        maxBlocks: number | undefined;
      } | {
        type: "add behavior";
        behaviorConfig: BehaviorConfig;
      } | {
        type: "remove behavior";
        behaviorConfig: BehaviorConfig;
      } | {
        type: "blur";
        editor: PortableTextSlateEditor;
      } | {
        type: "focus";
        editor: PortableTextSlateEditor;
      } | {
        type: "normalizing";
      } | {
        type: "update selection";
        selection: EditorSelection;
      } | {
        type: "done normalizing";
      } | {
        type: "done syncing value";
      } | {
        type: "syncing value";
      } | {
        type: "behavior event";
        behaviorEvent: BehaviorEvent;
        editor: PortableTextSlateEditor;
        nativeEvent?: {
          preventDefault: () => void;
        };
      } | {
        type: "set drag ghost";
        ghost: HTMLElement;
      } | {
        type: "dragstart";
        ghost?: HTMLElement;
        origin: Pick<EventPosition, "selection">;
      } | {
        type: "dragend";
      } | {
        type: "drop";
      }, undefined, never, never, never, never, never>;
    };
    readonly 'update selection': {
      readonly actions: readonly [xstate227.ActionFunction<{
        behaviors: Set<BehaviorConfig>;
        behaviorsSorted: boolean;
        converters: Set<Converter>;
        getLegacySchema: () => PortableTextMemberSchemaTypes;
        keyGenerator: () => string;
        pendingEvents: Array<InternalPatchEvent | MutationEvent>;
        pendingIncomingPatchesEvents: Array<PatchesEvent>;
        schema: EditorSchema;
        initialReadOnly: boolean;
        maxBlocks: number | undefined;
        selection: EditorSelection;
        initialValue: Array<PortableTextBlock> | undefined;
        internalDrag?: {
          origin: Pick<EventPosition, "selection">;
        };
        dragGhost?: HTMLElement;
        slateEditor?: PortableTextSlateEditor;
      }, {
        type: "update selection";
        selection: EditorSelection;
      }, InternalPatchEvent | MutationEvent | PatchesEvent | {
        type: "update readOnly";
        readOnly: boolean;
      } | {
        type: "update maxBlocks";
        maxBlocks: number | undefined;
      } | {
        type: "add behavior";
        behaviorConfig: BehaviorConfig;
      } | {
        type: "remove behavior";
        behaviorConfig: BehaviorConfig;
      } | {
        type: "blur";
        editor: PortableTextSlateEditor;
      } | {
        type: "focus";
        editor: PortableTextSlateEditor;
      } | {
        type: "normalizing";
      } | {
        type: "update selection";
        selection: EditorSelection;
      } | {
        type: "done normalizing";
      } | {
        type: "done syncing value";
      } | {
        type: "syncing value";
      } | {
        type: "behavior event";
        behaviorEvent: BehaviorEvent;
        editor: PortableTextSlateEditor;
        nativeEvent?: {
          preventDefault: () => void;
        };
      } | {
        type: "set drag ghost";
        ghost: HTMLElement;
      } | {
        type: "dragstart";
        ghost?: HTMLElement;
        origin: Pick<EventPosition, "selection">;
      } | {
        type: "dragend";
      } | {
        type: "drop";
      }, undefined, never, never, never, never, never>, xstate227.ActionFunction<{
        behaviors: Set<BehaviorConfig>;
        behaviorsSorted: boolean;
        converters: Set<Converter>;
        getLegacySchema: () => PortableTextMemberSchemaTypes;
        keyGenerator: () => string;
        pendingEvents: Array<InternalPatchEvent | MutationEvent>;
        pendingIncomingPatchesEvents: Array<PatchesEvent>;
        schema: EditorSchema;
        initialReadOnly: boolean;
        maxBlocks: number | undefined;
        selection: EditorSelection;
        initialValue: Array<PortableTextBlock> | undefined;
        internalDrag?: {
          origin: Pick<EventPosition, "selection">;
        };
        dragGhost?: HTMLElement;
        slateEditor?: PortableTextSlateEditor;
      }, {
        type: "update selection";
        selection: EditorSelection;
      }, InternalPatchEvent | MutationEvent | PatchesEvent | {
        type: "update readOnly";
        readOnly: boolean;
      } | {
        type: "update maxBlocks";
        maxBlocks: number | undefined;
      } | {
        type: "add behavior";
        behaviorConfig: BehaviorConfig;
      } | {
        type: "remove behavior";
        behaviorConfig: BehaviorConfig;
      } | {
        type: "blur";
        editor: PortableTextSlateEditor;
      } | {
        type: "focus";
        editor: PortableTextSlateEditor;
      } | {
        type: "normalizing";
      } | {
        type: "update selection";
        selection: EditorSelection;
      } | {
        type: "done normalizing";
      } | {
        type: "done syncing value";
      } | {
        type: "syncing value";
      } | {
        type: "behavior event";
        behaviorEvent: BehaviorEvent;
        editor: PortableTextSlateEditor;
        nativeEvent?: {
          preventDefault: () => void;
        };
      } | {
        type: "set drag ghost";
        ghost: HTMLElement;
      } | {
        type: "dragstart";
        ghost?: HTMLElement;
        origin: Pick<EventPosition, "selection">;
      } | {
        type: "dragend";
      } | {
        type: "drop";
      }, undefined, never, never, never, never, InternalPatchEvent | MutationEvent | PatchesEvent | {
        type: "blurred";
        event: react20.FocusEvent<HTMLDivElement, Element>;
      } | {
        type: "done loading";
      } | {
        type: "editable";
      } | {
        type: "error";
        name: string;
        description: string;
        data: unknown;
      } | {
        type: "focused";
        event: react20.FocusEvent<HTMLDivElement, Element>;
      } | {
        type: "invalid value";
        resolution: InvalidValueResolution | null;
        value: Array<PortableTextBlock> | undefined;
      } | {
        type: "loading";
      } | {
        type: "read only";
      } | {
        type: "ready";
      } | {
        type: "selection";
        selection: EditorSelection;
      } | {
        type: "value changed";
        value: Array<PortableTextBlock> | undefined;
      }>];
    };
    readonly 'set drag ghost': {
      readonly actions: xstate227.ActionFunction<{
        behaviors: Set<BehaviorConfig>;
        behaviorsSorted: boolean;
        converters: Set<Converter>;
        getLegacySchema: () => PortableTextMemberSchemaTypes;
        keyGenerator: () => string;
        pendingEvents: Array<InternalPatchEvent | MutationEvent>;
        pendingIncomingPatchesEvents: Array<PatchesEvent>;
        schema: EditorSchema;
        initialReadOnly: boolean;
        maxBlocks: number | undefined;
        selection: EditorSelection;
        initialValue: Array<PortableTextBlock> | undefined;
        internalDrag?: {
          origin: Pick<EventPosition, "selection">;
        };
        dragGhost?: HTMLElement;
        slateEditor?: PortableTextSlateEditor;
      }, {
        type: "set drag ghost";
        ghost: HTMLElement;
      }, InternalPatchEvent | MutationEvent | PatchesEvent | {
        type: "update readOnly";
        readOnly: boolean;
      } | {
        type: "update maxBlocks";
        maxBlocks: number | undefined;
      } | {
        type: "add behavior";
        behaviorConfig: BehaviorConfig;
      } | {
        type: "remove behavior";
        behaviorConfig: BehaviorConfig;
      } | {
        type: "blur";
        editor: PortableTextSlateEditor;
      } | {
        type: "focus";
        editor: PortableTextSlateEditor;
      } | {
        type: "normalizing";
      } | {
        type: "update selection";
        selection: EditorSelection;
      } | {
        type: "done normalizing";
      } | {
        type: "done syncing value";
      } | {
        type: "syncing value";
      } | {
        type: "behavior event";
        behaviorEvent: BehaviorEvent;
        editor: PortableTextSlateEditor;
        nativeEvent?: {
          preventDefault: () => void;
        };
      } | {
        type: "set drag ghost";
        ghost: HTMLElement;
      } | {
        type: "dragstart";
        ghost?: HTMLElement;
        origin: Pick<EventPosition, "selection">;
      } | {
        type: "dragend";
      } | {
        type: "drop";
      }, undefined, never, never, never, never, never>;
    };
  };
  readonly type: "parallel";
  readonly states: {
    readonly 'edit mode': {
      readonly initial: "read only";
      readonly states: {
        readonly 'read only': {
          readonly initial: "determine initial edit mode";
          readonly on: {
            readonly 'behavior event': {
              readonly actions: readonly ["sort behaviors", "handle behavior event"];
              readonly guard: ({
                event
              }: xstate_guards12.GuardArgs<{
                behaviors: Set<BehaviorConfig>;
                behaviorsSorted: boolean;
                converters: Set<Converter>;
                getLegacySchema: () => PortableTextMemberSchemaTypes;
                keyGenerator: () => string;
                pendingEvents: Array<InternalPatchEvent | MutationEvent>;
                pendingIncomingPatchesEvents: Array<PatchesEvent>;
                schema: EditorSchema;
                initialReadOnly: boolean;
                maxBlocks: number | undefined;
                selection: EditorSelection;
                initialValue: Array<PortableTextBlock> | undefined;
                internalDrag?: {
                  origin: Pick<EventPosition, "selection">;
                };
                dragGhost?: HTMLElement;
                slateEditor?: PortableTextSlateEditor;
              }, {
                type: "behavior event";
                behaviorEvent: BehaviorEvent;
                editor: PortableTextSlateEditor;
                nativeEvent?: {
                  preventDefault: () => void;
                };
              }>) => boolean;
            };
          };
          readonly states: {
            readonly 'determine initial edit mode': {
              readonly entry: readonly [() => void];
              readonly exit: readonly [() => void];
              readonly on: {
                readonly 'done syncing value': readonly [{
                  readonly target: "#editor.edit mode.read only.read only";
                  readonly guard: ({
                    context
                  }: xstate_guards12.GuardArgs<{
                    behaviors: Set<BehaviorConfig>;
                    behaviorsSorted: boolean;
                    converters: Set<Converter>;
                    getLegacySchema: () => PortableTextMemberSchemaTypes;
                    keyGenerator: () => string;
                    pendingEvents: Array<InternalPatchEvent | MutationEvent>;
                    pendingIncomingPatchesEvents: Array<PatchesEvent>;
                    schema: EditorSchema;
                    initialReadOnly: boolean;
                    maxBlocks: number | undefined;
                    selection: EditorSelection;
                    initialValue: Array<PortableTextBlock> | undefined;
                    internalDrag?: {
                      origin: Pick<EventPosition, "selection">;
                    };
                    dragGhost?: HTMLElement;
                    slateEditor?: PortableTextSlateEditor;
                  }, {
                    type: "done syncing value";
                  }>) => boolean;
                }, {
                  readonly target: "#editor.edit mode.editable";
                }];
              };
            };
            readonly 'read only': {
              readonly entry: readonly [() => void];
              readonly exit: readonly [() => void];
              readonly on: {
                readonly 'update readOnly': {
                  readonly guard: ({
                    event
                  }: xstate_guards12.GuardArgs<{
                    behaviors: Set<BehaviorConfig>;
                    behaviorsSorted: boolean;
                    converters: Set<Converter>;
                    getLegacySchema: () => PortableTextMemberSchemaTypes;
                    keyGenerator: () => string;
                    pendingEvents: Array<InternalPatchEvent | MutationEvent>;
                    pendingIncomingPatchesEvents: Array<PatchesEvent>;
                    schema: EditorSchema;
                    initialReadOnly: boolean;
                    maxBlocks: number | undefined;
                    selection: EditorSelection;
                    initialValue: Array<PortableTextBlock> | undefined;
                    internalDrag?: {
                      origin: Pick<EventPosition, "selection">;
                    };
                    dragGhost?: HTMLElement;
                    slateEditor?: PortableTextSlateEditor;
                  }, {
                    type: "update readOnly";
                    readOnly: boolean;
                  }>) => boolean;
                  readonly target: "#editor.edit mode.editable";
                  readonly actions: readonly ["emit editable"];
                };
              };
            };
          };
        };
        readonly editable: {
          readonly on: {
            readonly 'update readOnly': {
              readonly guard: ({
                event
              }: xstate_guards12.GuardArgs<{
                behaviors: Set<BehaviorConfig>;
                behaviorsSorted: boolean;
                converters: Set<Converter>;
                getLegacySchema: () => PortableTextMemberSchemaTypes;
                keyGenerator: () => string;
                pendingEvents: Array<InternalPatchEvent | MutationEvent>;
                pendingIncomingPatchesEvents: Array<PatchesEvent>;
                schema: EditorSchema;
                initialReadOnly: boolean;
                maxBlocks: number | undefined;
                selection: EditorSelection;
                initialValue: Array<PortableTextBlock> | undefined;
                internalDrag?: {
                  origin: Pick<EventPosition, "selection">;
                };
                dragGhost?: HTMLElement;
                slateEditor?: PortableTextSlateEditor;
              }, {
                type: "update readOnly";
                readOnly: boolean;
              }>) => boolean;
              readonly target: "#editor.edit mode.read only.read only";
              readonly actions: readonly ["emit read only"];
            };
            readonly 'behavior event': {
              readonly actions: readonly ["sort behaviors", "handle behavior event"];
            };
            readonly blur: {
              readonly actions: "handle blur";
            };
            readonly focus: {
              readonly target: ".focusing";
              readonly actions: readonly [xstate227.ActionFunction<{
                behaviors: Set<BehaviorConfig>;
                behaviorsSorted: boolean;
                converters: Set<Converter>;
                getLegacySchema: () => PortableTextMemberSchemaTypes;
                keyGenerator: () => string;
                pendingEvents: Array<InternalPatchEvent | MutationEvent>;
                pendingIncomingPatchesEvents: Array<PatchesEvent>;
                schema: EditorSchema;
                initialReadOnly: boolean;
                maxBlocks: number | undefined;
                selection: EditorSelection;
                initialValue: Array<PortableTextBlock> | undefined;
                internalDrag?: {
                  origin: Pick<EventPosition, "selection">;
                };
                dragGhost?: HTMLElement;
                slateEditor?: PortableTextSlateEditor;
              }, {
                type: "focus";
                editor: PortableTextSlateEditor;
              }, InternalPatchEvent | MutationEvent | PatchesEvent | {
                type: "update readOnly";
                readOnly: boolean;
              } | {
                type: "update maxBlocks";
                maxBlocks: number | undefined;
              } | {
                type: "add behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "remove behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "blur";
                editor: PortableTextSlateEditor;
              } | {
                type: "focus";
                editor: PortableTextSlateEditor;
              } | {
                type: "normalizing";
              } | {
                type: "update selection";
                selection: EditorSelection;
              } | {
                type: "done normalizing";
              } | {
                type: "done syncing value";
              } | {
                type: "syncing value";
              } | {
                type: "behavior event";
                behaviorEvent: BehaviorEvent;
                editor: PortableTextSlateEditor;
                nativeEvent?: {
                  preventDefault: () => void;
                };
              } | {
                type: "set drag ghost";
                ghost: HTMLElement;
              } | {
                type: "dragstart";
                ghost?: HTMLElement;
                origin: Pick<EventPosition, "selection">;
              } | {
                type: "dragend";
              } | {
                type: "drop";
              }, undefined, never, never, never, never, never>];
            };
          };
          readonly initial: "idle";
          readonly states: {
            readonly idle: {
              readonly entry: readonly [() => void];
              readonly exit: readonly [() => void];
              readonly on: {
                readonly dragstart: {
                  readonly actions: readonly [xstate227.ActionFunction<{
                    behaviors: Set<BehaviorConfig>;
                    behaviorsSorted: boolean;
                    converters: Set<Converter>;
                    getLegacySchema: () => PortableTextMemberSchemaTypes;
                    keyGenerator: () => string;
                    pendingEvents: Array<InternalPatchEvent | MutationEvent>;
                    pendingIncomingPatchesEvents: Array<PatchesEvent>;
                    schema: EditorSchema;
                    initialReadOnly: boolean;
                    maxBlocks: number | undefined;
                    selection: EditorSelection;
                    initialValue: Array<PortableTextBlock> | undefined;
                    internalDrag?: {
                      origin: Pick<EventPosition, "selection">;
                    };
                    dragGhost?: HTMLElement;
                    slateEditor?: PortableTextSlateEditor;
                  }, {
                    type: "dragstart";
                    ghost?: HTMLElement;
                    origin: Pick<EventPosition, "selection">;
                  }, InternalPatchEvent | MutationEvent | PatchesEvent | {
                    type: "update readOnly";
                    readOnly: boolean;
                  } | {
                    type: "update maxBlocks";
                    maxBlocks: number | undefined;
                  } | {
                    type: "add behavior";
                    behaviorConfig: BehaviorConfig;
                  } | {
                    type: "remove behavior";
                    behaviorConfig: BehaviorConfig;
                  } | {
                    type: "blur";
                    editor: PortableTextSlateEditor;
                  } | {
                    type: "focus";
                    editor: PortableTextSlateEditor;
                  } | {
                    type: "normalizing";
                  } | {
                    type: "update selection";
                    selection: EditorSelection;
                  } | {
                    type: "done normalizing";
                  } | {
                    type: "done syncing value";
                  } | {
                    type: "syncing value";
                  } | {
                    type: "behavior event";
                    behaviorEvent: BehaviorEvent;
                    editor: PortableTextSlateEditor;
                    nativeEvent?: {
                      preventDefault: () => void;
                    };
                  } | {
                    type: "set drag ghost";
                    ghost: HTMLElement;
                  } | {
                    type: "dragstart";
                    ghost?: HTMLElement;
                    origin: Pick<EventPosition, "selection">;
                  } | {
                    type: "dragend";
                  } | {
                    type: "drop";
                  }, undefined, never, never, never, never, never>];
                  readonly target: "dragging internally";
                };
              };
            };
            readonly focusing: {
              readonly initial: "checking if busy";
              readonly states: {
                readonly 'checking if busy': {
                  readonly entry: readonly [() => void];
                  readonly exit: readonly [() => void];
                  readonly always: readonly [{
                    readonly guard: "slate is busy";
                    readonly target: "busy";
                  }, {
                    readonly target: "#editor.edit mode.editable.idle";
                    readonly actions: readonly ["handle focus"];
                  }];
                };
                readonly busy: {
                  readonly entry: readonly [() => void];
                  readonly exit: readonly [() => void];
                  readonly after: {
                    readonly 10: {
                      readonly target: "checking if busy";
                    };
                  };
                };
              };
            };
            readonly 'dragging internally': {
              readonly entry: readonly [() => void];
              readonly exit: readonly [() => void, ({
                context
              }: xstate227.ActionArgs<{
                behaviors: Set<BehaviorConfig>;
                behaviorsSorted: boolean;
                converters: Set<Converter>;
                getLegacySchema: () => PortableTextMemberSchemaTypes;
                keyGenerator: () => string;
                pendingEvents: Array<InternalPatchEvent | MutationEvent>;
                pendingIncomingPatchesEvents: Array<PatchesEvent>;
                schema: EditorSchema;
                initialReadOnly: boolean;
                maxBlocks: number | undefined;
                selection: EditorSelection;
                initialValue: Array<PortableTextBlock> | undefined;
                internalDrag?: {
                  origin: Pick<EventPosition, "selection">;
                };
                dragGhost?: HTMLElement;
                slateEditor?: PortableTextSlateEditor;
              }, InternalPatchEvent | MutationEvent | PatchesEvent | {
                type: "update readOnly";
                readOnly: boolean;
              } | {
                type: "update maxBlocks";
                maxBlocks: number | undefined;
              } | {
                type: "add behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "remove behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "blur";
                editor: PortableTextSlateEditor;
              } | {
                type: "focus";
                editor: PortableTextSlateEditor;
              } | {
                type: "normalizing";
              } | {
                type: "update selection";
                selection: EditorSelection;
              } | {
                type: "done normalizing";
              } | {
                type: "done syncing value";
              } | {
                type: "syncing value";
              } | {
                type: "behavior event";
                behaviorEvent: BehaviorEvent;
                editor: PortableTextSlateEditor;
                nativeEvent?: {
                  preventDefault: () => void;
                };
              } | {
                type: "set drag ghost";
                ghost: HTMLElement;
              } | {
                type: "dragstart";
                ghost?: HTMLElement;
                origin: Pick<EventPosition, "selection">;
              } | {
                type: "dragend";
              } | {
                type: "drop";
              }, InternalPatchEvent | MutationEvent | PatchesEvent | {
                type: "update readOnly";
                readOnly: boolean;
              } | {
                type: "update maxBlocks";
                maxBlocks: number | undefined;
              } | {
                type: "add behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "remove behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "blur";
                editor: PortableTextSlateEditor;
              } | {
                type: "focus";
                editor: PortableTextSlateEditor;
              } | {
                type: "normalizing";
              } | {
                type: "update selection";
                selection: EditorSelection;
              } | {
                type: "done normalizing";
              } | {
                type: "done syncing value";
              } | {
                type: "syncing value";
              } | {
                type: "behavior event";
                behaviorEvent: BehaviorEvent;
                editor: PortableTextSlateEditor;
                nativeEvent?: {
                  preventDefault: () => void;
                };
              } | {
                type: "set drag ghost";
                ghost: HTMLElement;
              } | {
                type: "dragstart";
                ghost?: HTMLElement;
                origin: Pick<EventPosition, "selection">;
              } | {
                type: "dragend";
              } | {
                type: "drop";
              }>) => void, xstate227.ActionFunction<{
                behaviors: Set<BehaviorConfig>;
                behaviorsSorted: boolean;
                converters: Set<Converter>;
                getLegacySchema: () => PortableTextMemberSchemaTypes;
                keyGenerator: () => string;
                pendingEvents: Array<InternalPatchEvent | MutationEvent>;
                pendingIncomingPatchesEvents: Array<PatchesEvent>;
                schema: EditorSchema;
                initialReadOnly: boolean;
                maxBlocks: number | undefined;
                selection: EditorSelection;
                initialValue: Array<PortableTextBlock> | undefined;
                internalDrag?: {
                  origin: Pick<EventPosition, "selection">;
                };
                dragGhost?: HTMLElement;
                slateEditor?: PortableTextSlateEditor;
              }, InternalPatchEvent | MutationEvent | PatchesEvent | {
                type: "update readOnly";
                readOnly: boolean;
              } | {
                type: "update maxBlocks";
                maxBlocks: number | undefined;
              } | {
                type: "add behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "remove behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "blur";
                editor: PortableTextSlateEditor;
              } | {
                type: "focus";
                editor: PortableTextSlateEditor;
              } | {
                type: "normalizing";
              } | {
                type: "update selection";
                selection: EditorSelection;
              } | {
                type: "done normalizing";
              } | {
                type: "done syncing value";
              } | {
                type: "syncing value";
              } | {
                type: "behavior event";
                behaviorEvent: BehaviorEvent;
                editor: PortableTextSlateEditor;
                nativeEvent?: {
                  preventDefault: () => void;
                };
              } | {
                type: "set drag ghost";
                ghost: HTMLElement;
              } | {
                type: "dragstart";
                ghost?: HTMLElement;
                origin: Pick<EventPosition, "selection">;
              } | {
                type: "dragend";
              } | {
                type: "drop";
              }, InternalPatchEvent | MutationEvent | PatchesEvent | {
                type: "update readOnly";
                readOnly: boolean;
              } | {
                type: "update maxBlocks";
                maxBlocks: number | undefined;
              } | {
                type: "add behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "remove behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "blur";
                editor: PortableTextSlateEditor;
              } | {
                type: "focus";
                editor: PortableTextSlateEditor;
              } | {
                type: "normalizing";
              } | {
                type: "update selection";
                selection: EditorSelection;
              } | {
                type: "done normalizing";
              } | {
                type: "done syncing value";
              } | {
                type: "syncing value";
              } | {
                type: "behavior event";
                behaviorEvent: BehaviorEvent;
                editor: PortableTextSlateEditor;
                nativeEvent?: {
                  preventDefault: () => void;
                };
              } | {
                type: "set drag ghost";
                ghost: HTMLElement;
              } | {
                type: "dragstart";
                ghost?: HTMLElement;
                origin: Pick<EventPosition, "selection">;
              } | {
                type: "dragend";
              } | {
                type: "drop";
              }, undefined, never, never, never, never, never>, xstate227.ActionFunction<{
                behaviors: Set<BehaviorConfig>;
                behaviorsSorted: boolean;
                converters: Set<Converter>;
                getLegacySchema: () => PortableTextMemberSchemaTypes;
                keyGenerator: () => string;
                pendingEvents: Array<InternalPatchEvent | MutationEvent>;
                pendingIncomingPatchesEvents: Array<PatchesEvent>;
                schema: EditorSchema;
                initialReadOnly: boolean;
                maxBlocks: number | undefined;
                selection: EditorSelection;
                initialValue: Array<PortableTextBlock> | undefined;
                internalDrag?: {
                  origin: Pick<EventPosition, "selection">;
                };
                dragGhost?: HTMLElement;
                slateEditor?: PortableTextSlateEditor;
              }, InternalPatchEvent | MutationEvent | PatchesEvent | {
                type: "update readOnly";
                readOnly: boolean;
              } | {
                type: "update maxBlocks";
                maxBlocks: number | undefined;
              } | {
                type: "add behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "remove behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "blur";
                editor: PortableTextSlateEditor;
              } | {
                type: "focus";
                editor: PortableTextSlateEditor;
              } | {
                type: "normalizing";
              } | {
                type: "update selection";
                selection: EditorSelection;
              } | {
                type: "done normalizing";
              } | {
                type: "done syncing value";
              } | {
                type: "syncing value";
              } | {
                type: "behavior event";
                behaviorEvent: BehaviorEvent;
                editor: PortableTextSlateEditor;
                nativeEvent?: {
                  preventDefault: () => void;
                };
              } | {
                type: "set drag ghost";
                ghost: HTMLElement;
              } | {
                type: "dragstart";
                ghost?: HTMLElement;
                origin: Pick<EventPosition, "selection">;
              } | {
                type: "dragend";
              } | {
                type: "drop";
              }, InternalPatchEvent | MutationEvent | PatchesEvent | {
                type: "update readOnly";
                readOnly: boolean;
              } | {
                type: "update maxBlocks";
                maxBlocks: number | undefined;
              } | {
                type: "add behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "remove behavior";
                behaviorConfig: BehaviorConfig;
              } | {
                type: "blur";
                editor: PortableTextSlateEditor;
              } | {
                type: "focus";
                editor: PortableTextSlateEditor;
              } | {
                type: "normalizing";
              } | {
                type: "update selection";
                selection: EditorSelection;
              } | {
                type: "done normalizing";
              } | {
                type: "done syncing value";
              } | {
                type: "syncing value";
              } | {
                type: "behavior event";
                behaviorEvent: BehaviorEvent;
                editor: PortableTextSlateEditor;
                nativeEvent?: {
                  preventDefault: () => void;
                };
              } | {
                type: "set drag ghost";
                ghost: HTMLElement;
              } | {
                type: "dragstart";
                ghost?: HTMLElement;
                origin: Pick<EventPosition, "selection">;
              } | {
                type: "dragend";
              } | {
                type: "drop";
              }, undefined, never, never, never, never, never>];
              readonly tags: readonly ["dragging internally"];
              readonly on: {
                readonly dragend: {
                  readonly target: "idle";
                };
                readonly drop: {
                  readonly target: "idle";
                };
              };
            };
          };
        };
      };
    };
    readonly setup: {
      readonly initial: "setting up";
      readonly states: {
        readonly 'setting up': {
          readonly entry: readonly [() => void];
          readonly exit: readonly [() => void, "emit ready", "emit pending incoming patches", "clear pending incoming patches"];
          readonly on: {
            readonly 'internal.patch': {
              readonly actions: "defer event";
            };
            readonly mutation: {
              readonly actions: "defer event";
            };
            readonly 'done syncing value': {
              readonly target: "set up";
            };
            readonly patches: {
              readonly actions: readonly ["defer incoming patches"];
            };
          };
        };
        readonly 'set up': {
          readonly type: "parallel";
          readonly states: {
            readonly 'value sync': {
              readonly initial: "idle";
              readonly states: {
                readonly idle: {
                  readonly entry: readonly [() => void];
                  readonly exit: readonly [() => void];
                  readonly on: {
                    readonly patches: {
                      readonly actions: readonly [xstate227.ActionFunction<{
                        behaviors: Set<BehaviorConfig>;
                        behaviorsSorted: boolean;
                        converters: Set<Converter>;
                        getLegacySchema: () => PortableTextMemberSchemaTypes;
                        keyGenerator: () => string;
                        pendingEvents: Array<InternalPatchEvent | MutationEvent>;
                        pendingIncomingPatchesEvents: Array<PatchesEvent>;
                        schema: EditorSchema;
                        initialReadOnly: boolean;
                        maxBlocks: number | undefined;
                        selection: EditorSelection;
                        initialValue: Array<PortableTextBlock> | undefined;
                        internalDrag?: {
                          origin: Pick<EventPosition, "selection">;
                        };
                        dragGhost?: HTMLElement;
                        slateEditor?: PortableTextSlateEditor;
                      }, PatchesEvent, InternalPatchEvent | MutationEvent | PatchesEvent | {
                        type: "update readOnly";
                        readOnly: boolean;
                      } | {
                        type: "update maxBlocks";
                        maxBlocks: number | undefined;
                      } | {
                        type: "add behavior";
                        behaviorConfig: BehaviorConfig;
                      } | {
                        type: "remove behavior";
                        behaviorConfig: BehaviorConfig;
                      } | {
                        type: "blur";
                        editor: PortableTextSlateEditor;
                      } | {
                        type: "focus";
                        editor: PortableTextSlateEditor;
                      } | {
                        type: "normalizing";
                      } | {
                        type: "update selection";
                        selection: EditorSelection;
                      } | {
                        type: "done normalizing";
                      } | {
                        type: "done syncing value";
                      } | {
                        type: "syncing value";
                      } | {
                        type: "behavior event";
                        behaviorEvent: BehaviorEvent;
                        editor: PortableTextSlateEditor;
                        nativeEvent?: {
                          preventDefault: () => void;
                        };
                      } | {
                        type: "set drag ghost";
                        ghost: HTMLElement;
                      } | {
                        type: "dragstart";
                        ghost?: HTMLElement;
                        origin: Pick<EventPosition, "selection">;
                      } | {
                        type: "dragend";
                      } | {
                        type: "drop";
                      }, undefined, never, never, never, never, InternalPatchEvent | MutationEvent | PatchesEvent | {
                        type: "blurred";
                        event: react20.FocusEvent<HTMLDivElement, Element>;
                      } | {
                        type: "done loading";
                      } | {
                        type: "editable";
                      } | {
                        type: "error";
                        name: string;
                        description: string;
                        data: unknown;
                      } | {
                        type: "focused";
                        event: react20.FocusEvent<HTMLDivElement, Element>;
                      } | {
                        type: "invalid value";
                        resolution: InvalidValueResolution | null;
                        value: Array<PortableTextBlock> | undefined;
                      } | {
                        type: "loading";
                      } | {
                        type: "read only";
                      } | {
                        type: "ready";
                      } | {
                        type: "selection";
                        selection: EditorSelection;
                      } | {
                        type: "value changed";
                        value: Array<PortableTextBlock> | undefined;
                      }>];
                    };
                    readonly 'syncing value': {
                      readonly target: "syncing value";
                    };
                  };
                };
                readonly 'syncing value': {
                  readonly entry: readonly [() => void];
                  readonly exit: readonly [() => void, "emit pending incoming patches", "clear pending incoming patches"];
                  readonly on: {
                    readonly patches: {
                      readonly actions: readonly ["defer incoming patches"];
                    };
                    readonly 'done syncing value': {
                      readonly target: "idle";
                    };
                  };
                };
              };
            };
            readonly writing: {
              readonly initial: "pristine";
              readonly states: {
                readonly pristine: {
                  readonly initial: "idle";
                  readonly states: {
                    readonly idle: {
                      readonly entry: readonly [() => void];
                      readonly exit: readonly [() => void];
                      readonly on: {
                        readonly normalizing: {
                          readonly target: "normalizing";
                        };
                        readonly 'internal.patch': {
                          readonly actions: "defer event";
                          readonly target: "#editor.setup.set up.writing.dirty";
                        };
                        readonly mutation: {
                          readonly actions: "defer event";
                          readonly target: "#editor.setup.set up.writing.dirty";
                        };
                      };
                    };
                    readonly normalizing: {
                      readonly entry: readonly [() => void];
                      readonly exit: readonly [() => void];
                      readonly on: {
                        readonly 'done normalizing': {
                          readonly target: "idle";
                        };
                        readonly 'internal.patch': {
                          readonly actions: "defer event";
                        };
                        readonly mutation: {
                          readonly actions: "defer event";
                        };
                      };
                    };
                  };
                };
                readonly dirty: {
                  readonly entry: readonly [() => void, "emit pending events", "clear pending events"];
                  readonly exit: readonly [() => void];
                  readonly on: {
                    readonly 'internal.patch': {
                      readonly actions: "emit patch event";
                    };
                    readonly mutation: {
                      readonly actions: "emit mutation event";
                    };
                  };
                };
              };
            };
          };
        };
      };
    };
  };
}>;
type EventPosition = {
  block: 'start' | 'end';
  /**
   * Did the event origin from the editor DOM node itself or from a child node?
   */
  isEditor: boolean;
  selection: NonNullable<EditorSelection>;
};
/**
 * @beta
 */
type BlockOffset = {
  path: BlockPath;
  offset: number;
};
/**
 * @beta
 */
type BehaviorEvent = SyntheticBehaviorEvent | NativeBehaviorEvent | CustomBehaviorEvent;
type BehaviorEventTypeNamespace = SyntheticBehaviorEventNamespace | NativeBehaviorEventNamespace | CustomBehaviorEventNamespace;
type NamespacedBehaviorEventType<TNamespace extends BehaviorEventTypeNamespace | ''> = TNamespace extends '' ? BehaviorEvent['type'] : Extract<BehaviorEvent['type'], TNamespace | `${TNamespace}.${string}`>;
/**************************************
 * External events
 **************************************/
type ExternalBehaviorEventNamespace = 'blur' | 'focus' | 'insert';
type ExternalBehaviorEventType<TNamespace extends ExternalBehaviorEventNamespace, TType extends string = ''> = TType extends '' ? `${TNamespace}` : `${TNamespace}.${TType}`;
type ExternalBehaviorEvent = {
  type: ExternalBehaviorEventType<'blur'>;
} | {
  type: ExternalBehaviorEventType<'focus'>;
} | {
  type: ExternalBehaviorEventType<'insert', 'block object'>;
  placement: InsertPlacement;
  blockObject: {
    name: string;
    value?: {
      [prop: string]: unknown;
    };
  };
} | SyntheticBehaviorEvent | CustomBehaviorEvent;
/**************************************
 * Synthetic events
 **************************************/
declare const syntheticBehaviorEventTypes: readonly ["annotation.add", "annotation.remove", "block.set", "block.unset", "child.set", "child.unset", "decorator.add", "decorator.remove", "delete", "history.redo", "history.undo", "insert.inline object", "insert.block", "insert.span", "insert.text", "move.backward", "move.block", "move.forward", "select"];
type SyntheticBehaviorEventType = (typeof syntheticBehaviorEventTypes)[number] | (typeof abstractBehaviorEventTypes)[number];
type SyntheticBehaviorEventNamespace = ExtractNamespace<SyntheticBehaviorEventType>;
/**
 * @beta
 */
type SyntheticBehaviorEvent = {
  type: StrictExtract<SyntheticBehaviorEventType, 'annotation.add'>;
  annotation: {
    name: string;
    value: {
      [prop: string]: unknown;
    };
  };
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'annotation.remove'>;
  annotation: {
    name: string;
  };
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'block.set'>;
  at: BlockPath;
  props: Record<string, unknown>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'block.unset'>;
  at: BlockPath;
  props: Array<string>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'child.set'>;
  at: ChildPath;
  props: {
    [prop: string]: unknown;
  };
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'child.unset'>;
  at: ChildPath;
  props: Array<string>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'decorator.add'>;
  decorator: string;
  at?: {
    anchor: BlockOffset;
    focus: BlockOffset;
  };
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'decorator.remove'>;
  decorator: string;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'delete'>;
  at: NonNullable<EditorSelection>;
  /**
   * Defaults to forward deletion.
   */
  direction?: 'backward' | 'forward';
  /**
   * Defaults to character deletion.
   */
  unit?: 'character' | 'word' | 'line' | 'block';
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'history.redo'>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'history.undo'>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'insert.inline object'>;
  inlineObject: {
    name: string;
    value?: {
      [prop: string]: unknown;
    };
  };
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'insert.block'>;
  block: BlockWithOptionalKey;
  placement: InsertPlacement;
  select?: 'start' | 'end' | 'none';
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'insert.span'>;
  text: string;
  annotations?: Array<{
    name: string;
    value: {
      [prop: string]: unknown;
    };
  }>;
  decorators?: Array<string>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'insert.text'>;
  text: string;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'move.backward'>;
  distance: number;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'move.block'>;
  at: BlockPath;
  to: BlockPath;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'move.forward'>;
  distance: number;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'select'>;
  at: EditorSelection;
} | AbstractBehaviorEvent;
/**
 * @beta
 */
type InsertPlacement = 'auto' | 'after' | 'before';
/**************************************
 * Abstract events
 **************************************/
declare const abstractBehaviorEventTypes: readonly ["annotation.set", "annotation.toggle", "decorator.toggle", "delete.backward", "delete.block", "delete.child", "delete.forward", "delete.text", "deserialize", "deserialization.success", "deserialization.failure", "insert.blocks", "insert.break", "insert.soft break", "list item.add", "list item.remove", "list item.toggle", "move.block down", "move.block up", "select.previous block", "select.next block", "serialize", "serialization.success", "serialization.failure", "split", "style.add", "style.remove", "style.toggle"];
type AbstractBehaviorEvent = {
  type: StrictExtract<SyntheticBehaviorEventType, 'annotation.set'>;
  at: AnnotationPath;
  props: Record<string, unknown>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'annotation.toggle'>;
  annotation: {
    name: string;
    value: {
      [prop: string]: unknown;
    };
  };
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'decorator.toggle'>;
  decorator: string;
  at?: {
    anchor: BlockOffset;
    focus: BlockOffset;
  };
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'delete.backward'>;
  unit: 'character' | 'word' | 'line' | 'block';
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'delete.block'>;
  at: BlockPath;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'delete.child'>;
  at: ChildPath;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'delete.forward'>;
  unit: 'character' | 'word' | 'line' | 'block';
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'delete.text'>;
  at: {
    anchor: BlockOffset;
    focus: BlockOffset;
  };
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'deserialize'>;
  originEvent: PickFromUnion<NativeBehaviorEvent, 'type', 'drag.drop' | 'clipboard.paste'> | InputBehaviorEvent;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'serialize'>;
  originEvent: PickFromUnion<NativeBehaviorEvent, 'type', 'clipboard.copy' | 'clipboard.cut' | 'drag.dragstart'>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'deserialization.success'>;
  mimeType: MIMEType;
  data: Array<PortableTextBlock>;
  originEvent: PickFromUnion<NativeBehaviorEvent, 'type', 'drag.drop' | 'clipboard.paste'> | InputBehaviorEvent;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'deserialization.failure'>;
  mimeType: MIMEType;
  reason: string;
  originEvent: PickFromUnion<NativeBehaviorEvent, 'type', 'drag.drop' | 'clipboard.paste'> | InputBehaviorEvent;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'serialization.success'>;
  mimeType: MIMEType;
  data: string;
  originEvent: PickFromUnion<NativeBehaviorEvent, 'type', 'clipboard.copy' | 'clipboard.cut' | 'drag.dragstart'>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'serialization.failure'>;
  mimeType: MIMEType;
  reason: string;
  originEvent: PickFromUnion<NativeBehaviorEvent, 'type', 'clipboard.copy' | 'clipboard.cut' | 'drag.dragstart'>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'insert.blocks'>;
  blocks: Array<BlockWithOptionalKey>;
  placement: InsertPlacement;
  select?: 'start' | 'end' | 'none';
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'insert.break'>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'insert.soft break'>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'list item.add'>;
  listItem: string;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'list item.remove'>;
  listItem: string;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'list item.toggle'>;
  listItem: string;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'move.block down'>;
  at: BlockPath;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'move.block up'>;
  at: BlockPath;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'select.previous block'>;
  select?: 'start' | 'end';
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'select.next block'>;
  select?: 'start' | 'end';
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'split'>;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'style.add'>;
  style: string;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'style.remove'>;
  style: string;
} | {
  type: StrictExtract<SyntheticBehaviorEventType, 'style.toggle'>;
  style: string;
};
/**************************************
 * Native events
 **************************************/
declare const nativeBehaviorEventTypes: readonly ["clipboard.copy", "clipboard.cut", "clipboard.paste", "drag.dragstart", "drag.drag", "drag.dragend", "drag.dragenter", "drag.dragover", "drag.dragleave", "drag.drop", "input.*", "keyboard.keydown", "keyboard.keyup", "mouse.click"];
type NativeBehaviorEventType = (typeof nativeBehaviorEventTypes)[number];
type NativeBehaviorEventNamespace = ExtractNamespace<NativeBehaviorEventType>;
/**
 * @beta
 */
type NativeBehaviorEvent = ClipboardBehaviorEvent | DragBehaviorEvent | InputBehaviorEvent | KeyboardBehaviorEvent | MouseBehaviorEvent;
type ClipboardBehaviorEvent = {
  type: StrictExtract<NativeBehaviorEventType, 'clipboard.copy'>;
  originEvent: {
    dataTransfer: DataTransfer;
  };
  position: Pick<EventPosition, 'selection'>;
} | {
  type: StrictExtract<NativeBehaviorEventType, 'clipboard.cut'>;
  originEvent: {
    dataTransfer: DataTransfer;
  };
  position: Pick<EventPosition, 'selection'>;
} | {
  type: StrictExtract<NativeBehaviorEventType, 'clipboard.paste'>;
  originEvent: {
    dataTransfer: DataTransfer;
  };
  position: Pick<EventPosition, 'selection'>;
};
type DragBehaviorEvent = {
  type: StrictExtract<NativeBehaviorEventType, 'drag.dragstart'>;
  originEvent: {
    clientX: number;
    clientY: number;
    dataTransfer: DataTransfer;
  };
  position: Pick<EventPosition, 'selection'>;
} | {
  type: StrictExtract<NativeBehaviorEventType, 'drag.drag'>;
  originEvent: {
    dataTransfer: DataTransfer;
  };
} | {
  type: StrictExtract<NativeBehaviorEventType, 'drag.dragend'>;
  originEvent: {
    dataTransfer: DataTransfer;
  };
} | {
  type: StrictExtract<NativeBehaviorEventType, 'drag.dragenter'>;
  originEvent: {
    dataTransfer: DataTransfer;
  };
  position: EventPosition;
} | {
  type: StrictExtract<NativeBehaviorEventType, 'drag.dragover'>;
  originEvent: {
    dataTransfer: DataTransfer;
  };
  dragOrigin?: Pick<EventPosition, 'selection'>;
  position: EventPosition;
} | {
  type: StrictExtract<NativeBehaviorEventType, 'drag.drop'>;
  originEvent: {
    dataTransfer: DataTransfer;
  };
  dragOrigin?: Pick<EventPosition, 'selection'>;
  position: EventPosition;
} | {
  type: StrictExtract<NativeBehaviorEventType, 'drag.dragleave'>;
  originEvent: {
    dataTransfer: DataTransfer;
  };
};
/**
 * Used to represent native InputEvents that hold a DataTransfer object.
 *
 * These can either be one of:
 *
 * - insertFromPaste
 * - insertFromPasteAsQuotation
 * - insertFromDrop
 * - insertReplacementText
 * - insertFromYank
 */
type InputBehaviorEvent = {
  type: StrictExtract<NativeBehaviorEventType, 'input.*'>;
  originEvent: {
    dataTransfer: DataTransfer;
  };
};
type KeyboardBehaviorEvent = {
  type: StrictExtract<NativeBehaviorEventType, 'keyboard.keydown'>;
  originEvent: Pick<KeyboardEvent, 'key' | 'code' | 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey'>;
} | {
  type: StrictExtract<NativeBehaviorEventType, 'keyboard.keyup'>;
  originEvent: Pick<KeyboardEvent, 'key' | 'code' | 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey'>;
};
type MouseBehaviorEvent = {
  type: StrictExtract<NativeBehaviorEventType, 'mouse.click'>;
  position: EventPosition;
};
/**************************************
 * Custom events
 **************************************/
type CustomBehaviorEventNamespace = 'custom';
type CustomBehaviorEventType<TNamespace extends CustomBehaviorEventNamespace, TType extends string = ''> = TType extends '' ? `${TNamespace}` : `${TNamespace}.${TType}`;
/**
 * @beta
 */
type CustomBehaviorEvent<TPayload extends Record<string, unknown> = Record<string, unknown>, TType extends string = string, TInternalType extends CustomBehaviorEventType<'custom', TType> = CustomBehaviorEventType<'custom', TType>> = {
  type: TInternalType;
} & TPayload;
/**************************************
 * Resolve behavior event
 **************************************/
type ResolveBehaviorEvent<TBehaviorEventType extends '*' | `${BehaviorEventTypeNamespace}.*` | BehaviorEvent['type'], TPayload extends Record<string, unknown> = Record<string, unknown>> = TBehaviorEventType extends '*' ? BehaviorEvent : TBehaviorEventType extends `${infer TNamespace}.*` ? TNamespace extends BehaviorEventTypeNamespace ? PickFromUnion<BehaviorEvent, 'type', NamespacedBehaviorEventType<TNamespace>> : never : TBehaviorEventType extends `custom.${infer TType}` ? CustomBehaviorEvent<TPayload, TType> : TBehaviorEventType extends BehaviorEvent['type'] ? PickFromUnion<BehaviorEvent, 'type', TBehaviorEventType> : never;
type ExtractNamespace<TType extends string> = TType extends `${infer Namespace}.${string}` ? Namespace : TType;
/**
 * @beta
 */
type BehaviorGuard<TBehaviorEvent, TGuardResponse> = (payload: {
  snapshot: EditorSnapshot;
  event: TBehaviorEvent;
  dom: EditorDom;
}) => TGuardResponse | false;
/**
 * @beta
 */
type Behavior<TBehaviorEventType extends '*' | `${BehaviorEventTypeNamespace}.*` | BehaviorEvent['type'] = '*' | `${BehaviorEventTypeNamespace}.*` | BehaviorEvent['type'], TGuardResponse = true, TBehaviorEvent extends ResolveBehaviorEvent<TBehaviorEventType> = ResolveBehaviorEvent<TBehaviorEventType>> = {
  /**
   * Editor Event that triggers this Behavior.
   */
  on: TBehaviorEventType;
  /**
   * Predicate function that determines if the Behavior should be executed.
   * Returning a non-nullable value from the guard will pass the value to the
   * actions and execute them.
   */
  guard?: BehaviorGuard<TBehaviorEvent, TGuardResponse>;
  /**
   * Array of Behavior Action sets.
   * Each set represents a step in the history stack.
   */
  actions: Array<BehaviorActionSet<TBehaviorEvent, TGuardResponse>>;
};
/**
 * @beta
 *
 * @example
 *
 * ```tsx
 * const noLowerCaseA = defineBehavior({
 *   on: 'insert.text',
 *   guard: ({event, snapshot}) => event.text === 'a',
 *   actions: [({event, snapshot}) => [{type: 'insert.text', text: 'A'}]],
 * })
 * ```
 *
 */
declare function defineBehavior<TPayload extends Record<string, unknown>, TBehaviorEventType extends '*' | `${BehaviorEventTypeNamespace}.*` | BehaviorEvent['type'] = CustomBehaviorEvent['type'], TGuardResponse = true>(behavior: Behavior<TBehaviorEventType, TGuardResponse, ResolveBehaviorEvent<TBehaviorEventType, TPayload>>): Behavior;
/**
 * @public
 */
type EditorConfig = {
  /**
   * @beta
   */
  keyGenerator?: () => string;
  /**
   * @deprecated Will be removed in the next major version
   */
  maxBlocks?: number;
  readOnly?: boolean;
  initialValue?: Array<PortableTextBlock>;
} & ({
  schemaDefinition: SchemaDefinition;
  schema?: undefined;
} | {
  schemaDefinition?: undefined;
  schema: ArraySchemaType<PortableTextBlock> | ArrayDefinition;
});
/**
 * @public
 */
type EditorEvent = ExternalEditorEvent | ExternalBehaviorEvent | {
  type: 'update value';
  value: Array<PortableTextBlock> | undefined;
};
/**
 * @public
 */
type Editor = {
  dom: EditorDom;
  getSnapshot: () => EditorSnapshot;
  /**
   * @beta
   */
  registerBehavior: (config: {
    behavior: Behavior;
  }) => () => void;
  send: (event: EditorEvent) => void;
  on: ActorRef<Snapshot<unknown>, EventObject, EditorEmittedEvent>['on'];
};
/**
 * @public
 * @deprecated
 * This component has been renamed. Use `EventListenerPlugin` instead.
 *
 * ```
 * import {EventListenerPlugin} from '@portabletext/editor/plugins'
 * ```
 */
declare function EditorEventListener(props: {
  on: (event: EditorEmittedEvent) => void;
}): null;
/**
 * @public
 */
type EditorProviderProps = {
  initialConfig: EditorConfig;
  children?: React$1.ReactNode;
};
/**
 * @public
 * The EditorProvider component is used to set up the editor context and configure the Portable Text Editor.
 * @example
 * ```tsx
 * import {EditorProvider} from '@portabletext/editor'
 *
 * function App() {
 *  return (
 *    <EditorProvider initialConfig={{ ... }} >
 *      ...
 *    </EditorProvider>
 *  )
 * }
 *
 * ```
 * @group Components
 */
declare function EditorProvider(props: EditorProviderProps): React$1.JSX.Element;
/**
 * @public
 */
type EditorSelector<TSelected> = (snapshot: EditorSnapshot) => TSelected;
/**
 * @public
 * Hook to select a value from the editor state.
 * @example
 * Pass a selector as the second argument
 * ```tsx
 * import { useEditorSelector } from '@portabletext/editor'
 *
 * function MyComponent(editor) {
 *  const value = useEditorSelector(editor, selector)
 * }
 * ```
 * @example
 * Pass an inline selector as the second argument.
 * In this case, use the editor context to obtain the schema.
 * ```tsx
 * import { useEditorSelector } from '@portabletext/editor'
 *
 * function MyComponent(editor) {
 *  const schema = useEditorSelector(editor, (snapshot) => snapshot.context.schema)
 * }
 * ```
 * @group Hooks
 */
declare function useEditorSelector<TSelected>(editor: Editor, selector: EditorSelector<TSelected>, compare?: (a: TSelected, b: TSelected) => boolean): TSelected;
/**
 * @deprecated Use `useEditor` to get the current editor instance.
 * @public
 * Get the current editor object from the React context.
 */
declare const usePortableTextEditor: () => PortableTextEditor;
/**
 * @deprecated Use `useEditorSelector` to get the current editor selection.
 * @public
 * Get the current editor selection from the React context.
 */
declare const usePortableTextEditorSelection: () => EditorSelection;
/**
 * @public
 */
declare const defaultKeyGenerator: () => string;
/**
 * @public
 * Get the current editor context from the `EditorProvider`.
 * Must be used inside the `EditorProvider` component.
 * @returns The current editor object.
 * @example
 * ```tsx
 * import { useEditor } from '@portabletext/editor'
 *
 * function MyComponent() {
 *  const editor = useEditor()
 * }
 * ```
 * @group Hooks
 */
declare function useEditor(): Editor;
type EditorDom = {
  getBlockNodes: (snapshot: EditorSnapshot) => Array<Node>;
  getChildNodes: (snapshot: EditorSnapshot) => Array<Node>;
  /**
   * Let the Editor set the drag ghost. This is to be sure that it will get
   * properly removed again when the drag ends.
   */
  setDragGhost: ({
    event,
    ghost
  }: {
    event: PickFromUnion<BehaviorEvent, 'type', 'drag.dragstart'>;
    ghost: {
      element: HTMLElement;
      x: number;
      y: number;
    };
  }) => void;
};
/**
 * @beta
 */
type BehaviorAction = {
  type: 'execute';
  event: SyntheticBehaviorEvent;
} | {
  type: 'forward';
  event: NativeBehaviorEvent | SyntheticBehaviorEvent | CustomBehaviorEvent;
} | {
  type: 'raise';
  event: SyntheticBehaviorEvent | CustomBehaviorEvent;
} | {
  type: 'effect';
  effect: () => void;
};
/**
 * @beta
 */
declare function execute(event: SyntheticBehaviorEvent): PickFromUnion<BehaviorAction, 'type', 'execute'>;
/**
 * @beta
 */
declare function forward(event: NativeBehaviorEvent | SyntheticBehaviorEvent | CustomBehaviorEvent): PickFromUnion<BehaviorAction, 'type', 'forward'>;
/**
 * @beta
 */
declare function raise(event: SyntheticBehaviorEvent | CustomBehaviorEvent): PickFromUnion<BehaviorAction, 'type', 'raise'>;
/**
 * @beta
 */
declare function effect(effect: () => void): PickFromUnion<BehaviorAction, 'type', 'effect'>;
/**
 * @beta
 */
type BehaviorActionSet<TBehaviorEvent, TGuardResponse> = (payload: {
  snapshot: EditorSnapshot;
  event: TBehaviorEvent;
  dom: EditorDom;
}, guardResponse: TGuardResponse) => Array<BehaviorAction>;
export { type AddedAnnotationPaths, type AnnotationDefinition, type AnnotationPath, type AnnotationSchemaType, type BaseDefinition, Behavior, BehaviorAction, BehaviorActionSet, BehaviorEvent, BehaviorGuard, type BlockAnnotationRenderProps, type BlockChildRenderProps, type BlockDecoratorRenderProps, type BlockListItemRenderProps, type BlockObjectDefinition, type BlockObjectSchemaType, type BlockOffset, type BlockPath, type BlockRenderProps, type BlockStyleRenderProps, type BlurChange, type ChildPath, type ConnectionChange, CustomBehaviorEvent, type DecoratorDefinition, type DecoratorSchemaType, type EditableAPI, type EditableAPIDeleteOptions, type Editor, type EditorChange, type EditorChanges, type EditorConfig, type EditorContext, type EditorEmittedEvent, type EditorEvent, EditorEventListener, EditorProvider, type EditorProviderProps, type EditorSchema, type EditorSelection, type EditorSelectionPoint, type EditorSelector, type EditorSnapshot, type ErrorChange, type FieldDefinition, type FocusChange, type HotkeyOptions, type InlineObjectDefinition, type InlineObjectSchemaType, InsertPlacement, type InvalidValue, type InvalidValueResolution, type ListDefinition, type ListSchemaType, type LoadingChange, type MutationChange, type MutationEvent, NativeBehaviorEvent, type OnBeforeInputFn, type OnCopyFn, type OnPasteFn, type OnPasteResult, type OnPasteResultOrPromise, type PasteData, type Patch$1 as Patch, type PatchChange, type PatchObservable, type PatchesEvent, type PortableTextBlock$1 as PortableTextBlock, type PortableTextChild$1 as PortableTextChild, PortableTextEditable, type PortableTextEditableProps, PortableTextEditor, type PortableTextEditorProps, type PortableTextMemberSchemaTypes, type PortableTextObject$1 as PortableTextObject, type PortableTextSpan$1 as PortableTextSpan, type PortableTextTextBlock$1 as PortableTextTextBlock, type RangeDecoration, type RangeDecorationOnMovedDetails, type ReadyChange, type RedoChange, type RenderAnnotationFunction, type RenderBlockFunction, type RenderChildFunction, type RenderDecoratorFunction, type RenderEditableFunction, type RenderListItemFunction, type RenderPlaceholderFunction, type RenderStyleFunction, type SchemaDefinition$1 as SchemaDefinition, type ScrollSelectionIntoViewFunction, type SelectionChange, type StyleDefinition, type StyleSchemaType, SyntheticBehaviorEvent, type UndoChange, type UnsetChange, type ValueChange, defaultKeyGenerator, defineBehavior, defineSchema, effect, execute, forward, raise, useEditor, useEditorSelector, usePortableTextEditor, usePortableTextEditorSelection };