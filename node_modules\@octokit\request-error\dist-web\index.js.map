{"version": 3, "sources": ["../dist-src/index.js"], "sourcesContent": ["import { Deprecation } from \"deprecation\";\nimport once from \"once\";\nconst logOnceCode = once((deprecation) => console.warn(deprecation));\nconst logOnceHeaders = once((deprecation) => console.warn(deprecation));\nclass RequestError extends Error {\n  constructor(message, statusCode, options) {\n    super(message);\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n    this.name = \"HttpError\";\n    this.status = statusCode;\n    let headers;\n    if (\"headers\" in options && typeof options.headers !== \"undefined\") {\n      headers = options.headers;\n    }\n    if (\"response\" in options) {\n      this.response = options.response;\n      headers = options.response.headers;\n    }\n    const requestCopy = Object.assign({}, options.request);\n    if (options.request.headers.authorization) {\n      requestCopy.headers = Object.assign({}, options.request.headers, {\n        authorization: options.request.headers.authorization.replace(\n          /(?<! ) .*$/,\n          \" [REDACTED]\"\n        )\n      });\n    }\n    requestCopy.url = requestCopy.url.replace(/\\bclient_secret=\\w+/g, \"client_secret=[REDACTED]\").replace(/\\baccess_token=\\w+/g, \"access_token=[REDACTED]\");\n    this.request = requestCopy;\n    Object.defineProperty(this, \"code\", {\n      get() {\n        logOnceCode(\n          new Deprecation(\n            \"[@octokit/request-error] `error.code` is deprecated, use `error.status`.\"\n          )\n        );\n        return statusCode;\n      }\n    });\n    Object.defineProperty(this, \"headers\", {\n      get() {\n        logOnceHeaders(\n          new Deprecation(\n            \"[@octokit/request-error] `error.headers` is deprecated, use `error.response.headers`.\"\n          )\n        );\n        return headers || {};\n      }\n    });\n  }\n}\nexport {\n  RequestError\n};\n"], "mappings": ";AAAA,SAAS,mBAAmB;AAC5B,OAAO,UAAU;AACjB,IAAM,cAAc,KAAK,CAAC,gBAAgB,QAAQ,KAAK,WAAW,CAAC;AACnE,IAAM,iBAAiB,KAAK,CAAC,gBAAgB,QAAQ,KAAK,WAAW,CAAC;AACtE,IAAM,eAAN,cAA2B,MAAM;AAAA,EAC/B,YAAY,SAAS,YAAY,SAAS;AACxC,UAAM,OAAO;AACb,QAAI,MAAM,mBAAmB;AAC3B,YAAM,kBAAkB,MAAM,KAAK,WAAW;AAAA,IAChD;AACA,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,QAAI;AACJ,QAAI,aAAa,WAAW,OAAO,QAAQ,YAAY,aAAa;AAClE,gBAAU,QAAQ;AAAA,IACpB;AACA,QAAI,cAAc,SAAS;AACzB,WAAK,WAAW,QAAQ;AACxB,gBAAU,QAAQ,SAAS;AAAA,IAC7B;AACA,UAAM,cAAc,OAAO,OAAO,CAAC,GAAG,QAAQ,OAAO;AACrD,QAAI,QAAQ,QAAQ,QAAQ,eAAe;AACzC,kBAAY,UAAU,OAAO,OAAO,CAAC,GAAG,QAAQ,QAAQ,SAAS;AAAA,QAC/D,eAAe,QAAQ,QAAQ,QAAQ,cAAc;AAAA,UACnD;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,gBAAY,MAAM,YAAY,IAAI,QAAQ,wBAAwB,0BAA0B,EAAE,QAAQ,uBAAuB,yBAAyB;AACtJ,SAAK,UAAU;AACf,WAAO,eAAe,MAAM,QAAQ;AAAA,MAClC,MAAM;AACJ;AAAA,UACE,IAAI;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,WAAO,eAAe,MAAM,WAAW;AAAA,MACrC,MAAM;AACJ;AAAA,UACE,IAAI;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO,WAAW,CAAC;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH;AACF;", "names": []}