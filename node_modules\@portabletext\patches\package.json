{"name": "@portabletext/patches", "version": "1.1.6", "description": "Portable Text Patches", "keywords": ["portable-text", "portable-text-editor", "patches"], "homepage": "https://www.sanity.io/", "bugs": {"url": "https://github.com/portabletext/editor/issues"}, "repository": {"type": "git", "url": "git+https://github.com/portabletext/editor.git", "directory": "packages/patches"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"source": "./src/index.ts", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "src"], "dependencies": {"@sanity/diff-match-patch": "^3.2.0", "lodash": "^4.17.21"}, "devDependencies": {"@sanity/pkg-utils": "^7.9.6", "@types/lodash": "^4.17.16", "typescript": "5.8.3", "vitest": "^3.2.4"}, "scripts": {"build": "pkg-utils build --strict --check --clean", "check:lint": "biome lint .", "check:types": "tsc --noEmit --pretty", "check:types:watch": "tsc --noEmit --pretty --watch", "clean": "del .turbo && del dist && del node_modules", "dev": "pkg-utils watch", "lint:fix": "biome lint --write .", "test": "vitest --run", "test:unit": "vitest --run --project unit", "test:unit:watch": "vitest --project unit", "test:watch": "vitest"}}