{"name": "@portabletext/sanity-bridge", "version": "1.1.2", "description": "Convert a Sanity Schema to a Portable Text Schema", "keywords": ["sanity", "portable-text"], "homepage": "https://www.portabletext.org/", "bugs": {"url": "https://github.com/portabletext/editor/issues"}, "repository": {"type": "git", "url": "git+https://github.com/portabletext/editor.git", "directory": "packages/sanity-bridge"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"source": "./src/index.ts", "import": "./dist/index.js", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "src"], "dependencies": {"get-random-values-esm": "^1.0.2", "lodash.startcase": "^4.4.0", "@portabletext/schema": "^1.0.0"}, "devDependencies": {"@sanity/pkg-utils": "^7.11.1", "@sanity/schema": "^4.4.1", "@sanity/types": "^4.4.1", "@types/lodash.startcase": "^4.4.9", "typescript": "^5.9.2"}, "peerDependencies": {"@sanity/schema": "^4.4.1", "@sanity/types": "^4.4.1"}, "engines": {"node": ">=20.19 <22 || >=22.12"}, "publishConfig": {"access": "public"}, "scripts": {"build": "pkg-utils build --strict --check --clean", "check:lint": "biome lint .", "check:types": "tsc", "check:types:watch": "tsc --watch", "clean": "del .turbo && del lib && del node_modules", "dev": "pkg-utils watch", "lint:fix": "biome lint --write ."}}