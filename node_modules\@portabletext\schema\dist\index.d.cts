/**
 * @public
 */
type Schema = {
  block: {
    name: string;
  };
  span: {
    name: string;
  };
  styles: ReadonlyArray<StyleSchemaType>;
  lists: ReadonlyArray<ListSchemaType>;
  decorators: ReadonlyArray<DecoratorSchemaType>;
  annotations: ReadonlyArray<AnnotationSchemaType>;
  blockObjects: ReadonlyArray<BlockObjectSchemaType>;
  inlineObjects: ReadonlyArray<InlineObjectSchemaType>;
};
/**
 * @public
 */
type StyleSchemaType = BaseDefinition & {
  /**
   * @deprecated
   * Use `name` instead
   */
  value: string;
};
/**
 * @public
 */
type ListSchemaType = BaseDefinition & {
  /**
   * @deprecated
   * Use `name` instead
   */
  value: string;
};
/**
 * @public
 */
type DecoratorSchemaType = BaseDefinition & {
  /**
   * @deprecated
   * Use `name` instead
   */
  value: string;
};
/**
 * @public
 */
type AnnotationSchemaType = BaseDefinition & {
  fields: ReadonlyArray<FieldDefinition>;
};
/**
 * @public
 */
type BlockObjectSchemaType = BaseDefinition & {
  fields: ReadonlyArray<FieldDefinition>;
};
/**
 * @public
 */
type InlineObjectSchemaType = BaseDefinition & {
  fields: ReadonlyArray<FieldDefinition>;
};
/**
 * @public
 */
type FieldDefinition = BaseDefinition & {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
};
/**
 * @public
 */
type BaseDefinition = {
  name: string;
  title?: string;
};
/**
 * @public
 */
type SchemaDefinition = {
  block?: {
    name: string;
  };
  styles?: ReadonlyArray<StyleDefinition>;
  lists?: ReadonlyArray<ListDefinition>;
  decorators?: ReadonlyArray<DecoratorDefinition>;
  annotations?: ReadonlyArray<AnnotationDefinition>;
  blockObjects?: ReadonlyArray<BlockObjectDefinition>;
  inlineObjects?: ReadonlyArray<InlineObjectDefinition>;
};
/**
 * @public
 * A helper wrapper that adds editor support, such as autocomplete and type checking, for a schema definition.
 * @example
 * ```ts
 * import { defineSchema } from '@portabletext/editor'
 *
 * const schemaDefinition = defineSchema({
 *  decorators: [{name: 'strong'}, {name: 'em'}, {name: 'underline'}],
 *  annotations: [{name: 'link'}],
 *  styles: [
 *    {name: 'normal'},
 *    {name: 'h1'},
 *    {name: 'h2'},
 *    {name: 'h3'},
 *    {name: 'blockquote'},
 *  ],
 *  lists: [],
 *  inlineObjects: [],
 *  blockObjects: [],
 * }
 * ```
 */
declare function defineSchema<const TSchemaDefinition extends SchemaDefinition>(definition: TSchemaDefinition): TSchemaDefinition;
/**
 * @public
 */
type StyleDefinition<TBaseDefinition extends BaseDefinition = BaseDefinition> = TBaseDefinition;
/**
 * @public
 */
type ListDefinition<TBaseDefinition extends BaseDefinition = BaseDefinition> = TBaseDefinition;
/**
 * @public
 */
type DecoratorDefinition<TBaseDefinition extends BaseDefinition = BaseDefinition> = TBaseDefinition;
/**
 * @public
 */
type AnnotationDefinition<TBaseDefinition extends BaseDefinition = BaseDefinition> = TBaseDefinition & {
  fields?: ReadonlyArray<FieldDefinition>;
};
/**
 * @public
 */
type BlockObjectDefinition<TBaseDefinition extends BaseDefinition = BaseDefinition> = TBaseDefinition & {
  fields?: ReadonlyArray<FieldDefinition>;
};
/**
 * @public
 */
type InlineObjectDefinition<TBaseDefinition extends BaseDefinition = BaseDefinition> = TBaseDefinition & {
  fields?: ReadonlyArray<FieldDefinition>;
};
/**
 * @public
 */
declare function compileSchema(definition: SchemaDefinition): Schema;
export { type AnnotationDefinition, type AnnotationSchemaType, type BaseDefinition, type BlockObjectDefinition, type BlockObjectSchemaType, type DecoratorDefinition, type DecoratorSchemaType, type FieldDefinition, type InlineObjectDefinition, type InlineObjectSchemaType, type ListDefinition, type ListSchemaType, type Schema, type SchemaDefinition, type StyleDefinition, type StyleSchemaType, compileSchema, defineSchema };