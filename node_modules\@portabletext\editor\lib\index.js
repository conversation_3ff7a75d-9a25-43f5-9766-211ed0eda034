import { c } from "react-compiler-runtime";
import React, { useEffect, createContext, useContext, useState, useRef, Component, useMemo, startTransition, useCallback, forwardRef, useImperativeHandle } from "react";
import { useEditor, EditorContext } from "./_chunks-es/use-editor.js";
import { jsx, jsxs, Fragment } from "react/jsx-runtime";
import { useSelector, useActorRef } from "@xstate/react";
import noop from "lodash/noop.js";
import { Element as Element$1, Text, Range, Editor, Node, Transforms, Path, Operation, deleteText, setSelection, Point, createEditor } from "slate";
import { useSelected, useSlateSelector, useSlateStatic, withReact, ReactEditor, Slate, useSlate, Editable } from "slate-react";
import debug$f from "debug";
import { DOMEditor, isDOMNode, EDITOR_TO_PENDING_SELECTION } from "slate-dom";
import { getBlockStartPoint, getBlockKeyFromSelectionPoint, isTextBlock, getChildKeyFromSelectionPoint, blockOffsetToSpanSelectionPoint, isSpan, parseBlock, parseAnnotation, parseInlineObject, isKeyedSegment, isListBlock, isTypedObject, getSelectionStartPoint, getSelectionEndPoint, getTextBlockText, parseBlocks } from "./_chunks-es/util.slice-blocks.js";
import { getBlockEndPoint, isSelectionCollapsed, isEqualSelectionPoints, isEmptyTextBlock } from "./_chunks-es/util.is-selection-collapsed.js";
import isEqual from "lodash/isEqual.js";
import { isSelectionCollapsed as isSelectionCollapsed$1, getFocusTextBlock, getFocusSpan as getFocusSpan$1, isSelectionExpanded, getFocusBlock as getFocusBlock$1, getSelectedValue, getSelectionStartPoint as getSelectionStartPoint$1, getFocusChild as getFocusChild$1 } from "./_chunks-es/selector.is-selection-expanded.js";
import { getFocusInlineObject, getSelectedBlocks, getSelectionStartBlock as getSelectionStartBlock$1, getSelectionEndBlock as getSelectionEndBlock$1, isOverlappingSelection, isSelectingEntireBlocks, getMarkState, getActiveDecorators, getActiveAnnotationsMarks, getTrimmedSelection, getCaretWordSelection, getFocusBlockObject, getPreviousBlock, getNextBlock, isAtTheEndOfBlock, isAtTheStartOfBlock, getFirstBlock as getFirstBlock$1, getLastBlock as getLastBlock$1, getFocusListBlock, getSelectionEndPoint as getSelectionEndPoint$1, isActiveAnnotation, isActiveDecorator, getSelectedTextBlocks, isActiveListItem, isActiveStyle, getActiveAnnotations } from "./_chunks-es/selector.is-selecting-entire-blocks.js";
import getRandomValues from "get-random-values-esm";
import { defineBehavior, forward, raise, effect } from "./behaviors/index.js";
import uniq from "lodash/uniq.js";
import { Subject } from "rxjs";
import { compileSchemaDefinitionToPortableTextMemberSchemaTypes, createPortableTextMemberSchemaTypes, portableTextMemberSchemaTypesToSchema } from "@portabletext/sanity-bridge";
import { compileSchema } from "@portabletext/schema";
import { defineSchema } from "@portabletext/schema";
import { setup, assign, enqueueActions, emit, assertEvent, stateIn, fromCallback, and, not, raise as raise$1, createActor } from "xstate";
import { htmlToBlocks } from "@portabletext/block-tools";
import { toHTML } from "@portabletext/to-html";
import { Schema } from "@sanity/schema";
import flatten from "lodash/flatten.js";
import omit from "lodash/omit.js";
import { applyAll, unset, insert, set, setIfMissing, diffMatchPatch as diffMatchPatch$1 } from "@portabletext/patches";
import { blockOffsetsToSelection } from "./_chunks-es/util.child-selection-point-to-block-offset.js";
import { selectionPointToBlockOffset, sliceTextBlock } from "./_chunks-es/util.slice-text-block.js";
import get from "lodash/get.js";
import isUndefined from "lodash/isUndefined.js";
import omitBy from "lodash/omitBy.js";
import { createDraft, finishDraft } from "immer";
import { createKeyboardShortcut, code, underline, italic, bold, undo, redo } from "@portabletext/keyboard-shortcuts";
import isPlainObject from "lodash/isPlainObject.js";
function EditorEventListener(props) {
  const $ = c(4), editor = useEditor();
  let t0, t1;
  return $[0] !== editor || $[1] !== props.on ? (t0 = () => {
    const subscription = editor.on("*", props.on);
    return () => {
      subscription.unsubscribe();
    };
  }, t1 = [editor, props.on], $[0] = editor, $[1] = props.on, $[2] = t0, $[3] = t1) : (t0 = $[2], t1 = $[3]), useEffect(t0, t1), null;
}
const rootName = "sanity-pte:";
debug$f(rootName);
function debugWithName(name) {
  const namespace = `${rootName}${name}`;
  return debug$f && debug$f.enabled(namespace) ? debug$f(namespace) : debug$f(rootName);
}
const VOID_CHILD_KEY = "void-child";
function keepObjectEquality(object, keyMap) {
  const value = keyMap[object._key];
  return value && isEqual(object, value) ? value : (keyMap[object._key] = object, object);
}
function toSlateValue(value, {
  schemaTypes
}, keyMap = {}) {
  return value && Array.isArray(value) ? value.map((block) => {
    const {
      _type,
      _key,
      ...rest
    } = block;
    if (block && block._type === schemaTypes.block.name) {
      const textBlock = block;
      let hasInlines = !1;
      const hasMissingStyle = typeof textBlock.style > "u", hasMissingMarkDefs = typeof textBlock.markDefs > "u", hasMissingChildren = typeof textBlock.children > "u", children = (textBlock.children || []).map((child) => {
        const {
          _type: cType,
          _key: cKey,
          ...cRest
        } = child;
        return cType !== "span" ? (hasInlines = !0, keepObjectEquality({
          _type: cType,
          _key: cKey,
          children: [{
            _key: VOID_CHILD_KEY,
            _type: "span",
            text: "",
            marks: []
          }],
          value: cRest,
          __inline: !0
        }, keyMap)) : child;
      });
      return !hasMissingStyle && !hasMissingMarkDefs && !hasMissingChildren && !hasInlines && Element$1.isElement(block) ? block : (hasMissingStyle && (rest.style = schemaTypes.styles[0].name), keepObjectEquality({
        _type,
        _key,
        ...rest,
        children
      }, keyMap));
    }
    return keepObjectEquality({
      _type,
      _key,
      children: [{
        _key: VOID_CHILD_KEY,
        _type: "span",
        text: "",
        marks: []
      }],
      value: rest
    }, keyMap);
  }) : [];
}
function fromSlateValue(value, textBlockType, keyMap = {}) {
  return value.map((block) => {
    const {
      _key,
      _type
    } = block;
    if (!_key || !_type)
      throw new Error("Not a valid block");
    if (_type === textBlockType && "children" in block && Array.isArray(block.children) && _key) {
      let hasInlines = !1;
      const children = block.children.map((child) => {
        const {
          _type: _cType
        } = child;
        if ("value" in child && _cType !== "span") {
          hasInlines = !0;
          const {
            value: v,
            _key: k,
            _type: t,
            __inline: _i,
            children: _c,
            ...rest
          } = child;
          return keepObjectEquality({
            ...rest,
            ...v,
            _key: k,
            _type: t
          }, keyMap);
        }
        return child;
      });
      return hasInlines ? keepObjectEquality({
        ...block,
        children,
        _key,
        _type
      }, keyMap) : block;
    }
    const blockValue = "value" in block && block.value;
    return keepObjectEquality({
      _key,
      _type,
      ...typeof blockValue == "object" ? blockValue : {}
    }, keyMap);
  });
}
function isEqualToEmptyEditor(children, schemaTypes) {
  return children === void 0 || children && Array.isArray(children) && children.length === 0 || children && Array.isArray(children) && children.length === 1 && Element$1.isElement(children[0]) && children[0]._type === schemaTypes.block.name && "style" in children[0] && children[0].style === schemaTypes.styles[0].name && !("listItem" in children[0]) && Array.isArray(children[0].children) && children[0].children.length === 1 && Text.isText(children[0].children[0]) && children[0].children[0]._type === "span" && !children[0].children[0].marks?.join("") && children[0].children[0].text === "";
}
function getBlockPath({
  editor,
  _key
}) {
  const [, blockPath] = Array.from(Editor.nodes(editor, {
    at: [],
    match: (n) => n._key === _key
  })).at(0) ?? [void 0, void 0], blockIndex = blockPath?.at(0);
  if (blockIndex !== void 0)
    return [blockIndex];
}
function getFocusBlock({
  editor
}) {
  if (!editor.selection)
    return [void 0, void 0];
  try {
    return Editor.node(editor, editor.selection.focus.path.slice(0, 1)) ?? [void 0, void 0];
  } catch {
    return [void 0, void 0];
  }
}
function getFocusSpan({
  editor
}) {
  if (!editor.selection)
    return [void 0, void 0];
  try {
    const [node, path] = Editor.node(editor, editor.selection.focus.path);
    if (editor.isTextSpan(node))
      return [node, path];
  } catch {
    return [void 0, void 0];
  }
  return [void 0, void 0];
}
function getSelectionStartBlock({
  editor
}) {
  if (!editor.selection)
    return [void 0, void 0];
  const selectionStartPoint = Range.start(editor.selection);
  return getPointBlock({
    editor,
    point: selectionStartPoint
  });
}
function getSelectionEndBlock({
  editor
}) {
  if (!editor.selection)
    return [void 0, void 0];
  const selectionEndPoint = Range.end(editor.selection);
  return getPointBlock({
    editor,
    point: selectionEndPoint
  });
}
function getPointBlock({
  editor,
  point
}) {
  try {
    const [block] = Editor.node(editor, point.path.slice(0, 1)) ?? [void 0, void 0];
    return block ? [block, point.path.slice(0, 1)] : [void 0, void 0];
  } catch {
    return [void 0, void 0];
  }
}
function getFocusChild({
  editor
}) {
  const [focusBlock, focusBlockPath] = getFocusBlock({
    editor
  }), childIndex = editor.selection?.focus.path.at(1);
  if (!focusBlock || !focusBlockPath || childIndex === void 0)
    return [void 0, void 0];
  try {
    const focusChild = Node.child(focusBlock, childIndex);
    return focusChild ? [focusChild, [...focusBlockPath, childIndex]] : [void 0, void 0];
  } catch {
    return [void 0, void 0];
  }
}
function getPointChild({
  editor,
  point
}) {
  const [block, blockPath] = getPointBlock({
    editor,
    point
  }), childIndex = point.path.at(1);
  if (!block || !blockPath || childIndex === void 0)
    return [void 0, void 0];
  try {
    const pointChild = Node.child(block, childIndex);
    return pointChild ? [pointChild, [...blockPath, childIndex]] : [void 0, void 0];
  } catch {
    return [void 0, void 0];
  }
}
function getFirstBlock({
  editor
}) {
  if (editor.children.length === 0)
    return [void 0, void 0];
  const firstBlockPath = Editor.start(editor, []).path.at(0);
  try {
    return firstBlockPath !== void 0 ? Editor.node(editor, [firstBlockPath]) ?? [void 0, void 0] : [void 0, void 0];
  } catch {
    return [void 0, void 0];
  }
}
function getLastBlock({
  editor
}) {
  if (editor.children.length === 0)
    return [void 0, void 0];
  const lastBlockPath = Editor.end(editor, []).path.at(0);
  try {
    return lastBlockPath !== void 0 ? Editor.node(editor, [lastBlockPath]) ?? [void 0, void 0] : [void 0, void 0];
  } catch {
    return [void 0, void 0];
  }
}
function getNodeBlock({
  editor,
  schema,
  node
}) {
  if (Editor.isEditor(node))
    return;
  if (isBlockElement({
    editor,
    schema
  }, node))
    return elementToBlock({
      schema,
      element: node
    });
  const parent = Array.from(Editor.nodes(editor, {
    mode: "highest",
    at: [],
    match: (n) => isBlockElement({
      editor,
      schema
    }, n) && n.children.some((child) => child._key === node._key)
  })).at(0)?.at(0);
  return Element$1.isElement(parent) ? elementToBlock({
    schema,
    element: parent
  }) : void 0;
}
function elementToBlock({
  schema,
  element
}) {
  return fromSlateValue([element], schema.block.name)?.at(0);
}
function isBlockElement({
  editor,
  schema
}, node) {
  return Element$1.isElement(node) && !editor.isInline(node) && (schema.block.name === node._type || schema.blockObjects.some((blockObject) => blockObject.name === node._type));
}
function isListItemActive({
  editor,
  listItem
}) {
  if (!editor.selection)
    return !1;
  const selectedBlocks = [...Editor.nodes(editor, {
    at: editor.selection,
    match: (node) => editor.isTextBlock(node)
  })];
  return selectedBlocks.length > 0 ? selectedBlocks.every(([node]) => editor.isListBlock(node) && node.listItem === listItem) : !1;
}
function isStyleActive({
  editor,
  style
}) {
  if (!editor.selection)
    return !1;
  const selectedBlocks = [...Editor.nodes(editor, {
    at: editor.selection,
    match: (node) => editor.isTextBlock(node)
  })];
  return selectedBlocks.length > 0 ? selectedBlocks.every(([node]) => node.style === style) : !1;
}
function slateRangeToSelection({
  schema,
  editor,
  range
}) {
  const [anchorBlock] = getPointBlock({
    editor,
    point: range.anchor
  }), [focusBlock] = getPointBlock({
    editor,
    point: range.focus
  });
  if (!anchorBlock || !focusBlock)
    return null;
  const [anchorChild] = anchorBlock._type === schema.block.name ? getPointChild({
    editor,
    point: range.anchor
  }) : [void 0, void 0], [focusChild] = focusBlock._type === schema.block.name ? getPointChild({
    editor,
    point: range.focus
  }) : [void 0, void 0], selection = {
    anchor: {
      path: [{
        _key: anchorBlock._key
      }],
      offset: range.anchor.offset
    },
    focus: {
      path: [{
        _key: focusBlock._key
      }],
      offset: range.focus.offset
    },
    backward: Range.isBackward(range)
  };
  return anchorChild && (selection.anchor.path.push("children"), selection.anchor.path.push({
    _key: anchorChild._key
  })), focusChild && (selection.focus.path.push("children"), selection.focus.path.push({
    _key: focusChild._key
  })), selection;
}
function getEventPosition({
  editorActor,
  slateEditor,
  event
}) {
  if (editorActor.getSnapshot().matches({
    setup: "setting up"
  }))
    return;
  const eventNode = getEventNode({
    slateEditor,
    event
  });
  if (!eventNode)
    return;
  const eventBlock = getNodeBlock({
    editor: slateEditor,
    schema: editorActor.getSnapshot().context.schema,
    node: eventNode
  }), eventPositionBlock = getEventPositionBlock({
    node: eventNode,
    slateEditor,
    event
  }), eventSelection = getEventSelection({
    schema: editorActor.getSnapshot().context.schema,
    slateEditor,
    event
  });
  if (eventBlock && eventPositionBlock && !eventSelection && !Editor.isEditor(eventNode))
    return {
      block: eventPositionBlock,
      isEditor: !1,
      selection: {
        anchor: getBlockStartPoint({
          context: editorActor.getSnapshot().context,
          block: {
            node: eventBlock,
            path: [{
              _key: eventBlock._key
            }]
          }
        }),
        focus: getBlockEndPoint({
          context: editorActor.getSnapshot().context,
          block: {
            node: eventBlock,
            path: [{
              _key: eventBlock._key
            }]
          }
        })
      }
    };
  if (!eventPositionBlock || !eventSelection)
    return;
  const eventSelectionFocusBlockKey = getBlockKeyFromSelectionPoint(eventSelection.focus);
  if (eventSelectionFocusBlockKey !== void 0)
    return isSelectionCollapsed(eventSelection) && eventBlock && eventSelectionFocusBlockKey !== eventBlock._key ? {
      block: eventPositionBlock,
      isEditor: !1,
      selection: {
        anchor: getBlockStartPoint({
          context: editorActor.getSnapshot().context,
          block: {
            node: eventBlock,
            path: [{
              _key: eventBlock._key
            }]
          }
        }),
        focus: getBlockEndPoint({
          context: editorActor.getSnapshot().context,
          block: {
            node: eventBlock,
            path: [{
              _key: eventBlock._key
            }]
          }
        })
      }
    } : {
      block: eventPositionBlock,
      isEditor: Editor.isEditor(eventNode),
      selection: eventSelection
    };
}
function getEventNode({
  slateEditor,
  event
}) {
  return DOMEditor.hasTarget(slateEditor, event.target) ? DOMEditor.toSlateNode(slateEditor, event.target) : void 0;
}
function getEventPositionBlock({
  node,
  slateEditor,
  event
}) {
  const [firstBlock] = getFirstBlock({
    editor: slateEditor
  });
  if (!firstBlock)
    return;
  const firstBlockRect = DOMEditor.toDOMNode(slateEditor, firstBlock).getBoundingClientRect();
  if (event.pageY < firstBlockRect.top)
    return "start";
  const [lastBlock] = getLastBlock({
    editor: slateEditor
  });
  if (!lastBlock)
    return;
  const lastBlockRef = DOMEditor.toDOMNode(slateEditor, lastBlock).getBoundingClientRect();
  if (event.pageY > lastBlockRef.bottom)
    return "end";
  const elementRect = DOMEditor.toDOMNode(slateEditor, node).getBoundingClientRect(), top = elementRect.top, height = elementRect.height;
  return Math.abs(top - event.pageY) < height / 2 ? "start" : "end";
}
function getEventSelection({
  schema,
  slateEditor,
  event
}) {
  const range = getSlateRangeFromEvent(slateEditor, event);
  return range ? slateRangeToSelection({
    schema,
    editor: slateEditor,
    range
  }) : null;
}
function getSlateRangeFromEvent(editor, event) {
  if (!event.target || !isDOMNode(event.target))
    return;
  const window2 = DOMEditor.getWindow(editor);
  let domRange;
  if (window2.document.caretPositionFromPoint !== void 0) {
    const position = window2.document.caretPositionFromPoint(event.clientX, event.clientY);
    if (position)
      try {
        domRange = window2.document.createRange(), domRange.setStart(position.offsetNode, position.offset), domRange.setEnd(position.offsetNode, position.offset);
      } catch {
      }
  } else if (window2.document.caretRangeFromPoint !== void 0)
    domRange = window2.document.caretRangeFromPoint(event.clientX, event.clientY) ?? void 0;
  else {
    console.warn("Neither caretPositionFromPoint nor caretRangeFromPoint is supported");
    return;
  }
  if (!domRange)
    return;
  let range;
  try {
    range = DOMEditor.toSlateRange(editor, domRange, {
      exactMatch: !1,
      // It can still throw even with this option set to true
      suppressThrow: !1
    });
  } catch {
  }
  return range;
}
function normalizePoint(point, value) {
  if (!point || !value)
    return null;
  const newPath = [];
  let newOffset = point.offset || 0;
  const blockKey = typeof point.path[0] == "object" && "_key" in point.path[0] && point.path[0]._key, childKey = typeof point.path[2] == "object" && "_key" in point.path[2] && point.path[2]._key, block = value.find((blk) => blk._key === blockKey);
  if (block)
    newPath.push({
      _key: block._key
    });
  else
    return null;
  if (block && point.path[1] === "children") {
    if (!block.children || Array.isArray(block.children) && block.children.length === 0)
      return null;
    const child = Array.isArray(block.children) && block.children.find((cld) => cld._key === childKey);
    if (child)
      newPath.push("children"), newPath.push({
        _key: child._key
      }), newOffset = child.text && child.text.length >= point.offset ? point.offset : child.text && child.text.length || 0;
    else
      return null;
  }
  return {
    path: newPath,
    offset: newOffset
  };
}
function normalizeSelection(selection, value) {
  if (!selection || !value || value.length === 0)
    return null;
  let newAnchor = null, newFocus = null;
  const {
    anchor,
    focus
  } = selection;
  return anchor && value.find((blk) => isEqual({
    _key: blk._key
  }, anchor.path[0])) && (newAnchor = normalizePoint(anchor, value)), focus && value.find((blk) => isEqual({
    _key: blk._key
  }, focus.path[0])) && (newFocus = normalizePoint(focus, value)), newAnchor && newFocus ? {
    anchor: newAnchor,
    focus: newFocus,
    backward: selection.backward
  } : null;
}
function toSlateRange(snapshot) {
  if (!snapshot.context.selection)
    return null;
  if (isEqualSelectionPoints(snapshot.context.selection.anchor, snapshot.context.selection.focus)) {
    const anchorPoint2 = toSlateSelectionPoint(snapshot, snapshot.context.selection.anchor, snapshot.context.selection.backward ? "forward" : "backward");
    return anchorPoint2 ? {
      anchor: anchorPoint2,
      focus: anchorPoint2
    } : null;
  }
  const anchorPoint = toSlateSelectionPoint(snapshot, snapshot.context.selection.anchor, snapshot.context.selection.backward ? "forward" : "backward"), focusPoint = toSlateSelectionPoint(snapshot, snapshot.context.selection.focus, snapshot.context.selection.backward ? "backward" : "forward");
  return !anchorPoint || !focusPoint ? null : {
    anchor: anchorPoint,
    focus: focusPoint
  };
}
function toSlateSelectionPoint(snapshot, selectionPoint, direction) {
  const blockKey = getBlockKeyFromSelectionPoint(selectionPoint);
  if (!blockKey)
    return;
  const blockIndex = snapshot.blockIndexMap.get(blockKey);
  if (blockIndex === void 0)
    return;
  const block = snapshot.context.value.at(blockIndex);
  if (!block)
    return;
  if (!isTextBlock(snapshot.context, block))
    return {
      path: [blockIndex, 0],
      offset: 0
    };
  let childKey = getChildKeyFromSelectionPoint({
    path: selectionPoint.path
  });
  const spanSelectionPoint = childKey ? void 0 : blockOffsetToSpanSelectionPoint({
    context: {
      schema: snapshot.context.schema,
      value: [block]
    },
    blockOffset: {
      path: [{
        _key: blockKey
      }],
      offset: selectionPoint.offset
    },
    direction
  });
  if (childKey = spanSelectionPoint ? getChildKeyFromSelectionPoint(spanSelectionPoint) : childKey, !childKey)
    return {
      path: [blockIndex, 0],
      offset: 0
    };
  let offset = spanSelectionPoint?.offset ?? selectionPoint.offset, childPath = [], childIndex = -1, pathChild;
  for (const child of block.children)
    if (childIndex++, child._key === childKey) {
      pathChild = child, isSpan(snapshot.context, child) ? childPath = [childIndex] : (childPath = [childIndex, 0], offset = 0);
      break;
    }
  return childPath.length === 0 ? {
    path: [blockIndex, 0],
    offset: 0
  } : {
    path: [blockIndex].concat(childPath),
    offset: isSpan(snapshot.context, pathChild) ? Math.min(pathChild.text.length, offset) : offset
  };
}
const IS_PROCESSING_REMOTE_CHANGES = /* @__PURE__ */ new WeakMap(), KEY_TO_SLATE_ELEMENT = /* @__PURE__ */ new WeakMap(), KEY_TO_VALUE_ELEMENT = /* @__PURE__ */ new WeakMap(), SLATE_TO_PORTABLE_TEXT_RANGE = /* @__PURE__ */ new WeakMap(), EditorActorContext = createContext({});
function DropIndicator() {
  const $ = c(1);
  let t0;
  return $[0] === Symbol.for("react.memo_cache_sentinel") ? (t0 = /* @__PURE__ */ jsx("div", { contentEditable: !1, className: "pt-drop-indicator", style: {
    position: "absolute",
    width: "100%",
    height: 1,
    borderBottom: "1px solid currentColor",
    zIndex: 5
  }, children: /* @__PURE__ */ jsx("span", {}) }), $[0] = t0) : t0 = $[0], t0;
}
function RenderDefaultBlockObject(props) {
  const $ = c(4);
  let t0;
  $[0] === Symbol.for("react.memo_cache_sentinel") ? (t0 = {
    userSelect: "none"
  }, $[0] = t0) : t0 = $[0];
  let t1;
  return $[1] !== props.blockObject._key || $[2] !== props.blockObject._type ? (t1 = /* @__PURE__ */ jsxs("div", { style: t0, children: [
    "[",
    props.blockObject._type,
    ": ",
    props.blockObject._key,
    "]"
  ] }), $[1] = props.blockObject._key, $[2] = props.blockObject._type, $[3] = t1) : t1 = $[3], t1;
}
function RenderDefaultInlineObject(props) {
  const $ = c(4);
  let t0;
  $[0] === Symbol.for("react.memo_cache_sentinel") ? (t0 = {
    userSelect: "none"
  }, $[0] = t0) : t0 = $[0];
  let t1;
  return $[1] !== props.inlineObject._key || $[2] !== props.inlineObject._type ? (t1 = /* @__PURE__ */ jsxs("span", { style: t0, children: [
    "[",
    props.inlineObject._type,
    ": ",
    props.inlineObject._key,
    "]"
  ] }), $[1] = props.inlineObject._key, $[2] = props.inlineObject._type, $[3] = t1) : t1 = $[3], t1;
}
function getDragSelection({
  eventSelection,
  snapshot
}) {
  let dragSelection = eventSelection;
  if (getFocusInlineObject({
    ...snapshot,
    context: {
      ...snapshot.context,
      selection: eventSelection
    }
  }))
    return dragSelection;
  const draggingCollapsedSelection = isSelectionCollapsed$1({
    context: {
      ...snapshot.context,
      selection: eventSelection
    }
  }), draggedTextBlock = getFocusTextBlock({
    ...snapshot,
    context: {
      ...snapshot.context,
      selection: eventSelection
    }
  }), draggedSpan = getFocusSpan$1({
    ...snapshot,
    context: {
      ...snapshot.context,
      selection: eventSelection
    }
  });
  draggingCollapsedSelection && draggedTextBlock && draggedSpan && (dragSelection = {
    anchor: getBlockStartPoint({
      context: snapshot.context,
      block: draggedTextBlock
    }),
    focus: getBlockEndPoint({
      context: snapshot.context,
      block: draggedTextBlock
    })
  });
  const selectedBlocks = getSelectedBlocks(snapshot);
  if (snapshot.context.selection && isSelectionExpanded(snapshot) && selectedBlocks.length > 1) {
    const selectionStartBlock = getSelectionStartBlock$1(snapshot), selectionEndBlock = getSelectionEndBlock$1(snapshot);
    if (!selectionStartBlock || !selectionEndBlock)
      return dragSelection;
    const selectionStartPoint = getBlockStartPoint({
      context: snapshot.context,
      block: selectionStartBlock
    }), selectionEndPoint = getBlockEndPoint({
      context: snapshot.context,
      block: selectionEndBlock
    });
    isOverlappingSelection(eventSelection)({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: {
          anchor: selectionStartPoint,
          focus: selectionEndPoint
        }
      }
    }) && (dragSelection = {
      anchor: selectionStartPoint,
      focus: selectionEndPoint
    });
  }
  return dragSelection;
}
const defaultKeyGenerator = () => randomKey(12), getByteHexTable = /* @__PURE__ */ (() => {
  let table;
  return () => {
    if (table)
      return table;
    table = [];
    for (let i = 0; i < 256; ++i)
      table[i] = (i + 256).toString(16).slice(1);
    return table;
  };
})();
function whatwgRNG(length = 16) {
  const rnds8 = new Uint8Array(length);
  return getRandomValues(rnds8), rnds8;
}
function randomKey(length) {
  const table = getByteHexTable();
  return whatwgRNG(length).reduce((str, n) => str + table[n], "").slice(0, length);
}
function createEditorPriority(config) {
  return {
    id: defaultKeyGenerator(),
    name: config?.name,
    reference: config?.reference
  };
}
const corePriority = createEditorPriority({
  name: "core"
});
function createCoreBlockElementBehaviorsConfig({
  key,
  onSetDragPositionBlock
}) {
  return [{
    behavior: defineBehavior({
      on: "drag.dragover",
      guard: ({
        snapshot,
        event
      }) => {
        const dropFocusBlock = getFocusBlock$1({
          ...snapshot,
          context: {
            ...snapshot.context,
            selection: event.position.selection
          }
        });
        if (!dropFocusBlock || dropFocusBlock.node._key !== key)
          return !1;
        const dragOrigin = event.dragOrigin;
        if (!dragOrigin)
          return !1;
        const dragSelection = getDragSelection({
          eventSelection: dragOrigin.selection,
          snapshot
        });
        return getSelectedBlocks({
          ...snapshot,
          context: {
            ...snapshot.context,
            selection: dragSelection
          }
        }).some((draggedBlock) => draggedBlock.node._key === key) ? !1 : isSelectingEntireBlocks({
          ...snapshot,
          context: {
            ...snapshot.context,
            selection: dragSelection
          }
        });
      },
      actions: [({
        event
      }) => [{
        type: "effect",
        effect: () => {
          onSetDragPositionBlock(event.position.block);
        }
      }]]
    }),
    priority: createEditorPriority({
      reference: {
        priority: corePriority,
        importance: "lower"
      }
    })
  }, {
    behavior: defineBehavior({
      on: "drag.*",
      guard: ({
        event
      }) => event.type !== "drag.dragover",
      actions: [({
        event
      }) => [{
        type: "effect",
        effect: () => {
          onSetDragPositionBlock(void 0);
        }
      }, forward(event)]]
    }),
    priority: createEditorPriority({
      reference: {
        priority: corePriority,
        importance: "lower"
      }
    })
  }];
}
function useCoreBlockElementBehaviors(t0) {
  const $ = c(5), {
    key,
    onSetDragPositionBlock
  } = t0, editorActor = useContext(EditorActorContext);
  let t1, t2;
  $[0] !== editorActor || $[1] !== key || $[2] !== onSetDragPositionBlock ? (t1 = () => {
    const behaviorConfigs = createCoreBlockElementBehaviorsConfig({
      key,
      onSetDragPositionBlock
    });
    for (const behaviorConfig of behaviorConfigs)
      editorActor.send({
        type: "add behavior",
        behaviorConfig
      });
    return () => {
      for (const behaviorConfig_0 of behaviorConfigs)
        editorActor.send({
          type: "remove behavior",
          behaviorConfig: behaviorConfig_0
        });
    };
  }, t2 = [editorActor, key, onSetDragPositionBlock], $[0] = editorActor, $[1] = key, $[2] = onSetDragPositionBlock, $[3] = t1, $[4] = t2) : (t1 = $[3], t2 = $[4]), useEffect(t1, t2);
}
function RenderBlockObject(props) {
  const [dragPositionBlock, setDragPositionBlock] = useState(), blockObjectRef = useRef(null), selected = useSelected(), focused = useSlateSelector((editor) => selected && editor.selection !== null && Range.isCollapsed(editor.selection));
  useCoreBlockElementBehaviors({
    key: props.element._key,
    onSetDragPositionBlock: setDragPositionBlock
  });
  const legacySchemaType = props.legacySchema.blockObjects.find((schemaType) => schemaType.name === props.element._type);
  legacySchemaType || console.error(`Unable to find Block Object "${props.element._type}" in Schema`);
  const blockObject = props.blockObject ?? {
    _key: props.element._key,
    _type: props.element._type
  };
  return /* @__PURE__ */ jsxs("div", { ...props.attributes, className: "pt-block pt-object-block", "data-block-key": props.element._key, "data-block-name": props.element._type, "data-block-type": "object", children: [
    dragPositionBlock === "start" ? /* @__PURE__ */ jsx(DropIndicator, {}) : null,
    props.children,
    /* @__PURE__ */ jsx("div", { ref: blockObjectRef, contentEditable: !1, draggable: !props.readOnly, children: props.renderBlock && legacySchemaType ? props.renderBlock({
      children: /* @__PURE__ */ jsx(RenderDefaultBlockObject, { blockObject }),
      editorElementRef: blockObjectRef,
      focused,
      path: [{
        _key: props.element._key
      }],
      schemaType: legacySchemaType,
      selected,
      type: legacySchemaType,
      value: blockObject
    }) : /* @__PURE__ */ jsx(RenderDefaultBlockObject, { blockObject }) }),
    dragPositionBlock === "end" ? /* @__PURE__ */ jsx(DropIndicator, {}) : null
  ] });
}
function RenderInlineObject(props) {
  const inlineObjectRef = useRef(null), slateEditor = useSlateStatic(), selected = useSelected(), focused = useSlateSelector((editor) => selected && editor.selection !== null && Range.isCollapsed(editor.selection)), legacySchemaType = props.legacySchema.inlineObjects.find((inlineObject) => inlineObject.name === props.element._type);
  legacySchemaType || console.error(`Unable to find Inline Object "${props.element._type}" in Schema`);
  const path = DOMEditor.findPath(slateEditor, props.element), [block] = getPointBlock({
    editor: slateEditor,
    point: {
      path,
      offset: 0
    }
  });
  block || console.error(`Unable to find parent block of inline object ${props.element._key}`);
  const inlineObject_0 = {
    _key: props.element._key,
    _type: props.element._type,
    ..."value" in props.element && typeof props.element.value == "object" ? props.element.value : {}
  };
  return /* @__PURE__ */ jsxs("span", { ...props.attributes, draggable: !props.readOnly, className: "pt-inline-object", "data-child-key": inlineObject_0._key, "data-child-name": inlineObject_0._type, "data-child-type": "object", children: [
    props.children,
    /* @__PURE__ */ jsx("span", { ref: inlineObjectRef, style: {
      display: "inline-block"
    }, children: props.renderChild && block && legacySchemaType ? props.renderChild({
      annotations: [],
      children: /* @__PURE__ */ jsx(RenderDefaultInlineObject, { inlineObject: inlineObject_0 }),
      editorElementRef: inlineObjectRef,
      selected,
      focused,
      path: [{
        _key: block._key
      }, "children", {
        _key: props.element._key
      }],
      schemaType: legacySchemaType,
      value: inlineObject_0,
      type: legacySchemaType
    }) : /* @__PURE__ */ jsx(RenderDefaultInlineObject, { inlineObject: inlineObject_0 }) })
  ] });
}
function RenderTextBlock(props) {
  const [dragPositionBlock, setDragPositionBlock] = useState(), blockRef = useRef(null), selected = useSelected(), focused = useSlateSelector((editor) => selected && editor.selection !== null && Range.isCollapsed(editor.selection));
  useCoreBlockElementBehaviors({
    key: props.element._key,
    onSetDragPositionBlock: setDragPositionBlock
  });
  const listIndex = useSlateSelector((editor_0) => editor_0.listIndexMap.get(props.textBlock._key));
  let children = props.children;
  if (props.renderStyle && props.textBlock.style) {
    const legacyStyleSchemaType = props.textBlock.style !== void 0 ? props.legacySchema.styles.find((style) => style.value === props.textBlock.style) : void 0;
    legacyStyleSchemaType ? children = props.renderStyle({
      block: props.textBlock,
      children,
      editorElementRef: blockRef,
      focused,
      path: [{
        _key: props.textBlock._key
      }],
      schemaType: legacyStyleSchemaType,
      selected,
      value: props.textBlock.style
    }) : console.error(`Unable to find Schema type for text block style ${props.textBlock.style}`);
  }
  if (props.renderListItem && props.textBlock.listItem) {
    const legacyListItemSchemaType = props.legacySchema.lists.find((list) => list.value === props.textBlock.listItem);
    legacyListItemSchemaType ? children = props.renderListItem({
      block: props.textBlock,
      children,
      editorElementRef: blockRef,
      focused,
      level: props.textBlock.level ?? 1,
      path: [{
        _key: props.textBlock._key
      }],
      selected,
      value: props.textBlock.listItem,
      schemaType: legacyListItemSchemaType
    }) : console.error(`Unable to find Schema type for text block list item ${props.textBlock.listItem}`);
  }
  return /* @__PURE__ */ jsxs("div", { ...props.attributes, className: ["pt-block", "pt-text-block", ...props.textBlock.style ? [`pt-text-block-style-${props.textBlock.style}`] : [], ...props.textBlock.listItem ? ["pt-list-item", `pt-list-item-${props.textBlock.listItem}`, `pt-list-item-level-${props.textBlock.level ?? 1}`] : []].join(" "), spellCheck: props.spellCheck, "data-block-key": props.textBlock._key, "data-block-name": props.textBlock._type, "data-block-type": "text", ...props.textBlock.listItem !== void 0 ? {
    "data-list-item": props.textBlock.listItem
  } : {}, ...props.textBlock.level !== void 0 ? {
    "data-level": props.textBlock.level
  } : {}, ...props.textBlock.style !== void 0 ? {
    "data-style": props.textBlock.style
  } : {}, ...listIndex !== void 0 ? {
    "data-list-index": listIndex
  } : {}, children: [
    dragPositionBlock === "start" ? /* @__PURE__ */ jsx(DropIndicator, {}) : null,
    /* @__PURE__ */ jsx("div", { ref: blockRef, children: props.renderBlock ? props.renderBlock({
      children,
      editorElementRef: blockRef,
      focused,
      level: props.textBlock.level,
      listItem: props.textBlock.listItem,
      path: [{
        _key: props.textBlock._key
      }],
      selected,
      schemaType: props.legacySchema.block,
      style: props.textBlock.style,
      type: props.legacySchema.block,
      value: props.textBlock
    }) : children }),
    dragPositionBlock === "end" ? /* @__PURE__ */ jsx(DropIndicator, {}) : null
  ] });
}
function RenderElement(props) {
  const $ = c(34), editorActor = useContext(EditorActorContext), schema = useSelector(editorActor, _temp$1), legacySchema = useSelector(editorActor, _temp2), slateStatic = useSlateStatic();
  if ("__inline" in props.element && props.element.__inline === !0) {
    let t02;
    return $[0] !== legacySchema || $[1] !== props.attributes || $[2] !== props.children || $[3] !== props.element || $[4] !== props.readOnly || $[5] !== props.renderChild || $[6] !== schema ? (t02 = /* @__PURE__ */ jsx(RenderInlineObject, { attributes: props.attributes, element: props.element, legacySchema, readOnly: props.readOnly, renderChild: props.renderChild, schema, children: props.children }), $[0] = legacySchema, $[1] = props.attributes, $[2] = props.children, $[3] = props.element, $[4] = props.readOnly, $[5] = props.renderChild, $[6] = schema, $[7] = t02) : t02 = $[7], t02;
  }
  let block, t0;
  if ($[8] !== props.element._key || $[9] !== schema || $[10] !== slateStatic.blockIndexMap || $[11] !== slateStatic.value) {
    const blockIndex = slateStatic.blockIndexMap.get(props.element._key);
    block = blockIndex !== void 0 ? slateStatic.value.at(blockIndex) : void 0, t0 = isTextBlock({
      schema
    }, block), $[8] = props.element._key, $[9] = schema, $[10] = slateStatic.blockIndexMap, $[11] = slateStatic.value, $[12] = block, $[13] = t0;
  } else
    block = $[12], t0 = $[13];
  if (t0) {
    let t12;
    return $[14] !== block || $[15] !== legacySchema || $[16] !== props.attributes || $[17] !== props.children || $[18] !== props.element || $[19] !== props.readOnly || $[20] !== props.renderBlock || $[21] !== props.renderListItem || $[22] !== props.renderStyle || $[23] !== props.spellCheck ? (t12 = /* @__PURE__ */ jsx(RenderTextBlock, { attributes: props.attributes, element: props.element, legacySchema, readOnly: props.readOnly, renderBlock: props.renderBlock, renderListItem: props.renderListItem, renderStyle: props.renderStyle, spellCheck: props.spellCheck, textBlock: block, children: props.children }), $[14] = block, $[15] = legacySchema, $[16] = props.attributes, $[17] = props.children, $[18] = props.element, $[19] = props.readOnly, $[20] = props.renderBlock, $[21] = props.renderListItem, $[22] = props.renderStyle, $[23] = props.spellCheck, $[24] = t12) : t12 = $[24], t12;
  }
  let t1;
  return $[25] !== block || $[26] !== legacySchema || $[27] !== props.attributes || $[28] !== props.children || $[29] !== props.element || $[30] !== props.readOnly || $[31] !== props.renderBlock || $[32] !== schema ? (t1 = /* @__PURE__ */ jsx(RenderBlockObject, { attributes: props.attributes, blockObject: block, element: props.element, legacySchema, readOnly: props.readOnly, renderBlock: props.renderBlock, schema, children: props.children }), $[25] = block, $[26] = legacySchema, $[27] = props.attributes, $[28] = props.children, $[29] = props.element, $[30] = props.readOnly, $[31] = props.renderBlock, $[32] = schema, $[33] = t1) : t1 = $[33], t1;
}
function _temp2(s_0) {
  return s_0.context.getLegacySchema();
}
function _temp$1(s) {
  return s.context.schema;
}
const PortableTextEditorContext = createContext(null), usePortableTextEditor = () => {
  const editor = useContext(PortableTextEditorContext);
  if (!editor)
    throw new Error("The `usePortableTextEditor` hook must be used inside the <PortableTextEditor> component's context.");
  return editor;
}, forEachActor = (actorRef, callback) => {
  callback(actorRef);
  const children = actorRef.getSnapshot().children;
  children && Object.values(children).forEach((child) => {
    forEachActor(child, callback);
  });
};
function stopActor(actorRef) {
  const persistedSnapshots = [];
  forEachActor(actorRef, (ref) => {
    persistedSnapshots.push([ref, ref.getSnapshot()]), ref.observers = /* @__PURE__ */ new Set();
  });
  const systemSnapshot = actorRef.system.getSnapshot?.();
  actorRef.stop(), actorRef.system._snapshot = systemSnapshot, persistedSnapshots.forEach(([ref, snapshot]) => {
    ref._processingStatus = 0, ref._snapshot = snapshot;
  });
}
const converterJson = {
  mimeType: "application/json",
  serialize: ({
    snapshot,
    event
  }) => {
    const portableTextConverter = snapshot.context.converters.find((converter) => converter.mimeType === "application/x-portable-text");
    return portableTextConverter ? {
      ...portableTextConverter.serialize({
        snapshot,
        event
      }),
      mimeType: "application/json",
      originEvent: event.originEvent
    } : {
      type: "serialization.failure",
      mimeType: "application/json",
      originEvent: event.originEvent,
      reason: "No application/x-portable-text Converter found"
    };
  },
  deserialize: ({
    snapshot,
    event
  }) => {
    const portableTextConverter = snapshot.context.converters.find((converter) => converter.mimeType === "application/x-portable-text");
    return portableTextConverter ? {
      ...portableTextConverter.deserialize({
        snapshot,
        event
      }),
      mimeType: "application/json"
    } : {
      type: "deserialization.failure",
      mimeType: "application/json",
      reason: "No application/x-portable-text Converter found"
    };
  }
}, converterPortableText = {
  mimeType: "application/x-portable-text",
  serialize: ({
    snapshot,
    event
  }) => {
    if (!snapshot.context.selection)
      return {
        type: "serialization.failure",
        mimeType: "application/x-portable-text",
        originEvent: event.originEvent,
        reason: "No selection"
      };
    const blocks = getSelectedValue(snapshot);
    return blocks.length === 0 ? {
      type: "serialization.failure",
      mimeType: "application/x-portable-text",
      reason: "No blocks serialized",
      originEvent: event.originEvent
    } : {
      type: "serialization.success",
      data: JSON.stringify(blocks),
      mimeType: "application/x-portable-text",
      originEvent: event.originEvent
    };
  },
  deserialize: ({
    snapshot,
    event
  }) => {
    const blocks = JSON.parse(event.data);
    if (!Array.isArray(blocks))
      return {
        type: "deserialization.failure",
        mimeType: "application/x-portable-text",
        reason: "Data is not an array"
      };
    const parsedBlocks = blocks.flatMap((block) => {
      const parsedBlock = parseBlock({
        context: snapshot.context,
        block,
        options: {
          refreshKeys: !1,
          validateFields: !1
        }
      });
      return parsedBlock ? [parsedBlock] : [];
    });
    return parsedBlocks.length === 0 && blocks.length > 0 ? {
      type: "deserialization.failure",
      mimeType: "application/x-portable-text",
      reason: "No blocks were parsed"
    } : {
      type: "deserialization.success",
      data: parsedBlocks,
      mimeType: "application/x-portable-text"
    };
  }
};
function createConverterTextHtml(legacySchema) {
  return {
    mimeType: "text/html",
    serialize: ({
      snapshot,
      event
    }) => {
      if (!snapshot.context.selection)
        return {
          type: "serialization.failure",
          mimeType: "text/html",
          originEvent: event.originEvent,
          reason: "No selection"
        };
      const blocks = getSelectedValue(snapshot), html = toHTML(blocks, {
        onMissingComponent: !1,
        components: {
          unknownType: ({
            children
          }) => children !== void 0 ? `${children}` : ""
        }
      });
      return html === "" ? {
        type: "serialization.failure",
        mimeType: "text/html",
        originEvent: event.originEvent,
        reason: "Serialized HTML is empty"
      } : {
        type: "serialization.success",
        data: html,
        mimeType: "text/html",
        originEvent: event.originEvent
      };
    },
    deserialize: ({
      snapshot,
      event
    }) => {
      const parsedBlocks = htmlToBlocks(event.data, legacySchema.portableText, {
        keyGenerator: snapshot.context.keyGenerator,
        unstable_whitespaceOnPasteMode: legacySchema.block.options.unstable_whitespaceOnPasteMode
      }).flatMap((block) => {
        const parsedBlock = parseBlock({
          context: snapshot.context,
          block,
          options: {
            refreshKeys: !1,
            validateFields: !1
          }
        });
        return parsedBlock ? [parsedBlock] : [];
      });
      return parsedBlocks.length === 0 ? {
        type: "deserialization.failure",
        mimeType: "text/html",
        reason: "No blocks deserialized"
      } : {
        type: "deserialization.success",
        data: parsedBlocks,
        mimeType: "text/html"
      };
    }
  };
}
function createConverterTextPlain(legacySchema) {
  return {
    mimeType: "text/plain",
    serialize: ({
      snapshot,
      event
    }) => snapshot.context.selection ? {
      type: "serialization.success",
      data: getSelectedValue(snapshot).map((block) => isTextBlock(snapshot.context, block) ? block.children.map((child) => child._type === snapshot.context.schema.span.name ? child.text : event.originEvent === "drag.dragstart" ? `[${snapshot.context.schema.inlineObjects.find((inlineObjectType) => inlineObjectType.name === child._type)?.title ?? "Object"}]` : "").join("") : event.originEvent === "drag.dragstart" ? `[${snapshot.context.schema.blockObjects.find((blockObjectType) => blockObjectType.name === block._type)?.title ?? "Object"}]` : "").filter((block) => block !== "").join(`

`),
      mimeType: "text/plain",
      originEvent: event.originEvent
    } : {
      type: "serialization.failure",
      mimeType: "text/plain",
      originEvent: event.originEvent,
      reason: "No selection"
    },
    deserialize: ({
      snapshot,
      event
    }) => {
      const textToHtml = `<html><body>${escapeHtml(event.data).split(/\n{2,}/).map((line) => line ? `<p>${line.replace(/(?:\r\n|\r|\n)/g, "<br/>")}</p>` : "<p></p>").join("")}</body></html>`, parsedBlocks = htmlToBlocks(textToHtml, legacySchema.portableText, {
        keyGenerator: snapshot.context.keyGenerator
      }).flatMap((block) => {
        const parsedBlock = parseBlock({
          context: snapshot.context,
          block,
          options: {
            refreshKeys: !1,
            validateFields: !1
          }
        });
        return parsedBlock ? [parsedBlock] : [];
      });
      return parsedBlocks.length === 0 ? {
        type: "deserialization.failure",
        mimeType: "text/plain",
        reason: "No blocks deserialized"
      } : {
        type: "deserialization.success",
        data: parsedBlocks,
        mimeType: "text/plain"
      };
    }
  };
}
const entityMap = {
  "&": "&amp;",
  "<": "&lt;",
  ">": "&gt;",
  '"': "&quot;",
  "'": "&#39;",
  "/": "&#x2F;",
  "`": "&#x60;",
  "=": "&#x3D;"
};
function escapeHtml(str) {
  return String(str).replace(/[&<>"'`=/]/g, (s) => entityMap[s]);
}
function createCoreConverters(legacySchema) {
  return [converterJson, converterPortableText, createConverterTextHtml(legacySchema), createConverterTextPlain(legacySchema)];
}
function compileType(rawType) {
  return Schema.compile({
    name: "blockTypeSchema",
    types: [rawType]
  }).get(rawType.name);
}
const levelIndexMaps = /* @__PURE__ */ new Map();
function buildIndexMaps(context, {
  blockIndexMap,
  listIndexMap
}) {
  blockIndexMap.clear(), listIndexMap.clear(), levelIndexMaps.clear();
  let previousListItem;
  for (let blockIndex = 0; blockIndex < context.value.length; blockIndex++) {
    const block = context.value.at(blockIndex);
    if (block === void 0)
      continue;
    if (blockIndexMap.set(block._key, blockIndex), !isTextBlock(context, block)) {
      levelIndexMaps.clear(), previousListItem = void 0;
      continue;
    }
    if (block.listItem === void 0 || block.level === void 0) {
      levelIndexMaps.clear(), previousListItem = void 0;
      continue;
    }
    if (!previousListItem) {
      const levelIndexMap2 = levelIndexMaps.get(block.listItem) ?? /* @__PURE__ */ new Map();
      levelIndexMap2.set(block.level, 1), levelIndexMaps.set(block.listItem, levelIndexMap2), listIndexMap.set(block._key, 1), previousListItem = {
        listItem: block.listItem,
        level: block.level
      };
      continue;
    }
    if (previousListItem.listItem === block.listItem && previousListItem.level < block.level) {
      const levelIndexMap2 = levelIndexMaps.get(block.listItem) ?? /* @__PURE__ */ new Map();
      levelIndexMap2.set(block.level, 1), levelIndexMaps.set(block.listItem, levelIndexMap2), listIndexMap.set(block._key, 1), previousListItem = {
        listItem: block.listItem,
        level: block.level
      };
      continue;
    }
    levelIndexMaps.forEach((levelIndexMap2, listItem) => {
      listItem !== block.listItem && levelIndexMap2.set(block.level, 0);
    });
    const levelIndexMap = levelIndexMaps.get(block.listItem) ?? /* @__PURE__ */ new Map(), levelCounter = levelIndexMap.get(block.level) ?? 0;
    levelIndexMap.set(block.level, levelCounter + 1), listIndexMap.set(block._key, levelCounter + 1), previousListItem = {
      listItem: block.listItem,
      level: block.level
    };
  }
}
function createPlaceholderBlock(context) {
  return {
    _type: context.schema.block.name,
    _key: context.keyGenerator(),
    style: context.schema.styles[0].name ?? "normal",
    markDefs: [],
    children: [{
      _type: context.schema.span.name,
      _key: context.keyGenerator(),
      text: "",
      marks: []
    }]
  };
}
const insertTextOperationImplementation = ({
  context,
  operation
}) => {
  const snapshot = {
    blockIndexMap: operation.editor.blockIndexMap,
    context: {
      value: operation.editor.value,
      selection: operation.editor.selection ? slateRangeToSelection({
        schema: context.schema,
        editor: operation.editor,
        range: operation.editor.selection
      }) : null,
      schema: context.schema,
      keyGenerator: context.keyGenerator,
      converters: [],
      readOnly: !1
    },
    decoratorState: operation.editor.decoratorState
  }, markState = getMarkState(snapshot), activeDecorators = getActiveDecorators(snapshot), activeAnnotations = getActiveAnnotationsMarks(snapshot), [focusSpan] = getFocusSpan({
    editor: operation.editor
  });
  if (!focusSpan) {
    Transforms.insertText(operation.editor, operation.text);
    return;
  }
  if (markState && markState.state === "unchanged") {
    const markStateDecorators = (markState.marks ?? []).filter((mark) => context.schema.decorators.map((decorator) => decorator.name).includes(mark));
    if (markStateDecorators.length === activeDecorators.length && markStateDecorators.every((mark) => activeDecorators.includes(mark))) {
      Transforms.insertText(operation.editor, operation.text);
      return;
    }
  }
  Transforms.insertNodes(operation.editor, {
    _type: focusSpan._type,
    _key: context.keyGenerator(),
    text: operation.text,
    marks: [...activeDecorators, ...activeAnnotations]
  }), EDITOR_TO_PENDING_SELECTION.set(operation.editor, operation.editor.selection), operation.editor.decoratorState = {};
};
function getPreviousSpan({
  editor,
  blockPath,
  spanPath
}) {
  let previousSpan;
  for (const [child, childPath] of Node.children(editor, blockPath, {
    reverse: !0
  }))
    if (editor.isTextSpan(child) && Path.isBefore(childPath, spanPath)) {
      previousSpan = child;
      break;
    }
  return previousSpan;
}
function getNextSpan({
  editor,
  blockPath,
  spanPath
}) {
  let nextSpan;
  for (const [child, childPath] of Node.children(editor, blockPath))
    if (editor.isTextSpan(child) && Path.isAfter(childPath, spanPath)) {
      nextSpan = child;
      break;
    }
  return nextSpan;
}
function withRemoteChanges(editor, fn) {
  const prev = isChangingRemotely(editor) || !1;
  IS_PROCESSING_REMOTE_CHANGES.set(editor, !0), fn(), IS_PROCESSING_REMOTE_CHANGES.set(editor, prev);
}
function isChangingRemotely(editor) {
  return IS_PROCESSING_REMOTE_CHANGES.get(editor);
}
const IS_UDOING = /* @__PURE__ */ new WeakMap(), IS_REDOING = /* @__PURE__ */ new WeakMap();
function withUndoing(editor, fn) {
  const prev = isUndoing(editor);
  IS_UDOING.set(editor, !0), fn(), IS_UDOING.set(editor, prev);
}
function isUndoing(editor) {
  return IS_UDOING.get(editor) ?? !1;
}
function setIsUndoing(editor, isUndoing2) {
  IS_UDOING.set(editor, isUndoing2);
}
function withRedoing(editor, fn) {
  const prev = isRedoing(editor);
  IS_REDOING.set(editor, !0), fn(), IS_REDOING.set(editor, prev);
}
function isRedoing(editor) {
  return IS_REDOING.get(editor) ?? !1;
}
function setIsRedoing(editor, isRedoing2) {
  IS_REDOING.set(editor, isRedoing2);
}
function defaultCompare(a, b) {
  return a === b;
}
function useEditorSelector(editor, selector, t0) {
  const $ = c(3), compare = t0 === void 0 ? defaultCompare : t0;
  let t1;
  return $[0] !== editor || $[1] !== selector ? (t1 = (editorActorSnapshot) => {
    const snapshot = getEditorSnapshot({
      editorActorSnapshot,
      slateEditorInstance: editor._internal.slateEditor.instance
    });
    return selector(snapshot);
  }, $[0] = editor, $[1] = selector, $[2] = t1) : t1 = $[2], useSelector(editor._internal.editorActor, t1, compare);
}
function getEditorSnapshot({
  editorActorSnapshot,
  slateEditorInstance
}) {
  return {
    blockIndexMap: slateEditorInstance.blockIndexMap,
    context: {
      converters: [...editorActorSnapshot.context.converters],
      keyGenerator: editorActorSnapshot.context.keyGenerator,
      readOnly: editorActorSnapshot.matches({
        "edit mode": "read only"
      }),
      schema: editorActorSnapshot.context.schema,
      selection: editorActorSnapshot.context.selection,
      value: slateEditorInstance.value
    },
    decoratorState: slateEditorInstance.decoratorState
  };
}
const debug$e = debugWithName("plugin:withPortableTextMarkModel");
function createWithPortableTextMarkModel(editorActor) {
  return function(editor) {
    const {
      apply: apply2,
      normalizeNode
    } = editor, decorators = editorActor.getSnapshot().context.schema.decorators.map((t) => t.name);
    return editor.normalizeNode = (nodeEntry) => {
      const [node, path] = nodeEntry;
      if (editor.isTextBlock(node)) {
        const children = Node.children(editor, path);
        for (const [child, childPath] of children) {
          const nextNode = node.children[childPath[1] + 1];
          if (editor.isTextSpan(child) && editor.isTextSpan(nextNode) && child.marks?.every((mark) => nextNode.marks?.includes(mark)) && nextNode.marks?.every((mark) => child.marks?.includes(mark))) {
            debug$e("Merging spans", JSON.stringify(child, null, 2), JSON.stringify(nextNode, null, 2)), editorActor.send({
              type: "normalizing"
            }), Transforms.mergeNodes(editor, {
              at: [childPath[0], childPath[1] + 1],
              voids: !0
            }), editorActor.send({
              type: "done normalizing"
            });
            return;
          }
        }
      }
      if (editor.isTextBlock(node) && !Array.isArray(node.markDefs)) {
        debug$e("Adding .markDefs to block node"), editorActor.send({
          type: "normalizing"
        }), Transforms.setNodes(editor, {
          markDefs: []
        }, {
          at: path
        }), editorActor.send({
          type: "done normalizing"
        });
        return;
      }
      if (editor.isTextSpan(node) && !Array.isArray(node.marks)) {
        debug$e("Adding .marks to span node"), editorActor.send({
          type: "normalizing"
        }), Transforms.setNodes(editor, {
          marks: []
        }, {
          at: path
        }), editorActor.send({
          type: "done normalizing"
        });
        return;
      }
      if (editor.isTextSpan(node)) {
        const blockPath = Path.parent(path), [block] = Editor.node(editor, blockPath), decorators2 = editorActor.getSnapshot().context.schema.decorators.map((decorator) => decorator.name), annotations = node.marks?.filter((mark) => !decorators2.includes(mark));
        if (editor.isTextBlock(block) && node.text === "" && annotations && annotations.length > 0) {
          debug$e("Removing annotations from empty span node"), editorActor.send({
            type: "normalizing"
          }), Transforms.setNodes(editor, {
            marks: node.marks?.filter((mark) => decorators2.includes(mark))
          }, {
            at: path
          }), editorActor.send({
            type: "done normalizing"
          });
          return;
        }
      }
      if (editor.isTextBlock(node)) {
        const decorators2 = editorActor.getSnapshot().context.schema.decorators.map((decorator) => decorator.name);
        for (const [child, childPath] of Node.children(editor, path))
          if (editor.isTextSpan(child)) {
            const marks = child.marks ?? [], orphanedAnnotations = marks.filter((mark) => !decorators2.includes(mark) && !node.markDefs?.find((def) => def._key === mark));
            if (orphanedAnnotations.length > 0) {
              debug$e("Removing orphaned annotations from span node"), editorActor.send({
                type: "normalizing"
              }), Transforms.setNodes(editor, {
                marks: marks.filter((mark) => !orphanedAnnotations.includes(mark))
              }, {
                at: childPath
              }), editorActor.send({
                type: "done normalizing"
              });
              return;
            }
          }
      }
      if (editor.isTextSpan(node)) {
        const blockPath = Path.parent(path), [block] = Editor.node(editor, blockPath);
        if (editor.isTextBlock(block)) {
          const decorators2 = editorActor.getSnapshot().context.schema.decorators.map((decorator) => decorator.name), marks = node.marks ?? [], orphanedAnnotations = marks.filter((mark) => !decorators2.includes(mark) && !block.markDefs?.find((def) => def._key === mark));
          if (orphanedAnnotations.length > 0) {
            debug$e("Removing orphaned annotations from span node"), editorActor.send({
              type: "normalizing"
            }), Transforms.setNodes(editor, {
              marks: marks.filter((mark) => !orphanedAnnotations.includes(mark))
            }, {
              at: path
            }), editorActor.send({
              type: "done normalizing"
            });
            return;
          }
        }
      }
      if (editor.isTextBlock(node)) {
        const markDefs = node.markDefs ?? [], markDefKeys = /* @__PURE__ */ new Set(), newMarkDefs = [];
        for (const markDef of markDefs)
          markDefKeys.has(markDef._key) || (markDefKeys.add(markDef._key), newMarkDefs.push(markDef));
        if (markDefs.length !== newMarkDefs.length) {
          debug$e("Removing duplicate markDefs"), editorActor.send({
            type: "normalizing"
          }), Transforms.setNodes(editor, {
            markDefs: newMarkDefs
          }, {
            at: path
          }), editorActor.send({
            type: "done normalizing"
          });
          return;
        }
      }
      if (editor.isTextBlock(node) && !editor.operations.some((op) => op.type === "merge_node" && "markDefs" in op.properties && op.path.length === 1)) {
        const newMarkDefs = (node.markDefs || []).filter((def) => node.children.find((child) => Text.isText(child) && Array.isArray(child.marks) && child.marks.includes(def._key)));
        if (node.markDefs && !isEqual(newMarkDefs, node.markDefs)) {
          debug$e("Removing markDef not in use"), editorActor.send({
            type: "normalizing"
          }), Transforms.setNodes(editor, {
            markDefs: newMarkDefs
          }, {
            at: path
          }), editorActor.send({
            type: "done normalizing"
          });
          return;
        }
      }
      normalizeNode(nodeEntry);
    }, editor.apply = (op) => {
      if (isChangingRemotely(editor)) {
        apply2(op);
        return;
      }
      if (isUndoing(editor) || isRedoing(editor)) {
        apply2(op);
        return;
      }
      if (op.type === "set_selection" && op.properties && op.newProperties && op.properties.anchor && op.properties.focus && op.newProperties.anchor && op.newProperties.focus) {
        const previousSelectionIsCollapsed = Range.isCollapsed({
          anchor: op.properties.anchor,
          focus: op.properties.focus
        }), newSelectionIsCollapsed = Range.isCollapsed({
          anchor: op.newProperties.anchor,
          focus: op.newProperties.focus
        });
        if (previousSelectionIsCollapsed && newSelectionIsCollapsed) {
          const focusSpan = Array.from(Editor.nodes(editor, {
            mode: "lowest",
            at: op.properties.focus,
            match: (n) => editor.isTextSpan(n),
            voids: !1
          }))[0]?.[0], newFocusSpan = Array.from(Editor.nodes(editor, {
            mode: "lowest",
            at: op.newProperties.focus,
            match: (n) => editor.isTextSpan(n),
            voids: !1
          }))[0]?.[0], movedToNextSpan = focusSpan && newFocusSpan && op.newProperties.focus.path[0] === op.properties.focus.path[0] && op.newProperties.focus.path[1] === op.properties.focus.path[1] + 1 && focusSpan.text.length === op.properties.focus.offset && op.newProperties.focus.offset === 0, movedToPreviousSpan = focusSpan && newFocusSpan && op.newProperties.focus.path[0] === op.properties.focus.path[0] && op.newProperties.focus.path[1] === op.properties.focus.path[1] - 1 && op.properties.focus.offset === 0 && newFocusSpan.text.length === op.newProperties.focus.offset;
          !movedToNextSpan && !movedToPreviousSpan && (editor.decoratorState = {});
        }
      }
      if (op.type === "insert_node") {
        const {
          selection
        } = editor;
        if (selection) {
          const [_block, blockPath] = Editor.node(editor, selection, {
            depth: 1
          }), previousSpan = getPreviousSpan({
            editor,
            blockPath,
            spanPath: op.path
          }), previousSpanAnnotations = previousSpan ? previousSpan.marks?.filter((mark) => !decorators.includes(mark)) : [], nextSpan = getNextSpan({
            editor,
            blockPath,
            spanPath: [op.path[0], op.path[1] - 1]
          }), nextSpanAnnotations = nextSpan ? nextSpan.marks?.filter((mark) => !decorators.includes(mark)) : [], annotationsEnding = previousSpanAnnotations?.filter((annotation) => !nextSpanAnnotations?.includes(annotation)) ?? [], atTheEndOfAnnotation = annotationsEnding.length > 0;
          if (atTheEndOfAnnotation && isSpan(editorActor.getSnapshot().context, op.node) && op.node.marks?.some((mark) => annotationsEnding.includes(mark))) {
            Transforms.insertNodes(editor, {
              ...op.node,
              _key: editorActor.getSnapshot().context.keyGenerator(),
              marks: op.node.marks?.filter((mark) => !annotationsEnding.includes(mark)) ?? []
            });
            return;
          }
          const annotationsStarting = nextSpanAnnotations?.filter((annotation) => !previousSpanAnnotations?.includes(annotation)) ?? [], atTheStartOfAnnotation = annotationsStarting.length > 0;
          if (atTheStartOfAnnotation && isSpan(editorActor.getSnapshot().context, op.node) && op.node.marks?.some((mark) => annotationsStarting.includes(mark))) {
            Transforms.insertNodes(editor, {
              ...op.node,
              _key: editorActor.getSnapshot().context.keyGenerator(),
              marks: op.node.marks?.filter((mark) => !annotationsStarting.includes(mark)) ?? []
            });
            return;
          }
          const nextSpanDecorators = nextSpan?.marks?.filter((mark) => decorators.includes(mark)) ?? [];
          if (nextSpanDecorators.length > 0 && atTheEndOfAnnotation && !atTheStartOfAnnotation && isSpan(editorActor.getSnapshot().context, op.node) && op.node.marks?.length === 0) {
            Transforms.insertNodes(editor, {
              ...op.node,
              _key: editorActor.getSnapshot().context.keyGenerator(),
              marks: nextSpanDecorators
            });
            return;
          }
        }
      }
      if (op.type === "insert_text") {
        const snapshot = getEditorSnapshot({
          editorActorSnapshot: editorActor.getSnapshot(),
          slateEditorInstance: editor
        }), markState = getMarkState(snapshot);
        if (!markState) {
          apply2(op);
          return;
        }
        if (markState.state === "unchanged") {
          apply2(op);
          return;
        }
        Transforms.insertNodes(editor, {
          _type: "span",
          _key: editorActor.getSnapshot().context.keyGenerator(),
          text: op.text,
          marks: markState.marks
        });
        return;
      }
      if (op.type === "remove_text") {
        const {
          selection
        } = editor;
        if (selection && Range.isExpanded(selection)) {
          const [block, blockPath] = Editor.node(editor, selection, {
            depth: 1
          }), [span, spanPath] = Array.from(Editor.nodes(editor, {
            mode: "lowest",
            at: {
              path: op.path,
              offset: op.offset
            },
            match: (n) => editor.isTextSpan(n),
            voids: !1
          }))[0] ?? [void 0, void 0];
          if (span && block && isTextBlock(editorActor.getSnapshot().context, block)) {
            const markDefs = block.markDefs ?? [], marks = span.marks ?? [], spanHasAnnotations = marks.some((mark) => markDefs.find((markDef) => markDef._key === mark)), deletingFromTheEnd = op.offset + op.text.length === span.text.length, deletingAllText = op.offset === 0 && deletingFromTheEnd, previousSpan = getPreviousSpan({
              editor,
              blockPath,
              spanPath
            }), nextSpan = getNextSpan({
              editor,
              blockPath,
              spanPath
            }), previousSpanHasSameAnnotation = previousSpan ? previousSpan.marks?.some((mark) => !decorators.includes(mark) && marks.includes(mark)) : !1, nextSpanHasSameAnnotation = nextSpan ? nextSpan.marks?.some((mark) => !decorators.includes(mark) && marks.includes(mark)) : !1;
            if (spanHasAnnotations && deletingAllText && !previousSpanHasSameAnnotation && !nextSpanHasSameAnnotation) {
              const snapshot = getEditorSnapshot({
                editorActorSnapshot: editorActor.getSnapshot(),
                slateEditorInstance: editor
              });
              Editor.withoutNormalizing(editor, () => {
                apply2(op), Transforms.setNodes(editor, {
                  marks: getActiveDecorators(snapshot)
                }, {
                  at: op.path
                });
              }), editor.onChange();
              return;
            }
          }
        }
      }
      if (op.type === "merge_node" && op.path.length === 1 && "markDefs" in op.properties && op.properties._type === editorActor.getSnapshot().context.schema.block.name && Array.isArray(op.properties.markDefs) && op.properties.markDefs.length > 0 && op.path[0] - 1 >= 0) {
        const [targetBlock, targetPath] = Editor.node(editor, [op.path[0] - 1]);
        if (editor.isTextBlock(targetBlock)) {
          const oldDefs = Array.isArray(targetBlock.markDefs) && targetBlock.markDefs || [], newMarkDefs = uniq([...oldDefs, ...op.properties.markDefs]);
          debug$e("Copying markDefs over to merged block", op), Transforms.setNodes(editor, {
            markDefs: newMarkDefs
          }, {
            at: targetPath,
            voids: !1
          }), apply2(op);
          return;
        }
      }
      apply2(op);
    }, editor;
  };
}
const removeDecoratorOperationImplementation = ({
  operation
}) => {
  const editor = operation.editor, mark = operation.decorator, {
    selection
  } = editor;
  if (selection) {
    if (Range.isExpanded(selection))
      Transforms.setNodes(editor, {}, {
        match: Text.isText,
        split: !0,
        hanging: !0
      }), editor.selection && [...Editor.nodes(editor, {
        at: editor.selection,
        match: Text.isText
      })].forEach(([node, path]) => {
        const block = editor.children[path[0]];
        Element$1.isElement(block) && block.children.includes(node) && Transforms.setNodes(editor, {
          marks: (Array.isArray(node.marks) ? node.marks : []).filter((eMark) => eMark !== mark),
          _type: "span"
        }, {
          at: path
        });
      });
    else {
      const [block, blockPath] = Editor.node(editor, selection, {
        depth: 1
      }), lonelyEmptySpan = editor.isTextBlock(block) && block.children.length === 1 && editor.isTextSpan(block.children[0]) && block.children[0].text === "" ? block.children[0] : void 0;
      if (lonelyEmptySpan) {
        const existingMarksWithoutDecorator = (lonelyEmptySpan.marks ?? []).filter((existingMark) => existingMark !== mark);
        Transforms.setNodes(editor, {
          marks: existingMarksWithoutDecorator
        }, {
          at: blockPath,
          match: (node) => editor.isTextSpan(node)
        });
      } else
        editor.decoratorState[mark] = !1;
    }
    if (editor.selection) {
      const selection2 = editor.selection;
      editor.selection = {
        ...selection2
      };
    }
  }
};
function cloneDiff(diff2) {
  const [type, patch] = diff2;
  return [type, patch];
}
function getCommonOverlap(textA, textB) {
  let text1 = textA, text2 = textB;
  const text1Length = text1.length, text2Length = text2.length;
  if (text1Length === 0 || text2Length === 0) return 0;
  text1Length > text2Length ? text1 = text1.substring(text1Length - text2Length) : text1Length < text2Length && (text2 = text2.substring(0, text1Length));
  const textLength = Math.min(text1Length, text2Length);
  if (text1 === text2) return textLength;
  let best = 0, length = 1;
  for (let found = 0; found !== -1; ) {
    const pattern = text1.substring(textLength - length);
    if (found = text2.indexOf(pattern), found === -1) return best;
    length += found, (found === 0 || text1.substring(textLength - length) === text2.substring(0, length)) && (best = length, length++);
  }
  return best;
}
function getCommonPrefix(text1, text2) {
  if (!text1 || !text2 || text1[0] !== text2[0]) return 0;
  let pointerMin = 0, pointerMax = Math.min(text1.length, text2.length), pointerMid = pointerMax, pointerStart = 0;
  for (; pointerMin < pointerMid; ) text1.substring(pointerStart, pointerMid) === text2.substring(pointerStart, pointerMid) ? (pointerMin = pointerMid, pointerStart = pointerMin) : pointerMax = pointerMid, pointerMid = Math.floor((pointerMax - pointerMin) / 2 + pointerMin);
  return pointerMid;
}
function getCommonSuffix(text1, text2) {
  if (!text1 || !text2 || text1[text1.length - 1] !== text2[text2.length - 1]) return 0;
  let pointerMin = 0, pointerMax = Math.min(text1.length, text2.length), pointerMid = pointerMax, pointerEnd = 0;
  for (; pointerMin < pointerMid; ) text1.substring(text1.length - pointerMid, text1.length - pointerEnd) === text2.substring(text2.length - pointerMid, text2.length - pointerEnd) ? (pointerMin = pointerMid, pointerEnd = pointerMin) : pointerMax = pointerMid, pointerMid = Math.floor((pointerMax - pointerMin) / 2 + pointerMin);
  return pointerMid;
}
function isHighSurrogate(char) {
  const charCode = char.charCodeAt(0);
  return charCode >= 55296 && charCode <= 56319;
}
function isLowSurrogate(char) {
  const charCode = char.charCodeAt(0);
  return charCode >= 56320 && charCode <= 57343;
}
function bisect(text1, text2, deadline) {
  const text1Length = text1.length, text2Length = text2.length, maxD = Math.ceil((text1Length + text2Length) / 2), vOffset = maxD, vLength = 2 * maxD, v1 = new Array(vLength), v2 = new Array(vLength);
  for (let x = 0; x < vLength; x++) v1[x] = -1, v2[x] = -1;
  v1[vOffset + 1] = 0, v2[vOffset + 1] = 0;
  const delta = text1Length - text2Length, front = delta % 2 !== 0;
  let k1start = 0, k1end = 0, k2start = 0, k2end = 0;
  for (let d = 0; d < maxD && !(Date.now() > deadline); d++) {
    for (let k1 = -d + k1start; k1 <= d - k1end; k1 += 2) {
      const k1Offset = vOffset + k1;
      let x1;
      k1 === -d || k1 !== d && v1[k1Offset - 1] < v1[k1Offset + 1] ? x1 = v1[k1Offset + 1] : x1 = v1[k1Offset - 1] + 1;
      let y1 = x1 - k1;
      for (; x1 < text1Length && y1 < text2Length && text1.charAt(x1) === text2.charAt(y1); ) x1++, y1++;
      if (v1[k1Offset] = x1, x1 > text1Length) k1end += 2;
      else if (y1 > text2Length) k1start += 2;
      else if (front) {
        const k2Offset = vOffset + delta - k1;
        if (k2Offset >= 0 && k2Offset < vLength && v2[k2Offset] !== -1) {
          const x2 = text1Length - v2[k2Offset];
          if (x1 >= x2) return bisectSplit(text1, text2, x1, y1, deadline);
        }
      }
    }
    for (let k2 = -d + k2start; k2 <= d - k2end; k2 += 2) {
      const k2Offset = vOffset + k2;
      let x2;
      k2 === -d || k2 !== d && v2[k2Offset - 1] < v2[k2Offset + 1] ? x2 = v2[k2Offset + 1] : x2 = v2[k2Offset - 1] + 1;
      let y2 = x2 - k2;
      for (; x2 < text1Length && y2 < text2Length && text1.charAt(text1Length - x2 - 1) === text2.charAt(text2Length - y2 - 1); ) x2++, y2++;
      if (v2[k2Offset] = x2, x2 > text1Length) k2end += 2;
      else if (y2 > text2Length) k2start += 2;
      else if (!front) {
        const k1Offset = vOffset + delta - k2;
        if (k1Offset >= 0 && k1Offset < vLength && v1[k1Offset] !== -1) {
          const x1 = v1[k1Offset], y1 = vOffset + x1 - k1Offset;
          if (x2 = text1Length - x2, x1 >= x2) return bisectSplit(text1, text2, x1, y1, deadline);
        }
      }
    }
  }
  return [[DIFF_DELETE, text1], [DIFF_INSERT, text2]];
}
function bisectSplit(text1, text2, x, y, deadline) {
  const text1a = text1.substring(0, x), text2a = text2.substring(0, y), text1b = text1.substring(x), text2b = text2.substring(y), diffs = doDiff(text1a, text2a, {
    checkLines: !1,
    deadline
  }), diffsb = doDiff(text1b, text2b, {
    checkLines: !1,
    deadline
  });
  return diffs.concat(diffsb);
}
function findHalfMatch(text1, text2, timeout = 1) {
  if (timeout <= 0) return null;
  const longText = text1.length > text2.length ? text1 : text2, shortText = text1.length > text2.length ? text2 : text1;
  if (longText.length < 4 || shortText.length * 2 < longText.length) return null;
  const halfMatch1 = halfMatchI(longText, shortText, Math.ceil(longText.length / 4)), halfMatch2 = halfMatchI(longText, shortText, Math.ceil(longText.length / 2));
  let halfMatch;
  if (halfMatch1 && halfMatch2) halfMatch = halfMatch1[4].length > halfMatch2[4].length ? halfMatch1 : halfMatch2;
  else {
    if (!halfMatch1 && !halfMatch2) return null;
    halfMatch2 ? halfMatch1 || (halfMatch = halfMatch2) : halfMatch = halfMatch1;
  }
  if (!halfMatch) throw new Error("Unable to find a half match.");
  let text1A, text1B, text2A, text2B;
  text1.length > text2.length ? (text1A = halfMatch[0], text1B = halfMatch[1], text2A = halfMatch[2], text2B = halfMatch[3]) : (text2A = halfMatch[0], text2B = halfMatch[1], text1A = halfMatch[2], text1B = halfMatch[3]);
  const midCommon = halfMatch[4];
  return [text1A, text1B, text2A, text2B, midCommon];
}
function halfMatchI(longText, shortText, i) {
  const seed = longText.slice(i, i + Math.floor(longText.length / 4));
  let j = -1, bestCommon = "", bestLongTextA, bestLongTextB, bestShortTextA, bestShortTextB;
  for (; (j = shortText.indexOf(seed, j + 1)) !== -1; ) {
    const prefixLength = getCommonPrefix(longText.slice(i), shortText.slice(j)), suffixLength = getCommonSuffix(longText.slice(0, i), shortText.slice(0, j));
    bestCommon.length < suffixLength + prefixLength && (bestCommon = shortText.slice(j - suffixLength, j) + shortText.slice(j, j + prefixLength), bestLongTextA = longText.slice(0, i - suffixLength), bestLongTextB = longText.slice(i + prefixLength), bestShortTextA = shortText.slice(0, j - suffixLength), bestShortTextB = shortText.slice(j + prefixLength));
  }
  return bestCommon.length * 2 >= longText.length ? [bestLongTextA || "", bestLongTextB || "", bestShortTextA || "", bestShortTextB || "", bestCommon || ""] : null;
}
function charsToLines(diffs, lineArray) {
  for (let x = 0; x < diffs.length; x++) {
    const chars = diffs[x][1], text = [];
    for (let y = 0; y < chars.length; y++) text[y] = lineArray[chars.charCodeAt(y)];
    diffs[x][1] = text.join("");
  }
}
function linesToChars(textA, textB) {
  const lineArray = [], lineHash = {};
  lineArray[0] = "";
  function diffLinesToMunge(text) {
    let chars = "", lineStart = 0, lineEnd = -1, lineArrayLength = lineArray.length;
    for (; lineEnd < text.length - 1; ) {
      lineEnd = text.indexOf(`
`, lineStart), lineEnd === -1 && (lineEnd = text.length - 1);
      let line = text.slice(lineStart, lineEnd + 1);
      (lineHash.hasOwnProperty ? lineHash.hasOwnProperty(line) : lineHash[line] !== void 0) ? chars += String.fromCharCode(lineHash[line]) : (lineArrayLength === maxLines && (line = text.slice(lineStart), lineEnd = text.length), chars += String.fromCharCode(lineArrayLength), lineHash[line] = lineArrayLength, lineArray[lineArrayLength++] = line), lineStart = lineEnd + 1;
    }
    return chars;
  }
  let maxLines = 4e4;
  const chars1 = diffLinesToMunge(textA);
  maxLines = 65535;
  const chars2 = diffLinesToMunge(textB);
  return {
    chars1,
    chars2,
    lineArray
  };
}
function doLineModeDiff(textA, textB, opts) {
  let text1 = textA, text2 = textB;
  const a = linesToChars(text1, text2);
  text1 = a.chars1, text2 = a.chars2;
  const linearray = a.lineArray;
  let diffs = doDiff(text1, text2, {
    checkLines: !1,
    deadline: opts.deadline
  });
  charsToLines(diffs, linearray), diffs = cleanupSemantic(diffs), diffs.push([DIFF_EQUAL, ""]);
  let pointer = 0, countDelete = 0, countInsert = 0, textDelete = "", textInsert = "";
  for (; pointer < diffs.length; ) {
    switch (diffs[pointer][0]) {
      case DIFF_INSERT:
        countInsert++, textInsert += diffs[pointer][1];
        break;
      case DIFF_DELETE:
        countDelete++, textDelete += diffs[pointer][1];
        break;
      case DIFF_EQUAL:
        if (countDelete >= 1 && countInsert >= 1) {
          diffs.splice(pointer - countDelete - countInsert, countDelete + countInsert), pointer = pointer - countDelete - countInsert;
          const aa = doDiff(textDelete, textInsert, {
            checkLines: !1,
            deadline: opts.deadline
          });
          for (let j = aa.length - 1; j >= 0; j--) diffs.splice(pointer, 0, aa[j]);
          pointer += aa.length;
        }
        countInsert = 0, countDelete = 0, textDelete = "", textInsert = "";
        break;
      default:
        throw new Error("Unknown diff operation.");
    }
    pointer++;
  }
  return diffs.pop(), diffs;
}
function computeDiff(text1, text2, opts) {
  let diffs;
  if (!text1) return [[DIFF_INSERT, text2]];
  if (!text2) return [[DIFF_DELETE, text1]];
  const longtext = text1.length > text2.length ? text1 : text2, shorttext = text1.length > text2.length ? text2 : text1, i = longtext.indexOf(shorttext);
  if (i !== -1) return diffs = [[DIFF_INSERT, longtext.substring(0, i)], [DIFF_EQUAL, shorttext], [DIFF_INSERT, longtext.substring(i + shorttext.length)]], text1.length > text2.length && (diffs[0][0] = DIFF_DELETE, diffs[2][0] = DIFF_DELETE), diffs;
  if (shorttext.length === 1) return [[DIFF_DELETE, text1], [DIFF_INSERT, text2]];
  const halfMatch = findHalfMatch(text1, text2);
  if (halfMatch) {
    const text1A = halfMatch[0], text1B = halfMatch[1], text2A = halfMatch[2], text2B = halfMatch[3], midCommon = halfMatch[4], diffsA = doDiff(text1A, text2A, opts), diffsB = doDiff(text1B, text2B, opts);
    return diffsA.concat([[DIFF_EQUAL, midCommon]], diffsB);
  }
  return opts.checkLines && text1.length > 100 && text2.length > 100 ? doLineModeDiff(text1, text2, opts) : bisect(text1, text2, opts.deadline);
}
var __defProp$2 = Object.defineProperty, __getOwnPropSymbols$2 = Object.getOwnPropertySymbols, __hasOwnProp$2 = Object.prototype.hasOwnProperty, __propIsEnum$2 = Object.prototype.propertyIsEnumerable, __defNormalProp$2 = (obj, key, value) => key in obj ? __defProp$2(obj, key, {
  enumerable: !0,
  configurable: !0,
  writable: !0,
  value
}) : obj[key] = value, __spreadValues$2 = (a, b) => {
  for (var prop in b || (b = {})) __hasOwnProp$2.call(b, prop) && __defNormalProp$2(a, prop, b[prop]);
  if (__getOwnPropSymbols$2) for (var prop of __getOwnPropSymbols$2(b)) __propIsEnum$2.call(b, prop) && __defNormalProp$2(a, prop, b[prop]);
  return a;
};
const DIFF_DELETE = -1, DIFF_INSERT = 1, DIFF_EQUAL = 0;
function diff(textA, textB, opts) {
  if (textA === null || textB === null) throw new Error("Null input. (diff)");
  const diffs = doDiff(textA, textB, createInternalOpts(opts || {}));
  return adjustDiffForSurrogatePairs(diffs), diffs;
}
function doDiff(textA, textB, options) {
  let text1 = textA, text2 = textB;
  if (text1 === text2) return text1 ? [[DIFF_EQUAL, text1]] : [];
  let commonlength = getCommonPrefix(text1, text2);
  const commonprefix = text1.substring(0, commonlength);
  text1 = text1.substring(commonlength), text2 = text2.substring(commonlength), commonlength = getCommonSuffix(text1, text2);
  const commonsuffix = text1.substring(text1.length - commonlength);
  text1 = text1.substring(0, text1.length - commonlength), text2 = text2.substring(0, text2.length - commonlength);
  let diffs = computeDiff(text1, text2, options);
  return commonprefix && diffs.unshift([DIFF_EQUAL, commonprefix]), commonsuffix && diffs.push([DIFF_EQUAL, commonsuffix]), diffs = cleanupMerge(diffs), diffs;
}
function createDeadLine(timeout) {
  let t = 1;
  return typeof timeout < "u" && (t = timeout <= 0 ? Number.MAX_VALUE : timeout), Date.now() + t * 1e3;
}
function createInternalOpts(opts) {
  return __spreadValues$2({
    checkLines: !0,
    deadline: createDeadLine(opts.timeout || 1)
  }, opts);
}
function combineChar(data, char, dir) {
  return dir === 1 ? data + char : char + data;
}
function splitChar(data, dir) {
  return dir === 1 ? [data.substring(0, data.length - 1), data[data.length - 1]] : [data.substring(1), data[0]];
}
function hasSharedChar(diffs, i, j, dir) {
  return dir === 1 ? diffs[i][1][diffs[i][1].length - 1] === diffs[j][1][diffs[j][1].length - 1] : diffs[i][1][0] === diffs[j][1][0];
}
function deisolateChar(diffs, i, dir) {
  const inv = dir === 1 ? -1 : 1;
  let insertIdx = null, deleteIdx = null, j = i + dir;
  for (; j >= 0 && j < diffs.length && (insertIdx === null || deleteIdx === null); j += dir) {
    const [op, text2] = diffs[j];
    if (text2.length !== 0) {
      if (op === DIFF_INSERT) {
        insertIdx === null && (insertIdx = j);
        continue;
      } else if (op === DIFF_DELETE) {
        deleteIdx === null && (deleteIdx = j);
        continue;
      } else if (op === DIFF_EQUAL) {
        if (insertIdx === null && deleteIdx === null) {
          const [rest, char2] = splitChar(diffs[i][1], dir);
          diffs[i][1] = rest, diffs[j][1] = combineChar(diffs[j][1], char2, inv);
          return;
        }
        break;
      }
    }
  }
  if (insertIdx !== null && deleteIdx !== null && hasSharedChar(diffs, insertIdx, deleteIdx, dir)) {
    const [insertText, insertChar] = splitChar(diffs[insertIdx][1], inv), [deleteText2] = splitChar(diffs[deleteIdx][1], inv);
    diffs[insertIdx][1] = insertText, diffs[deleteIdx][1] = deleteText2, diffs[i][1] = combineChar(diffs[i][1], insertChar, dir);
    return;
  }
  const [text, char] = splitChar(diffs[i][1], dir);
  diffs[i][1] = text, insertIdx === null ? (diffs.splice(j, 0, [DIFF_INSERT, char]), deleteIdx !== null && deleteIdx >= j && deleteIdx++) : diffs[insertIdx][1] = combineChar(diffs[insertIdx][1], char, inv), deleteIdx === null ? diffs.splice(j, 0, [DIFF_DELETE, char]) : diffs[deleteIdx][1] = combineChar(diffs[deleteIdx][1], char, inv);
}
function adjustDiffForSurrogatePairs(diffs) {
  for (let i = 0; i < diffs.length; i++) {
    const [diffType, diffText] = diffs[i];
    if (diffText.length === 0) continue;
    const firstChar = diffText[0], lastChar = diffText[diffText.length - 1];
    isHighSurrogate(lastChar) && diffType === DIFF_EQUAL && deisolateChar(diffs, i, 1), isLowSurrogate(firstChar) && diffType === DIFF_EQUAL && deisolateChar(diffs, i, -1);
  }
  for (let i = 0; i < diffs.length; i++) diffs[i][1].length === 0 && diffs.splice(i, 1);
}
function cleanupSemantic(rawDiffs) {
  let diffs = rawDiffs.map((diff2) => cloneDiff(diff2)), hasChanges = !1;
  const equalities = [];
  let equalitiesLength = 0, lastEquality = null, pointer = 0, lengthInsertions1 = 0, lengthDeletions1 = 0, lengthInsertions2 = 0, lengthDeletions2 = 0;
  for (; pointer < diffs.length; ) diffs[pointer][0] === DIFF_EQUAL ? (equalities[equalitiesLength++] = pointer, lengthInsertions1 = lengthInsertions2, lengthDeletions1 = lengthDeletions2, lengthInsertions2 = 0, lengthDeletions2 = 0, lastEquality = diffs[pointer][1]) : (diffs[pointer][0] === DIFF_INSERT ? lengthInsertions2 += diffs[pointer][1].length : lengthDeletions2 += diffs[pointer][1].length, lastEquality && lastEquality.length <= Math.max(lengthInsertions1, lengthDeletions1) && lastEquality.length <= Math.max(lengthInsertions2, lengthDeletions2) && (diffs.splice(equalities[equalitiesLength - 1], 0, [DIFF_DELETE, lastEquality]), diffs[equalities[equalitiesLength - 1] + 1][0] = DIFF_INSERT, equalitiesLength--, equalitiesLength--, pointer = equalitiesLength > 0 ? equalities[equalitiesLength - 1] : -1, lengthInsertions1 = 0, lengthDeletions1 = 0, lengthInsertions2 = 0, lengthDeletions2 = 0, lastEquality = null, hasChanges = !0)), pointer++;
  for (hasChanges && (diffs = cleanupMerge(diffs)), diffs = cleanupSemanticLossless(diffs), pointer = 1; pointer < diffs.length; ) {
    if (diffs[pointer - 1][0] === DIFF_DELETE && diffs[pointer][0] === DIFF_INSERT) {
      const deletion = diffs[pointer - 1][1], insertion = diffs[pointer][1], overlapLength1 = getCommonOverlap(deletion, insertion), overlapLength2 = getCommonOverlap(insertion, deletion);
      overlapLength1 >= overlapLength2 ? (overlapLength1 >= deletion.length / 2 || overlapLength1 >= insertion.length / 2) && (diffs.splice(pointer, 0, [DIFF_EQUAL, insertion.substring(0, overlapLength1)]), diffs[pointer - 1][1] = deletion.substring(0, deletion.length - overlapLength1), diffs[pointer + 1][1] = insertion.substring(overlapLength1), pointer++) : (overlapLength2 >= deletion.length / 2 || overlapLength2 >= insertion.length / 2) && (diffs.splice(pointer, 0, [DIFF_EQUAL, deletion.substring(0, overlapLength2)]), diffs[pointer - 1][0] = DIFF_INSERT, diffs[pointer - 1][1] = insertion.substring(0, insertion.length - overlapLength2), diffs[pointer + 1][0] = DIFF_DELETE, diffs[pointer + 1][1] = deletion.substring(overlapLength2), pointer++), pointer++;
    }
    pointer++;
  }
  return diffs;
}
const nonAlphaNumericRegex = /[^a-zA-Z0-9]/, whitespaceRegex = /\s/, linebreakRegex = /[\r\n]/, blanklineEndRegex = /\n\r?\n$/, blanklineStartRegex = /^\r?\n\r?\n/;
function cleanupSemanticLossless(rawDiffs) {
  const diffs = rawDiffs.map((diff2) => cloneDiff(diff2));
  function diffCleanupSemanticScore(one, two) {
    if (!one || !two) return 6;
    const char1 = one.charAt(one.length - 1), char2 = two.charAt(0), nonAlphaNumeric1 = char1.match(nonAlphaNumericRegex), nonAlphaNumeric2 = char2.match(nonAlphaNumericRegex), whitespace1 = nonAlphaNumeric1 && char1.match(whitespaceRegex), whitespace2 = nonAlphaNumeric2 && char2.match(whitespaceRegex), lineBreak1 = whitespace1 && char1.match(linebreakRegex), lineBreak2 = whitespace2 && char2.match(linebreakRegex), blankLine1 = lineBreak1 && one.match(blanklineEndRegex), blankLine2 = lineBreak2 && two.match(blanklineStartRegex);
    return blankLine1 || blankLine2 ? 5 : lineBreak1 || lineBreak2 ? 4 : nonAlphaNumeric1 && !whitespace1 && whitespace2 ? 3 : whitespace1 || whitespace2 ? 2 : nonAlphaNumeric1 || nonAlphaNumeric2 ? 1 : 0;
  }
  let pointer = 1;
  for (; pointer < diffs.length - 1; ) {
    if (diffs[pointer - 1][0] === DIFF_EQUAL && diffs[pointer + 1][0] === DIFF_EQUAL) {
      let equality1 = diffs[pointer - 1][1], edit = diffs[pointer][1], equality2 = diffs[pointer + 1][1];
      const commonOffset = getCommonSuffix(equality1, edit);
      if (commonOffset) {
        const commonString = edit.substring(edit.length - commonOffset);
        equality1 = equality1.substring(0, equality1.length - commonOffset), edit = commonString + edit.substring(0, edit.length - commonOffset), equality2 = commonString + equality2;
      }
      let bestEquality1 = equality1, bestEdit = edit, bestEquality2 = equality2, bestScore = diffCleanupSemanticScore(equality1, edit) + diffCleanupSemanticScore(edit, equality2);
      for (; edit.charAt(0) === equality2.charAt(0); ) {
        equality1 += edit.charAt(0), edit = edit.substring(1) + equality2.charAt(0), equality2 = equality2.substring(1);
        const score = diffCleanupSemanticScore(equality1, edit) + diffCleanupSemanticScore(edit, equality2);
        score >= bestScore && (bestScore = score, bestEquality1 = equality1, bestEdit = edit, bestEquality2 = equality2);
      }
      diffs[pointer - 1][1] !== bestEquality1 && (bestEquality1 ? diffs[pointer - 1][1] = bestEquality1 : (diffs.splice(pointer - 1, 1), pointer--), diffs[pointer][1] = bestEdit, bestEquality2 ? diffs[pointer + 1][1] = bestEquality2 : (diffs.splice(pointer + 1, 1), pointer--));
    }
    pointer++;
  }
  return diffs;
}
function cleanupMerge(rawDiffs) {
  let diffs = rawDiffs.map((diff2) => cloneDiff(diff2));
  diffs.push([DIFF_EQUAL, ""]);
  let pointer = 0, countDelete = 0, countInsert = 0, textDelete = "", textInsert = "", commonlength;
  for (; pointer < diffs.length; ) switch (diffs[pointer][0]) {
    case DIFF_INSERT:
      countInsert++, textInsert += diffs[pointer][1], pointer++;
      break;
    case DIFF_DELETE:
      countDelete++, textDelete += diffs[pointer][1], pointer++;
      break;
    case DIFF_EQUAL:
      countDelete + countInsert > 1 ? (countDelete !== 0 && countInsert !== 0 && (commonlength = getCommonPrefix(textInsert, textDelete), commonlength !== 0 && (pointer - countDelete - countInsert > 0 && diffs[pointer - countDelete - countInsert - 1][0] === DIFF_EQUAL ? diffs[pointer - countDelete - countInsert - 1][1] += textInsert.substring(0, commonlength) : (diffs.splice(0, 0, [DIFF_EQUAL, textInsert.substring(0, commonlength)]), pointer++), textInsert = textInsert.substring(commonlength), textDelete = textDelete.substring(commonlength)), commonlength = getCommonSuffix(textInsert, textDelete), commonlength !== 0 && (diffs[pointer][1] = textInsert.substring(textInsert.length - commonlength) + diffs[pointer][1], textInsert = textInsert.substring(0, textInsert.length - commonlength), textDelete = textDelete.substring(0, textDelete.length - commonlength))), pointer -= countDelete + countInsert, diffs.splice(pointer, countDelete + countInsert), textDelete.length && (diffs.splice(pointer, 0, [DIFF_DELETE, textDelete]), pointer++), textInsert.length && (diffs.splice(pointer, 0, [DIFF_INSERT, textInsert]), pointer++), pointer++) : pointer !== 0 && diffs[pointer - 1][0] === DIFF_EQUAL ? (diffs[pointer - 1][1] += diffs[pointer][1], diffs.splice(pointer, 1)) : pointer++, countInsert = 0, countDelete = 0, textDelete = "", textInsert = "";
      break;
    default:
      throw new Error("Unknown diff operation");
  }
  diffs[diffs.length - 1][1] === "" && diffs.pop();
  let hasChanges = !1;
  for (pointer = 1; pointer < diffs.length - 1; ) diffs[pointer - 1][0] === DIFF_EQUAL && diffs[pointer + 1][0] === DIFF_EQUAL && (diffs[pointer][1].substring(diffs[pointer][1].length - diffs[pointer - 1][1].length) === diffs[pointer - 1][1] ? (diffs[pointer][1] = diffs[pointer - 1][1] + diffs[pointer][1].substring(0, diffs[pointer][1].length - diffs[pointer - 1][1].length), diffs[pointer + 1][1] = diffs[pointer - 1][1] + diffs[pointer + 1][1], diffs.splice(pointer - 1, 1), hasChanges = !0) : diffs[pointer][1].substring(0, diffs[pointer + 1][1].length) === diffs[pointer + 1][1] && (diffs[pointer - 1][1] += diffs[pointer + 1][1], diffs[pointer][1] = diffs[pointer][1].substring(diffs[pointer + 1][1].length) + diffs[pointer + 1][1], diffs.splice(pointer + 1, 1), hasChanges = !0)), pointer++;
  return hasChanges && (diffs = cleanupMerge(diffs)), diffs;
}
function trueCount(...args) {
  return args.reduce((n, bool) => n + (bool ? 1 : 0), 0);
}
function cleanupEfficiency(rawDiffs, editCost = 4) {
  let diffs = rawDiffs.map((diff2) => cloneDiff(diff2)), hasChanges = !1;
  const equalities = [];
  let equalitiesLength = 0, lastEquality = null, pointer = 0, preIns = !1, preDel = !1, postIns = !1, postDel = !1;
  for (; pointer < diffs.length; ) diffs[pointer][0] === DIFF_EQUAL ? (diffs[pointer][1].length < editCost && (postIns || postDel) ? (equalities[equalitiesLength++] = pointer, preIns = postIns, preDel = postDel, lastEquality = diffs[pointer][1]) : (equalitiesLength = 0, lastEquality = null), postIns = !1, postDel = !1) : (diffs[pointer][0] === DIFF_DELETE ? postDel = !0 : postIns = !0, lastEquality && (preIns && preDel && postIns && postDel || lastEquality.length < editCost / 2 && trueCount(preIns, preDel, postIns, postDel) === 3) && (diffs.splice(equalities[equalitiesLength - 1], 0, [DIFF_DELETE, lastEquality]), diffs[equalities[equalitiesLength - 1] + 1][0] = DIFF_INSERT, equalitiesLength--, lastEquality = null, preIns && preDel ? (postIns = !0, postDel = !0, equalitiesLength = 0) : (equalitiesLength--, pointer = equalitiesLength > 0 ? equalities[equalitiesLength - 1] : -1, postIns = !1, postDel = !1), hasChanges = !0)), pointer++;
  return hasChanges && (diffs = cleanupMerge(diffs)), diffs;
}
var __defProp$1 = Object.defineProperty, __getOwnPropSymbols$1 = Object.getOwnPropertySymbols, __hasOwnProp$1 = Object.prototype.hasOwnProperty, __propIsEnum$1 = Object.prototype.propertyIsEnumerable, __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, {
  enumerable: !0,
  configurable: !0,
  writable: !0,
  value
}) : obj[key] = value, __spreadValues$1 = (a, b) => {
  for (var prop in b || (b = {})) __hasOwnProp$1.call(b, prop) && __defNormalProp$1(a, prop, b[prop]);
  if (__getOwnPropSymbols$1) for (var prop of __getOwnPropSymbols$1(b)) __propIsEnum$1.call(b, prop) && __defNormalProp$1(a, prop, b[prop]);
  return a;
};
const DEFAULT_OPTIONS = {
  /**
   * At what point is no match declared (0.0 = perfection, 1.0 = very loose).
   */
  threshold: 0.5,
  /**
   * How far to search for a match (0 = exact location, 1000+ = broad match).
   * A match this many characters away from the expected location will add
   * 1.0 to the score (0.0 is a perfect match).
   */
  distance: 1e3
};
function applyDefaults(options) {
  return __spreadValues$1(__spreadValues$1({}, DEFAULT_OPTIONS), options);
}
const MAX_BITS$1 = 32;
function bitap(text, pattern, loc, opts = {}) {
  if (pattern.length > MAX_BITS$1) throw new Error("Pattern too long for this browser.");
  const options = applyDefaults(opts), s = getAlphabetFromPattern(pattern);
  function getBitapScore(e, x) {
    const accuracy = e / pattern.length, proximity = Math.abs(loc - x);
    return options.distance ? accuracy + proximity / options.distance : proximity ? 1 : accuracy;
  }
  let scoreThreshold = options.threshold, bestLoc = text.indexOf(pattern, loc);
  bestLoc !== -1 && (scoreThreshold = Math.min(getBitapScore(0, bestLoc), scoreThreshold), bestLoc = text.lastIndexOf(pattern, loc + pattern.length), bestLoc !== -1 && (scoreThreshold = Math.min(getBitapScore(0, bestLoc), scoreThreshold)));
  const matchmask = 1 << pattern.length - 1;
  bestLoc = -1;
  let binMin, binMid, binMax = pattern.length + text.length, lastRd = [];
  for (let d = 0; d < pattern.length; d++) {
    for (binMin = 0, binMid = binMax; binMin < binMid; ) getBitapScore(d, loc + binMid) <= scoreThreshold ? binMin = binMid : binMax = binMid, binMid = Math.floor((binMax - binMin) / 2 + binMin);
    binMax = binMid;
    let start = Math.max(1, loc - binMid + 1);
    const finish = Math.min(loc + binMid, text.length) + pattern.length, rd = new Array(finish + 2);
    rd[finish + 1] = (1 << d) - 1;
    for (let j = finish; j >= start; j--) {
      const charMatch = s[text.charAt(j - 1)];
      if (d === 0 ? rd[j] = (rd[j + 1] << 1 | 1) & charMatch : rd[j] = (rd[j + 1] << 1 | 1) & charMatch | ((lastRd[j + 1] | lastRd[j]) << 1 | 1) | lastRd[j + 1], rd[j] & matchmask) {
        const score = getBitapScore(d, j - 1);
        if (score <= scoreThreshold) if (scoreThreshold = score, bestLoc = j - 1, bestLoc > loc) start = Math.max(1, 2 * loc - bestLoc);
        else break;
      }
    }
    if (getBitapScore(d + 1, loc) > scoreThreshold) break;
    lastRd = rd;
  }
  return bestLoc;
}
function getAlphabetFromPattern(pattern) {
  const s = {};
  for (let i = 0; i < pattern.length; i++) s[pattern.charAt(i)] = 0;
  for (let i = 0; i < pattern.length; i++) s[pattern.charAt(i)] |= 1 << pattern.length - i - 1;
  return s;
}
function match(text, pattern, searchLocation, options = {}) {
  if (text === null || pattern === null || searchLocation === null) throw new Error("Null input. (match())");
  const loc = Math.max(0, Math.min(searchLocation, text.length));
  if (text === pattern) return 0;
  if (text.length) {
    if (text.substring(loc, loc + pattern.length) === pattern) return loc;
  } else return -1;
  return bitap(text, pattern, loc, options);
}
function diffText1(diffs) {
  const text = [];
  for (let x = 0; x < diffs.length; x++) diffs[x][0] !== DIFF_INSERT && (text[x] = diffs[x][1]);
  return text.join("");
}
function diffText2(diffs) {
  const text = [];
  for (let x = 0; x < diffs.length; x++) diffs[x][0] !== DIFF_DELETE && (text[x] = diffs[x][1]);
  return text.join("");
}
function levenshtein(diffs) {
  let leven = 0, insertions = 0, deletions = 0;
  for (let x = 0; x < diffs.length; x++) {
    const op = diffs[x][0], data = diffs[x][1];
    switch (op) {
      case DIFF_INSERT:
        insertions += data.length;
        break;
      case DIFF_DELETE:
        deletions += data.length;
        break;
      case DIFF_EQUAL:
        leven += Math.max(insertions, deletions), insertions = 0, deletions = 0;
        break;
      default:
        throw new Error("Unknown diff operation.");
    }
  }
  return leven += Math.max(insertions, deletions), leven;
}
function xIndex(diffs, location) {
  let chars1 = 0, chars2 = 0, lastChars1 = 0, lastChars2 = 0, x;
  for (x = 0; x < diffs.length && (diffs[x][0] !== DIFF_INSERT && (chars1 += diffs[x][1].length), diffs[x][0] !== DIFF_DELETE && (chars2 += diffs[x][1].length), !(chars1 > location)); x++) lastChars1 = chars1, lastChars2 = chars2;
  return diffs.length !== x && diffs[x][0] === DIFF_DELETE ? lastChars2 : lastChars2 + (location - lastChars1);
}
function countUtf8Bytes(str) {
  let bytes = 0;
  for (let i = 0; i < str.length; i++) {
    const codePoint = str.codePointAt(i);
    if (typeof codePoint > "u") throw new Error("Failed to get codepoint");
    bytes += utf8len(codePoint);
  }
  return bytes;
}
function adjustIndiciesToUcs2(patches, base, options = {}) {
  let byteOffset = 0, idx = 0;
  function advanceTo(target) {
    for (; byteOffset < target; ) {
      const codePoint = base.codePointAt(idx);
      if (typeof codePoint > "u") return idx;
      byteOffset += utf8len(codePoint), codePoint > 65535 ? idx += 2 : idx += 1;
    }
    if (!options.allowExceedingIndices && byteOffset !== target) throw new Error("Failed to determine byte offset");
    return idx;
  }
  const adjusted = [];
  for (const patch of patches) adjusted.push({
    diffs: patch.diffs.map((diff2) => cloneDiff(diff2)),
    start1: advanceTo(patch.start1),
    start2: advanceTo(patch.start2),
    utf8Start1: patch.utf8Start1,
    utf8Start2: patch.utf8Start2,
    length1: patch.length1,
    length2: patch.length2,
    utf8Length1: patch.utf8Length1,
    utf8Length2: patch.utf8Length2
  });
  return adjusted;
}
function utf8len(codePoint) {
  return codePoint <= 127 ? 1 : codePoint <= 2047 ? 2 : codePoint <= 65535 ? 3 : 4;
}
const MAX_BITS = 32, DEFAULT_MARGIN = 4;
function addPadding(patches, margin = DEFAULT_MARGIN) {
  const paddingLength = margin;
  let nullPadding = "";
  for (let x = 1; x <= paddingLength; x++) nullPadding += String.fromCharCode(x);
  for (const p of patches) p.start1 += paddingLength, p.start2 += paddingLength, p.utf8Start1 += paddingLength, p.utf8Start2 += paddingLength;
  let patch = patches[0], diffs = patch.diffs;
  if (diffs.length === 0 || diffs[0][0] !== DIFF_EQUAL) diffs.unshift([DIFF_EQUAL, nullPadding]), patch.start1 -= paddingLength, patch.start2 -= paddingLength, patch.utf8Start1 -= paddingLength, patch.utf8Start2 -= paddingLength, patch.length1 += paddingLength, patch.length2 += paddingLength, patch.utf8Length1 += paddingLength, patch.utf8Length2 += paddingLength;
  else if (paddingLength > diffs[0][1].length) {
    const firstDiffLength = diffs[0][1].length, extraLength = paddingLength - firstDiffLength;
    diffs[0][1] = nullPadding.substring(firstDiffLength) + diffs[0][1], patch.start1 -= extraLength, patch.start2 -= extraLength, patch.utf8Start1 -= extraLength, patch.utf8Start2 -= extraLength, patch.length1 += extraLength, patch.length2 += extraLength, patch.utf8Length1 += extraLength, patch.utf8Length2 += extraLength;
  }
  if (patch = patches[patches.length - 1], diffs = patch.diffs, diffs.length === 0 || diffs[diffs.length - 1][0] !== DIFF_EQUAL) diffs.push([DIFF_EQUAL, nullPadding]), patch.length1 += paddingLength, patch.length2 += paddingLength, patch.utf8Length1 += paddingLength, patch.utf8Length2 += paddingLength;
  else if (paddingLength > diffs[diffs.length - 1][1].length) {
    const extraLength = paddingLength - diffs[diffs.length - 1][1].length;
    diffs[diffs.length - 1][1] += nullPadding.substring(0, extraLength), patch.length1 += extraLength, patch.length2 += extraLength, patch.utf8Length1 += extraLength, patch.utf8Length2 += extraLength;
  }
  return nullPadding;
}
function createPatchObject(start1, start2) {
  return {
    diffs: [],
    start1,
    start2,
    utf8Start1: start1,
    utf8Start2: start2,
    length1: 0,
    length2: 0,
    utf8Length1: 0,
    utf8Length2: 0
  };
}
function splitMax(patches, margin = DEFAULT_MARGIN) {
  const patchSize = MAX_BITS;
  for (let x = 0; x < patches.length; x++) {
    if (patches[x].length1 <= patchSize) continue;
    const bigpatch = patches[x];
    patches.splice(x--, 1);
    let start1 = bigpatch.start1, start2 = bigpatch.start2, preContext = "";
    for (; bigpatch.diffs.length !== 0; ) {
      const patch = createPatchObject(start1 - preContext.length, start2 - preContext.length);
      let empty = !0;
      if (preContext !== "") {
        const precontextByteCount = countUtf8Bytes(preContext);
        patch.length1 = preContext.length, patch.utf8Length1 = precontextByteCount, patch.length2 = preContext.length, patch.utf8Length2 = precontextByteCount, patch.diffs.push([DIFF_EQUAL, preContext]);
      }
      for (; bigpatch.diffs.length !== 0 && patch.length1 < patchSize - margin; ) {
        const diffType = bigpatch.diffs[0][0];
        let diffText = bigpatch.diffs[0][1], diffTextByteCount = countUtf8Bytes(diffText);
        if (diffType === DIFF_INSERT) {
          patch.length2 += diffText.length, patch.utf8Length2 += diffTextByteCount, start2 += diffText.length;
          const diff2 = bigpatch.diffs.shift();
          diff2 && patch.diffs.push(diff2), empty = !1;
        } else diffType === DIFF_DELETE && patch.diffs.length === 1 && patch.diffs[0][0] === DIFF_EQUAL && diffText.length > 2 * patchSize ? (patch.length1 += diffText.length, patch.utf8Length1 += diffTextByteCount, start1 += diffText.length, empty = !1, patch.diffs.push([diffType, diffText]), bigpatch.diffs.shift()) : (diffText = diffText.substring(0, patchSize - patch.length1 - margin), diffTextByteCount = countUtf8Bytes(diffText), patch.length1 += diffText.length, patch.utf8Length1 += diffTextByteCount, start1 += diffText.length, diffType === DIFF_EQUAL ? (patch.length2 += diffText.length, patch.utf8Length2 += diffTextByteCount, start2 += diffText.length) : empty = !1, patch.diffs.push([diffType, diffText]), diffText === bigpatch.diffs[0][1] ? bigpatch.diffs.shift() : bigpatch.diffs[0][1] = bigpatch.diffs[0][1].substring(diffText.length));
      }
      preContext = diffText2(patch.diffs), preContext = preContext.substring(preContext.length - margin);
      const postContext = diffText1(bigpatch.diffs).substring(0, margin), postContextByteCount = countUtf8Bytes(postContext);
      postContext !== "" && (patch.length1 += postContext.length, patch.length2 += postContext.length, patch.utf8Length1 += postContextByteCount, patch.utf8Length2 += postContextByteCount, patch.diffs.length !== 0 && patch.diffs[patch.diffs.length - 1][0] === DIFF_EQUAL ? patch.diffs[patch.diffs.length - 1][1] += postContext : patch.diffs.push([DIFF_EQUAL, postContext])), empty || patches.splice(++x, 0, patch);
    }
  }
}
function apply(patches, originalText, opts = {}) {
  if (typeof patches == "string") throw new Error("Patches must be an array - pass the patch to `parsePatch()` first");
  let text = originalText;
  if (patches.length === 0) return [text, []];
  const parsed = adjustIndiciesToUcs2(patches, text, {
    allowExceedingIndices: opts.allowExceedingIndices
  }), margin = opts.margin || DEFAULT_MARGIN, deleteThreshold = opts.deleteThreshold || 0.4, nullPadding = addPadding(parsed, margin);
  text = nullPadding + text + nullPadding, splitMax(parsed, margin);
  let delta = 0;
  const results = [];
  for (let x = 0; x < parsed.length; x++) {
    const expectedLoc = parsed[x].start2 + delta, text1 = diffText1(parsed[x].diffs);
    let startLoc, endLoc = -1;
    if (text1.length > MAX_BITS ? (startLoc = match(text, text1.substring(0, MAX_BITS), expectedLoc), startLoc !== -1 && (endLoc = match(text, text1.substring(text1.length - MAX_BITS), expectedLoc + text1.length - MAX_BITS), (endLoc === -1 || startLoc >= endLoc) && (startLoc = -1))) : startLoc = match(text, text1, expectedLoc), startLoc === -1) results[x] = !1, delta -= parsed[x].length2 - parsed[x].length1;
    else {
      results[x] = !0, delta = startLoc - expectedLoc;
      let text2;
      if (endLoc === -1 ? text2 = text.substring(startLoc, startLoc + text1.length) : text2 = text.substring(startLoc, endLoc + MAX_BITS), text1 === text2) text = text.substring(0, startLoc) + diffText2(parsed[x].diffs) + text.substring(startLoc + text1.length);
      else {
        let diffs = diff(text1, text2, {
          checkLines: !1
        });
        if (text1.length > MAX_BITS && levenshtein(diffs) / text1.length > deleteThreshold) results[x] = !1;
        else {
          diffs = cleanupSemanticLossless(diffs);
          let index1 = 0, index2 = 0;
          for (let y = 0; y < parsed[x].diffs.length; y++) {
            const mod = parsed[x].diffs[y];
            mod[0] !== DIFF_EQUAL && (index2 = xIndex(diffs, index1)), mod[0] === DIFF_INSERT ? text = text.substring(0, startLoc + index2) + mod[1] + text.substring(startLoc + index2) : mod[0] === DIFF_DELETE && (text = text.substring(0, startLoc + index2) + text.substring(startLoc + xIndex(diffs, index1 + mod[1].length))), mod[0] !== DIFF_DELETE && (index1 += mod[1].length);
          }
        }
      }
    }
  }
  return text = text.substring(nullPadding.length, text.length - nullPadding.length), [text, results];
}
const patchHeader = /^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;
function parse(textline) {
  if (!textline) return [];
  const patches = [], lines = textline.split(`
`);
  let textPointer = 0;
  for (; textPointer < lines.length; ) {
    const m = lines[textPointer].match(patchHeader);
    if (!m) throw new Error(`Invalid patch string: ${lines[textPointer]}`);
    const patch = createPatchObject(toInt(m[1]), toInt(m[3]));
    for (patches.push(patch), m[2] === "" ? (patch.start1--, patch.utf8Start1--, patch.length1 = 1, patch.utf8Length1 = 1) : m[2] === "0" ? (patch.length1 = 0, patch.utf8Length1 = 0) : (patch.start1--, patch.utf8Start1--, patch.utf8Length1 = toInt(m[2]), patch.length1 = patch.utf8Length1), m[4] === "" ? (patch.start2--, patch.utf8Start2--, patch.length2 = 1, patch.utf8Length2 = 1) : m[4] === "0" ? (patch.length2 = 0, patch.utf8Length2 = 0) : (patch.start2--, patch.utf8Start2--, patch.utf8Length2 = toInt(m[4]), patch.length2 = patch.utf8Length2), textPointer++; textPointer < lines.length; ) {
      const currentLine = lines[textPointer], sign = currentLine.charAt(0);
      if (sign === "@") break;
      if (sign === "") {
        textPointer++;
        continue;
      }
      let line;
      try {
        line = decodeURI(currentLine.slice(1));
      } catch {
        throw new Error(`Illegal escape in parse: ${currentLine}`);
      }
      const utf8Diff = countUtf8Bytes(line) - line.length;
      if (sign === "-") patch.diffs.push([DIFF_DELETE, line]), patch.length1 -= utf8Diff;
      else if (sign === "+") patch.diffs.push([DIFF_INSERT, line]), patch.length2 -= utf8Diff;
      else if (sign === " ") patch.diffs.push([DIFF_EQUAL, line]), patch.length1 -= utf8Diff, patch.length2 -= utf8Diff;
      else throw new Error(`Invalid patch mode "${sign}" in: ${line}`);
      textPointer++;
    }
  }
  return patches;
}
function toInt(num) {
  return parseInt(num, 10);
}
const CURRENT_UNDO_STEP = /* @__PURE__ */ new WeakMap();
function withUndoStep(editor, fn) {
  const current = CURRENT_UNDO_STEP.get(editor);
  if (current) {
    fn();
    return;
  }
  CURRENT_UNDO_STEP.set(editor, current ?? {
    undoStepId: defaultKeyGenerator()
  }), fn(), CURRENT_UNDO_STEP.set(editor, void 0);
}
function getCurrentUndoStepId(editor) {
  return CURRENT_UNDO_STEP.get(editor)?.undoStepId;
}
const debug$d = debugWithName("plugin:withUndoRedo"), SAVING = /* @__PURE__ */ new WeakMap(), REMOTE_PATCHES = /* @__PURE__ */ new WeakMap(), UNDO_STEP_LIMIT = 1e3, isSaving = (editor) => {
  const state = SAVING.get(editor);
  return state === void 0 ? !0 : state;
}, getRemotePatches = (editor) => (REMOTE_PATCHES.get(editor) || REMOTE_PATCHES.set(editor, []), REMOTE_PATCHES.get(editor) || []);
function createWithUndoRedo(options) {
  const {
    editorActor
  } = options;
  return (editor) => {
    let previousSnapshot = fromSlateValue(editor.children, editorActor.getSnapshot().context.schema.block.name);
    const remotePatches = getRemotePatches(editor);
    let previousUndoStepId = getCurrentUndoStepId(editor);
    options.subscriptions.push(() => {
      debug$d("Subscribing to patches");
      const sub = editorActor.on("patches", ({
        patches,
        snapshot
      }) => {
        let reset = !1;
        patches.forEach((patch) => {
          if (!reset && patch.origin !== "local" && remotePatches) {
            if (patch.type === "unset" && patch.path.length === 0) {
              debug$d("Someone else cleared the content, resetting undo/redo history"), editor.history = {
                undos: [],
                redos: []
              }, remotePatches.splice(0, remotePatches.length), SAVING.set(editor, !0), reset = !0;
              return;
            }
            remotePatches.push({
              patch,
              time: /* @__PURE__ */ new Date(),
              snapshot,
              previousSnapshot
            });
          }
        }), previousSnapshot = snapshot;
      });
      return () => {
        debug$d("Unsubscribing to patches"), sub.unsubscribe();
      };
    }), editor.history = {
      undos: [],
      redos: []
    };
    const {
      apply: apply2
    } = editor;
    return editor.apply = (op) => {
      if (editorActor.getSnapshot().matches({
        "edit mode": "read only"
      })) {
        apply2(op);
        return;
      }
      if (isChangingRemotely(editor)) {
        apply2(op);
        return;
      }
      if (isUndoing(editor) || isRedoing(editor)) {
        apply2(op);
        return;
      }
      const {
        operations,
        history
      } = editor, {
        undos
      } = history, step = undos[undos.length - 1], lastOp = step && step.operations && step.operations[step.operations.length - 1], overwrite = shouldOverwrite(op, lastOp), save = isSaving(editor), currentUndoStepId = getCurrentUndoStepId(editor);
      let merge = currentUndoStepId === previousUndoStepId;
      if (save) {
        if (step ? operations.length === 0 && (merge = currentUndoStepId === void 0 && previousUndoStepId === void 0 ? shouldMerge(op, lastOp) || overwrite : merge) : merge = !1, step && merge)
          step.operations.push(op);
        else {
          const newStep = {
            operations: [...editor.selection === null ? [] : [createSelectOperation(editor)], op],
            timestamp: /* @__PURE__ */ new Date()
          };
          undos.push(newStep), debug$d("Created new undo step", step);
        }
        for (; undos.length > UNDO_STEP_LIMIT; )
          undos.shift();
        shouldClear(op) && (history.redos = []);
      }
      previousUndoStepId = currentUndoStepId, apply2(op);
    }, editor;
  };
}
const historyUndoOperationImplementation = ({
  operation
}) => {
  const editor = operation.editor, {
    undos
  } = editor.history, remotePatches = getRemotePatches(editor);
  if (undos.length > 0) {
    const step = undos[undos.length - 1];
    if (debug$d("Undoing", step), step.operations.length > 0) {
      const otherPatches = remotePatches.filter((item) => item.time >= step.timestamp);
      let transformedOperations = step.operations;
      otherPatches.forEach((item) => {
        transformedOperations = flatten(transformedOperations.map((op) => transformOperation(editor, item.patch, op, item.snapshot, item.previousSnapshot)));
      });
      const reversedOperations = transformedOperations.map(Operation.inverse).reverse();
      try {
        Editor.withoutNormalizing(editor, () => {
          withUndoing(editor, () => {
            withoutSaving(editor, () => {
              reversedOperations.forEach((op) => {
                editor.apply(op);
              });
            });
          });
        });
      } catch (err) {
        debug$d("Could not perform undo step", err), remotePatches.splice(0, remotePatches.length), Transforms.deselect(editor), editor.history = {
          undos: [],
          redos: []
        }, SAVING.set(editor, !0), setIsUndoing(editor, !1), editor.onChange();
        return;
      }
      editor.history.redos.push(step), editor.history.undos.pop();
    }
  }
}, historyRedoOperationImplementation = ({
  operation
}) => {
  const editor = operation.editor, {
    redos
  } = editor.history, remotePatches = getRemotePatches(editor);
  if (redos.length > 0) {
    const step = redos[redos.length - 1];
    if (debug$d("Redoing", step), step.operations.length > 0) {
      const otherPatches = remotePatches.filter((item) => item.time >= step.timestamp);
      let transformedOperations = step.operations;
      otherPatches.forEach((item) => {
        transformedOperations = flatten(transformedOperations.map((op) => transformOperation(editor, item.patch, op, item.snapshot, item.previousSnapshot)));
      });
      try {
        Editor.withoutNormalizing(editor, () => {
          withRedoing(editor, () => {
            withoutSaving(editor, () => {
              transformedOperations.forEach((op) => {
                editor.apply(op);
              });
            });
          });
        });
      } catch (err) {
        debug$d("Could not perform redo step", err), remotePatches.splice(0, remotePatches.length), Transforms.deselect(editor), editor.history = {
          undos: [],
          redos: []
        }, SAVING.set(editor, !0), setIsRedoing(editor, !1), editor.onChange();
        return;
      }
      editor.history.undos.push(step), editor.history.redos.pop();
    }
  }
};
function transformOperation(editor, patch, operation, snapshot, previousSnapshot) {
  const transformedOperation = {
    ...operation
  };
  if (patch.type === "insert" && patch.path.length === 1) {
    const insertBlockIndex = (snapshot || []).findIndex((blk) => isEqual({
      _key: blk._key
    }, patch.path[0]));
    return debug$d(`Adjusting block path (+${patch.items.length}) for '${transformedOperation.type}' operation and patch '${patch.type}'`), [adjustBlockPath(transformedOperation, patch.items.length, insertBlockIndex)];
  }
  if (patch.type === "unset" && patch.path.length === 1) {
    const unsetBlockIndex = (previousSnapshot || []).findIndex((blk) => isEqual({
      _key: blk._key
    }, patch.path[0]));
    return "path" in transformedOperation && Array.isArray(transformedOperation.path) && transformedOperation.path[0] === unsetBlockIndex ? (debug$d("Skipping transformation that targeted removed block"), []) : [adjustBlockPath(transformedOperation, -1, unsetBlockIndex)];
  }
  if (patch.type === "unset" && patch.path.length === 0)
    return debug$d(`Adjusting selection for unset everything patch and ${operation.type} operation`), [];
  if (patch.type === "diffMatchPatch") {
    const operationTargetBlock = findOperationTargetBlock(editor, transformedOperation);
    return !operationTargetBlock || !isEqual({
      _key: operationTargetBlock._key
    }, patch.path[0]) ? [transformedOperation] : (parse(patch.value).forEach((diffPatch) => {
      let adjustOffsetBy = 0, changedOffset = diffPatch.utf8Start1;
      const {
        diffs
      } = diffPatch;
      if (diffs.forEach((diff2, index) => {
        const [diffType, text] = diff2;
        diffType === DIFF_INSERT ? (adjustOffsetBy += text.length, changedOffset += text.length) : diffType === DIFF_DELETE ? (adjustOffsetBy -= text.length, changedOffset -= text.length) : diffType === DIFF_EQUAL && (diffs.slice(index).every(([dType]) => dType === DIFF_EQUAL) || (changedOffset += text.length));
      }), transformedOperation.type === "insert_text" && changedOffset < transformedOperation.offset && (transformedOperation.offset += adjustOffsetBy), transformedOperation.type === "remove_text" && changedOffset <= transformedOperation.offset - transformedOperation.text.length && (transformedOperation.offset += adjustOffsetBy), transformedOperation.type === "set_selection") {
        const currentFocus = transformedOperation.properties?.focus ? {
          ...transformedOperation.properties.focus
        } : void 0, currentAnchor = transformedOperation?.properties?.anchor ? {
          ...transformedOperation.properties.anchor
        } : void 0, newFocus = transformedOperation?.newProperties?.focus ? {
          ...transformedOperation.newProperties.focus
        } : void 0, newAnchor = transformedOperation?.newProperties?.anchor ? {
          ...transformedOperation.newProperties.anchor
        } : void 0;
        (currentFocus && currentAnchor || newFocus && newAnchor) && ([currentFocus, currentAnchor, newFocus, newAnchor].forEach((point) => {
          point && changedOffset < point.offset && (point.offset += adjustOffsetBy);
        }), currentFocus && currentAnchor && (transformedOperation.properties = {
          focus: currentFocus,
          anchor: currentAnchor
        }), newFocus && newAnchor && (transformedOperation.newProperties = {
          focus: newFocus,
          anchor: newAnchor
        }));
      }
    }), [transformedOperation]);
  }
  return [transformedOperation];
}
function adjustBlockPath(operation, level, blockIndex) {
  const transformedOperation = {
    ...operation
  };
  if (blockIndex >= 0 && transformedOperation.type !== "set_selection" && Array.isArray(transformedOperation.path) && transformedOperation.path[0] >= blockIndex + level && transformedOperation.path[0] + level > -1) {
    const newPath = [transformedOperation.path[0] + level, ...transformedOperation.path.slice(1)];
    transformedOperation.path = newPath;
  }
  if (transformedOperation.type === "set_selection") {
    const currentFocus = transformedOperation.properties?.focus ? {
      ...transformedOperation.properties.focus
    } : void 0, currentAnchor = transformedOperation?.properties?.anchor ? {
      ...transformedOperation.properties.anchor
    } : void 0, newFocus = transformedOperation?.newProperties?.focus ? {
      ...transformedOperation.newProperties.focus
    } : void 0, newAnchor = transformedOperation?.newProperties?.anchor ? {
      ...transformedOperation.newProperties.anchor
    } : void 0;
    (currentFocus && currentAnchor || newFocus && newAnchor) && ([currentFocus, currentAnchor, newFocus, newAnchor].forEach((point) => {
      point && point.path[0] >= blockIndex + level && point.path[0] + level > -1 && (point.path = [point.path[0] + level, ...point.path.slice(1)]);
    }), currentFocus && currentAnchor && (transformedOperation.properties = {
      focus: currentFocus,
      anchor: currentAnchor
    }), newFocus && newAnchor && (transformedOperation.newProperties = {
      focus: newFocus,
      anchor: newAnchor
    }));
  }
  return transformedOperation;
}
const shouldMerge = (op, prev) => !!(op.type === "set_selection" || prev && op.type === "insert_text" && prev.type === "insert_text" && op.offset === prev.offset + prev.text.length && Path.equals(op.path, prev.path) && op.text !== " " || prev && op.type === "remove_text" && prev.type === "remove_text" && op.offset + op.text.length === prev.offset && Path.equals(op.path, prev.path)), shouldOverwrite = (op, prev) => !!(prev && op.type === "set_selection" && prev.type === "set_selection"), shouldClear = (op) => op.type !== "set_selection";
function withoutSaving(editor, fn) {
  const prev = isSaving(editor);
  SAVING.set(editor, !1), fn(), SAVING.set(editor, prev);
}
function createSelectOperation(editor) {
  return {
    type: "set_selection",
    properties: {
      ...editor.selection
    },
    newProperties: {
      ...editor.selection
    }
  };
}
function findOperationTargetBlock(editor, operation) {
  let block;
  return operation.type === "set_selection" && editor.selection ? block = editor.children[editor.selection.focus.path[0]] : "path" in operation && (block = editor.children[operation.path[0]]), block;
}
const addAnnotationOperationImplementation = ({
  context,
  operation
}) => {
  const parsedAnnotation = parseAnnotation({
    annotation: {
      _type: operation.annotation.name,
      ...operation.annotation.value
    },
    context,
    options: {
      refreshKeys: !1,
      validateFields: !0
    }
  });
  if (!parsedAnnotation)
    throw new Error(`Failed to parse annotation ${JSON.stringify(operation.annotation)}`);
  const editor = operation.editor;
  if (!editor.selection || Range.isCollapsed(editor.selection))
    return;
  let paths, spanPath, markDefPath;
  const markDefPaths = [], selectedBlocks = Editor.nodes(editor, {
    at: editor.selection,
    match: (node) => editor.isTextBlock(node),
    reverse: Range.isBackward(editor.selection)
  });
  let blockIndex = 0;
  for (const [block, blockPath] of selectedBlocks) {
    if (block.children.length === 0 || block.children.length === 1 && block.children[0].text === "")
      continue;
    const annotationKey = blockIndex === 0 ? parsedAnnotation._key : context.keyGenerator(), markDefs = block.markDefs ?? [];
    markDefs.find((markDef) => markDef._type === parsedAnnotation._type && markDef._key === annotationKey) === void 0 && (Transforms.setNodes(editor, {
      markDefs: [...markDefs, {
        ...parsedAnnotation,
        _key: annotationKey
      }]
    }, {
      at: blockPath
    }), markDefPath = [{
      _key: block._key
    }, "markDefs", {
      _key: annotationKey
    }], Range.isBackward(editor.selection) ? markDefPaths.unshift(markDefPath) : markDefPaths.push(markDefPath)), Transforms.setNodes(editor, {}, {
      match: Text.isText,
      split: !0
    });
    const children = Node.children(editor, blockPath);
    for (const [span, path] of children) {
      if (!editor.isTextSpan(span) || !Range.includes(editor.selection, path))
        continue;
      const marks = span.marks ?? [], existingSameTypeAnnotations = marks.filter((mark) => markDefs.some((markDef) => markDef._key === mark && markDef._type === parsedAnnotation._type));
      Transforms.setNodes(editor, {
        marks: [...marks.filter((mark) => !existingSameTypeAnnotations.includes(mark)), annotationKey]
      }, {
        at: path
      }), spanPath = [{
        _key: block._key
      }, "children", {
        _key: span._key
      }];
    }
    blockIndex++;
  }
  return markDefPath && spanPath && (paths = {
    markDefPath,
    markDefPaths,
    spanPath
  }), paths;
}, removeAnnotationOperationImplementation = ({
  operation
}) => {
  const editor = operation.editor;
  if (editor.selection)
    if (Range.isCollapsed(editor.selection)) {
      const [block, blockPath] = Editor.node(editor, editor.selection, {
        depth: 1
      });
      if (!editor.isTextBlock(block))
        return;
      const potentialAnnotations = (block.markDefs ?? []).filter((markDef) => markDef._type === operation.annotation.name), [selectedChild, selectedChildPath] = Editor.node(editor, editor.selection, {
        depth: 2
      });
      if (!editor.isTextSpan(selectedChild))
        return;
      const annotationToRemove = selectedChild.marks?.find((mark) => potentialAnnotations.some((markDef) => markDef._key === mark));
      if (!annotationToRemove)
        return;
      const previousSpansWithSameAnnotation = [];
      for (const [child, childPath] of Node.children(editor, blockPath, {
        reverse: !0
      }))
        if (editor.isTextSpan(child) && Path.isBefore(childPath, selectedChildPath))
          if (child.marks?.includes(annotationToRemove))
            previousSpansWithSameAnnotation.push([child, childPath]);
          else
            break;
      const nextSpansWithSameAnnotation = [];
      for (const [child, childPath] of Node.children(editor, blockPath))
        if (editor.isTextSpan(child) && Path.isAfter(childPath, selectedChildPath))
          if (child.marks?.includes(annotationToRemove))
            nextSpansWithSameAnnotation.push([child, childPath]);
          else
            break;
      for (const [child, childPath] of [...previousSpansWithSameAnnotation, [selectedChild, selectedChildPath], ...nextSpansWithSameAnnotation])
        Transforms.setNodes(editor, {
          marks: child.marks?.filter((mark) => mark !== annotationToRemove)
        }, {
          at: childPath
        });
    } else {
      Transforms.setNodes(editor, {}, {
        match: (node) => editor.isTextSpan(node),
        split: !0,
        hanging: !0
      });
      const blocks = Editor.nodes(editor, {
        at: editor.selection,
        match: (node) => editor.isTextBlock(node)
      });
      for (const [block, blockPath] of blocks) {
        const children = Node.children(editor, blockPath);
        for (const [child, childPath] of children) {
          if (!editor.isTextSpan(child) || !Range.includes(editor.selection, childPath))
            continue;
          const markDefs = block.markDefs ?? [], marks = child.marks ?? [], marksWithoutAnnotation = marks.filter((mark) => markDefs.find((markDef2) => markDef2._key === mark)?._type !== operation.annotation.name);
          marksWithoutAnnotation.length !== marks.length && Transforms.setNodes(editor, {
            marks: marksWithoutAnnotation
          }, {
            at: childPath
          });
        }
      }
    }
}, blockSetOperationImplementation = ({
  context,
  operation
}) => {
  const location = toSlateRange({
    context: {
      schema: context.schema,
      value: operation.editor.value,
      selection: {
        anchor: {
          path: operation.at,
          offset: 0
        },
        focus: {
          path: operation.at,
          offset: 0
        }
      }
    },
    blockIndexMap: operation.editor.blockIndexMap
  });
  if (!location)
    throw new Error(`Unable to convert ${JSON.stringify(operation.at)} into a Slate Range`);
  const block = Editor.node(operation.editor, location, {
    depth: 1
  })?.[0];
  if (!block)
    throw new Error(`Unable to find block at ${JSON.stringify(operation.at)}`);
  const parsedBlock = fromSlateValue([block], context.schema.block.name, KEY_TO_VALUE_ELEMENT.get(operation.editor)).at(0);
  if (!parsedBlock)
    throw new Error(`Unable to parse block at ${JSON.stringify(operation.at)}`);
  const {
    _type,
    ...filteredProps
  } = operation.props, updatedBlock = parseBlock({
    context,
    block: {
      ...parsedBlock,
      ...filteredProps
    },
    options: {
      refreshKeys: !1,
      validateFields: !0
    }
  });
  if (!updatedBlock)
    throw new Error(`Unable to update block at ${JSON.stringify(operation.at)}`);
  const slateBlock = toSlateValue([updatedBlock], {
    schemaTypes: context.schema
  })?.at(0);
  if (!slateBlock)
    throw new Error("Unable to convert block to Slate value");
  Transforms.setNodes(operation.editor, slateBlock, {
    at: location
  });
}, blockUnsetOperationImplementation = ({
  context,
  operation
}) => {
  const location = toSlateRange({
    context: {
      schema: context.schema,
      value: operation.editor.value,
      selection: {
        anchor: {
          path: operation.at,
          offset: 0
        },
        focus: {
          path: operation.at,
          offset: 0
        }
      }
    },
    blockIndexMap: operation.editor.blockIndexMap
  });
  if (!location)
    throw new Error(`Unable to convert ${JSON.stringify(operation.at)} into a Slate Range`);
  const block = Editor.node(operation.editor, location, {
    depth: 1
  })?.[0];
  if (!block)
    throw new Error(`Unable to find block at ${JSON.stringify(operation.at)}`);
  const parsedBlock = fromSlateValue([block], context.schema.block.name, KEY_TO_VALUE_ELEMENT.get(operation.editor)).at(0);
  if (!parsedBlock)
    throw new Error(`Unable to parse block at ${JSON.stringify(operation.at)}`);
  if (isTextBlock(context, parsedBlock)) {
    const propsToRemove = operation.props.filter((prop) => prop !== "_type"), updatedTextBlock = parseBlock({
      context,
      block: omit(parsedBlock, propsToRemove),
      options: {
        refreshKeys: !1,
        validateFields: !0
      }
    });
    if (!updatedTextBlock)
      throw new Error(`Unable to update block at ${JSON.stringify(operation.at)}`);
    const propsToSet = {};
    for (const prop of propsToRemove)
      prop in updatedTextBlock ? propsToSet[prop] = updatedTextBlock[prop] : propsToSet[prop] = void 0;
    Transforms.setNodes(operation.editor, propsToSet, {
      at: location
    });
    return;
  }
  const updatedBlockObject = parseBlock({
    context,
    block: omit(parsedBlock, operation.props.filter((prop) => prop !== "_type")),
    options: {
      refreshKeys: !1,
      validateFields: !0
    }
  });
  if (!updatedBlockObject)
    throw new Error(`Unable to update block at ${JSON.stringify(operation.at)}`);
  const {
    _type,
    _key,
    ...props
  } = updatedBlockObject;
  Transforms.setNodes(operation.editor, {
    _type,
    _key,
    value: props
  }, {
    at: location
  });
}, childSetOperationImplementation = ({
  context,
  operation
}) => {
  const location = toSlateRange({
    context: {
      schema: context.schema,
      value: operation.editor.value,
      selection: {
        anchor: {
          path: operation.at,
          offset: 0
        },
        focus: {
          path: operation.at,
          offset: 0
        }
      }
    },
    blockIndexMap: operation.editor.blockIndexMap
  });
  if (!location)
    throw new Error(`Unable to convert ${JSON.stringify(operation.at)} into a Slate Range`);
  const childEntry = Editor.node(operation.editor, location, {
    depth: 2
  }), child = childEntry?.[0], childPath = childEntry?.[1];
  if (!child || !childPath)
    throw new Error(`Unable to find child at ${JSON.stringify(operation.at)}`);
  if (operation.editor.isTextSpan(child)) {
    const {
      _type,
      text,
      ...rest
    } = operation.props;
    Transforms.setNodes(operation.editor, {
      ...child,
      ...rest
    }, {
      at: childPath
    }), typeof text == "string" && child.text !== text && (operation.editor.apply({
      type: "remove_text",
      path: childPath,
      offset: 0,
      text: child.text
    }), operation.editor.apply({
      type: "insert_text",
      path: childPath,
      offset: 0,
      text
    }));
    return;
  }
  if (Element$1.isElement(child)) {
    const definition = context.schema.inlineObjects.find((definition2) => definition2.name === child._type);
    if (!definition)
      throw new Error(`Unable to find schema definition for Inline Object type ${child._type}`);
    const value = "value" in child && typeof child.value == "object" ? child.value : {}, {
      _type,
      _key,
      ...rest
    } = operation.props;
    for (const prop in rest)
      definition.fields.some((field) => field.name === prop) || delete rest[prop];
    Transforms.setNodes(operation.editor, {
      ...child,
      _key: typeof _key == "string" ? _key : child._key,
      value: {
        ...value,
        ...rest
      }
    }, {
      at: childPath
    });
    return;
  }
  throw new Error(`Unable to determine the type of child at ${JSON.stringify(operation.at)}`);
}, childUnsetOperationImplementation = ({
  context,
  operation
}) => {
  const location = toSlateRange({
    context: {
      schema: context.schema,
      value: operation.editor.value,
      selection: {
        anchor: {
          path: operation.at,
          offset: 0
        },
        focus: {
          path: operation.at,
          offset: 0
        }
      }
    },
    blockIndexMap: operation.editor.blockIndexMap
  });
  if (!location)
    throw new Error(`Unable to convert ${JSON.stringify(operation.at)} into a Slate Range`);
  const childEntry = Editor.node(operation.editor, location, {
    depth: 2
  }), child = childEntry?.[0], childPath = childEntry?.[1];
  if (!child || !childPath)
    throw new Error(`Unable to find child at ${JSON.stringify(operation.at)}`);
  if (operation.editor.isTextSpan(child)) {
    operation.props.includes("text") && operation.editor.apply({
      type: "remove_text",
      path: childPath,
      offset: 0,
      text: child.text
    });
    const newNode = {};
    for (const prop of operation.props)
      if (prop !== "_type") {
        if (prop === "_key") {
          newNode._key = context.keyGenerator();
          continue;
        }
        newNode[prop] = null;
      }
    Transforms.setNodes(operation.editor, newNode, {
      at: childPath
    });
    return;
  }
  if (Element$1.isElement(child)) {
    const value = "value" in child && typeof child.value == "object" ? child.value : {}, patches = operation.props.map((prop) => ({
      type: "unset",
      path: [prop]
    })), newValue = applyAll(value, patches);
    Transforms.setNodes(operation.editor, {
      ...child,
      _key: operation.props.includes("_key") ? context.keyGenerator() : child._key,
      value: newValue
    }, {
      at: childPath
    });
    return;
  }
  throw new Error(`Unable to determine the type of child at ${JSON.stringify(operation.at)}`);
}, decoratorAddOperationImplementation = ({
  context,
  operation
}) => {
  const editor = operation.editor, mark = operation.decorator, value = fromSlateValue(editor.children, context.schema.block.name, KEY_TO_VALUE_ELEMENT.get(editor)), manualAnchor = operation.at?.anchor ? blockOffsetToSpanSelectionPoint({
    context: {
      ...context,
      value
    },
    blockOffset: operation.at.anchor,
    direction: "backward"
  }) : void 0, manualFocus = operation.at?.focus ? blockOffsetToSpanSelectionPoint({
    context: {
      ...context,
      value
    },
    blockOffset: operation.at.focus,
    direction: "forward"
  }) : void 0, manualSelection = manualAnchor && manualFocus ? {
    anchor: manualAnchor,
    focus: manualFocus
  } : void 0, selection = manualSelection ? toSlateRange({
    context: {
      schema: context.schema,
      value: operation.editor.value,
      selection: manualSelection
    },
    blockIndexMap: operation.editor.blockIndexMap
  }) ?? editor.selection : editor.selection;
  if (!selection)
    return;
  const editorSelection = slateRangeToSelection({
    schema: context.schema,
    editor,
    range: selection
  }), anchorOffset = editorSelection ? selectionPointToBlockOffset({
    context: {
      ...context,
      value
    },
    selectionPoint: editorSelection.anchor
  }) : void 0, focusOffset = editorSelection ? selectionPointToBlockOffset({
    context: {
      ...context,
      value
    },
    selectionPoint: editorSelection.focus
  }) : void 0;
  if (!anchorOffset || !focusOffset)
    throw new Error("Unable to find anchor or focus offset");
  if (Range.isExpanded(selection)) {
    Transforms.setNodes(editor, {}, {
      at: selection,
      match: Text.isText,
      split: !0,
      hanging: !0
    });
    const newValue = fromSlateValue(editor.children, context.schema.block.name, KEY_TO_VALUE_ELEMENT.get(editor)), newSelection = blockOffsetsToSelection({
      context: {
        ...context,
        value: newValue
      },
      offsets: {
        anchor: anchorOffset,
        focus: focusOffset
      },
      backward: editorSelection?.backward
    }), trimmedSelection = getTrimmedSelection({
      blockIndexMap: editor.blockIndexMap,
      context: {
        converters: [],
        keyGenerator: context.keyGenerator,
        readOnly: !1,
        schema: context.schema,
        selection: newSelection,
        value: newValue
      },
      decoratorState: editor.decoratorState
    });
    if (!trimmedSelection)
      throw new Error("Unable to find trimmed selection");
    const newRange = toSlateRange({
      context: {
        schema: context.schema,
        value: operation.editor.value,
        selection: trimmedSelection
      },
      blockIndexMap: operation.editor.blockIndexMap
    });
    if (!newRange)
      throw new Error("Unable to find new selection");
    const splitTextNodes = Range.isRange(newRange) ? [...Editor.nodes(editor, {
      at: newRange,
      match: (node) => Text.isText(node)
    })] : [];
    for (const [node, path] of splitTextNodes) {
      const marks = [...(Array.isArray(node.marks) ? node.marks : []).filter((eMark) => eMark !== mark), mark];
      Transforms.setNodes(editor, {
        marks
      }, {
        at: path,
        match: Text.isText,
        split: !0,
        hanging: !0
      });
    }
  } else {
    if (!Array.from(Editor.nodes(editor, {
      at: selection,
      match: (node) => editor.isTextSpan(node)
    }))?.at(0))
      return;
    const [block, blockPath] = Editor.node(editor, selection, {
      depth: 1
    }), lonelyEmptySpan = editor.isTextBlock(block) && block.children.length === 1 && editor.isTextSpan(block.children[0]) && block.children[0].text === "" ? block.children[0] : void 0;
    if (lonelyEmptySpan) {
      const existingMarks = lonelyEmptySpan.marks ?? [], existingMarksWithoutDecorator = existingMarks.filter((existingMark) => existingMark !== mark);
      Transforms.setNodes(editor, {
        marks: existingMarks.length === existingMarksWithoutDecorator.length ? [...existingMarks, mark] : existingMarksWithoutDecorator
      }, {
        at: blockPath,
        match: (node) => editor.isTextSpan(node)
      });
    } else
      editor.decoratorState[mark] = !0;
  }
  if (editor.selection) {
    const selection2 = editor.selection;
    editor.selection = {
      ...selection2
    };
  }
}, deleteOperationImplementation = ({
  context,
  operation
}) => {
  const anchorBlockKey = getBlockKeyFromSelectionPoint(operation.at.anchor), focusBlockKey = getBlockKeyFromSelectionPoint(operation.at.focus), startBlockKey = operation.at.backward ? focusBlockKey : anchorBlockKey, endBlockKey = operation.at.backward ? anchorBlockKey : focusBlockKey, endOffset = operation.at.backward ? operation.at.focus.offset : operation.at.anchor.offset;
  if (!startBlockKey)
    throw new Error("Failed to get start block key");
  if (!endBlockKey)
    throw new Error("Failed to get end block key");
  const startBlockIndex = operation.editor.blockIndexMap.get(startBlockKey);
  if (startBlockIndex === void 0)
    throw new Error("Failed to get start block index");
  const startBlock = operation.editor.value.at(startBlockIndex);
  if (!startBlock)
    throw new Error("Failed to get start block");
  const endBlockIndex = operation.editor.blockIndexMap.get(endBlockKey);
  if (endBlockIndex === void 0)
    throw new Error("Failed to get end block index");
  const endBlock = operation.editor.value.at(endBlockIndex);
  if (!endBlock)
    throw new Error("Failed to get end block");
  const anchorBlockPath = anchorBlockKey !== void 0 ? getBlockPath({
    editor: operation.editor,
    _key: anchorBlockKey
  }) : void 0, focusBlockPath = focusBlockKey !== void 0 ? getBlockPath({
    editor: operation.editor,
    _key: focusBlockKey
  }) : void 0;
  if (operation.at.anchor.path.length === 1 && operation.at.focus.path.length === 1 && anchorBlockPath && focusBlockPath && anchorBlockPath[0] === focusBlockPath[0]) {
    Transforms.removeNodes(operation.editor, {
      at: [anchorBlockPath[0]]
    }), operation.editor.children.length === 0 && Transforms.insertNodes(operation.editor, createPlaceholderBlock(context));
    return;
  }
  const range = toSlateRange({
    context: {
      schema: context.schema,
      value: operation.editor.value,
      selection: operation.at
    },
    blockIndexMap: operation.editor.blockIndexMap
  });
  if (!range)
    throw new Error(`Failed to get Slate Range for selection ${JSON.stringify(operation.at)}`);
  const hanging = isTextBlock(context, endBlock) && endOffset === 0;
  deleteText(operation.editor, {
    at: range,
    reverse: operation.direction === "backward",
    unit: operation.unit,
    hanging
  }), operation.editor.selection && isTextBlock(context, startBlock) && isTextBlock(context, endBlock) && setSelection(operation.editor, {
    anchor: operation.editor.selection.focus,
    focus: operation.editor.selection.focus
  });
}, insertInlineObjectOperationImplementation = ({
  context,
  operation
}) => {
  const parsedInlineObject = parseInlineObject({
    context,
    inlineObject: {
      _type: operation.inlineObject.name,
      ...operation.inlineObject.value ?? {}
    },
    options: {
      refreshKeys: !1,
      validateFields: !0
    }
  });
  if (!parsedInlineObject)
    throw new Error(`Failed to parse inline object ${JSON.stringify(operation.inlineObject)}`);
  if (!operation.editor.selection) {
    console.error("Unable to insert inline object without selection");
    return;
  }
  const [focusTextBlock] = Array.from(Editor.nodes(operation.editor, {
    at: operation.editor.selection.focus.path,
    match: (node) => operation.editor.isTextBlock(node)
  })).at(0) ?? [void 0, void 0];
  if (!focusTextBlock) {
    console.error("Unable to perform action without focus text block");
    return;
  }
  const child = toSlateValue([{
    _type: context.schema.block.name,
    _key: context.keyGenerator(),
    children: [parsedInlineObject]
  }], {
    schemaTypes: context.schema
  }).at(0)?.children.at(0);
  if (!child) {
    console.error("Unable to insert inline object");
    return;
  }
  Transforms.insertNodes(operation.editor, child);
}, insertSpanOperationImplementation = ({
  context,
  operation
}) => {
  if (!operation.editor.selection) {
    console.error("Unable to perform action without selection", operation);
    return;
  }
  const [focusBlock, focusBlockPath] = Array.from(Editor.nodes(operation.editor, {
    at: operation.editor.selection.focus.path,
    match: (node) => operation.editor.isTextBlock(node)
  }))[0] ?? [void 0, void 0];
  if (!focusBlock || !focusBlockPath) {
    console.error("Unable to perform action without focus block", operation);
    return;
  }
  const markDefs = focusBlock.markDefs ?? [], annotations = operation.annotations ? operation.annotations.map((annotation) => ({
    _type: annotation.name,
    _key: context.keyGenerator(),
    ...annotation.value
  })) : void 0;
  annotations && annotations.length > 0 && Transforms.setNodes(operation.editor, {
    markDefs: [...markDefs, ...annotations]
  }), Transforms.insertNodes(operation.editor, {
    _type: "span",
    _key: context.keyGenerator(),
    text: operation.text,
    marks: [...annotations?.map((annotation) => annotation._key) ?? [], ...operation.decorators ?? []]
  });
}, insertBlockOperationImplementation = ({
  context,
  operation
}) => {
  const parsedBlock = parseBlock({
    block: operation.block,
    context,
    options: {
      refreshKeys: !1,
      validateFields: !0
    }
  });
  if (!parsedBlock)
    throw new Error(`Failed to parse block ${JSON.stringify(operation.block)}`);
  const fragment = toSlateValue([parsedBlock], {
    schemaTypes: context.schema
  })[0];
  if (!fragment)
    throw new Error(`Failed to convert block to Slate fragment ${JSON.stringify(parsedBlock)}`);
  insertBlock({
    context,
    block: fragment,
    placement: operation.placement,
    select: operation.select ?? "start",
    editor: operation.editor
  });
};
function insertBlock({
  context,
  block,
  placement,
  select,
  editor
}) {
  const [startBlock, startBlockPath] = getSelectionStartBlock({
    editor
  }), [endBlock, endBlockPath] = getSelectionEndBlock({
    editor
  });
  if (!editor.selection || !startBlock || !startBlockPath || !endBlock || !endBlockPath) {
    select !== "none" && DOMEditor.focus(editor);
    const [lastBlock, lastBlockPath] = getLastBlock({
      editor
    });
    if (placement === "before")
      Transforms.insertNodes(editor, [block], {
        at: [0]
      }), select === "start" ? Transforms.select(editor, Editor.start(editor, [0])) : select === "end" && Transforms.select(editor, Editor.end(editor, [0]));
    else if (placement === "after") {
      const nextPath = lastBlockPath ? [lastBlockPath[0] + 1] : [0];
      Transforms.insertNodes(editor, [block], {
        at: nextPath
      }), select === "start" ? Transforms.select(editor, Editor.start(editor, nextPath)) : select === "end" && Transforms.select(editor, Editor.end(editor, nextPath));
    } else {
      if (lastBlock && isEqualToEmptyEditor([lastBlock], context.schema)) {
        Transforms.removeNodes(editor, {
          at: lastBlockPath
        }), Transforms.insertNodes(editor, [block], {
          at: lastBlockPath,
          select: !1
        }), Transforms.deselect(editor), select === "start" ? Transforms.select(editor, Editor.start(editor, lastBlockPath)) : select === "end" && Transforms.select(editor, Editor.end(editor, lastBlockPath));
        return;
      }
      if (editor.isTextBlock(block) && lastBlock && editor.isTextBlock(lastBlock)) {
        const selectionBefore = Editor.end(editor, lastBlockPath);
        Transforms.insertFragment(editor, [block], {
          at: Editor.end(editor, lastBlockPath)
        }), select === "start" ? Transforms.select(editor, selectionBefore) : select === "none" && Transforms.deselect(editor);
        return;
      }
      const nextPath = lastBlockPath ? [lastBlockPath[0] + 1] : [0];
      Transforms.insertNodes(editor, [block], {
        at: nextPath,
        select: !1
      }), select === "start" ? Transforms.select(editor, Editor.start(editor, nextPath)) : select === "end" && Transforms.select(editor, Editor.end(editor, nextPath));
    }
  } else if (placement === "before") {
    const currentSelection = editor.selection, selectionStartPoint = Range.start(currentSelection);
    Transforms.insertNodes(editor, [block], {
      at: [selectionStartPoint.path[0]],
      select: !1
    }), select === "start" ? Transforms.select(editor, Editor.start(editor, [selectionStartPoint.path[0]])) : select === "end" && Transforms.select(editor, Editor.end(editor, [selectionStartPoint.path[0]]));
  } else if (placement === "after") {
    const currentSelection = editor.selection, nextPath = [Range.end(currentSelection).path[0] + 1];
    Transforms.insertNodes(editor, [block], {
      at: nextPath,
      select: !1
    }), select === "start" ? Transforms.select(editor, Editor.start(editor, nextPath)) : select === "end" && Transforms.select(editor, Editor.end(editor, nextPath));
  } else {
    const currentSelection = editor.selection, endBlockEndPoint = Editor.start(editor, endBlockPath);
    if (Range.isExpanded(currentSelection) && !editor.isTextBlock(block)) {
      Transforms.delete(editor, {
        at: currentSelection
      });
      const newSelection = editor.selection, [focusBlock, focusBlockPath] = getFocusBlock({
        editor
      });
      Transforms.insertNodes(editor, [block], {
        voids: !0
      });
      const adjustedSelection = newSelection.anchor.offset === 0 ? Range.transform(newSelection, {
        type: "insert_node",
        node: block,
        path: [newSelection.anchor.path[0]]
      }) : newSelection;
      select === "none" && adjustedSelection && Transforms.select(editor, adjustedSelection), focusBlock && isEqualToEmptyEditor([focusBlock], context.schema) && Transforms.removeNodes(editor, {
        at: focusBlockPath
      });
      return;
    }
    if (editor.isTextBlock(endBlock) && editor.isTextBlock(block)) {
      const selectionStartPoint = Range.start(currentSelection);
      if (isEqualToEmptyEditor([endBlock], context.schema)) {
        const currentSelection2 = editor.selection;
        Transforms.insertNodes(editor, [block], {
          at: endBlockPath,
          select: !1
        }), Transforms.removeNodes(editor, {
          at: Path.next(endBlockPath)
        }), select === "start" ? Transforms.select(editor, selectionStartPoint) : select === "end" ? Transforms.select(editor, Editor.end(editor, endBlockPath)) : Transforms.select(editor, currentSelection2);
        return;
      }
      const endBlockChildKeys = endBlock.children.map((child) => child._key), endBlockMarkDefsKeys = endBlock.markDefs?.map((markDef) => markDef._key) ?? [], markDefKeyMap = /* @__PURE__ */ new Map(), adjustedMarkDefs = block.markDefs?.map((markDef) => {
        if (endBlockMarkDefsKeys.includes(markDef._key)) {
          const newKey = context.keyGenerator();
          return markDefKeyMap.set(markDef._key, newKey), {
            ...markDef,
            _key: newKey
          };
        }
        return markDef;
      }), adjustedChildren = block.children.map((child) => {
        if (isSpan(context, child)) {
          const marks = child.marks?.map((mark) => markDefKeyMap.get(mark) || mark) ?? [];
          if (!isEqual(child.marks, marks))
            return {
              ...child,
              _key: endBlockChildKeys.includes(child._key) ? context.keyGenerator() : child._key,
              marks
            };
        }
        return endBlockChildKeys.includes(child._key) ? {
          ...child,
          _key: context.keyGenerator()
        } : child;
      });
      Transforms.setNodes(editor, {
        markDefs: [...endBlock.markDefs ?? [], ...adjustedMarkDefs ?? []]
      }, {
        at: endBlockPath
      });
      const adjustedBlock = isEqual(block.children, adjustedChildren) ? block : {
        ...block,
        children: adjustedChildren
      };
      if (select === "end") {
        Transforms.insertFragment(editor, [adjustedBlock], {
          voids: !0
        });
        return;
      }
      Transforms.insertFragment(editor, [adjustedBlock], {
        at: currentSelection,
        voids: !0
      }), select === "start" ? Transforms.select(editor, selectionStartPoint) : Point.equals(selectionStartPoint, endBlockEndPoint) || Transforms.select(editor, selectionStartPoint);
    } else if (editor.isTextBlock(endBlock)) {
      const endBlockStartPoint = Editor.start(editor, endBlockPath), endBlockEndPoint2 = Editor.end(editor, endBlockPath), selectionStartPoint = Range.start(currentSelection), selectionEndPoint = Range.end(currentSelection);
      if (Range.isCollapsed(currentSelection) && Point.equals(selectionStartPoint, endBlockStartPoint))
        Transforms.insertNodes(editor, [block], {
          at: endBlockPath,
          select: !1
        }), (select === "start" || select === "end") && Transforms.select(editor, Editor.start(editor, endBlockPath)), isEmptyTextBlock(context, endBlock) && Transforms.removeNodes(editor, {
          at: Path.next(endBlockPath)
        });
      else if (Range.isCollapsed(currentSelection) && Point.equals(selectionEndPoint, endBlockEndPoint2)) {
        const nextPath = [endBlockPath[0] + 1];
        Transforms.insertNodes(editor, [block], {
          at: nextPath,
          select: !1
        }), (select === "start" || select === "end") && Transforms.select(editor, Editor.start(editor, nextPath));
      } else if (Range.isExpanded(currentSelection) && Point.equals(selectionStartPoint, endBlockStartPoint) && Point.equals(selectionEndPoint, endBlockEndPoint2))
        Transforms.insertFragment(editor, [block], {
          at: currentSelection
        }), select === "start" ? Transforms.select(editor, Editor.start(editor, endBlockPath)) : select === "end" && Transforms.select(editor, Editor.end(editor, endBlockPath));
      else if (Range.isExpanded(currentSelection) && Point.equals(selectionStartPoint, endBlockStartPoint))
        Transforms.insertFragment(editor, [block], {
          at: currentSelection
        }), select === "start" ? Transforms.select(editor, Editor.start(editor, endBlockPath)) : select === "end" && Transforms.select(editor, Editor.end(editor, endBlockPath));
      else if (Range.isExpanded(currentSelection) && Point.equals(selectionEndPoint, endBlockEndPoint2))
        Transforms.insertFragment(editor, [block], {
          at: currentSelection
        }), select === "start" ? Transforms.select(editor, Editor.start(editor, Path.next(endBlockPath))) : select === "end" && Transforms.select(editor, Editor.end(editor, Path.next(endBlockPath)));
      else {
        const currentSelection2 = editor.selection, [focusChild] = getFocusChild({
          editor
        });
        if (focusChild && editor.isTextSpan(focusChild))
          Transforms.splitNodes(editor, {
            at: currentSelection2
          }), Transforms.insertFragment(editor, [block], {
            at: currentSelection2
          }), select === "start" || select === "end" ? Transforms.select(editor, [endBlockPath[0] + 1]) : Transforms.select(editor, currentSelection2);
        else {
          const nextPath = [endBlockPath[0] + 1];
          Transforms.insertNodes(editor, [block], {
            at: nextPath,
            select: !1
          }), Transforms.select(editor, currentSelection2), select === "start" ? Transforms.select(editor, Editor.start(editor, nextPath)) : select === "end" && Transforms.select(editor, Editor.end(editor, nextPath));
        }
      }
    } else {
      Transforms.insertNodes(editor, [block], {
        select: !1
      });
      const nextPath = [endBlockPath[0] + 1];
      select === "start" ? Transforms.select(editor, Editor.start(editor, nextPath)) : select === "end" && Transforms.select(editor, Editor.end(editor, nextPath));
    }
  }
}
const moveBackwardOperationImplementation = ({
  operation
}) => {
  Transforms.move(operation.editor, {
    unit: "character",
    distance: operation.distance,
    reverse: !0
  });
}, moveBlockOperationImplementation = ({
  operation
}) => {
  const originKey = getBlockKeyFromSelectionPoint({
    path: operation.at
  });
  if (!originKey)
    throw new Error("Failed to get block key from selection point");
  const originBlockIndex = operation.editor.blockIndexMap.get(originKey);
  if (originBlockIndex === void 0)
    throw new Error("Failed to get block index from block key");
  const destinationKey = getBlockKeyFromSelectionPoint({
    path: operation.to
  });
  if (!destinationKey)
    throw new Error("Failed to get block key from selection point");
  const destinationBlockIndex = operation.editor.blockIndexMap.get(destinationKey);
  if (destinationBlockIndex === void 0)
    throw new Error("Failed to get block index from block key");
  Transforms.moveNodes(operation.editor, {
    at: [originBlockIndex],
    to: [destinationBlockIndex],
    mode: "highest"
  });
}, moveForwardOperationImplementation = ({
  operation
}) => {
  Transforms.move(operation.editor, {
    unit: "character",
    distance: operation.distance
  });
}, selectOperationImplementation = ({
  context,
  operation
}) => {
  const newSelection = toSlateRange({
    context: {
      schema: context.schema,
      value: operation.editor.value,
      selection: operation.at
    },
    blockIndexMap: operation.editor.blockIndexMap
  });
  newSelection ? Transforms.select(operation.editor, newSelection) : Transforms.deselect(operation.editor);
}, behaviorOperationImplementations = {
  "annotation.add": addAnnotationOperationImplementation,
  "annotation.remove": removeAnnotationOperationImplementation,
  "block.set": blockSetOperationImplementation,
  "block.unset": blockUnsetOperationImplementation,
  "child.set": childSetOperationImplementation,
  "child.unset": childUnsetOperationImplementation,
  "decorator.add": decoratorAddOperationImplementation,
  "decorator.remove": removeDecoratorOperationImplementation,
  delete: deleteOperationImplementation,
  "history.redo": historyRedoOperationImplementation,
  "history.undo": historyUndoOperationImplementation,
  "insert.block": insertBlockOperationImplementation,
  "insert.inline object": insertInlineObjectOperationImplementation,
  "insert.span": insertSpanOperationImplementation,
  "insert.text": insertTextOperationImplementation,
  "move.backward": moveBackwardOperationImplementation,
  "move.block": moveBlockOperationImplementation,
  "move.forward": moveForwardOperationImplementation,
  select: selectOperationImplementation
};
function performOperation({
  context,
  operation
}) {
  try {
    switch (operation.type) {
      case "annotation.add": {
        behaviorOperationImplementations["annotation.add"]({
          context,
          operation
        });
        break;
      }
      case "annotation.remove": {
        behaviorOperationImplementations["annotation.remove"]({
          context,
          operation
        });
        break;
      }
      case "block.set": {
        behaviorOperationImplementations["block.set"]({
          context,
          operation
        });
        break;
      }
      case "block.unset": {
        behaviorOperationImplementations["block.unset"]({
          context,
          operation
        });
        break;
      }
      case "child.set": {
        behaviorOperationImplementations["child.set"]({
          context,
          operation
        });
        break;
      }
      case "child.unset": {
        behaviorOperationImplementations["child.unset"]({
          context,
          operation
        });
        break;
      }
      case "decorator.add": {
        behaviorOperationImplementations["decorator.add"]({
          context,
          operation
        });
        break;
      }
      case "decorator.remove": {
        behaviorOperationImplementations["decorator.remove"]({
          context,
          operation
        });
        break;
      }
      case "delete": {
        behaviorOperationImplementations.delete({
          context,
          operation
        });
        break;
      }
      case "history.redo": {
        behaviorOperationImplementations["history.redo"]({
          context,
          operation
        });
        break;
      }
      case "history.undo": {
        behaviorOperationImplementations["history.undo"]({
          context,
          operation
        });
        break;
      }
      case "insert.block": {
        behaviorOperationImplementations["insert.block"]({
          context,
          operation
        });
        break;
      }
      case "insert.inline object": {
        behaviorOperationImplementations["insert.inline object"]({
          context,
          operation
        });
        break;
      }
      case "insert.span": {
        behaviorOperationImplementations["insert.span"]({
          context,
          operation
        });
        break;
      }
      case "insert.text": {
        behaviorOperationImplementations["insert.text"]({
          context,
          operation
        });
        break;
      }
      case "move.backward": {
        behaviorOperationImplementations["move.backward"]({
          context,
          operation
        });
        break;
      }
      case "move.block": {
        behaviorOperationImplementations["move.block"]({
          context,
          operation
        });
        break;
      }
      case "move.forward": {
        behaviorOperationImplementations["move.forward"]({
          context,
          operation
        });
        break;
      }
      default: {
        behaviorOperationImplementations.select({
          context,
          operation
        });
        break;
      }
    }
  } catch (error) {
    console.error(new Error(`Executing "${operation.type}" failed due to: ${error.message}`));
  }
}
const CURRENT_OPERATION_ID = /* @__PURE__ */ new WeakMap();
function withApplyingBehaviorOperations(editor, fn) {
  CURRENT_OPERATION_ID.set(editor, defaultKeyGenerator()), Editor.withoutNormalizing(editor, fn), CURRENT_OPERATION_ID.set(editor, void 0);
}
function getCurrentOperationId(editor) {
  return CURRENT_OPERATION_ID.get(editor);
}
function isApplyingBehaviorOperations(editor) {
  return getCurrentOperationId(editor) !== void 0;
}
function createWithEventListeners(editorActor) {
  return function(editor) {
    if (editorActor.getSnapshot().context.maxBlocks !== void 0)
      return editor;
    const {
      delete: editorDelete,
      select
    } = editor;
    return editor.delete = (options) => {
      if (isApplyingBehaviorOperations(editor)) {
        editorDelete(options);
        return;
      }
      const at = options?.at ?? editor.selection;
      if (!at) {
        console.error("Unexpected call to .delete(...) without `at` option");
        return;
      }
      const range = Editor.range(editor, at), selection = slateRangeToSelection({
        schema: editorActor.getSnapshot().context.schema,
        editor,
        range
      });
      if (!selection) {
        console.error("Unexpected call to .delete(...) with invalid `at` option");
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "delete",
          at: selection,
          direction: options?.reverse ? "backward" : "forward",
          unit: options?.unit
        },
        editor
      });
    }, editor.deleteBackward = (unit) => {
      if (isApplyingBehaviorOperations(editor)) {
        console.error("Unexpected call to .deleteBackward(...)");
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "delete.backward",
          unit
        },
        editor
      });
    }, editor.deleteForward = (unit) => {
      if (isApplyingBehaviorOperations(editor)) {
        console.error("Unexpected call to .deleteForward(...)");
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "delete.forward",
          unit
        },
        editor
      });
    }, editor.insertBreak = () => {
      if (isApplyingBehaviorOperations(editor)) {
        console.error("Unexpected call to .insertBreak(...)");
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "insert.break"
        },
        editor
      });
    }, editor.insertData = (dataTransfer) => {
      if (isApplyingBehaviorOperations(editor)) {
        console.error("Unexpected call to .insertData(...)");
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "input.*",
          originEvent: {
            dataTransfer
          }
        },
        editor
      });
    }, editor.insertSoftBreak = () => {
      if (isApplyingBehaviorOperations(editor)) {
        insertTextOperationImplementation({
          context: {
            keyGenerator: editorActor.getSnapshot().context.keyGenerator,
            schema: editorActor.getSnapshot().context.schema
          },
          operation: {
            text: `
`,
            editor
          }
        });
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "insert.soft break"
        },
        editor
      });
    }, editor.insertText = (text) => {
      if (isApplyingBehaviorOperations(editor)) {
        insertTextOperationImplementation({
          context: {
            keyGenerator: editorActor.getSnapshot().context.keyGenerator,
            schema: editorActor.getSnapshot().context.schema
          },
          operation: {
            text,
            editor
          }
        });
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "insert.text",
          text
        },
        editor
      });
    }, editor.redo = () => {
      if (isApplyingBehaviorOperations(editor)) {
        performOperation({
          context: {
            keyGenerator: editorActor.getSnapshot().context.keyGenerator,
            schema: editorActor.getSnapshot().context.schema
          },
          operation: {
            type: "history.redo",
            editor
          }
        });
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "history.redo"
        },
        editor
      });
    }, editor.select = (location) => {
      if (isApplyingBehaviorOperations(editor)) {
        select(location);
        return;
      }
      const range = Editor.range(editor, location);
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "select",
          at: slateRangeToSelection({
            schema: editorActor.getSnapshot().context.schema,
            editor,
            range
          })
        },
        editor
      });
    }, editor.setFragmentData = () => {
      console.error("Unexpected call to .setFragmentData(...)");
    }, editor.undo = () => {
      if (isApplyingBehaviorOperations(editor)) {
        performOperation({
          context: {
            keyGenerator: editorActor.getSnapshot().context.keyGenerator,
            schema: editorActor.getSnapshot().context.schema
          },
          operation: {
            type: "history.undo",
            editor
          }
        });
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "history.undo"
        },
        editor
      });
    }, editor;
  };
}
function createWithMaxBlocks(editorActor) {
  return function(editor) {
    const {
      apply: apply2
    } = editor;
    return editor.apply = (operation) => {
      if (editorActor.getSnapshot().matches({
        "edit mode": "read only"
      })) {
        apply2(operation);
        return;
      }
      if (isChangingRemotely(editor)) {
        apply2(operation);
        return;
      }
      if (isUndoing(editor) || isRedoing(editor)) {
        apply2(operation);
        return;
      }
      const rows = editorActor.getSnapshot().context.maxBlocks ?? -1;
      rows > 0 && editor.children.length >= rows && (operation.type === "insert_node" || operation.type === "split_node") && operation.path.length === 1 || apply2(operation);
    }, editor;
  };
}
function createWithObjectKeys(editorActor) {
  return function(editor) {
    const {
      apply: apply2,
      normalizeNode
    } = editor;
    return editor.apply = (operation) => {
      if (isChangingRemotely(editor)) {
        apply2(operation);
        return;
      }
      if (isUndoing(editor) || isRedoing(editor)) {
        apply2(operation);
        return;
      }
      if (operation.type === "split_node") {
        const existingKeys = [...Node.descendants(editor)].map(([node]) => node._key);
        apply2({
          ...operation,
          properties: {
            ...operation.properties,
            _key: operation.properties._key === void 0 || existingKeys.includes(operation.properties._key) ? editorActor.getSnapshot().context.keyGenerator() : operation.properties._key
          }
        });
        return;
      }
      if (operation.type === "insert_node" && !Editor.isEditor(operation.node)) {
        const existingKeys = [...Node.descendants(editor)].map(([node]) => node._key);
        apply2({
          ...operation,
          node: {
            ...operation.node,
            _key: operation.node._key === void 0 || existingKeys.includes(operation.node._key) ? editorActor.getSnapshot().context.keyGenerator() : operation.node._key
          }
        });
        return;
      }
      if (operation.type === "merge_node") {
        const index = operation.path[operation.path.length - 1], prevPath = Path.previous(operation.path), prevIndex = prevPath[prevPath.length - 1];
        if (operation.path.length !== 1 || prevPath.length !== 1) {
          apply2(operation);
          return;
        }
        const block = editor.value.at(index), previousBlock = editor.value.at(prevIndex);
        if (!block || !previousBlock) {
          apply2(operation);
          return;
        }
        if (!isTextBlock(editorActor.getSnapshot().context, block) || !isTextBlock(editorActor.getSnapshot().context, previousBlock)) {
          apply2(operation);
          return;
        }
        const previousBlockChildKeys = previousBlock.children.map((child) => child._key), previousBlockMarkDefKeys = previousBlock.markDefs?.map((markDef) => markDef._key) ?? [], markDefKeyMap = /* @__PURE__ */ new Map(), adjustedMarkDefs = block.markDefs?.map((markDef) => {
          if (previousBlockMarkDefKeys.includes(markDef._key)) {
            const newKey = editorActor.getSnapshot().context.keyGenerator();
            return markDefKeyMap.set(markDef._key, newKey), {
              ...markDef,
              _key: newKey
            };
          }
          return markDef;
        });
        let childIndex = 0;
        for (const child of block.children) {
          if (isSpan(editorActor.getSnapshot().context, child)) {
            const marks = child.marks?.map((mark) => markDefKeyMap.get(mark) || mark) ?? [];
            isEqual(child.marks, marks) || Transforms.setNodes(editor, {
              marks
            }, {
              at: [index, childIndex]
            });
          }
          previousBlockChildKeys.includes(child._key) && Transforms.setNodes(editor, {
            _key: editorActor.getSnapshot().context.keyGenerator()
          }, {
            at: [index, childIndex]
          }), childIndex++;
        }
        apply2({
          ...operation,
          properties: {
            ...operation.properties,
            // Make sure the adjusted markDefs are carried along for the merge
            // operation
            markDefs: adjustedMarkDefs
          }
        });
        return;
      }
      apply2(operation);
    }, editor.normalizeNode = (entry) => {
      const [node, path] = entry;
      if (Element$1.isElement(node) && node._type === editorActor.getSnapshot().context.schema.block.name) {
        if (!node._key) {
          editorActor.send({
            type: "normalizing"
          }), Transforms.setNodes(editor, {
            _key: editorActor.getSnapshot().context.keyGenerator()
          }, {
            at: path
          }), editorActor.send({
            type: "done normalizing"
          });
          return;
        }
        for (const [child, childPath] of Node.children(editor, path))
          if (!child._key) {
            editorActor.send({
              type: "normalizing"
            }), Transforms.setNodes(editor, {
              _key: editorActor.getSnapshot().context.keyGenerator()
            }, {
              at: childPath
            }), editorActor.send({
              type: "done normalizing"
            });
            return;
          }
      }
      normalizeNode(entry);
    }, editor;
  };
}
function createApplyPatch(schema) {
  return (editor, patch) => {
    let changed = !1;
    try {
      switch (patch.type) {
        case "insert":
          changed = insertPatch(editor, patch, schema);
          break;
        case "unset":
          changed = unsetPatch(editor, patch);
          break;
        case "set":
          changed = setPatch(editor, patch);
          break;
        case "diffMatchPatch":
          changed = diffMatchPatch(editor, patch);
          break;
      }
    } catch (err) {
      console.error(err);
    }
    return changed;
  };
}
function diffMatchPatch(editor, patch) {
  const block = findBlock(editor.children, patch.path);
  if (!block)
    return !1;
  const child = findBlockChild(block, patch.path);
  if (!child || !(block && editor.isTextBlock(block.node) && patch.path.length === 4 && patch.path[1] === "children" && patch.path[3] === "text") || !Text.isText(child.node))
    return !1;
  const patches = parse(patch.value), [newValue] = apply(patches, child.node.text, {
    allowExceedingIndices: !0
  }), diff$1 = cleanupEfficiency(diff(child.node.text, newValue), 5);
  let offset = 0;
  for (const [op, text] of diff$1)
    op === DIFF_INSERT ? (editor.apply({
      type: "insert_text",
      path: [block.index, child.index],
      offset,
      text
    }), offset += text.length) : op === DIFF_DELETE ? editor.apply({
      type: "remove_text",
      path: [block.index, child.index],
      offset,
      text
    }) : op === DIFF_EQUAL && (offset += text.length);
  return !0;
}
function insertPatch(editor, patch, schema) {
  const block = findBlock(editor.children, patch.path);
  if (!block || patch.path.length > 1 && patch.path[1] !== "children")
    return !1;
  if (patch.path.length === 1) {
    const {
      items: items2,
      position: position2
    } = patch, blocksToInsert = toSlateValue(items2, {
      schemaTypes: schema
    }, KEY_TO_SLATE_ELEMENT.get(editor)), targetBlockIndex = block.index, normalizedIdx2 = position2 === "after" ? targetBlockIndex + 1 : targetBlockIndex, editorWasEmptyBefore = isEqualToEmptyEditor(editor.children, schema);
    return Transforms.insertNodes(editor, blocksToInsert, {
      at: [normalizedIdx2]
    }), editorWasEmptyBefore && typeof patch.path[0] == "number" && patch.path[0] === 0 && Transforms.removeNodes(editor, {
      at: [position2 === "before" ? targetBlockIndex + 1 : targetBlockIndex]
    }), !0;
  }
  const {
    items,
    position
  } = patch, targetChild = findBlockChild(block, patch.path);
  if (!targetChild)
    return !1;
  const childrenToInsert = toSlateValue([{
    ...block.node,
    children: items
  }], {
    schemaTypes: schema
  }, KEY_TO_SLATE_ELEMENT.get(editor)), normalizedIdx = position === "after" ? targetChild.index + 1 : targetChild.index, childInsertPath = [block.index, normalizedIdx];
  return childrenToInsert && Element$1.isElement(childrenToInsert[0]) && Transforms.insertNodes(editor, childrenToInsert[0].children, {
    at: childInsertPath
  }), !0;
}
function setPatch(editor, patch) {
  let value = patch.value;
  typeof patch.path[3] == "string" && (value = {}, value[patch.path[3]] = patch.value);
  const block = findBlock(editor.children, patch.path);
  if (!block)
    return !1;
  const isTextBlock2 = editor.isTextBlock(block.node);
  if (isTextBlock2 && patch.path.length > 1 && patch.path[1] !== "children")
    return !1;
  const child = findBlockChild(block, patch.path);
  if (isTextBlock2 && child) {
    if (Text.isText(child.node))
      if (Text.isText(value)) {
        const oldText = child.node.text, newText = value.text;
        oldText !== newText && (editor.apply({
          type: "remove_text",
          path: [block.index, child.index],
          offset: 0,
          text: oldText
        }), editor.apply({
          type: "insert_text",
          path: [block.index, child.index],
          offset: 0,
          text: newText
        }), editor.onChange());
      } else {
        const propPath = patch.path.slice(3), propEntry = propPath.at(0);
        if (propEntry === void 0 || typeof propEntry == "string" && ["_key", "_type", "text"].includes(propEntry))
          return !1;
        const newNode = applyAll(child.node, [{
          ...patch,
          path: propPath
        }]);
        Transforms.setNodes(editor, newNode, {
          at: [block.index, child.index]
        });
      }
    else {
      const propPath = patch.path.slice(3), reservedProps = ["_key", "_type", "children", "__inline"], propEntry = propPath.at(0);
      if (propEntry === void 0 || typeof propEntry == "string" && reservedProps.includes(propEntry))
        return !1;
      const value2 = "value" in child.node && typeof child.node.value == "object" ? child.node.value : {}, newValue = applyAll(value2, [{
        ...patch,
        path: patch.path.slice(3)
      }]);
      Transforms.setNodes(editor, {
        ...child.node,
        value: newValue
      }, {
        at: [block.index, child.index]
      });
    }
    return !0;
  } else if (Element$1.isElement(block.node) && patch.path.length === 1) {
    const {
      children,
      ...nextRest
    } = value, {
      children: _prevChildren,
      ...prevRest
    } = block.node || {
      children: void 0
    };
    editor.apply({
      type: "set_node",
      path: [block.index],
      properties: {
        ...prevRest
      },
      newProperties: nextRest
    });
    const blockNode = block.node;
    blockNode.children.forEach((child2, childIndex) => {
      editor.apply({
        type: "remove_node",
        path: [block.index, blockNode.children.length - 1 - childIndex],
        node: child2
      });
    }), Array.isArray(children) && children.forEach((child2, childIndex) => {
      editor.apply({
        type: "insert_node",
        path: [block.index, childIndex],
        node: child2
      });
    });
  } else if (block && "value" in block.node)
    if (patch.path.length > 1 && patch.path[1] !== "children") {
      const newVal = applyAll(block.node.value, [{
        ...patch,
        path: patch.path.slice(1)
      }]);
      Transforms.setNodes(editor, {
        ...block.node,
        value: newVal
      }, {
        at: [block.index]
      });
    } else
      return !1;
  return !0;
}
function unsetPatch(editor, patch) {
  if (patch.path.length === 0) {
    const previousSelection = editor.selection;
    Transforms.deselect(editor);
    const children = Node.children(editor, [], {
      reverse: !0
    });
    for (const [_, path] of children)
      Transforms.removeNodes(editor, {
        at: path
      });
    return Transforms.insertNodes(editor, editor.pteCreateTextBlock({
      decorators: []
    })), previousSelection && Transforms.select(editor, {
      anchor: {
        path: [0, 0],
        offset: 0
      },
      focus: {
        path: [0, 0],
        offset: 0
      }
    }), editor.onChange(), !0;
  }
  const block = findBlock(editor.children, patch.path);
  if (!block)
    return !1;
  if (patch.path.length === 1) {
    if (editor.children.length === 1) {
      const previousSelection = editor.selection;
      return Transforms.deselect(editor), Transforms.removeNodes(editor, {
        at: [block.index]
      }), Transforms.insertNodes(editor, editor.pteCreateTextBlock({
        decorators: []
      })), previousSelection && Transforms.select(editor, {
        anchor: {
          path: [0, 0],
          offset: 0
        },
        focus: {
          path: [0, 0],
          offset: 0
        }
      }), editor.onChange(), !0;
    }
    return Transforms.removeNodes(editor, {
      at: [block.index]
    }), !0;
  }
  const child = findBlockChild(block, patch.path);
  if (editor.isTextBlock(block.node) && child && patch.path[1] === "children" && patch.path.length === 3)
    return Transforms.removeNodes(editor, {
      at: [block.index, child.index]
    }), !0;
  if (child && !Text.isText(child.node)) {
    const propEntry = patch.path.slice(3).at(0);
    if (propEntry === void 0 || typeof propEntry == "string" && ["_key", "_type", "children", "__inline"].includes(propEntry))
      return !1;
    const value = "value" in child.node && typeof child.node.value == "object" ? child.node.value : {}, newValue = applyAll(value, [{
      ...patch,
      path: patch.path.slice(3)
    }]);
    return Transforms.setNodes(editor, {
      ...child.node,
      value: newValue
    }, {
      at: [block.index, child.index]
    }), !0;
  }
  if (child && Text.isText(child.node)) {
    const propPath = patch.path.slice(3), propEntry = propPath.at(0);
    if (propEntry === void 0 || typeof propEntry == "string" && ["_key", "_type"].includes(propEntry))
      return !1;
    if (typeof propEntry == "string" && propEntry === "text")
      return editor.apply({
        type: "remove_text",
        path: [block.index, child.index],
        offset: 0,
        text: child.node.text
      }), !0;
    const newNode = applyAll(child.node, [{
      ...patch,
      path: propPath
    }]), newKeys = Object.keys(newNode), removedProperties = Object.keys(child.node).filter((property) => !newKeys.includes(property));
    return Transforms.unsetNodes(editor, removedProperties, {
      at: [block.index, child.index]
    }), !0;
  }
  if (!child) {
    if ("value" in block.node) {
      const newVal = applyAll(block.node.value, [{
        ...patch,
        path: patch.path.slice(1)
      }]);
      return Transforms.setNodes(editor, {
        ...block.node,
        value: newVal
      }, {
        at: [block.index]
      }), !0;
    }
    return !1;
  }
  return !1;
}
function findBlock(children, path) {
  let blockIndex = -1;
  const block = children.find((node, index) => {
    const isMatch = isKeyedSegment(path[0]) ? node._key === path[0]._key : index === path[0];
    return isMatch && (blockIndex = index), isMatch;
  });
  if (block)
    return {
      node: block,
      index: blockIndex
    };
}
function findBlockChild(block, path) {
  const blockNode = block.node;
  if (!Element$1.isElement(blockNode) || path[1] !== "children")
    return;
  let childIndex = -1;
  const child = blockNode.children.find((node, index) => {
    const isMatch = isKeyedSegment(path[2]) ? node._key === path[2]._key : index === path[2];
    return isMatch && (childIndex = index), isMatch;
  });
  if (child)
    return {
      node: child,
      index: childIndex
    };
}
function insertTextPatch(schema, children, operation, beforeValue) {
  const block = isTextBlock({
    schema
  }, children[operation.path[0]]) && children[operation.path[0]];
  if (!block)
    throw new Error("Could not find block");
  const textChild = isTextBlock({
    schema
  }, block) && isSpan({
    schema
  }, block.children[operation.path[1]]) && block.children[operation.path[1]];
  if (!textChild)
    throw new Error("Could not find child");
  const path = [{
    _key: block._key
  }, "children", {
    _key: textChild._key
  }, "text"], prevBlock = beforeValue[operation.path[0]], prevChild = isTextBlock({
    schema
  }, prevBlock) && prevBlock.children[operation.path[1]], prevText = isSpan({
    schema
  }, prevChild) ? prevChild.text : "", patch = diffMatchPatch$1(prevText, textChild.text, path);
  return patch.value.length ? [patch] : [];
}
function removeTextPatch(schema, children, operation, beforeValue) {
  const block = children[operation.path[0]];
  if (!block)
    throw new Error("Could not find block");
  const child = isTextBlock({
    schema
  }, block) && block.children[operation.path[1]] || void 0, textChild = isSpan({
    schema
  }, child) ? child : void 0;
  if (child && !textChild)
    throw new Error("Expected span");
  if (!textChild)
    throw new Error("Could not find child");
  const path = [{
    _key: block._key
  }, "children", {
    _key: textChild._key
  }, "text"], beforeBlock = beforeValue[operation.path[0]], prevTextChild = isTextBlock({
    schema
  }, beforeBlock) && beforeBlock.children[operation.path[1]], prevText = isSpan({
    schema
  }, prevTextChild) && prevTextChild.text, patch = diffMatchPatch$1(prevText || "", textChild.text, path);
  return patch.value ? [patch] : [];
}
function setNodePatch(schema, children, operation) {
  if (operation.path.length === 1) {
    const block = children[operation.path[0]];
    if (typeof block._key != "string")
      throw new Error("Expected block to have a _key");
    const setNode = omitBy({
      ...children[operation.path[0]],
      ...operation.newProperties
    }, isUndefined);
    return [set(fromSlateValue([setNode], schema.block.name)[0], [{
      _key: block._key
    }])];
  } else if (operation.path.length === 2) {
    const block = children[operation.path[0]];
    if (isTextBlock({
      schema
    }, block)) {
      const child = block.children[operation.path[1]];
      if (child) {
        const blockKey = block._key, childKey = child._key, patches = [], keys = Object.keys(operation.newProperties);
        return keys.forEach((keyName) => {
          if (keys.length === 1 && keyName === "_key") {
            const val = get(operation.newProperties, keyName);
            patches.push(set(val, [{
              _key: blockKey
            }, "children", block.children.indexOf(child), keyName]));
          } else {
            const val = get(operation.newProperties, keyName);
            patches.push(set(val, [{
              _key: blockKey
            }, "children", {
              _key: childKey
            }, keyName]));
          }
        }), patches;
      }
      throw new Error("Could not find a valid child");
    }
    throw new Error("Could not find a valid block");
  } else
    throw new Error(`Unexpected path encountered: ${JSON.stringify(operation.path)}`);
}
function insertNodePatch(schema, children, operation, beforeValue) {
  const block = beforeValue[operation.path[0]];
  if (operation.path.length === 1) {
    const position = operation.path[0] === 0 ? "before" : "after", beforeBlock = beforeValue[operation.path[0] - 1], targetKey = operation.path[0] === 0 ? block?._key : beforeBlock?._key;
    return targetKey ? [insert([fromSlateValue([operation.node], schema.block.name)[0]], position, [{
      _key: targetKey
    }])] : [setIfMissing(beforeValue, []), insert([fromSlateValue([operation.node], schema.block.name)[0]], "before", [operation.path[0]])];
  } else if (isTextBlock({
    schema
  }, block) && operation.path.length === 2 && children[operation.path[0]]) {
    const position = block.children.length === 0 || !block.children[operation.path[1] - 1] ? "before" : "after", node = {
      ...operation.node
    };
    !node._type && Text.isText(node) && (node._type = "span", node.marks = []);
    const child = fromSlateValue([{
      _key: "bogus",
      _type: schema.block.name,
      children: [node]
    }], schema.block.name)[0].children[0];
    return [insert([child], position, [{
      _key: block._key
    }, "children", block.children.length <= 1 || !block.children[operation.path[1] - 1] ? 0 : {
      _key: block.children[operation.path[1] - 1]._key
    }])];
  }
  return [];
}
function splitNodePatch(schema, children, operation, beforeValue) {
  const patches = [], splitBlock = children[operation.path[0]];
  if (!isTextBlock({
    schema
  }, splitBlock))
    throw new Error(`Block with path ${JSON.stringify(operation.path[0])} is not a text block and can't be split`);
  if (operation.path.length === 1) {
    const oldBlock = beforeValue[operation.path[0]];
    if (isTextBlock({
      schema
    }, oldBlock)) {
      const targetValue = fromSlateValue([children[operation.path[0] + 1]], schema.block.name)[0];
      targetValue && (patches.push(insert([targetValue], "after", [{
        _key: splitBlock._key
      }])), oldBlock.children.slice(operation.position).forEach((span) => {
        const path = [{
          _key: oldBlock._key
        }, "children", {
          _key: span._key
        }];
        patches.push(unset(path));
      }));
    }
    return patches;
  }
  if (operation.path.length === 2) {
    const splitSpan = splitBlock.children[operation.path[1]];
    if (isSpan({
      schema
    }, splitSpan)) {
      const targetSpans = fromSlateValue([{
        ...splitBlock,
        children: splitBlock.children.slice(operation.path[1] + 1, operation.path[1] + 2)
      }], schema.block.name)[0].children;
      patches.push(insert(targetSpans, "after", [{
        _key: splitBlock._key
      }, "children", {
        _key: splitSpan._key
      }])), patches.push(set(splitSpan.text, [{
        _key: splitBlock._key
      }, "children", {
        _key: splitSpan._key
      }, "text"]));
    }
    return patches;
  }
  return patches;
}
function removeNodePatch(schema, beforeValue, operation) {
  const block = beforeValue[operation.path[0]];
  if (operation.path.length === 1) {
    if (block && block._key)
      return [unset([{
        _key: block._key
      }])];
    throw new Error("Block not found");
  } else if (isTextBlock({
    schema
  }, block) && operation.path.length === 2) {
    const spanToRemove = block.children[operation.path[1]];
    return spanToRemove ? block.children.filter((span) => span._key === operation.node._key).length > 1 ? (console.warn(`Multiple spans have \`_key\` ${operation.node._key}. It's ambiguous which one to remove.`, JSON.stringify(block, null, 2)), []) : [unset([{
      _key: block._key
    }, "children", {
      _key: spanToRemove._key
    }])] : [];
  } else
    return [];
}
function mergeNodePatch(schema, children, operation, beforeValue) {
  const patches = [], block = beforeValue[operation.path[0]], updatedBlock = children[operation.path[0]];
  if (operation.path.length === 1)
    if (block?._key) {
      const newBlock = fromSlateValue([children[operation.path[0] - 1]], schema.block.name)[0];
      patches.push(set(newBlock, [{
        _key: newBlock._key
      }])), patches.push(unset([{
        _key: block._key
      }]));
    } else
      throw new Error("Target key not found!");
  else if (isTextBlock({
    schema
  }, block) && isTextBlock({
    schema
  }, updatedBlock) && operation.path.length === 2) {
    const updatedSpan = updatedBlock.children[operation.path[1] - 1] && isSpan({
      schema
    }, updatedBlock.children[operation.path[1] - 1]) ? updatedBlock.children[operation.path[1] - 1] : void 0, removedSpan = block.children[operation.path[1]] && isSpan({
      schema
    }, block.children[operation.path[1]]) ? block.children[operation.path[1]] : void 0;
    updatedSpan && (block.children.filter((span) => span._key === updatedSpan._key).length === 1 ? patches.push(set(updatedSpan.text, [{
      _key: block._key
    }, "children", {
      _key: updatedSpan._key
    }, "text"])) : console.warn(`Multiple spans have \`_key\` ${updatedSpan._key}. It's ambiguous which one to update.`, JSON.stringify(block, null, 2))), removedSpan && (block.children.filter((span) => span._key === removedSpan._key).length === 1 ? patches.push(unset([{
      _key: block._key
    }, "children", {
      _key: removedSpan._key
    }])) : console.warn(`Multiple spans have \`_key\` ${removedSpan._key}. It's ambiguous which one to remove.`, JSON.stringify(block, null, 2)));
  }
  return patches;
}
function moveNodePatch(schema, beforeValue, operation) {
  const patches = [], block = beforeValue[operation.path[0]], targetBlock = beforeValue[operation.newPath[0]];
  if (!targetBlock)
    return patches;
  if (operation.path.length === 1) {
    const position = operation.path[0] > operation.newPath[0] ? "before" : "after";
    patches.push(unset([{
      _key: block._key
    }])), patches.push(insert([fromSlateValue([block], schema.block.name)[0]], position, [{
      _key: targetBlock._key
    }]));
  } else if (operation.path.length === 2 && isTextBlock({
    schema
  }, block) && isTextBlock({
    schema
  }, targetBlock)) {
    const child = block.children[operation.path[1]], targetChild = targetBlock.children[operation.newPath[1]], position = operation.newPath[1] === targetBlock.children.length ? "after" : "before", childToInsert = fromSlateValue([block], schema.block.name)[0].children[operation.path[1]];
    patches.push(unset([{
      _key: block._key
    }, "children", {
      _key: child._key
    }])), patches.push(insert([childToInsert], position, [{
      _key: targetBlock._key
    }, "children", {
      _key: targetChild._key
    }]));
  }
  return patches;
}
const PATCHING = /* @__PURE__ */ new WeakMap();
function withoutPatching(editor, fn) {
  const prev = isPatching(editor);
  PATCHING.set(editor, !1), fn(), PATCHING.set(editor, prev);
}
function isPatching(editor) {
  return PATCHING.get(editor);
}
const debug$c = debugWithName("plugin:withPatches");
function createWithPatches({
  editorActor,
  relayActor,
  subscriptions
}) {
  let previousChildren;
  const applyPatch = createApplyPatch(editorActor.getSnapshot().context.schema);
  return function(editor) {
    IS_PROCESSING_REMOTE_CHANGES.set(editor, !1), PATCHING.set(editor, !0), previousChildren = [...editor.children];
    const {
      apply: apply2
    } = editor;
    let bufferedPatches = [];
    const handleBufferedRemotePatches = () => {
      if (bufferedPatches.length === 0)
        return;
      const patches = bufferedPatches;
      bufferedPatches = [];
      let changed = !1;
      withRemoteChanges(editor, () => {
        Editor.withoutNormalizing(editor, () => {
          withoutPatching(editor, () => {
            withoutSaving(editor, () => {
              for (const patch of patches) {
                debug$c.enabled && debug$c(`Handling remote patch ${JSON.stringify(patch)}`);
                try {
                  changed = applyPatch(editor, patch);
                } catch (error) {
                  console.error(`Applying patch ${JSON.stringify(patch)} failed due to: ${error.message}`);
                }
              }
            });
          });
        }), changed && (editor.normalize(), editor.onChange());
      });
    }, handlePatches = ({
      patches
    }) => {
      const remotePatches = patches.filter((p) => p.origin !== "local");
      remotePatches.length !== 0 && (bufferedPatches = bufferedPatches.concat(remotePatches), handleBufferedRemotePatches());
    };
    return subscriptions.push(() => {
      debug$c("Subscribing to remote patches");
      const sub = editorActor.on("patches", handlePatches);
      return () => {
        debug$c("Unsubscribing to remote patches"), sub.unsubscribe();
      };
    }), editor.apply = (operation) => {
      let patches = [];
      previousChildren = editor.children;
      const editorWasEmpty = isEqualToEmptyEditor(previousChildren, editorActor.getSnapshot().context.schema);
      apply2(operation);
      const editorIsEmpty = isEqualToEmptyEditor(editor.children, editorActor.getSnapshot().context.schema);
      if (!isPatching(editor))
        return editor;
      switch (editorWasEmpty && !editorIsEmpty && operation.type !== "set_selection" && patches.push(insert(previousChildren, "before", [0])), operation.type) {
        case "insert_text":
          patches = [...patches, ...insertTextPatch(editorActor.getSnapshot().context.schema, editor.children, operation, previousChildren)];
          break;
        case "remove_text":
          patches = [...patches, ...removeTextPatch(editorActor.getSnapshot().context.schema, editor.children, operation, previousChildren)];
          break;
        case "remove_node":
          patches = [...patches, ...removeNodePatch(editorActor.getSnapshot().context.schema, previousChildren, operation)];
          break;
        case "split_node":
          patches = [...patches, ...splitNodePatch(editorActor.getSnapshot().context.schema, editor.children, operation, previousChildren)];
          break;
        case "insert_node":
          patches = [...patches, ...insertNodePatch(editorActor.getSnapshot().context.schema, editor.children, operation, previousChildren)];
          break;
        case "set_node":
          patches = [...patches, ...setNodePatch(editorActor.getSnapshot().context.schema, editor.children, operation)];
          break;
        case "merge_node":
          patches = [...patches, ...mergeNodePatch(editorActor.getSnapshot().context.schema, editor.children, operation, previousChildren)];
          break;
        case "move_node":
          patches = [...patches, ...moveNodePatch(editorActor.getSnapshot().context.schema, previousChildren, operation)];
          break;
      }
      if (!editorWasEmpty && editorIsEmpty && ["merge_node", "set_node", "remove_text", "remove_node"].includes(operation.type) && (patches = [...patches, unset([])], relayActor.send({
        type: "unset",
        previousValue: fromSlateValue(previousChildren, editorActor.getSnapshot().context.schema.block.name, KEY_TO_VALUE_ELEMENT.get(editor))
      })), editorWasEmpty && patches.length > 0 && (patches = [setIfMissing([], []), ...patches]), patches.length > 0)
        for (const patch of patches)
          editorActor.send({
            type: "internal.patch",
            patch: {
              ...patch,
              origin: "local"
            },
            operationId: getCurrentOperationId(editor),
            value: editor.value
          });
      return editor;
    }, editor;
  };
}
const debug$b = debugWithName("plugin:withPlaceholderBlock");
function createWithPlaceholderBlock(editorActor) {
  return function(editor) {
    const {
      apply: apply2
    } = editor;
    return editor.apply = (op) => {
      if (editorActor.getSnapshot().matches({
        "edit mode": "read only"
      })) {
        apply2(op);
        return;
      }
      if (isChangingRemotely(editor)) {
        apply2(op);
        return;
      }
      if (isUndoing(editor) || isRedoing(editor)) {
        apply2(op);
        return;
      }
      if (op.type === "remove_node") {
        const blockIndex = op.path.at(0), isLonelyBlock = op.path.length === 1 && blockIndex === 0 && editor.children.length === 1, isBlockObject = op.node._type !== editorActor.getSnapshot().context.schema.block.name;
        isLonelyBlock && isBlockObject && (debug$b("Adding placeholder block"), Editor.insertNode(editor, editor.pteCreateTextBlock({
          decorators: []
        })));
      }
      apply2(op);
    }, editor;
  };
}
const debug$a = debugWithName("plugin:withSchemaTypes");
function createWithSchemaTypes({
  editorActor
}) {
  return function(editor) {
    editor.isTextBlock = (value) => Editor.isEditor(value) ? !1 : isTextBlock(editorActor.getSnapshot().context, value), editor.isTextSpan = (value) => Editor.isEditor(value) ? !1 : isSpan(editorActor.getSnapshot().context, value), editor.isListBlock = (value) => Editor.isEditor(value) ? !1 : isListBlock(editorActor.getSnapshot().context, value), editor.isVoid = (element) => Editor.isEditor(element) ? !1 : editorActor.getSnapshot().context.schema.block.name !== element._type && (editorActor.getSnapshot().context.schema.blockObjects.map((obj) => obj.name).includes(element._type) || editorActor.getSnapshot().context.schema.inlineObjects.map((obj) => obj.name).includes(element._type)), editor.isInline = (element) => Editor.isEditor(element) ? !1 : editorActor.getSnapshot().context.schema.inlineObjects.map((obj) => obj.name).includes(element._type) && "__inline" in element && element.__inline === !0;
    const {
      normalizeNode
    } = editor;
    return editor.normalizeNode = (entry) => {
      const [node, path] = entry;
      if (node._type === void 0 && path.length === 2) {
        debug$a("Setting span type on text node without a type");
        const span = node, key = span._key || editorActor.getSnapshot().context.keyGenerator();
        editorActor.send({
          type: "normalizing"
        }), Transforms.setNodes(editor, {
          ...span,
          _type: editorActor.getSnapshot().context.schema.span.name,
          _key: key
        }, {
          at: path
        }), editorActor.send({
          type: "done normalizing"
        });
        return;
      }
      if (node._key === void 0 && (path.length === 1 || path.length === 2)) {
        debug$a("Setting missing key on child node without a key");
        const key = editorActor.getSnapshot().context.keyGenerator();
        editorActor.send({
          type: "normalizing"
        }), Transforms.setNodes(editor, {
          _key: key
        }, {
          at: path
        }), editorActor.send({
          type: "done normalizing"
        });
        return;
      }
      normalizeNode(entry);
    }, editor;
  };
}
function createWithUtils({
  editorActor
}) {
  return function(editor) {
    return editor.pteCreateTextBlock = (options) => toSlateValue([{
      _type: editorActor.getSnapshot().context.schema.block.name,
      _key: editorActor.getSnapshot().context.keyGenerator(),
      style: editorActor.getSnapshot().context.schema.styles[0].name || "normal",
      ...options.listItem ? {
        listItem: options.listItem
      } : {},
      ...options.level ? {
        level: options.level
      } : {},
      markDefs: [],
      children: [{
        _type: "span",
        _key: editorActor.getSnapshot().context.keyGenerator(),
        text: "",
        marks: options.decorators.filter((decorator) => editorActor.getSnapshot().context.schema.decorators.find(({
          name
        }) => name === decorator))
      }]
    }], {
      schemaTypes: editorActor.getSnapshot().context.schema
    })[0], editor;
  };
}
function pluginUpdateSelection({
  editor,
  editorActor
}) {
  const updateSelection = () => {
    if (editor.selection) {
      const existingSelection = SLATE_TO_PORTABLE_TEXT_RANGE.get(editor.selection);
      if (existingSelection)
        editorActor.send({
          type: "update selection",
          selection: existingSelection
        });
      else {
        const selection = slateRangeToSelection({
          schema: editorActor.getSnapshot().context.schema,
          editor,
          range: editor.selection
        });
        SLATE_TO_PORTABLE_TEXT_RANGE.set(editor.selection, selection), editorActor.send({
          type: "update selection",
          selection
        });
      }
    } else
      editorActor.send({
        type: "update selection",
        selection: null
      });
  }, {
    onChange
  } = editor;
  return editor.onChange = () => {
    onChange(), editorActor.getSnapshot().matches({
      setup: "setting up"
    }) || updateSelection();
  }, editor;
}
function isEditorNode(node) {
  return typeof node == "object" && node !== null ? !("_type" in node) && "children" in node && Array.isArray(node.children) : !1;
}
function isTextBlockNode(context, node) {
  return isTypedObject(node) && node._type === context.schema.block.name;
}
function isSpanNode(context, node) {
  return typeof node != "object" || node === null || "children" in node ? !1 : "_type" in node ? node._type === context.schema.span.name : "text" in node;
}
function isPartialSpanNode(node) {
  return typeof node == "object" && node !== null && "text" in node && typeof node.text == "string";
}
function isObjectNode(context, node) {
  return !isEditorNode(node) && !isTextBlockNode(context, node) && !isSpanNode(context, node) && !isPartialSpanNode(node);
}
function getBlock(root, path) {
  const index = path.at(0);
  if (!(index === void 0 || path.length !== 1))
    return root.children.at(index);
}
function getNode(context, root, path) {
  if (path.length === 0)
    return root;
  if (path.length === 1)
    return getBlock(root, path);
  if (path.length === 2) {
    const block = getBlock(root, path.slice(0, 1));
    return !block || !isTextBlockNode(context, block) ? void 0 : block.children.at(path[1]) || void 0;
  }
}
function getSpan(context, root, path) {
  const node = getNode(context, root, path);
  if (node && isSpanNode(context, node))
    return node;
}
function getParent(context, root, path) {
  if (path.length === 0)
    return;
  const parentPath = path.slice(0, -1);
  if (parentPath.length === 0)
    return root;
  const blockIndex = parentPath.at(0);
  if (blockIndex === void 0 || parentPath.length !== 1)
    return;
  const block = root.children.at(blockIndex);
  if (block && isTextBlockNode(context, block))
    return block;
}
function applyOperationToPortableText(context, value, operation) {
  const draft = createDraft({
    children: value
  });
  try {
    applyOperationToPortableTextDraft(context, draft, operation);
  } catch (e) {
    console.error(e);
  }
  return finishDraft(draft).children;
}
function applyOperationToPortableTextDraft(context, root, operation) {
  switch (operation.type) {
    case "insert_node": {
      const {
        path,
        node: insertedNode
      } = operation, parent = getParent(context, root, path), index = path[path.length - 1];
      if (!parent || index > parent.children.length)
        break;
      if (path.length === 1) {
        if (isTextBlockNode(context, insertedNode)) {
          parent.children.splice(index, 0, {
            ...insertedNode,
            children: insertedNode.children.map((child) => "__inline" in child ? {
              _key: child._key,
              _type: child._type,
              ..."value" in child && typeof child.value == "object" ? child.value : {}
            } : child)
          });
          break;
        }
        if (Element$1.isElement(insertedNode) && !("__inline" in insertedNode)) {
          parent.children.splice(index, 0, {
            _key: insertedNode._key,
            _type: insertedNode._type,
            ..."value" in insertedNode && typeof insertedNode.value == "object" ? insertedNode.value : {}
          });
          break;
        }
      }
      if (path.length === 2) {
        if (!isTextBlockNode(context, parent))
          break;
        if (isPartialSpanNode(insertedNode)) {
          parent.children.splice(index, 0, insertedNode);
          break;
        }
        if ("__inline" in insertedNode) {
          parent.children.splice(index, 0, {
            _key: insertedNode._key,
            _type: insertedNode._type,
            ..."value" in insertedNode && typeof insertedNode.value == "object" ? insertedNode.value : {}
          });
          break;
        }
      }
      break;
    }
    case "insert_text": {
      const {
        path,
        offset,
        text
      } = operation;
      if (text.length === 0) break;
      const span = getSpan(context, root, path);
      if (!span)
        break;
      const before = span.text.slice(0, offset), after = span.text.slice(offset);
      span.text = before + text + after;
      break;
    }
    case "merge_node": {
      const {
        path
      } = operation, node = getNode(context, root, path), prevPath = Path.previous(path), prev = getNode(context, root, prevPath), parent = getParent(context, root, path);
      if (!node || !prev || !parent)
        break;
      const index = path[path.length - 1];
      if (isPartialSpanNode(node) && isPartialSpanNode(prev))
        prev.text += node.text;
      else if (isTextBlockNode(context, node) && isTextBlockNode(context, prev))
        prev.children.push(...node.children);
      else
        break;
      parent.children.splice(index, 1);
      break;
    }
    case "move_node": {
      const {
        path,
        newPath
      } = operation;
      if (Path.isAncestor(path, newPath))
        break;
      const node = getNode(context, root, path), parent = getParent(context, root, path), index = path[path.length - 1];
      if (!node || !parent)
        break;
      parent.children.splice(index, 1);
      const truePath = Path.transform(path, operation), newParent = getNode(context, root, Path.parent(truePath)), newIndex = truePath[truePath.length - 1];
      if (!newParent || !("children" in newParent) || !Array.isArray(newParent.children))
        break;
      newParent.children.splice(newIndex, 0, node);
      break;
    }
    case "remove_node": {
      const {
        path
      } = operation, index = path[path.length - 1];
      getParent(context, root, path)?.children.splice(index, 1);
      break;
    }
    case "remove_text": {
      const {
        path,
        offset,
        text
      } = operation;
      if (text.length === 0)
        break;
      const span = getSpan(context, root, path);
      if (!span)
        break;
      const before = span.text.slice(0, offset), after = span.text.slice(offset + text.length);
      span.text = before + after;
      break;
    }
    case "set_node": {
      const {
        path,
        properties,
        newProperties
      } = operation, node = getNode(context, root, path);
      if (!node || isEditorNode(node))
        break;
      if (isObjectNode(context, node)) {
        const valueBefore = "value" in properties && typeof properties.value == "object" ? properties.value : {}, valueAfter = "value" in newProperties && typeof newProperties.value == "object" ? newProperties.value : {};
        for (const key in newProperties) {
          if (key === "value")
            continue;
          const value = newProperties[key];
          value == null ? delete node[key] : node[key] = value;
        }
        for (const key in properties)
          key !== "value" && (newProperties.hasOwnProperty(key) || delete node[key]);
        for (const key in valueAfter) {
          const value = valueAfter[key];
          value == null ? delete node[key] : node[key] = value;
        }
        for (const key in valueBefore)
          valueAfter.hasOwnProperty(key) || delete node[key];
        break;
      }
      if (isTextBlockNode(context, node)) {
        for (const key in newProperties) {
          if (key === "children" || key === "text")
            break;
          const value = newProperties[key];
          value == null ? delete node[key] : node[key] = value;
        }
        for (const key in properties)
          newProperties.hasOwnProperty(key) || delete node[key];
        break;
      }
      if (isPartialSpanNode(node)) {
        for (const key in newProperties) {
          if (key === "text")
            break;
          const value = newProperties[key];
          value == null ? delete node[key] : node[key] = value;
        }
        for (const key in properties)
          newProperties.hasOwnProperty(key) || delete node[key];
        break;
      }
      break;
    }
    case "split_node": {
      const {
        path,
        position,
        properties
      } = operation;
      if (path.length === 0)
        break;
      const parent = getParent(context, root, path), index = path[path.length - 1];
      if (!parent)
        break;
      if (isEditorNode(parent)) {
        const block = getBlock(root, path);
        if (!block || !isTextBlockNode(context, block))
          break;
        const before = block.children.slice(0, position), after = block.children.slice(position);
        block.children = before;
        const newTextBlockNode = {
          ...properties,
          children: after,
          _type: context.schema.block.name
        };
        parent.children.splice(index + 1, 0, newTextBlockNode);
        break;
      }
      if (isTextBlockNode(context, parent)) {
        const node = getNode(context, root, path);
        if (!node || !isSpanNode(context, node))
          break;
        const before = node.text.slice(0, position), after = node.text.slice(position);
        node.text = before;
        const newSpanNode = {
          ...properties,
          text: after
        };
        parent.children.splice(index + 1, 0, newSpanNode);
      }
      break;
    }
  }
  return root;
}
function pluginUpdateValue(context, editor) {
  const {
    apply: apply2
  } = editor;
  return editor.apply = (operation) => {
    if (operation.type === "set_selection") {
      apply2(operation);
      return;
    }
    if (editor.value = applyOperationToPortableText(context, editor.value, operation), operation.type === "insert_text" || operation.type === "remove_text") {
      apply2(operation);
      return;
    }
    buildIndexMaps({
      schema: context.schema,
      value: editor.value
    }, {
      blockIndexMap: editor.blockIndexMap,
      listIndexMap: editor.listIndexMap
    }), apply2(operation);
  }, editor;
}
const withPlugins = (editor, options) => {
  const e = editor, {
    editorActor,
    relayActor
  } = options, withObjectKeys = createWithObjectKeys(editorActor), withSchemaTypes = createWithSchemaTypes({
    editorActor
  }), withPatches = createWithPatches({
    editorActor,
    relayActor,
    subscriptions: options.subscriptions
  }), withMaxBlocks = createWithMaxBlocks(editorActor), withUndoRedo = createWithUndoRedo({
    editorActor,
    subscriptions: options.subscriptions
  }), withPortableTextMarkModel = createWithPortableTextMarkModel(editorActor), withPlaceholderBlock = createWithPlaceholderBlock(editorActor), withUtils = createWithUtils({
    editorActor
  });
  return createWithEventListeners(editorActor)(withSchemaTypes(withObjectKeys(withPortableTextMarkModel(withPlaceholderBlock(withUtils(withMaxBlocks(withUndoRedo(withPatches(pluginUpdateValue(editorActor.getSnapshot().context, pluginUpdateSelection({
    editorActor,
    editor: e
  })))))))))));
}, debug$9 = debugWithName("setup");
function createSlateEditor(config) {
  debug$9("Creating new Slate editor instance");
  const instance = withPlugins(withReact(createEditor()), {
    editorActor: config.editorActor,
    relayActor: config.relayActor,
    subscriptions: config.subscriptions
  });
  KEY_TO_VALUE_ELEMENT.set(instance, {}), KEY_TO_SLATE_ELEMENT.set(instance, {}), instance.decoratedRanges = [], instance.decoratorState = {};
  const placeholderBlock = createPlaceholderBlock(config.editorActor.getSnapshot().context);
  instance.value = [placeholderBlock], instance.blockIndexMap = /* @__PURE__ */ new Map(), instance.listIndexMap = /* @__PURE__ */ new Map(), buildIndexMaps({
    schema: config.editorActor.getSnapshot().context.schema,
    value: instance.value
  }, {
    blockIndexMap: instance.blockIndexMap,
    listIndexMap: instance.listIndexMap
  });
  const initialValue = toSlateValue(instance.value, {
    schemaTypes: config.editorActor.getSnapshot().context.schema
  });
  return {
    instance,
    initialValue
  };
}
function createEditorDom(sendBack, slateEditor) {
  return {
    getBlockNodes: (snapshot) => getBlockNodes(slateEditor, snapshot),
    getChildNodes: (snapshot) => getChildNodes(slateEditor, snapshot),
    setDragGhost: ({
      event,
      ghost
    }) => setDragGhost({
      sendBack,
      event,
      ghost
    })
  };
}
function getBlockNodes(slateEditor, snapshot) {
  if (!snapshot.context.selection)
    return [];
  const range = toSlateRange(snapshot);
  if (!range)
    return [];
  try {
    return Array.from(Editor.nodes(slateEditor, {
      at: range,
      mode: "highest",
      match: (n) => !Editor.isEditor(n)
    })).map(([blockNode]) => DOMEditor.toDOMNode(slateEditor, blockNode));
  } catch {
    return [];
  }
}
function getChildNodes(slateEditor, snapshot) {
  if (!snapshot.context.selection)
    return [];
  const range = toSlateRange(snapshot);
  if (!range)
    return [];
  try {
    return Array.from(Editor.nodes(slateEditor, {
      at: range,
      mode: "lowest",
      match: (n) => !Editor.isEditor(n)
    })).map(([childNode]) => DOMEditor.toDOMNode(slateEditor, childNode));
  } catch {
    return [];
  }
}
function setDragGhost({
  sendBack,
  event,
  ghost
}) {
  event.originEvent.dataTransfer.setDragImage(ghost.element, ghost.x, ghost.y), sendBack({
    type: "set drag ghost",
    ghost: ghost.element
  });
}
const addAnnotationOnCollapsedSelection = defineBehavior({
  on: "annotation.add",
  guard: ({
    snapshot
  }) => {
    if (!isSelectionCollapsed$1(snapshot))
      return !1;
    const caretWordSelection = getCaretWordSelection(snapshot);
    return !caretWordSelection || !isSelectionExpanded({
      context: {
        ...snapshot.context,
        selection: caretWordSelection
      }
    }) ? !1 : {
      caretWordSelection
    };
  },
  actions: [({
    event
  }, {
    caretWordSelection
  }) => [raise({
    type: "select",
    at: caretWordSelection
  }), raise({
    type: "annotation.add",
    annotation: event.annotation
  })]]
}), coreAnnotationBehaviors = {
  addAnnotationOnCollapsedSelection
}, defaultKeyboardShortcuts = {
  arrowDown: createKeyboardShortcut({
    default: [{
      key: "ArrowDown",
      alt: !1,
      ctrl: !1,
      meta: !1,
      shift: !1
    }]
  }),
  arrowUp: createKeyboardShortcut({
    default: [{
      key: "ArrowUp",
      alt: !1,
      ctrl: !1,
      meta: !1,
      shift: !1
    }]
  }),
  break: createKeyboardShortcut({
    default: [{
      key: "Enter",
      shift: !1
    }]
  }),
  lineBreak: createKeyboardShortcut({
    default: [{
      key: "Enter",
      shift: !0
    }]
  }),
  decorators: {
    strong: bold,
    em: italic,
    underline,
    code
  },
  history: {
    undo,
    redo
  },
  tab: createKeyboardShortcut({
    default: [{
      key: "Tab",
      alt: !1,
      ctrl: !1,
      meta: !1,
      shift: !1
    }]
  }),
  shiftTab: createKeyboardShortcut({
    default: [{
      key: "Tab",
      alt: !1,
      ctrl: !1,
      meta: !1,
      shift: !0
    }]
  })
}, arrowDownOnLonelyBlockObject = defineBehavior({
  on: "keyboard.keydown",
  guard: ({
    snapshot,
    event
  }) => {
    if (!defaultKeyboardShortcuts.arrowDown.guard(event.originEvent) || !isSelectionCollapsed$1(snapshot))
      return !1;
    const focusBlockObject = getFocusBlockObject(snapshot), nextBlock = getNextBlock(snapshot);
    return focusBlockObject && !nextBlock;
  },
  actions: [({
    snapshot
  }) => [raise({
    type: "insert.block",
    block: {
      _type: snapshot.context.schema.block.name
    },
    placement: "after"
  })]]
}), arrowUpOnLonelyBlockObject = defineBehavior({
  on: "keyboard.keydown",
  guard: ({
    snapshot,
    event
  }) => {
    if (!defaultKeyboardShortcuts.arrowUp.guard(event.originEvent) || !isSelectionCollapsed$1(snapshot))
      return !1;
    const focusBlockObject = getFocusBlockObject(snapshot), previousBlock = getPreviousBlock(snapshot);
    return focusBlockObject && !previousBlock;
  },
  actions: [({
    snapshot
  }) => [raise({
    type: "insert.block",
    block: {
      _type: snapshot.context.schema.block.name
    },
    placement: "before"
  })]]
}), breakingBlockObject = defineBehavior({
  on: "insert.break",
  guard: ({
    snapshot
  }) => {
    const focusBlockObject = getFocusBlockObject(snapshot);
    return isSelectionCollapsed$1(snapshot) && focusBlockObject !== void 0;
  },
  actions: [({
    snapshot
  }) => [raise({
    type: "insert.block",
    block: {
      _type: snapshot.context.schema.block.name
    },
    placement: "after"
  })]]
}), clickingAboveLonelyBlockObject = defineBehavior({
  on: "mouse.click",
  guard: ({
    snapshot,
    event
  }) => {
    if (snapshot.context.readOnly || snapshot.context.selection && !isSelectionCollapsed$1(snapshot))
      return !1;
    const focusBlockObject = getFocusBlockObject({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: event.position.selection
      }
    }), previousBlock = getPreviousBlock({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: event.position.selection
      }
    });
    return event.position.isEditor && event.position.block === "start" && focusBlockObject && !previousBlock;
  },
  actions: [({
    snapshot,
    event
  }) => [raise({
    type: "select",
    at: event.position.selection
  }), raise({
    type: "insert.block",
    block: {
      _type: snapshot.context.schema.block.name
    },
    placement: "before",
    select: "start"
  })]]
}), clickingBelowLonelyBlockObject = defineBehavior({
  on: "mouse.click",
  guard: ({
    snapshot,
    event
  }) => {
    if (snapshot.context.readOnly || snapshot.context.selection && !isSelectionCollapsed$1(snapshot))
      return !1;
    const focusBlockObject = getFocusBlockObject({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: event.position.selection
      }
    }), nextBlock = getNextBlock({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: event.position.selection
      }
    });
    return event.position.isEditor && event.position.block === "end" && focusBlockObject && !nextBlock;
  },
  actions: [({
    snapshot,
    event
  }) => [raise({
    type: "select",
    at: event.position.selection
  }), raise({
    type: "insert.block",
    block: {
      _type: snapshot.context.schema.block.name
    },
    placement: "after",
    select: "start"
  })]]
}), deletingEmptyTextBlockAfterBlockObject = defineBehavior({
  on: "delete.backward",
  guard: ({
    snapshot
  }) => {
    const focusTextBlock = getFocusTextBlock(snapshot), selectionCollapsed = isSelectionCollapsed$1(snapshot), previousBlock = getPreviousBlock(snapshot);
    return !focusTextBlock || !selectionCollapsed || !previousBlock ? !1 : isEmptyTextBlock(snapshot.context, focusTextBlock.node) && !isTextBlock(snapshot.context, previousBlock.node) ? {
      focusTextBlock,
      previousBlock
    } : !1;
  },
  actions: [(_, {
    focusTextBlock,
    previousBlock
  }) => [raise({
    type: "delete.block",
    at: focusTextBlock.path
  }), raise({
    type: "select",
    at: {
      anchor: {
        path: previousBlock.path,
        offset: 0
      },
      focus: {
        path: previousBlock.path,
        offset: 0
      }
    }
  })]]
}), deletingEmptyTextBlockBeforeBlockObject = defineBehavior({
  on: "delete.forward",
  guard: ({
    snapshot
  }) => {
    const focusTextBlock = getFocusTextBlock(snapshot), selectionCollapsed = isSelectionCollapsed$1(snapshot), nextBlock = getNextBlock(snapshot);
    return !focusTextBlock || !selectionCollapsed || !nextBlock ? !1 : isEmptyTextBlock(snapshot.context, focusTextBlock.node) && !isTextBlock(snapshot.context, nextBlock.node) ? {
      focusTextBlock,
      nextBlock
    } : !1;
  },
  actions: [(_, {
    focusTextBlock,
    nextBlock
  }) => [raise({
    type: "delete.block",
    at: focusTextBlock.path
  }), raise({
    type: "select",
    at: {
      anchor: {
        path: nextBlock.path,
        offset: 0
      },
      focus: {
        path: nextBlock.path,
        offset: 0
      }
    }
  })]]
}), coreBlockObjectBehaviors = {
  arrowDownOnLonelyBlockObject,
  arrowUpOnLonelyBlockObject,
  breakingBlockObject,
  clickingAboveLonelyBlockObject,
  clickingBelowLonelyBlockObject,
  deletingEmptyTextBlockAfterBlockObject,
  deletingEmptyTextBlockBeforeBlockObject
}, coreDecoratorBehaviors = {
  strongShortcut: defineBehavior({
    on: "keyboard.keydown",
    guard: ({
      snapshot,
      event
    }) => defaultKeyboardShortcuts.decorators.strong.guard(event.originEvent) && snapshot.context.schema.decorators.some((decorator) => decorator.name === "strong"),
    actions: [() => [raise({
      type: "decorator.toggle",
      decorator: "strong"
    })]]
  }),
  emShortcut: defineBehavior({
    on: "keyboard.keydown",
    guard: ({
      snapshot,
      event
    }) => defaultKeyboardShortcuts.decorators.em.guard(event.originEvent) && snapshot.context.schema.decorators.some((decorator) => decorator.name === "em"),
    actions: [() => [raise({
      type: "decorator.toggle",
      decorator: "em"
    })]]
  }),
  underlineShortcut: defineBehavior({
    on: "keyboard.keydown",
    guard: ({
      snapshot,
      event
    }) => defaultKeyboardShortcuts.decorators.underline.guard(event.originEvent) && snapshot.context.schema.decorators.some((decorator) => decorator.name === "underline"),
    actions: [() => [raise({
      type: "decorator.toggle",
      decorator: "underline"
    })]]
  }),
  codeShortcut: defineBehavior({
    on: "keyboard.keydown",
    guard: ({
      snapshot,
      event
    }) => defaultKeyboardShortcuts.decorators.code.guard(event.originEvent) && snapshot.context.schema.decorators.some((decorator) => decorator.name === "code"),
    actions: [() => [raise({
      type: "decorator.toggle",
      decorator: "code"
    })]]
  })
};
function getCompoundClientRect(nodes) {
  if (nodes.length === 0)
    return new DOMRect(0, 0, 0, 0);
  const elements = nodes.filter((node) => node instanceof Element), firstRect = elements.at(0)?.getBoundingClientRect();
  if (!firstRect)
    return new DOMRect(0, 0, 0, 0);
  let left = firstRect.left, top = firstRect.top, right = firstRect.right, bottom = firstRect.bottom;
  for (let i = 1; i < elements.length; i++) {
    const rect = elements[i].getBoundingClientRect();
    left = Math.min(left, rect.left), top = Math.min(top, rect.top), right = Math.max(right, rect.right), bottom = Math.max(bottom, rect.bottom);
  }
  return new DOMRect(left, top, right - left, bottom - top);
}
const coreDndBehaviors = [
  /**
   * Core Behavior that:
   * 1. Calculates and selects a "drag selection"
   * 2. Constructs and sets a drag ghost element
   * 3. Forwards the dragstart event
   */
  defineBehavior({
    on: "drag.dragstart",
    guard: ({
      snapshot,
      dom,
      event
    }) => {
      const dragSelection = getDragSelection({
        snapshot,
        eventSelection: event.position.selection
      }), selectingEntireBlocks = isSelectingEntireBlocks({
        ...snapshot,
        context: {
          ...snapshot.context,
          selection: dragSelection
        }
      }), draggedDomNodes = {
        blockNodes: dom.getBlockNodes({
          ...snapshot,
          context: {
            ...snapshot.context,
            selection: dragSelection
          }
        }),
        childNodes: dom.getChildNodes({
          ...snapshot,
          context: {
            ...snapshot.context,
            selection: dragSelection
          }
        })
      };
      return {
        dragSelection,
        draggedDomNodes,
        selectingEntireBlocks
      };
    },
    actions: [({
      dom,
      event
    }, {
      dragSelection,
      draggedDomNodes,
      selectingEntireBlocks
    }) => {
      const dragGhost = document.createElement("div");
      if (selectingEntireBlocks) {
        const clonedBlockNodes = draggedDomNodes.blockNodes.map((node) => node.cloneNode(!0));
        for (const block of clonedBlockNodes)
          block instanceof HTMLElement && (block.style.position = "relative"), dragGhost.appendChild(block);
        const customGhost = dragGhost.querySelector("[data-pt-drag-ghost-element]");
        if (customGhost && dragGhost.replaceChildren(customGhost), dragGhost.setAttribute("data-dragged", ""), dragGhost.style.position = "absolute", dragGhost.style.left = "-99999px", dragGhost.style.boxSizing = "border-box", document.body.appendChild(dragGhost), customGhost) {
          const customGhostRect = customGhost.getBoundingClientRect(), x = event.originEvent.clientX - customGhostRect.left, y = event.originEvent.clientY - customGhostRect.top;
          return dragGhost.style.width = `${customGhostRect.width}px`, dragGhost.style.height = `${customGhostRect.height}px`, [raise({
            type: "select",
            at: dragSelection
          }), effect(() => {
            dom.setDragGhost({
              event,
              ghost: {
                element: dragGhost,
                x,
                y
              }
            });
          }), forward(event)];
        } else {
          const blocksDomRect = getCompoundClientRect(draggedDomNodes.blockNodes), x = event.originEvent.clientX - blocksDomRect.left, y = event.originEvent.clientY - blocksDomRect.top;
          return dragGhost.style.width = `${blocksDomRect.width}px`, dragGhost.style.height = `${blocksDomRect.height}px`, [raise({
            type: "select",
            at: dragSelection
          }), effect(() => {
            dom.setDragGhost({
              event,
              ghost: {
                element: dragGhost,
                x,
                y
              }
            });
          }), forward(event)];
        }
      } else {
        const clonedChildNodes = draggedDomNodes.childNodes.map((node) => node.cloneNode(!0));
        for (const child of clonedChildNodes)
          dragGhost.appendChild(child);
        dragGhost.style.position = "absolute", dragGhost.style.left = "-99999px", dragGhost.style.boxSizing = "border-box", document.body.appendChild(dragGhost);
        const childrenDomRect = getCompoundClientRect(draggedDomNodes.childNodes), x = event.originEvent.clientX - childrenDomRect.left, y = event.originEvent.clientY - childrenDomRect.top;
        return dragGhost.style.width = `${childrenDomRect.width}px`, dragGhost.style.height = `${childrenDomRect.height}px`, [raise({
          type: "select",
          at: dragSelection
        }), effect(() => {
          dom.setDragGhost({
            event,
            ghost: {
              element: dragGhost,
              x,
              y
            }
          });
        }), forward(event)];
      }
    }]
  }),
  /**
   * When dragging over the drag origin, we don't want to show the caret in the
   * text.
   */
  defineBehavior({
    on: "drag.dragover",
    guard: ({
      snapshot,
      event
    }) => {
      const dragOrigin = event.dragOrigin;
      return dragOrigin ? isOverlappingSelection(event.position.selection)({
        ...snapshot,
        context: {
          ...snapshot.context,
          selection: dragOrigin.selection
        }
      }) : !1;
    },
    actions: []
  }),
  /**
   * If the drop position overlaps the drag origin, then the event should be
   * cancelled.
   */
  defineBehavior({
    on: "drag.drop",
    guard: ({
      snapshot,
      event
    }) => {
      const dragOrigin = event.dragOrigin, dropPosition = event.position.selection;
      return dragOrigin ? isOverlappingSelection(dropPosition)({
        ...snapshot,
        context: {
          ...snapshot.context,
          selection: dragOrigin.selection
        }
      }) : !1;
    },
    actions: []
  }),
  /**
   * If we drop and have access to a drag origin, then we can deserialize
   * without creating a new selection.
   */
  defineBehavior({
    on: "drag.drop",
    guard: ({
      event
    }) => event.dragOrigin !== void 0,
    actions: [({
      event
    }) => [raise({
      type: "deserialize",
      originEvent: event
    })]]
  }),
  /**
   * Otherwise, we should to create a new selection.
   */
  defineBehavior({
    on: "drag.drop",
    actions: [({
      event
    }) => [raise({
      type: "select",
      at: event.position.selection
    }), raise({
      type: "deserialize",
      originEvent: event
    })]]
  }),
  /**
   * Core Behavior that uses the drag origin to mimic a move operation during
   * internal dragging.
   */
  defineBehavior({
    on: "deserialization.success",
    guard: ({
      snapshot,
      event
    }) => {
      if (event.originEvent.type !== "drag.drop" || event.originEvent.dragOrigin === void 0)
        return !1;
      const dragOrigin = event.originEvent.dragOrigin, dragSelection = getDragSelection({
        eventSelection: dragOrigin.selection,
        snapshot
      }), dropPosition = event.originEvent.position.selection, droppingOnDragOrigin = dragOrigin ? isOverlappingSelection(dropPosition)({
        ...snapshot,
        context: {
          ...snapshot.context,
          selection: dragSelection
        }
      }) : !1, draggingEntireBlocks = isSelectingEntireBlocks({
        ...snapshot,
        context: {
          ...snapshot.context,
          selection: dragSelection
        }
      }), draggedBlocks = getSelectedBlocks({
        ...snapshot,
        context: {
          ...snapshot.context,
          selection: dragSelection
        }
      });
      return droppingOnDragOrigin ? !1 : {
        dropPosition,
        draggingEntireBlocks,
        draggedBlocks,
        dragOrigin,
        originEvent: event.originEvent
      };
    },
    actions: [({
      event
    }, {
      draggingEntireBlocks,
      draggedBlocks,
      dragOrigin,
      dropPosition,
      originEvent
    }) => [raise({
      type: "select",
      at: dropPosition
    }), ...draggingEntireBlocks ? draggedBlocks.map((block) => raise({
      type: "delete.block",
      at: block.path
    })) : [raise({
      type: "delete",
      at: dragOrigin.selection
    })], raise({
      type: "insert.blocks",
      blocks: event.data,
      placement: draggingEntireBlocks ? originEvent.position.block === "start" ? "before" : originEvent.position.block === "end" ? "after" : "auto" : "auto"
    })]]
  })
], breakingAtTheEndOfTextBlock = defineBehavior({
  on: "insert.break",
  guard: ({
    snapshot
  }) => {
    const focusTextBlock = getFocusTextBlock(snapshot), selectionCollapsed = isSelectionCollapsed$1(snapshot);
    if (!snapshot.context.selection || !focusTextBlock || !selectionCollapsed)
      return !1;
    const atTheEndOfBlock = isAtTheEndOfBlock(focusTextBlock)(snapshot), focusListItem = focusTextBlock.node.listItem, focusLevel = focusTextBlock.node.level;
    return atTheEndOfBlock ? {
      focusListItem,
      focusLevel
    } : !1;
  },
  actions: [({
    snapshot
  }, {
    focusListItem,
    focusLevel
  }) => [raise({
    type: "insert.block",
    block: {
      _type: snapshot.context.schema.block.name,
      children: [{
        _type: snapshot.context.schema.span.name,
        text: "",
        marks: []
      }],
      markDefs: [],
      listItem: focusListItem,
      level: focusLevel,
      style: snapshot.context.schema.styles[0]?.name
    },
    placement: "after"
  })]]
}), breakingAtTheStartOfTextBlock = defineBehavior({
  on: "insert.break",
  guard: ({
    snapshot
  }) => {
    const focusTextBlock = getFocusTextBlock(snapshot), selectionCollapsed = isSelectionCollapsed$1(snapshot);
    if (!snapshot.context.selection || !focusTextBlock || !selectionCollapsed)
      return !1;
    const focusSpan = getFocusSpan$1(snapshot), focusDecorators = focusSpan?.node.marks?.filter((mark) => snapshot.context.schema.decorators.some((decorator) => decorator.name === mark) ?? []), focusAnnotations = focusSpan?.node.marks?.filter((mark) => !snapshot.context.schema.decorators.some((decorator) => decorator.name === mark)) ?? [], focusListItem = focusTextBlock.node.listItem, focusLevel = focusTextBlock.node.level;
    return isAtTheStartOfBlock(focusTextBlock)(snapshot) ? {
      focusAnnotations,
      focusDecorators,
      focusListItem,
      focusLevel
    } : !1;
  },
  actions: [({
    snapshot
  }, {
    focusAnnotations,
    focusDecorators,
    focusListItem,
    focusLevel
  }) => [raise({
    type: "insert.block",
    block: {
      _type: snapshot.context.schema.block.name,
      children: [{
        _type: snapshot.context.schema.span.name,
        marks: focusAnnotations.length === 0 ? focusDecorators : [],
        text: ""
      }],
      listItem: focusListItem,
      level: focusLevel,
      style: snapshot.context.schema.styles[0]?.name
    },
    placement: "before",
    select: "none"
  })]]
}), breakingEntireDocument = defineBehavior({
  on: "insert.break",
  guard: ({
    snapshot
  }) => {
    if (!snapshot.context.selection || !isSelectionExpanded(snapshot))
      return !1;
    const firstBlock = getFirstBlock$1(snapshot), lastBlock = getLastBlock$1(snapshot);
    if (!firstBlock || !lastBlock)
      return !1;
    const firstBlockStartPoint = getBlockStartPoint({
      context: snapshot.context,
      block: firstBlock
    }), selectionStartPoint = getSelectionStartPoint(snapshot.context.selection), lastBlockEndPoint = getBlockEndPoint({
      context: snapshot.context,
      block: lastBlock
    }), selectionEndPoint = getSelectionEndPoint(snapshot.context.selection);
    return isEqualSelectionPoints(firstBlockStartPoint, selectionStartPoint) && isEqualSelectionPoints(lastBlockEndPoint, selectionEndPoint) ? {
      selection: snapshot.context.selection
    } : !1;
  },
  actions: [(_, {
    selection
  }) => [raise({
    type: "delete",
    at: selection
  })]]
}), breakingEntireBlocks = defineBehavior({
  on: "insert.break",
  guard: ({
    snapshot
  }) => {
    if (!snapshot.context.selection || !isSelectionExpanded(snapshot))
      return !1;
    const selectedBlocks = getSelectedBlocks(snapshot), selectionStartBlock = getSelectionStartBlock$1(snapshot), selectionEndBlock = getSelectionEndBlock$1(snapshot);
    if (!selectionStartBlock || !selectionEndBlock)
      return !1;
    const startBlockStartPoint = getBlockStartPoint({
      context: snapshot.context,
      block: selectionStartBlock
    }), selectionStartPoint = getSelectionStartPoint(snapshot.context.selection), endBlockEndPoint = getBlockEndPoint({
      context: snapshot.context,
      block: selectionEndBlock
    }), selectionEndPoint = getSelectionEndPoint(snapshot.context.selection);
    return isEqualSelectionPoints(selectionStartPoint, startBlockStartPoint) && isEqualSelectionPoints(selectionEndPoint, endBlockEndPoint) ? {
      selectedBlocks
    } : !1;
  },
  actions: [({
    snapshot
  }, {
    selectedBlocks
  }) => [raise({
    type: "insert.block",
    block: {
      _type: snapshot.context.schema.block.name,
      children: [{
        _type: snapshot.context.schema.span.name,
        text: "",
        marks: []
      }]
    },
    placement: "before",
    select: "start"
  }), ...selectedBlocks.map((block) => raise({
    type: "delete.block",
    at: block.path
  }))]]
}), breakingInlineObject = defineBehavior({
  on: "insert.break",
  guard: ({
    snapshot
  }) => {
    const selectionCollapsed = isSelectionCollapsed$1(snapshot), focusInlineObject = getFocusInlineObject(snapshot);
    return selectionCollapsed && focusInlineObject;
  },
  actions: [() => [raise({
    type: "move.forward",
    distance: 1
  }), raise({
    type: "split"
  })]]
}), coreInsertBreakBehaviors = {
  breakingAtTheEndOfTextBlock,
  breakingAtTheStartOfTextBlock,
  breakingEntireDocument,
  breakingEntireBlocks,
  breakingInlineObject
};
function isAtTheBeginningOfBlock({
  context,
  block
}) {
  return !isTextBlock(context, block) || !context.selection || !isSelectionCollapsed(context.selection) ? !1 : getChildKeyFromSelectionPoint(context.selection.focus) === block.children[0]._key && context.selection.focus.offset === 0;
}
const MAX_LIST_LEVEL = 10, clearListOnBackspace = defineBehavior({
  on: "delete.backward",
  guard: ({
    snapshot
  }) => {
    const focusTextBlock = getFocusTextBlock(snapshot);
    return !focusTextBlock || focusTextBlock.node.level !== 1 || !isAtTheBeginningOfBlock({
      context: snapshot.context,
      block: focusTextBlock.node
    }) ? !1 : {
      focusTextBlock
    };
  },
  actions: [(_, {
    focusTextBlock
  }) => [raise({
    type: "block.unset",
    props: ["listItem", "level"],
    at: focusTextBlock.path
  })]]
}), unindentListOnBackspace = defineBehavior({
  on: "delete.backward",
  guard: ({
    snapshot
  }) => {
    const selectionCollapsed = isSelectionCollapsed$1(snapshot), focusTextBlock = getFocusTextBlock(snapshot), focusSpan = getFocusSpan$1(snapshot);
    return !selectionCollapsed || !focusTextBlock || !focusSpan ? !1 : focusTextBlock.node.children[0]._key === focusSpan.node._key && snapshot.context.selection?.focus.offset === 0 && focusTextBlock.node.level !== void 0 && focusTextBlock.node.level > 1 ? {
      focusTextBlock,
      level: focusTextBlock.node.level - 1
    } : !1;
  },
  actions: [(_, {
    focusTextBlock,
    level
  }) => [raise({
    type: "block.set",
    props: {
      level
    },
    at: focusTextBlock.path
  })]]
}), mergeTextIntoListOnDelete = defineBehavior({
  on: "delete.forward",
  guard: ({
    snapshot
  }) => {
    const focusListBlock = getFocusListBlock(snapshot), nextBlock = getNextBlock(snapshot);
    return !focusListBlock || !nextBlock || !isTextBlock(snapshot.context, nextBlock.node) || !isEmptyTextBlock(snapshot.context, focusListBlock.node) ? !1 : {
      focusListBlock,
      nextBlock
    };
  },
  actions: [(_, {
    nextBlock
  }) => [raise({
    type: "insert.block",
    block: nextBlock.node,
    placement: "auto",
    select: "start"
  }), raise({
    type: "delete.block",
    at: nextBlock.path
  })]]
}), mergeTextIntoListOnBackspace = defineBehavior({
  on: "delete.backward",
  guard: ({
    snapshot
  }) => {
    const focusTextBlock = getFocusTextBlock(snapshot), previousBlock = getPreviousBlock(snapshot);
    if (!focusTextBlock || !previousBlock || !isAtTheBeginningOfBlock({
      context: snapshot.context,
      block: focusTextBlock.node
    }) || !isListBlock(snapshot.context, previousBlock.node) || !isEmptyTextBlock(snapshot.context, previousBlock.node))
      return !1;
    const previousBlockEndPoint = getBlockEndPoint({
      context: snapshot.context,
      block: previousBlock
    });
    return {
      focusTextBlock,
      previousBlockEndPoint
    };
  },
  actions: [(_, {
    focusTextBlock,
    previousBlockEndPoint
  }) => [raise({
    type: "select",
    at: {
      anchor: previousBlockEndPoint,
      focus: previousBlockEndPoint
    }
  }), raise({
    type: "insert.block",
    block: focusTextBlock.node,
    placement: "auto",
    select: "start"
  }), raise({
    type: "delete.block",
    at: focusTextBlock.path
  })]]
}), deletingListFromStart = defineBehavior({
  on: "delete",
  guard: ({
    snapshot,
    event
  }) => {
    const blocksToDelete = getSelectedBlocks({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: event.at
      }
    });
    if (blocksToDelete.length < 2)
      return !1;
    const startBlock = blocksToDelete.at(0)?.node, middleBlocks = blocksToDelete.slice(1, -1), endBlock = blocksToDelete.at(-1)?.node;
    if (!isListBlock(snapshot.context, startBlock) || !isListBlock(snapshot.context, endBlock))
      return !1;
    const deleteStartPoint = getSelectionStartPoint$1({
      context: {
        ...snapshot.context,
        selection: event.at
      }
    }), deleteEndPoint = getSelectionEndPoint$1({
      context: {
        ...snapshot.context,
        selection: event.at
      }
    });
    if (!deleteStartPoint || !deleteEndPoint)
      return !1;
    const startBlockStartPoint = getBlockStartPoint({
      context: snapshot.context,
      block: {
        node: startBlock,
        path: [{
          _key: startBlock._key
        }]
      }
    });
    if (!isEqualSelectionPoints(deleteStartPoint, startBlockStartPoint))
      return !1;
    const startBlockEndPoint = getBlockEndPoint({
      context: snapshot.context,
      block: {
        node: startBlock,
        path: [{
          _key: startBlock._key
        }]
      }
    }), endBlockEndPoint = getBlockEndPoint({
      context: snapshot.context,
      block: {
        node: endBlock,
        path: [{
          _key: endBlock._key
        }]
      }
    }), slicedEndBlock = sliceTextBlock({
      context: {
        schema: snapshot.context.schema,
        selection: {
          anchor: deleteEndPoint,
          focus: endBlockEndPoint
        }
      },
      block: endBlock
    });
    return {
      startBlockStartPoint,
      startBlockEndPoint,
      middleBlocks,
      endBlock,
      slicedEndBlock
    };
  },
  actions: [(_, {
    startBlockStartPoint,
    startBlockEndPoint,
    middleBlocks,
    endBlock,
    slicedEndBlock
  }) => [
    // All block in between can safely be deleted.
    ...middleBlocks.map((block) => raise({
      type: "delete.block",
      at: block.path
    })),
    // The last block is deleted as well.
    raise({
      type: "delete.block",
      at: [{
        _key: endBlock._key
      }]
    }),
    // But in case the delete operation didn't reach all the way to the end
    // of it, we first place the caret at the end of the start block...
    raise({
      type: "select",
      at: {
        anchor: startBlockEndPoint,
        focus: startBlockEndPoint
      }
    }),
    // ...and insert the rest of the end block at the end of it.
    raise({
      type: "insert.block",
      block: slicedEndBlock,
      placement: "auto",
      select: "none"
    }),
    // And finally, we delete the original text of the start block.
    raise({
      type: "delete",
      at: {
        anchor: startBlockStartPoint,
        focus: startBlockEndPoint
      }
    })
  ]]
}), clearListOnEnter = defineBehavior({
  on: "insert.break",
  guard: ({
    snapshot
  }) => {
    const selectionCollapsed = isSelectionCollapsed$1(snapshot), focusListBlock = getFocusListBlock(snapshot);
    return !selectionCollapsed || !focusListBlock || !isEmptyTextBlock(snapshot.context, focusListBlock.node) ? !1 : {
      focusListBlock
    };
  },
  actions: [(_, {
    focusListBlock
  }) => [raise({
    type: "block.unset",
    props: ["listItem", "level"],
    at: focusListBlock.path
  })]]
}), indentListOnTab = defineBehavior({
  on: "keyboard.keydown",
  guard: ({
    snapshot,
    event
  }) => {
    if (!defaultKeyboardShortcuts.tab.guard(event.originEvent))
      return !1;
    const selectedBlocks = getSelectedBlocks(snapshot), selectedListBlocks = selectedBlocks.flatMap((block) => isListBlock(snapshot.context, block.node) ? [{
      node: block.node,
      path: block.path
    }] : []);
    return selectedListBlocks.length === selectedBlocks.length ? {
      selectedListBlocks
    } : !1;
  },
  actions: [(_, {
    selectedListBlocks
  }) => selectedListBlocks.map((selectedListBlock) => raise({
    type: "block.set",
    props: {
      level: Math.min(MAX_LIST_LEVEL, Math.max(1, selectedListBlock.node.level + 1))
    },
    at: selectedListBlock.path
  }))]
}), unindentListOnShiftTab = defineBehavior({
  on: "keyboard.keydown",
  guard: ({
    snapshot,
    event
  }) => {
    if (!defaultKeyboardShortcuts.shiftTab.guard(event.originEvent))
      return !1;
    const selectedBlocks = getSelectedBlocks(snapshot), selectedListBlocks = selectedBlocks.flatMap((block) => isListBlock(snapshot.context, block.node) ? [{
      node: block.node,
      path: block.path
    }] : []);
    return selectedListBlocks.length === selectedBlocks.length ? {
      selectedListBlocks
    } : !1;
  },
  actions: [(_, {
    selectedListBlocks
  }) => selectedListBlocks.map((selectedListBlock) => raise({
    type: "block.set",
    props: {
      level: Math.min(MAX_LIST_LEVEL, Math.max(1, selectedListBlock.node.level - 1))
    },
    at: selectedListBlock.path
  }))]
}), inheritListLevel = defineBehavior({
  on: "insert.blocks",
  guard: ({
    snapshot,
    event
  }) => {
    const focusListBlock = getFocusListBlock(snapshot);
    if (!focusListBlock)
      return !1;
    const firstInsertedBlock = event.blocks.at(0), secondInsertedBlock = event.blocks.at(1), insertedListBlock = isListBlock(snapshot.context, firstInsertedBlock) ? firstInsertedBlock : isListBlock(snapshot.context, secondInsertedBlock) ? secondInsertedBlock : void 0;
    if (!insertedListBlock)
      return !1;
    const levelDifference = focusListBlock.node.level - insertedListBlock.level;
    return levelDifference === 0 ? !1 : {
      levelDifference,
      insertedListBlock
    };
  },
  actions: [({
    snapshot,
    event
  }, {
    levelDifference,
    insertedListBlock
  }) => {
    let adjustLevel = !0, listStartBlockFound = !1;
    return [raise({
      ...event,
      blocks: event.blocks.map((block) => (block._key === insertedListBlock._key && (listStartBlockFound = !0), adjustLevel ? listStartBlockFound && adjustLevel && isListBlock(snapshot.context, block) ? {
        ...block,
        level: Math.min(MAX_LIST_LEVEL, Math.max(1, block.level + levelDifference))
      } : (listStartBlockFound && (adjustLevel = !1), block) : block))
    })];
  }]
}), inheritListItem = defineBehavior({
  on: "insert.blocks",
  guard: ({
    snapshot,
    event
  }) => {
    const focusListBlock = getFocusListBlock(snapshot);
    if (!focusListBlock || isEmptyTextBlock(snapshot.context, focusListBlock.node))
      return !1;
    const firstInsertedBlock = event.blocks.at(0), secondInsertedBlock = event.blocks.at(1), insertedListBlock = isListBlock(snapshot.context, firstInsertedBlock) ? firstInsertedBlock : isListBlock(snapshot.context, secondInsertedBlock) ? secondInsertedBlock : void 0;
    return !insertedListBlock || focusListBlock.node.level !== insertedListBlock.level || focusListBlock.node.listItem === insertedListBlock.listItem ? !1 : {
      listItem: focusListBlock.node.listItem,
      insertedListBlock
    };
  },
  actions: [({
    snapshot,
    event
  }, {
    listItem,
    insertedListBlock
  }) => {
    let adjustListItem = !0, listStartBlockFound = !1;
    return [raise({
      ...event,
      blocks: event.blocks.map((block) => (block._key === insertedListBlock._key && (listStartBlockFound = !0), adjustListItem ? listStartBlockFound && adjustListItem && isListBlock(snapshot.context, block) ? {
        ...block,
        listItem: block.level === insertedListBlock.level ? listItem : block.listItem
      } : (listStartBlockFound && (adjustListItem = !1), block) : block))
    })];
  }]
}), inheritListProperties = defineBehavior({
  on: "insert.block",
  guard: ({
    snapshot,
    event
  }) => {
    if (event.placement !== "auto" || event.block._type !== snapshot.context.schema.block.name || event.block.listItem !== void 0)
      return !1;
    const focusListBlock = getFocusListBlock(snapshot);
    return !focusListBlock || !isEmptyTextBlock(snapshot.context, focusListBlock.node) ? !1 : {
      level: focusListBlock.node.level,
      listItem: focusListBlock.node.listItem
    };
  },
  actions: [({
    event
  }, {
    level,
    listItem
  }) => [raise({
    ...event,
    block: {
      ...event.block,
      level,
      listItem
    }
  })]]
}), coreListBehaviors = {
  clearListOnBackspace,
  unindentListOnBackspace,
  mergeTextIntoListOnDelete,
  mergeTextIntoListOnBackspace,
  deletingListFromStart,
  clearListOnEnter,
  indentListOnTab,
  unindentListOnShiftTab,
  inheritListLevel,
  inheritListItem,
  inheritListProperties
}, coreBehaviorsConfig = [coreAnnotationBehaviors.addAnnotationOnCollapsedSelection, coreDecoratorBehaviors.strongShortcut, coreDecoratorBehaviors.emShortcut, coreDecoratorBehaviors.underlineShortcut, coreDecoratorBehaviors.codeShortcut, ...coreDndBehaviors, coreBlockObjectBehaviors.clickingAboveLonelyBlockObject, coreBlockObjectBehaviors.clickingBelowLonelyBlockObject, coreBlockObjectBehaviors.arrowDownOnLonelyBlockObject, coreBlockObjectBehaviors.arrowUpOnLonelyBlockObject, coreBlockObjectBehaviors.breakingBlockObject, coreBlockObjectBehaviors.deletingEmptyTextBlockAfterBlockObject, coreBlockObjectBehaviors.deletingEmptyTextBlockBeforeBlockObject, coreListBehaviors.clearListOnBackspace, coreListBehaviors.unindentListOnBackspace, coreListBehaviors.mergeTextIntoListOnDelete, coreListBehaviors.mergeTextIntoListOnBackspace, coreListBehaviors.deletingListFromStart, coreListBehaviors.clearListOnEnter, coreListBehaviors.indentListOnTab, coreListBehaviors.unindentListOnShiftTab, coreListBehaviors.inheritListLevel, coreListBehaviors.inheritListItem, coreListBehaviors.inheritListProperties, coreInsertBreakBehaviors.breakingAtTheEndOfTextBlock, coreInsertBreakBehaviors.breakingAtTheStartOfTextBlock, coreInsertBreakBehaviors.breakingEntireDocument, coreInsertBreakBehaviors.breakingEntireBlocks, coreInsertBreakBehaviors.breakingInlineObject].map((behavior) => ({
  behavior,
  priority: corePriority
})), abstractAnnotationBehaviors = [defineBehavior({
  on: "annotation.set",
  guard: ({
    snapshot,
    event
  }) => {
    const blockKey = event.at[0]._key, markDefKey = event.at[2]._key, block = getFocusTextBlock({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: {
          anchor: {
            path: [{
              _key: blockKey
            }],
            offset: 0
          },
          focus: {
            path: [{
              _key: blockKey
            }],
            offset: 0
          }
        }
      }
    });
    if (!block)
      return !1;
    const updatedMarkDefs = block.node.markDefs?.map((markDef) => markDef._key === markDefKey ? {
      ...markDef,
      ...event.props
    } : markDef);
    return {
      blockKey,
      updatedMarkDefs
    };
  },
  actions: [(_, {
    blockKey,
    updatedMarkDefs
  }) => [raise({
    type: "block.set",
    at: [{
      _key: blockKey
    }],
    props: {
      markDefs: updatedMarkDefs
    }
  })]]
}), defineBehavior({
  on: "annotation.toggle",
  guard: ({
    snapshot,
    event
  }) => isActiveAnnotation(event.annotation.name)(snapshot),
  actions: [({
    event
  }) => [raise({
    type: "annotation.remove",
    annotation: event.annotation
  })]]
}), defineBehavior({
  on: "annotation.toggle",
  guard: ({
    snapshot,
    event
  }) => !isActiveAnnotation(event.annotation.name)(snapshot),
  actions: [({
    event
  }) => [raise({
    type: "annotation.add",
    annotation: event.annotation
  })]]
})], abstractDecoratorBehaviors = [defineBehavior({
  on: "decorator.toggle",
  guard: ({
    snapshot,
    event
  }) => isActiveDecorator(event.decorator)(snapshot),
  actions: [({
    event
  }) => [raise({
    type: "decorator.remove",
    decorator: event.decorator
  })]]
}), defineBehavior({
  on: "decorator.toggle",
  guard: ({
    snapshot,
    event
  }) => {
    const manualSelection = event.at ? blockOffsetsToSelection({
      context: snapshot.context,
      offsets: event.at
    }) : null;
    return manualSelection ? !isActiveDecorator(event.decorator)({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: manualSelection
      }
    }) : !isActiveDecorator(event.decorator)(snapshot);
  },
  actions: [({
    event
  }) => [raise({
    ...event,
    type: "decorator.add"
  })]]
})], abstractDeleteBehaviors = [defineBehavior({
  on: "delete.backward",
  guard: ({
    snapshot
  }) => snapshot.context.selection ? {
    selection: snapshot.context.selection
  } : !1,
  actions: [({
    event
  }, {
    selection
  }) => [raise({
    type: "delete",
    direction: "backward",
    unit: event.unit,
    at: selection
  })]]
}), defineBehavior({
  on: "delete",
  guard: ({
    snapshot,
    event
  }) => {
    if (event.direction !== "backward")
      return !1;
    const previousBlock = getPreviousBlock(snapshot), focusTextBlock = getFocusTextBlock(snapshot);
    if (!previousBlock || !focusTextBlock || !isAtTheStartOfBlock(focusTextBlock)(snapshot))
      return !1;
    const previousBlockEndPoint = getBlockEndPoint({
      context: snapshot.context,
      block: previousBlock
    });
    return isTextBlock(snapshot.context, previousBlock.node) ? {
      previousBlockEndPoint,
      focusTextBlock
    } : !1;
  },
  actions: [(_, {
    previousBlockEndPoint,
    focusTextBlock
  }) => [raise({
    type: "delete.block",
    at: focusTextBlock.path
  }), raise({
    type: "select",
    at: {
      anchor: previousBlockEndPoint,
      focus: previousBlockEndPoint
    }
  }), raise({
    type: "insert.block",
    block: focusTextBlock.node,
    placement: "auto",
    select: "start"
  })]]
}), defineBehavior({
  on: "delete.forward",
  guard: ({
    snapshot
  }) => snapshot.context.selection ? {
    selection: snapshot.context.selection
  } : !1,
  actions: [({
    event
  }, {
    selection
  }) => [raise({
    type: "delete",
    direction: "forward",
    unit: event.unit,
    at: selection
  })]]
}), defineBehavior({
  on: "delete",
  guard: ({
    snapshot,
    event
  }) => {
    if (event.direction !== "forward")
      return !1;
    const nextBlock = getNextBlock(snapshot), focusTextBlock = getFocusTextBlock(snapshot);
    return !nextBlock || !focusTextBlock || !isAtTheEndOfBlock(focusTextBlock)(snapshot) || !isTextBlock(snapshot.context, nextBlock.node) ? !1 : {
      nextBlock
    };
  },
  actions: [(_, {
    nextBlock
  }) => [raise({
    type: "delete.block",
    at: nextBlock.path
  }), raise({
    type: "insert.block",
    block: nextBlock.node,
    placement: "auto",
    select: "none"
  })]]
}), defineBehavior({
  on: "delete.block",
  actions: [({
    event
  }) => [raise({
    type: "delete",
    at: {
      anchor: {
        path: event.at,
        offset: 0
      },
      focus: {
        path: event.at,
        offset: 0
      }
    }
  })]]
}), defineBehavior({
  on: "delete.child",
  guard: ({
    snapshot,
    event
  }) => {
    const focusChild = getFocusChild$1({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: {
          anchor: {
            path: event.at,
            offset: 0
          },
          focus: {
            path: event.at,
            offset: 0
          }
        }
      }
    });
    return focusChild ? isSpan(snapshot.context, focusChild.node) ? {
      selection: {
        anchor: {
          path: event.at,
          offset: 0
        },
        focus: {
          path: event.at,
          offset: focusChild.node.text.length
        }
      }
    } : {
      selection: {
        anchor: {
          path: event.at,
          offset: 0
        },
        focus: {
          path: event.at,
          offset: 0
        }
      }
    } : !1;
  },
  actions: [(_, {
    selection
  }) => [raise({
    type: "delete",
    at: selection
  })]]
}), defineBehavior({
  on: "delete.text",
  guard: ({
    snapshot,
    event
  }) => {
    const selection = blockOffsetsToSelection({
      context: snapshot.context,
      offsets: event.at
    });
    if (!selection)
      return !1;
    const trimmedSelection = getTrimmedSelection({
      ...snapshot,
      context: {
        ...snapshot.context,
        value: snapshot.context.value,
        selection
      }
    });
    return trimmedSelection ? {
      selection: trimmedSelection
    } : !1;
  },
  actions: [(_, {
    selection
  }) => [raise({
    type: "delete",
    at: selection
  })]]
})], abstractInsertBehaviors = [defineBehavior({
  on: "insert.blocks",
  guard: ({
    event
  }) => event.placement === "before",
  actions: [({
    event
  }) => event.blocks.map((block, index) => raise({
    type: "insert.block",
    block,
    placement: index === 0 ? "before" : "after",
    select: event.select ?? "end"
  }))]
}), defineBehavior({
  on: "insert.blocks",
  guard: ({
    event
  }) => event.placement === "after",
  actions: [({
    event
  }) => event.blocks.map((block) => raise({
    type: "insert.block",
    block,
    placement: "after",
    select: event.select ?? "end"
  }))]
}), defineBehavior({
  on: "insert.blocks",
  guard: ({
    snapshot,
    event
  }) => {
    if (event.placement !== "auto")
      return !1;
    const focusTextBlock = getFocusTextBlock(snapshot);
    return focusTextBlock ? {
      focusTextBlock
    } : !1;
  },
  actions: [({
    snapshot,
    event
  }, {
    focusTextBlock
  }) => event.blocks.length === 1 ? [raise({
    type: "insert.block",
    block: event.blocks[0],
    placement: "auto",
    select: event.select ?? "end"
  })] : isEmptyTextBlock(snapshot.context, focusTextBlock.node) ? event.blocks.map((block, index) => raise({
    type: "insert.block",
    block,
    placement: index === 0 ? "auto" : "after",
    select: event.select ?? "end"
  })) : event.blocks.flatMap((block, index) => index === 0 ? [raise({
    type: "split"
  }), raise({
    type: "select.previous block",
    select: "end"
  }), raise({
    type: "insert.block",
    block,
    placement: "auto",
    select: event.select ?? "end"
  })] : index === event.blocks.length - 1 ? [raise({
    type: "select.next block",
    select: "start"
  }), raise({
    type: "insert.block",
    block,
    placement: "auto",
    select: event.select ?? "end"
  })] : [raise({
    type: "insert.block",
    block,
    placement: "after",
    select: event.select ?? "end"
  })])]
}), defineBehavior({
  on: "insert.blocks",
  guard: ({
    event
  }) => event.placement === "auto",
  actions: [({
    event
  }) => event.blocks.map((block, index) => raise({
    type: "insert.block",
    block,
    placement: index === 0 ? "auto" : "after",
    select: event.select ?? "end"
  }))]
}), defineBehavior({
  on: "insert.break",
  actions: [() => [raise({
    type: "split"
  })]]
}), defineBehavior({
  on: "insert.soft break",
  actions: [() => [raise({
    type: "insert.text",
    text: `
`
  })]]
})], abstractKeyboardBehaviors = [
  /**
   * Allow raising an `insert.break` event when pressing Enter on an inline
   * object.
   */
  defineBehavior({
    on: "keyboard.keydown",
    guard: ({
      snapshot,
      event
    }) => defaultKeyboardShortcuts.break.guard(event.originEvent) && isSelectionCollapsed$1(snapshot) && getFocusInlineObject(snapshot),
    actions: [() => [raise({
      type: "insert.break"
    })]]
  }),
  /**
   * On WebKit, Shift+Enter results in an `insertParagraph` input event rather
   * than an `insertLineBreak` input event. This Behavior makes sure we catch
   * that `keyboard.keydown` event beforehand and raise an `insert.soft break` manually.
   */
  defineBehavior({
    on: "keyboard.keydown",
    guard: ({
      event
    }) => defaultKeyboardShortcuts.lineBreak.guard(event.originEvent),
    actions: [() => [raise({
      type: "insert.soft break"
    })]]
  }),
  /**
   * Manual handling of undo shortcuts.
   */
  defineBehavior({
    on: "keyboard.keydown",
    guard: ({
      event
    }) => defaultKeyboardShortcuts.history.undo.guard(event.originEvent),
    actions: [() => [raise({
      type: "history.undo"
    })]]
  }),
  /**
   * Manual handling of redo shortcuts.
   */
  defineBehavior({
    on: "keyboard.keydown",
    guard: ({
      event
    }) => defaultKeyboardShortcuts.history.redo.guard(event.originEvent),
    actions: [() => [raise({
      type: "history.redo"
    })]]
  })
], abstractListItemBehaviors = [defineBehavior({
  on: "list item.add",
  guard: ({
    snapshot,
    event
  }) => snapshot.context.schema.lists.some((list) => list.name === event.listItem) ? {
    selectedTextBlocks: getSelectedTextBlocks(snapshot)
  } : !1,
  actions: [({
    event
  }, {
    selectedTextBlocks
  }) => selectedTextBlocks.map((block) => raise({
    type: "block.set",
    at: block.path,
    props: {
      level: block.node.level ?? 1,
      listItem: event.listItem
    }
  }))]
}), defineBehavior({
  on: "list item.remove",
  guard: ({
    snapshot
  }) => ({
    selectedTextBlocks: getSelectedTextBlocks(snapshot)
  }),
  actions: [(_, {
    selectedTextBlocks
  }) => selectedTextBlocks.map((block) => raise({
    type: "block.unset",
    at: block.path,
    props: ["level", "listItem"]
  }))]
}), defineBehavior({
  on: "list item.toggle",
  guard: ({
    snapshot,
    event
  }) => isActiveListItem(event.listItem)(snapshot),
  actions: [({
    event
  }) => [raise({
    type: "list item.remove",
    listItem: event.listItem
  })]]
}), defineBehavior({
  on: "list item.toggle",
  guard: ({
    snapshot,
    event
  }) => !isActiveListItem(event.listItem)(snapshot),
  actions: [({
    event
  }) => [raise({
    type: "list item.add",
    listItem: event.listItem
  })]]
})], abstractMoveBehaviors = [defineBehavior({
  on: "move.block up",
  guard: ({
    snapshot,
    event
  }) => {
    const previousBlock = getPreviousBlock({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: {
          anchor: {
            path: event.at,
            offset: 0
          },
          focus: {
            path: event.at,
            offset: 0
          }
        }
      }
    });
    return previousBlock ? {
      previousBlock
    } : !1;
  },
  actions: [({
    event
  }, {
    previousBlock
  }) => [raise({
    type: "move.block",
    at: event.at,
    to: previousBlock.path
  })]]
}), defineBehavior({
  on: "move.block down",
  guard: ({
    snapshot,
    event
  }) => {
    const nextBlock = getNextBlock({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: {
          anchor: {
            path: event.at,
            offset: 0
          },
          focus: {
            path: event.at,
            offset: 0
          }
        }
      }
    });
    return nextBlock ? {
      nextBlock
    } : !1;
  },
  actions: [({
    event
  }, {
    nextBlock
  }) => [raise({
    type: "move.block",
    at: event.at,
    to: nextBlock.path
  })]]
})], abstractSelectBehaviors = [defineBehavior({
  on: "select.previous block",
  guard: ({
    snapshot,
    event
  }) => {
    const previousBlock = getPreviousBlock(snapshot);
    if (!previousBlock)
      return !1;
    const point = event.select === "end" ? getBlockEndPoint({
      context: snapshot.context,
      block: previousBlock
    }) : getBlockStartPoint({
      context: snapshot.context,
      block: previousBlock
    });
    return {
      selection: {
        anchor: point,
        focus: point
      }
    };
  },
  actions: [(_, {
    selection
  }) => [raise({
    type: "select",
    at: selection
  })]]
}), defineBehavior({
  on: "select.next block",
  guard: ({
    snapshot,
    event
  }) => {
    const nextBlock = getNextBlock(snapshot);
    if (!nextBlock)
      return !1;
    const point = event.select === "end" ? getBlockEndPoint({
      context: snapshot.context,
      block: nextBlock
    }) : getBlockStartPoint({
      context: snapshot.context,
      block: nextBlock
    });
    return {
      selection: {
        anchor: point,
        focus: point
      }
    };
  },
  actions: [(_, {
    selection
  }) => [raise({
    type: "select",
    at: selection
  })]]
})], abstractSplitBehaviors = [
  /**
   * You can't split an inline object.
   */
  defineBehavior({
    on: "split",
    guard: ({
      snapshot
    }) => isSelectionCollapsed$1(snapshot) && getFocusInlineObject(snapshot),
    actions: []
  }),
  /**
   * You can't split a block object.
   */
  defineBehavior({
    on: "split",
    guard: ({
      snapshot
    }) => isSelectionCollapsed$1(snapshot) && getFocusBlockObject(snapshot),
    actions: []
  }),
  defineBehavior({
    on: "split",
    guard: ({
      snapshot
    }) => {
      const selection = snapshot.context.selection;
      if (!selection || isSelectionCollapsed(selection))
        return !1;
      const selectionStartBlock = getSelectionStartBlock$1(snapshot), selectionEndBlock = getSelectionEndBlock$1(snapshot);
      return !selectionStartBlock || !selectionEndBlock ? !1 : !isTextBlock(snapshot.context, selectionStartBlock.node) && isTextBlock(snapshot.context, selectionEndBlock.node) ? {
        selection
      } : !1;
    },
    actions: [(_, {
      selection
    }) => [raise({
      type: "delete",
      at: selection
    })]]
  }),
  defineBehavior({
    on: "split",
    guard: ({
      snapshot
    }) => {
      const selection = snapshot.context.selection;
      if (!selection || isSelectionCollapsed(selection))
        return !1;
      const selectionStartBlock = getSelectionStartBlock$1(snapshot), selectionEndBlock = getSelectionEndBlock$1(snapshot);
      if (!selectionStartBlock || !selectionEndBlock || selectionStartBlock.node._key === selectionEndBlock.node._key)
        return !1;
      const startPoint = getSelectionStartPoint(selection), startBlockEndPoint = getBlockEndPoint({
        context: snapshot.context,
        block: selectionStartBlock
      }), endPoint = getSelectionEndPoint(selection), endBlockStartPoint = getBlockStartPoint({
        context: snapshot.context,
        block: selectionEndBlock
      }), blocksInBetween = getSelectedValue(snapshot).filter((block) => block._key !== selectionStartBlock.node._key && block._key !== selectionEndBlock.node._key);
      return {
        startPoint,
        startBlockEndPoint,
        endPoint,
        endBlockStartPoint,
        blocksInBetween
      };
    },
    actions: [(_, {
      startPoint,
      startBlockEndPoint,
      endPoint,
      endBlockStartPoint,
      blocksInBetween
    }) => [raise({
      type: "delete",
      at: {
        anchor: startPoint,
        focus: startBlockEndPoint
      }
    }), ...blocksInBetween.map((block) => raise({
      type: "delete.block",
      at: [{
        _key: block._key
      }]
    })), raise({
      type: "delete",
      at: {
        anchor: endBlockStartPoint,
        focus: endPoint
      }
    })]]
  }),
  defineBehavior({
    on: "split",
    guard: ({
      snapshot
    }) => {
      const selection = snapshot.context.selection;
      return !selection || isSelectionCollapsed(selection) ? !1 : {
        selection
      };
    },
    actions: [(_, {
      selection
    }) => [raise({
      type: "delete",
      at: selection
    }), raise({
      type: "split"
    })]]
  }),
  defineBehavior({
    on: "split",
    guard: ({
      snapshot
    }) => {
      const selection = snapshot.context.selection;
      if (!selection || !isSelectionCollapsed(selection))
        return !1;
      const selectionStartPoint = getSelectionStartPoint(selection), focusTextBlock = getFocusTextBlock(snapshot);
      if (!focusTextBlock)
        return !1;
      const blockEndPoint = getBlockEndPoint({
        context: snapshot.context,
        block: focusTextBlock
      }), newTextBlockSelection = {
        anchor: selectionStartPoint,
        focus: blockEndPoint
      }, newTextBlock = parseBlock({
        block: sliceTextBlock({
          context: {
            ...snapshot.context,
            selection: newTextBlockSelection
          },
          block: focusTextBlock.node
        }),
        context: snapshot.context,
        options: {
          refreshKeys: !1,
          validateFields: !1
        }
      });
      return newTextBlock ? {
        newTextBlock,
        newTextBlockSelection
      } : !1;
    },
    actions: [(_, {
      newTextBlock,
      newTextBlockSelection
    }) => isSelectionCollapsed(newTextBlockSelection) ? [raise({
      type: "insert.block",
      block: newTextBlock,
      placement: "after",
      select: "start"
    })] : [raise({
      type: "delete",
      at: newTextBlockSelection
    }), raise({
      type: "insert.block",
      block: newTextBlock,
      placement: "after",
      select: "start"
    })]]
  })
], abstractStyleBehaviors = [defineBehavior({
  on: "style.add",
  guard: ({
    snapshot
  }) => ({
    selectedTextBlocks: getSelectedTextBlocks(snapshot)
  }),
  actions: [({
    event
  }, {
    selectedTextBlocks
  }) => selectedTextBlocks.map((block) => raise({
    type: "block.set",
    at: block.path,
    props: {
      style: event.style
    }
  }))]
}), defineBehavior({
  on: "style.remove",
  guard: ({
    snapshot
  }) => ({
    selectedTextBlocks: getSelectedTextBlocks(snapshot)
  }),
  actions: [(_, {
    selectedTextBlocks
  }) => selectedTextBlocks.map((block) => raise({
    type: "block.unset",
    at: block.path,
    props: ["style"]
  }))]
}), defineBehavior({
  on: "style.toggle",
  guard: ({
    snapshot,
    event
  }) => isActiveStyle(event.style)(snapshot),
  actions: [({
    event
  }) => [raise({
    type: "style.remove",
    style: event.style
  })]]
}), defineBehavior({
  on: "style.toggle",
  guard: ({
    snapshot,
    event
  }) => !isActiveStyle(event.style)(snapshot),
  actions: [({
    event
  }) => [raise({
    type: "style.add",
    style: event.style
  })]]
})], raiseDeserializationSuccessOrFailure = defineBehavior({
  on: "deserialize",
  guard: ({
    snapshot,
    event
  }) => {
    let success;
    const failures = [];
    for (const converter of snapshot.context.converters) {
      const data = event.originEvent.originEvent.dataTransfer.getData(converter.mimeType);
      if (!data)
        continue;
      const deserializeEvent = converter.deserialize({
        snapshot,
        event: {
          type: "deserialize",
          data
        }
      });
      if (deserializeEvent.type === "deserialization.success") {
        success = deserializeEvent;
        break;
      } else
        failures.push(deserializeEvent);
    }
    return success || {
      type: "deserialization.failure",
      mimeType: "*/*",
      reason: failures.map((failure) => failure.reason).join(", ")
    };
  },
  actions: [({
    event
  }, deserializeEvent) => [raise({
    ...deserializeEvent,
    originEvent: event.originEvent
  })]]
}), raiseSerializationSuccessOrFailure = defineBehavior({
  on: "serialize",
  guard: ({
    snapshot,
    event
  }) => {
    if (snapshot.context.converters.length === 0)
      return !1;
    const serializeEvents = snapshot.context.converters.map((converter) => converter.serialize({
      snapshot,
      event: {
        ...event,
        originEvent: event.originEvent.type
      }
    }));
    return serializeEvents.length === 0 ? !1 : serializeEvents;
  },
  actions: [({
    event
  }, serializeEvents) => serializeEvents.map((serializeEvent) => raise({
    ...serializeEvent,
    originEvent: event.originEvent
  }))]
}), abstractBehaviors = [
  defineBehavior({
    on: "clipboard.copy",
    guard: ({
      snapshot
    }) => {
      const focusSpan = getFocusSpan$1(snapshot), selectionCollapsed = isSelectionCollapsed$1(snapshot);
      return focusSpan && selectionCollapsed;
    },
    actions: []
  }),
  defineBehavior({
    on: "clipboard.copy",
    actions: [({
      event
    }) => [raise({
      type: "serialize",
      originEvent: event
    })]]
  }),
  defineBehavior({
    on: "clipboard.cut",
    guard: ({
      snapshot
    }) => {
      const focusSpan = getFocusSpan$1(snapshot), selectionCollapsed = isSelectionCollapsed$1(snapshot);
      return focusSpan && selectionCollapsed;
    },
    actions: []
  }),
  defineBehavior({
    on: "clipboard.cut",
    guard: ({
      snapshot
    }) => snapshot.context.selection ? {
      selection: snapshot.context.selection
    } : !1,
    actions: [({
      event
    }, {
      selection
    }) => [raise({
      type: "serialize",
      originEvent: event
    }), raise({
      type: "delete",
      at: selection
    })]]
  }),
  defineBehavior({
    on: "drag.dragstart",
    actions: [({
      event
    }) => [raise({
      type: "serialize",
      originEvent: event
    })]]
  }),
  defineBehavior({
    on: "serialization.success",
    actions: [({
      event
    }) => [{
      type: "effect",
      effect: () => {
        event.originEvent.originEvent.dataTransfer.setData(event.mimeType, event.data);
      }
    }]]
  }),
  defineBehavior({
    on: "serialization.failure",
    actions: [({
      event
    }) => [{
      type: "effect",
      effect: () => {
        console.warn(`Serialization of ${event.mimeType} failed with reason "${event.reason}"`);
      }
    }]]
  }),
  /**
   * If we are pasting text/plain into a text block then we can probably
   * assume that the intended behavior is that the pasted text inherits
   * formatting from the text it's pasted into.
   */
  defineBehavior({
    on: "deserialization.success",
    guard: ({
      snapshot,
      event
    }) => {
      if (getFocusTextBlock(snapshot) && event.mimeType === "text/plain" && event.originEvent.type === "clipboard.paste") {
        const activeDecorators = getActiveDecorators(snapshot);
        return {
          activeAnnotations: getActiveAnnotations(snapshot),
          activeDecorators,
          textRuns: event.data.flatMap((block) => isTextBlock(snapshot.context, block) ? [getTextBlockText(block)] : [])
        };
      }
      return !1;
    },
    actions: [(_, {
      activeAnnotations,
      activeDecorators,
      textRuns
    }) => textRuns.flatMap((textRun, index) => index !== textRuns.length - 1 ? [raise({
      type: "insert.span",
      text: textRun,
      decorators: activeDecorators,
      annotations: activeAnnotations.map(({
        _key,
        _type,
        ...value
      }) => ({
        name: _type,
        value
      }))
    }), raise({
      type: "insert.break"
    })] : [raise({
      type: "insert.span",
      text: textRun,
      decorators: activeDecorators,
      annotations: activeAnnotations.map(({
        _key,
        _type,
        ...value
      }) => ({
        name: _type,
        value
      }))
    })])]
  }),
  defineBehavior({
    on: "deserialization.success",
    actions: [({
      event
    }) => [raise({
      type: "insert.blocks",
      blocks: event.data,
      placement: "auto"
    })]]
  }),
  defineBehavior({
    on: "deserialization.failure",
    actions: [({
      event
    }) => [{
      type: "effect",
      effect: () => {
        console.warn(`Deserialization of ${event.mimeType} failed with reason "${event.reason}"`);
      }
    }]]
  }),
  defineBehavior({
    on: "clipboard.paste",
    guard: ({
      snapshot
    }) => snapshot.context.selection && isSelectionExpanded(snapshot) ? {
      selection: snapshot.context.selection
    } : !1,
    actions: [({
      event
    }, {
      selection
    }) => [raise({
      type: "delete",
      at: selection
    }), raise({
      type: "deserialize",
      originEvent: event
    })]]
  }),
  defineBehavior({
    on: "clipboard.paste",
    actions: [({
      event
    }) => [raise({
      type: "deserialize",
      originEvent: event
    })]]
  }),
  defineBehavior({
    on: "input.*",
    actions: [({
      event
    }) => [raise({
      type: "deserialize",
      originEvent: event
    })]]
  }),
  ...abstractAnnotationBehaviors,
  ...abstractDecoratorBehaviors,
  ...abstractDeleteBehaviors,
  ...abstractInsertBehaviors,
  ...abstractKeyboardBehaviors,
  ...abstractListItemBehaviors,
  ...abstractMoveBehaviors,
  ...abstractStyleBehaviors,
  ...abstractSelectBehaviors,
  ...abstractSplitBehaviors,
  raiseDeserializationSuccessOrFailure,
  raiseSerializationSuccessOrFailure
];
function isSyntheticBehaviorEvent(event) {
  return !isCustomBehaviorEvent(event) && !isNativeBehaviorEvent(event) && !isAbstractBehaviorEvent(event);
}
const abstractBehaviorEventTypes = ["annotation.set", "annotation.toggle", "decorator.toggle", "delete.backward", "delete.block", "delete.child", "delete.forward", "delete.text", "deserialize", "deserialization.success", "deserialization.failure", "insert.blocks", "insert.break", "insert.soft break", "list item.add", "list item.remove", "list item.toggle", "move.block down", "move.block up", "select.previous block", "select.next block", "serialize", "serialization.success", "serialization.failure", "split", "style.add", "style.remove", "style.toggle"];
function isAbstractBehaviorEvent(event) {
  return abstractBehaviorEventTypes.includes(event.type);
}
const nativeBehaviorEventTypes = ["clipboard.copy", "clipboard.cut", "clipboard.paste", "drag.dragstart", "drag.drag", "drag.dragend", "drag.dragenter", "drag.dragover", "drag.dragleave", "drag.drop", "input.*", "keyboard.keydown", "keyboard.keyup", "mouse.click"];
function isNativeBehaviorEvent(event) {
  return nativeBehaviorEventTypes.includes(event.type);
}
function isCustomBehaviorEvent(event) {
  return event.type.startsWith("custom.");
}
const debug$8 = debugWithName("behaviors:event");
function eventCategory(event) {
  return isNativeBehaviorEvent(event) ? "native" : isAbstractBehaviorEvent(event) ? "synthetic" : isCustomBehaviorEvent(event) ? "custom" : "synthetic";
}
function performEvent({
  mode,
  behaviors,
  remainingEventBehaviors,
  event,
  editor,
  keyGenerator,
  schema,
  getSnapshot,
  nativeEvent,
  sendBack
}) {
  debug$8(`(${mode}:${eventCategory(event)})`, JSON.stringify(event, null, 2));
  const eventBehaviors = [...remainingEventBehaviors, ...abstractBehaviors].filter((behavior) => {
    if (behavior.on === "*")
      return !0;
    const [listenedNamespace] = behavior.on.includes("*") && behavior.on.includes(".") ? behavior.on.split(".") : [void 0], [eventNamespace] = event.type.includes(".") ? event.type.split(".") : [void 0];
    return listenedNamespace !== void 0 && eventNamespace !== void 0 && listenedNamespace === eventNamespace || listenedNamespace !== void 0 && eventNamespace === void 0 && listenedNamespace === event.type ? !0 : behavior.on === event.type;
  });
  if (eventBehaviors.length === 0 && isSyntheticBehaviorEvent(event)) {
    nativeEvent?.preventDefault(), withApplyingBehaviorOperations(editor, () => {
      debug$8(`(execute:${eventCategory(event)})`, JSON.stringify(event, null, 2)), performOperation({
        context: {
          keyGenerator,
          schema
        },
        operation: {
          ...event,
          editor
        }
      });
    }), editor.onChange();
    return;
  }
  const guardSnapshot = getSnapshot();
  let nativeEventPrevented = !1, defaultBehaviorOverwritten = !1, eventBehaviorIndex = -1;
  for (const eventBehavior of eventBehaviors) {
    eventBehaviorIndex++;
    let shouldRun = !1;
    try {
      shouldRun = eventBehavior.guard === void 0 || eventBehavior.guard({
        snapshot: guardSnapshot,
        event,
        dom: createEditorDom(sendBack, editor)
      });
    } catch (error) {
      console.error(new Error(`Evaluating guard for "${event.type}" failed due to: ${error.message}`));
    }
    if (shouldRun) {
      defaultBehaviorOverwritten = !0;
      for (const actionSet of eventBehavior.actions) {
        const actionsSnapshot = getSnapshot();
        let actions = [];
        try {
          actions = actionSet({
            snapshot: actionsSnapshot,
            event,
            dom: createEditorDom(sendBack, editor)
          }, shouldRun);
        } catch (error) {
          console.error(new Error(`Evaluating actions for "${event.type}" failed due to: ${error.message}`));
        }
        if (actions.length !== 0) {
          if (nativeEventPrevented = actions.some((action) => action.type === "raise" || action.type === "execute") || !actions.some((action) => action.type === "forward"), actions.some((action) => action.type === "execute")) {
            withUndoStep(editor, () => {
              for (const action of actions) {
                if (action.type === "effect") {
                  try {
                    action.effect();
                  } catch (error) {
                    console.error(new Error(`Executing effect as a result of "${event.type}" failed due to: ${error.message}`));
                  }
                  continue;
                }
                if (action.type === "forward") {
                  const remainingEventBehaviors2 = eventBehaviors.slice(eventBehaviorIndex + 1);
                  performEvent({
                    mode: "forward",
                    behaviors,
                    remainingEventBehaviors: remainingEventBehaviors2,
                    event: action.event,
                    editor,
                    keyGenerator,
                    schema,
                    getSnapshot,
                    nativeEvent,
                    sendBack
                  });
                  continue;
                }
                if (action.type === "raise") {
                  performEvent({
                    mode: "raise",
                    behaviors,
                    remainingEventBehaviors: behaviors,
                    event: action.event,
                    editor,
                    keyGenerator,
                    schema,
                    getSnapshot,
                    nativeEvent,
                    sendBack
                  });
                  continue;
                }
                performEvent({
                  mode: "execute",
                  behaviors,
                  remainingEventBehaviors: [],
                  event: action.event,
                  editor,
                  keyGenerator,
                  schema,
                  getSnapshot,
                  nativeEvent: void 0,
                  sendBack
                });
              }
            });
            continue;
          }
          for (const action of actions) {
            if (action.type === "effect") {
              try {
                action.effect();
              } catch (error) {
                console.error(new Error(`Executing effect as a result of "${event.type}" failed due to: ${error.message}`));
              }
              continue;
            }
            if (action.type === "forward") {
              const remainingEventBehaviors2 = eventBehaviors.slice(eventBehaviorIndex + 1);
              performEvent({
                mode: "forward",
                behaviors,
                remainingEventBehaviors: remainingEventBehaviors2,
                event: action.event,
                editor,
                keyGenerator,
                schema,
                getSnapshot,
                nativeEvent,
                sendBack
              });
              continue;
            }
            if (action.type === "raise") {
              performEvent({
                mode: "raise",
                behaviors,
                remainingEventBehaviors: behaviors,
                event: action.event,
                editor,
                keyGenerator,
                schema,
                getSnapshot,
                nativeEvent,
                sendBack
              });
              continue;
            }
            action.type === "execute" && console.error("Unexpected action type: `execute`");
          }
        }
      }
      break;
    }
  }
  !defaultBehaviorOverwritten && isSyntheticBehaviorEvent(event) ? (nativeEvent?.preventDefault(), withApplyingBehaviorOperations(editor, () => {
    debug$8(`(execute:${eventCategory(event)})`, JSON.stringify(event, null, 2)), performOperation({
      context: {
        keyGenerator,
        schema
      },
      operation: {
        ...event,
        editor
      }
    });
  }), editor.onChange()) : nativeEventPrevented && nativeEvent?.preventDefault();
}
function sortByPriority(items) {
  if (items.length === 0)
    return [];
  const itemsWithPriority = items.filter((item) => item.priority !== void 0), itemsWithoutPriority = items.filter((item) => item.priority === void 0);
  if (itemsWithPriority.length === 0)
    return items;
  const itemsByPriorityId = new Map(itemsWithPriority.map((item) => [item.priority.id, item])), graph = /* @__PURE__ */ new Map(), inDegree = /* @__PURE__ */ new Map();
  function ensureNode(id) {
    graph.has(id) || (graph.set(id, /* @__PURE__ */ new Set()), inDegree.set(id, 0));
  }
  for (const item of itemsWithPriority) {
    const id = item.priority.id;
    ensureNode(id);
  }
  function addEdge(fromId, toId) {
    !graph.has(fromId) || !graph.has(toId) || (graph.get(fromId)?.add(toId), inDegree.set(toId, (inDegree.get(toId) ?? 0) + 1));
  }
  for (const item of itemsWithPriority) {
    const id = item.priority.id, visited = /* @__PURE__ */ new Set();
    let ref = item.priority.reference;
    for (; ref; ) {
      const refId = ref.priority.id;
      if (ensureNode(refId), visited.has(refId))
        throw new Error("Circular dependency detected in priorities");
      visited.add(refId), ref.importance === "higher" ? addEdge(id, refId) : addEdge(refId, id), ref = ref.priority.reference;
    }
  }
  const queue = [];
  for (const [id, degree] of inDegree)
    degree === 0 && queue.push(id);
  const result = [];
  for (; queue.length > 0; ) {
    const currentId = queue.shift(), currentItem = itemsByPriorityId.get(currentId);
    currentItem && result.push(currentItem);
    for (const neighborId of graph.get(currentId) ?? []) {
      const newDegree = (inDegree.get(neighborId) ?? 0) - 1;
      inDegree.set(neighborId, newDegree), newDegree === 0 && queue.push(neighborId);
    }
  }
  for (const item of itemsWithPriority)
    result.includes(item) || result.push(item);
  return [...result, ...itemsWithoutPriority];
}
function createEditorSnapshot({
  converters,
  editor,
  keyGenerator,
  readOnly,
  schema
}) {
  const selection = editor.selection ? slateRangeToSelection({
    schema,
    editor,
    range: editor.selection
  }) : null, context = {
    converters,
    keyGenerator,
    readOnly,
    schema,
    selection,
    value: editor.value
  };
  return {
    blockIndexMap: editor.blockIndexMap,
    context,
    decoratorState: editor.decoratorState
  };
}
const debug$7 = debugWithName("editor machine"), editorMachine = setup({
  types: {
    context: {},
    events: {},
    emitted: {},
    input: {},
    tags: {}
  },
  actions: {
    "add behavior to context": assign({
      behaviors: ({
        context,
        event
      }) => (assertEvent(event, "add behavior"), /* @__PURE__ */ new Set([...context.behaviors, event.behaviorConfig])),
      behaviorsSorted: !1
    }),
    "remove behavior from context": assign({
      behaviors: ({
        context,
        event
      }) => (assertEvent(event, "remove behavior"), context.behaviors.delete(event.behaviorConfig), /* @__PURE__ */ new Set([...context.behaviors]))
    }),
    "emit patch event": emit(({
      event
    }) => (assertEvent(event, "internal.patch"), event)),
    "emit mutation event": emit(({
      event
    }) => (assertEvent(event, "mutation"), event)),
    "emit read only": emit({
      type: "read only"
    }),
    "emit editable": emit({
      type: "editable"
    }),
    "defer event": assign({
      pendingEvents: ({
        context,
        event
      }) => (assertEvent(event, ["internal.patch", "mutation"]), [...context.pendingEvents, event])
    }),
    "emit pending events": enqueueActions(({
      context,
      enqueue
    }) => {
      for (const event of context.pendingEvents)
        enqueue.emit(event);
    }),
    "emit ready": emit({
      type: "ready"
    }),
    "clear pending events": assign({
      pendingEvents: []
    }),
    "defer incoming patches": assign({
      pendingIncomingPatchesEvents: ({
        context,
        event
      }) => event.type === "patches" ? [...context.pendingIncomingPatchesEvents, event] : context.pendingIncomingPatchesEvents
    }),
    "emit pending incoming patches": enqueueActions(({
      context,
      enqueue
    }) => {
      for (const event of context.pendingIncomingPatchesEvents)
        enqueue.emit(event);
    }),
    "clear pending incoming patches": assign({
      pendingIncomingPatchesEvents: []
    }),
    "handle blur": ({
      event
    }) => {
      assertEvent(event, "blur");
      try {
        ReactEditor.blur(event.editor);
      } catch (error) {
        console.error(new Error(`Failed to blur editor: ${error.message}`));
      }
    },
    "handle focus": ({
      context
    }) => {
      if (!context.slateEditor) {
        console.error("No Slate editor found to focus");
        return;
      }
      try {
        const currentSelection = context.slateEditor.selection;
        ReactEditor.focus(context.slateEditor), currentSelection && Transforms.select(context.slateEditor, currentSelection);
      } catch (error) {
        console.error(new Error(`Failed to focus editor: ${error.message}`));
      }
    },
    "handle behavior event": ({
      context,
      event,
      self
    }) => {
      assertEvent(event, ["behavior event"]);
      try {
        const behaviors = [...context.behaviors.values()].map((config) => config.behavior);
        performEvent({
          mode: "raise",
          behaviors,
          remainingEventBehaviors: behaviors,
          event: event.behaviorEvent,
          editor: event.editor,
          keyGenerator: context.keyGenerator,
          schema: context.schema,
          getSnapshot: () => createEditorSnapshot({
            converters: [...context.converters],
            editor: event.editor,
            keyGenerator: context.keyGenerator,
            readOnly: self.getSnapshot().matches({
              "edit mode": "read only"
            }),
            schema: context.schema
          }),
          nativeEvent: event.nativeEvent,
          sendBack: (event2) => self.send(event2)
        });
      } catch (error) {
        console.error(new Error(`Raising "${event.behaviorEvent.type}" failed due to: ${error.message}`));
      }
    },
    "sort behaviors": assign({
      behaviors: ({
        context
      }) => context.behaviorsSorted ? context.behaviors : new Set(sortByPriority([...context.behaviors.values()])),
      behaviorsSorted: !0
    })
  },
  guards: {
    "slate is busy": ({
      context
    }) => context.slateEditor ? context.slateEditor.operations.length > 0 : !1
  }
}).createMachine({
  id: "editor",
  context: ({
    input
  }) => ({
    behaviors: new Set(coreBehaviorsConfig),
    behaviorsSorted: !1,
    converters: new Set(input.converters ?? []),
    getLegacySchema: input.getLegacySchema,
    keyGenerator: input.keyGenerator,
    pendingEvents: [],
    pendingIncomingPatchesEvents: [],
    schema: input.schema,
    selection: null,
    initialReadOnly: input.readOnly ?? !1,
    maxBlocks: input.maxBlocks,
    initialValue: input.initialValue
  }),
  on: {
    "add behavior": {
      actions: "add behavior to context"
    },
    "remove behavior": {
      actions: "remove behavior from context"
    },
    "update maxBlocks": {
      actions: assign({
        maxBlocks: ({
          event
        }) => event.maxBlocks
      })
    },
    "update selection": {
      actions: [assign({
        selection: ({
          event
        }) => event.selection
      }), emit(({
        event
      }) => ({
        ...event,
        type: "selection"
      }))]
    },
    "set drag ghost": {
      actions: assign({
        dragGhost: ({
          event
        }) => event.ghost
      })
    }
  },
  type: "parallel",
  states: {
    "edit mode": {
      initial: "read only",
      states: {
        "read only": {
          initial: "determine initial edit mode",
          on: {
            "behavior event": {
              actions: ["sort behaviors", "handle behavior event"],
              guard: ({
                event
              }) => event.behaviorEvent.type === "clipboard.copy" || event.behaviorEvent.type === "mouse.click" || event.behaviorEvent.type === "serialize" || event.behaviorEvent.type === "serialization.failure" || event.behaviorEvent.type === "serialization.success" || event.behaviorEvent.type === "select"
            }
          },
          states: {
            "determine initial edit mode": {
              entry: [() => {
                debug$7("entry: edit mode->read only->determine initial edit mode");
              }],
              exit: [() => {
                debug$7("exit: edit mode->read only->determine initial edit mode");
              }],
              on: {
                "done syncing value": [{
                  target: "#editor.edit mode.read only.read only",
                  guard: ({
                    context
                  }) => context.initialReadOnly
                }, {
                  target: "#editor.edit mode.editable"
                }]
              }
            },
            "read only": {
              entry: [() => {
                debug$7("entry: edit mode->read only->read only");
              }],
              exit: [() => {
                debug$7("exit: edit mode->read only->read only");
              }],
              on: {
                "update readOnly": {
                  guard: ({
                    event
                  }) => !event.readOnly,
                  target: "#editor.edit mode.editable",
                  actions: ["emit editable"]
                }
              }
            }
          }
        },
        editable: {
          on: {
            "update readOnly": {
              guard: ({
                event
              }) => event.readOnly,
              target: "#editor.edit mode.read only.read only",
              actions: ["emit read only"]
            },
            "behavior event": {
              actions: ["sort behaviors", "handle behavior event"]
            },
            blur: {
              actions: "handle blur"
            },
            focus: {
              target: ".focusing",
              actions: [assign({
                slateEditor: ({
                  event
                }) => event.editor
              })]
            }
          },
          initial: "idle",
          states: {
            idle: {
              entry: [() => {
                debug$7("entry: edit mode->editable->idle");
              }],
              exit: [() => {
                debug$7("exit: edit mode->editable-idle");
              }],
              on: {
                dragstart: {
                  actions: [assign({
                    internalDrag: ({
                      event
                    }) => ({
                      origin: event.origin
                    })
                  })],
                  target: "dragging internally"
                }
              }
            },
            focusing: {
              initial: "checking if busy",
              states: {
                "checking if busy": {
                  entry: [() => {
                    debug$7("entry: edit mode->editable->focusing->checking if busy");
                  }],
                  exit: [() => {
                    debug$7("exit: edit mode->editable->focusing->checking if busy");
                  }],
                  always: [{
                    guard: "slate is busy",
                    target: "busy"
                  }, {
                    target: "#editor.edit mode.editable.idle",
                    actions: ["handle focus"]
                  }]
                },
                busy: {
                  entry: [() => {
                    debug$7("entry: edit mode->editable->focusing-busy");
                  }],
                  exit: [() => {
                    debug$7("exit: edit mode->editable->focusing->busy");
                  }],
                  after: {
                    10: {
                      target: "checking if busy"
                    }
                  }
                }
              }
            },
            "dragging internally": {
              entry: [() => {
                debug$7("entry: edit mode->editable->dragging internally");
              }],
              exit: [() => {
                debug$7("exit: edit mode->editable->dragging internally");
              }, ({
                context
              }) => {
                if (context.dragGhost)
                  try {
                    context.dragGhost.parentNode?.removeChild(context.dragGhost);
                  } catch (error) {
                    console.error(new Error(`Removing the drag ghost failed due to: ${error.message}`));
                  }
              }, assign({
                dragGhost: void 0
              }), assign({
                internalDrag: void 0
              })],
              tags: ["dragging internally"],
              on: {
                dragend: {
                  target: "idle"
                },
                drop: {
                  target: "idle"
                }
              }
            }
          }
        }
      }
    },
    setup: {
      initial: "setting up",
      states: {
        "setting up": {
          entry: [() => {
            debug$7("entry: setup->setting up");
          }],
          exit: [() => {
            debug$7("exit: setup->setting up");
          }, "emit ready", "emit pending incoming patches", "clear pending incoming patches"],
          on: {
            "internal.patch": {
              actions: "defer event"
            },
            mutation: {
              actions: "defer event"
            },
            "done syncing value": {
              target: "set up"
            },
            patches: {
              actions: ["defer incoming patches"]
            }
          }
        },
        "set up": {
          type: "parallel",
          states: {
            "value sync": {
              initial: "idle",
              states: {
                idle: {
                  entry: [() => {
                    debug$7("entry: setup->set up->value sync->idle");
                  }],
                  exit: [() => {
                    debug$7("exit: setup->set up->value sync->idle");
                  }],
                  on: {
                    patches: {
                      actions: [emit(({
                        event
                      }) => event)]
                    },
                    "syncing value": {
                      target: "syncing value"
                    }
                  }
                },
                "syncing value": {
                  entry: [() => {
                    debug$7("entry: setup->set up->value sync->syncing value");
                  }],
                  exit: [() => {
                    debug$7("exit: setup->set up->value sync->syncing value");
                  }, "emit pending incoming patches", "clear pending incoming patches"],
                  on: {
                    patches: {
                      actions: ["defer incoming patches"]
                    },
                    "done syncing value": {
                      target: "idle"
                    }
                  }
                }
              }
            },
            writing: {
              initial: "pristine",
              states: {
                pristine: {
                  initial: "idle",
                  states: {
                    idle: {
                      entry: [() => {
                        debug$7("entry: setup->set up->writing->pristine->idle");
                      }],
                      exit: [() => {
                        debug$7("exit: setup->set up->writing->pristine->idle");
                      }],
                      on: {
                        normalizing: {
                          target: "normalizing"
                        },
                        "internal.patch": {
                          actions: "defer event",
                          target: "#editor.setup.set up.writing.dirty"
                        },
                        mutation: {
                          actions: "defer event",
                          target: "#editor.setup.set up.writing.dirty"
                        }
                      }
                    },
                    normalizing: {
                      entry: [() => {
                        debug$7("entry: setup->set up->writing->pristine->normalizing");
                      }],
                      exit: [() => {
                        debug$7("exit: setup->set up->writing->pristine->normalizing");
                      }],
                      on: {
                        "done normalizing": {
                          target: "idle"
                        },
                        "internal.patch": {
                          actions: "defer event"
                        },
                        mutation: {
                          actions: "defer event"
                        }
                      }
                    }
                  }
                },
                dirty: {
                  entry: [() => {
                    debug$7("entry: setup->set up->writing->dirty");
                  }, "emit pending events", "clear pending events"],
                  exit: [() => {
                    debug$7("exit: setup->set up->writing->dirty");
                  }],
                  on: {
                    "internal.patch": {
                      actions: "emit patch event"
                    },
                    mutation: {
                      actions: "emit mutation event"
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}), debug$6 = debugWithName("mutation-machine"), mutationMachine = setup({
  types: {
    context: {},
    events: {},
    input: {},
    emitted: {}
  },
  actions: {
    "assign readOnly": assign({
      readOnly: ({
        context,
        event
      }) => event.type === "update readOnly" ? event.readOnly : context.readOnly
    }),
    "emit patch": emit(({
      event
    }) => (assertEvent(event, "patch"), {
      type: "patch",
      patch: event.patch
    })),
    "emit has pending mutations": emit({
      type: "has pending mutations"
    }),
    "emit mutations": enqueueActions(({
      context,
      enqueue
    }) => {
      for (const bulk of context.pendingMutations)
        enqueue.emit({
          type: "mutation",
          patches: bulk.patches,
          snapshot: bulk.value
        });
    }),
    "clear pending mutations": assign({
      pendingMutations: []
    }),
    "defer mutation": assign({
      pendingMutations: ({
        context,
        event
      }) => {
        if (assertEvent(event, "patch"), context.pendingMutations.length === 0)
          return [{
            operationId: event.operationId,
            value: event.value,
            patches: [event.patch]
          }];
        const lastBulk = context.pendingMutations.at(-1);
        return lastBulk && lastBulk.operationId === event.operationId ? context.pendingMutations.slice(0, -1).concat({
          value: event.value,
          operationId: lastBulk.operationId,
          patches: [...lastBulk.patches, event.patch]
        }) : context.pendingMutations.concat({
          value: event.value,
          operationId: event.operationId,
          patches: [event.patch]
        });
      }
    }),
    "clear pending patch events": assign({
      pendingPatchEvents: []
    }),
    "defer patch": assign({
      pendingPatchEvents: ({
        context,
        event
      }) => event.type === "patch" ? [...context.pendingPatchEvents, event] : context.pendingPatchEvents
    }),
    "emit pending patch events": enqueueActions(({
      context,
      enqueue
    }) => {
      for (const event of context.pendingPatchEvents)
        enqueue.emit(event);
    })
  },
  actors: {
    "type listener": fromCallback(({
      input,
      sendBack
    }) => {
      const originalApply = input.slateEditor.apply;
      return input.slateEditor.apply = (op) => {
        op.type === "insert_text" || op.type === "remove_text" ? sendBack({
          type: "typing"
        }) : sendBack({
          type: "not typing"
        }), originalApply(op);
      }, () => {
        input.slateEditor.apply = originalApply;
      };
    }),
    "mutation debouncer": fromCallback(({
      sendBack
    }) => {
      const interval = setInterval(() => {
        sendBack({
          type: "mutation delay passed"
        });
      }, process.env.NODE_ENV === "test" ? 250 : 0);
      return () => {
        clearInterval(interval);
      };
    })
  },
  guards: {
    "is read-only": ({
      context
    }) => context.readOnly,
    "is typing": stateIn({
      typing: "typing"
    }),
    "slate is normalizing": ({
      context
    }) => Editor.isNormalizing(context.slateEditor)
  },
  delays: {
    "type debounce": process.env.NODE_ENV === "test" ? 0 : 250
  }
}).createMachine({
  id: "mutation",
  context: ({
    input
  }) => ({
    pendingMutations: [],
    pendingPatchEvents: [],
    readOnly: input.readOnly,
    schema: input.schema,
    slateEditor: input.slateEditor
  }),
  on: {
    "update readOnly": {
      actions: ["assign readOnly"]
    }
  },
  type: "parallel",
  states: {
    typing: {
      initial: "idle",
      invoke: {
        src: "type listener",
        input: ({
          context
        }) => ({
          slateEditor: context.slateEditor
        })
      },
      states: {
        idle: {
          entry: [() => {
            debug$6("entry: typing->idle");
          }],
          exit: [() => {
            debug$6("exit: typing->idle"), debug$6("entry: typing->typing");
          }],
          on: {
            typing: {
              target: "typing"
            }
          }
        },
        typing: {
          after: {
            "type debounce": {
              target: "idle",
              actions: [() => {
                debug$6("exit: typing->typing");
              }]
            }
          },
          on: {
            "not typing": {
              target: "idle"
            },
            typing: {
              target: "typing",
              reenter: !0
            }
          }
        }
      }
    },
    mutations: {
      initial: "idle",
      states: {
        idle: {
          entry: [() => {
            debug$6("entry: mutations->idle");
          }],
          exit: [() => {
            debug$6("exit: mutations->idle");
          }],
          on: {
            patch: [{
              guard: "is read-only",
              actions: ["defer patch", "defer mutation"],
              target: "has pending mutations"
            }, {
              actions: ["emit patch", "defer mutation"],
              target: "has pending mutations"
            }]
          }
        },
        "has pending mutations": {
          entry: [() => {
            debug$6("entry: mutations->has pending mutations");
          }, "emit has pending mutations"],
          exit: [() => {
            debug$6("exit: mutations->has pending mutations");
          }],
          invoke: {
            src: "mutation debouncer"
          },
          on: {
            "mutation delay passed": {
              guard: and([not("is read-only"), not("is typing"), "slate is normalizing"]),
              target: "idle",
              actions: ["emit pending patch events", "clear pending patch events", "emit mutations", "clear pending mutations"]
            },
            patch: [{
              guard: "is read-only",
              actions: ["defer patch", "defer mutation"]
            }, {
              actions: ["emit patch", "defer mutation"]
            }]
          }
        }
      }
    }
  }
}), debug$5 = debugWithName("API:editable");
function createEditableAPI(editor, editorActor) {
  const types = editorActor.getSnapshot().context.schema;
  return {
    focus: () => {
      editorActor.send({
        type: "focus",
        editor
      });
    },
    blur: () => {
      editorActor.send({
        type: "blur",
        editor
      });
    },
    toggleMark: (mark) => {
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "decorator.toggle",
          decorator: mark
        },
        editor
      });
    },
    toggleList: (listItem) => {
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "list item.toggle",
          listItem
        },
        editor
      });
    },
    toggleBlockStyle: (style) => {
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "style.toggle",
          style
        },
        editor
      });
    },
    isMarkActive: (mark) => {
      const snapshot = getEditorSnapshot({
        editorActorSnapshot: editorActor.getSnapshot(),
        slateEditorInstance: editor
      });
      return getActiveDecorators(snapshot).includes(mark);
    },
    marks: () => {
      const snapshot = getEditorSnapshot({
        editorActorSnapshot: editorActor.getSnapshot(),
        slateEditorInstance: editor
      }), activeAnnotations = getActiveAnnotationsMarks(snapshot), activeDecorators = getActiveDecorators(snapshot);
      return [...activeAnnotations, ...activeDecorators];
    },
    undo: () => {
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "history.undo"
        },
        editor
      });
    },
    redo: () => {
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "history.redo"
        },
        editor
      });
    },
    select: (selection) => {
      const slateSelection = toSlateRange({
        context: {
          schema: editorActor.getSnapshot().context.schema,
          value: editor.value,
          selection
        },
        blockIndexMap: editor.blockIndexMap
      });
      slateSelection ? Transforms.select(editor, slateSelection) : Transforms.deselect(editor), editor.onChange();
    },
    focusBlock: () => {
      if (editor.selection) {
        const block = Node.descendant(editor, editor.selection.focus.path.slice(0, 1));
        if (block)
          return fromSlateValue([block], types.block.name, KEY_TO_VALUE_ELEMENT.get(editor))[0];
      }
    },
    focusChild: () => {
      if (editor.selection) {
        const block = Node.descendant(editor, editor.selection.focus.path.slice(0, 1));
        if (block && editor.isTextBlock(block))
          return fromSlateValue([block], types.block.name, KEY_TO_VALUE_ELEMENT.get(editor))[0].children[editor.selection.focus.path[1]];
      }
    },
    insertChild: (type, value) => {
      if (type.name !== types.span.name)
        return editorActor.send({
          type: "behavior event",
          behaviorEvent: {
            type: "insert.inline object",
            inlineObject: {
              name: type.name,
              value
            }
          },
          editor
        }), editor.selection ? slateRangeToSelection({
          schema: editorActor.getSnapshot().context.schema,
          editor,
          range: editor.selection
        })?.focus.path ?? [] : [];
      if (!editor.selection)
        throw new Error("The editor has no selection");
      const [focusBlock] = Array.from(Editor.nodes(editor, {
        at: editor.selection.focus.path.slice(0, 1),
        match: (n) => n._type === types.block.name
      }))[0] || [void 0];
      if (!focusBlock)
        throw new Error("No focused text block");
      if (type.name !== types.span.name && !types.inlineObjects.some((t) => t.name === type.name))
        throw new Error("This type cannot be inserted as a child to a text block");
      const child = toSlateValue([{
        _key: editorActor.getSnapshot().context.keyGenerator(),
        _type: types.block.name,
        children: [{
          _key: editorActor.getSnapshot().context.keyGenerator(),
          _type: type.name,
          ...value || {}
        }]
      }], {
        schemaTypes: editorActor.getSnapshot().context.schema
      })[0].children[0], focusChildPath = editor.selection.focus.path.slice(0, 2), isSpanNode2 = child._type === types.span.name, focusNode = Node.get(editor, focusChildPath);
      return isSpanNode2 && focusNode._type !== types.span.name && (debug$5("Inserting span child next to inline object child, moving selection + 1"), editor.move({
        distance: 1,
        unit: "character"
      })), Transforms.insertNodes(editor, child, {
        select: !0,
        at: editor.selection
      }), editor.onChange(), editor.selection ? slateRangeToSelection({
        schema: editorActor.getSnapshot().context.schema,
        editor,
        range: editor.selection
      })?.focus.path ?? [] : [];
    },
    insertBlock: (type, value) => (editorActor.send({
      type: "behavior event",
      behaviorEvent: {
        type: "insert.block",
        block: {
          _type: type.name,
          ...value || {}
        },
        placement: "auto"
      },
      editor
    }), editor.selection ? slateRangeToSelection({
      schema: editorActor.getSnapshot().context.schema,
      editor,
      range: editor.selection
    })?.focus.path ?? [] : []),
    hasBlockStyle: (style) => {
      try {
        return isStyleActive({
          editor,
          style
        });
      } catch {
        return !1;
      }
    },
    hasListStyle: (listItem) => {
      try {
        return isListItemActive({
          editor,
          listItem
        });
      } catch {
        return !1;
      }
    },
    isVoid: (element) => ![types.block.name, types.span.name].includes(element._type),
    findByPath: (path) => {
      const slatePath = toSlateRange({
        context: {
          schema: editorActor.getSnapshot().context.schema,
          value: editor.value,
          selection: {
            focus: {
              path,
              offset: 0
            },
            anchor: {
              path,
              offset: 0
            }
          }
        },
        blockIndexMap: editor.blockIndexMap
      });
      if (slatePath) {
        const [block, blockPath] = Editor.node(editor, slatePath.focus.path.slice(0, 1));
        if (block && blockPath && typeof block._key == "string") {
          if (path.length === 1 && slatePath.focus.path.length === 1)
            return [fromSlateValue([block], types.block.name)[0], [{
              _key: block._key
            }]];
          const ptBlock = fromSlateValue([block], types.block.name, KEY_TO_VALUE_ELEMENT.get(editor))[0];
          if (editor.isTextBlock(ptBlock)) {
            const ptChild = ptBlock.children[slatePath.focus.path[1]];
            if (ptChild)
              return [ptChild, [{
                _key: block._key
              }, "children", {
                _key: ptChild._key
              }]];
          }
        }
      }
      return [void 0, void 0];
    },
    findDOMNode: (element) => {
      let node;
      try {
        const [item] = Array.from(Editor.nodes(editor, {
          at: [],
          match: (n) => n._key === element._key
        }) || [])[0] || [void 0];
        node = ReactEditor.toDOMNode(editor, item);
      } catch {
      }
      return node;
    },
    activeAnnotations: () => {
      if (!editor.selection || editor.selection.focus.path.length < 2)
        return [];
      try {
        const activeAnnotations = [], spans = Editor.nodes(editor, {
          at: editor.selection,
          match: (node) => Text.isText(node) && node.marks !== void 0 && Array.isArray(node.marks) && node.marks.length > 0
        });
        for (const [span, path] of spans) {
          const [block] = Editor.node(editor, path, {
            depth: 1
          });
          editor.isTextBlock(block) && block.markDefs?.forEach((def) => {
            Text.isText(span) && span.marks && Array.isArray(span.marks) && span.marks.includes(def._key) && activeAnnotations.push(def);
          });
        }
        return activeAnnotations;
      } catch {
        return [];
      }
    },
    isAnnotationActive: (annotationType) => {
      const snapshot = getEditorSnapshot({
        editorActorSnapshot: editorActor.getSnapshot(),
        slateEditorInstance: editor
      });
      return isActiveAnnotation(annotationType)(snapshot);
    },
    addAnnotation: (type, value) => {
      let paths;
      return Editor.withoutNormalizing(editor, () => {
        paths = addAnnotationOperationImplementation({
          context: {
            keyGenerator: editorActor.getSnapshot().context.keyGenerator,
            schema: types
          },
          operation: {
            annotation: {
              name: type.name,
              value: value ?? {}
            },
            editor
          }
        });
      }), editor.onChange(), paths;
    },
    delete: (selection, options) => {
      if (selection) {
        const range = toSlateRange({
          context: {
            schema: editorActor.getSnapshot().context.schema,
            value: editor.value,
            selection
          },
          blockIndexMap: editor.blockIndexMap
        });
        if (!(range && range.anchor.path.length > 0 && range.focus.path.length > 0))
          throw new Error("Invalid range");
        if (range) {
          if (!options?.mode || options?.mode === "selected") {
            debug$5("Deleting content in selection"), Transforms.delete(editor, {
              at: range,
              hanging: !0,
              voids: !0
            }), editor.onChange();
            return;
          }
          if (options?.mode === "blocks" && (debug$5("Deleting blocks touched by selection"), Transforms.removeNodes(editor, {
            at: range,
            voids: !0,
            match: (node) => editor.isTextBlock(node) || !editor.isTextBlock(node) && Element$1.isElement(node)
          })), options?.mode === "children" && (debug$5("Deleting children touched by selection"), Transforms.removeNodes(editor, {
            at: range,
            voids: !0,
            match: (node) => node._type === types.span.name || // Text children
            !editor.isTextBlock(node) && Element$1.isElement(node)
          })), editor.children.length === 0) {
            const placeholderBlock = createPlaceholderBlock(editorActor.getSnapshot().context);
            editor.children = [placeholderBlock], editor.value = [placeholderBlock], buildIndexMaps({
              schema: editorActor.getSnapshot().context.schema,
              value: editor.value
            }, {
              blockIndexMap: editor.blockIndexMap,
              listIndexMap: editor.listIndexMap
            });
          }
          editor.onChange();
        }
      }
    },
    removeAnnotation: (type) => {
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "annotation.remove",
          annotation: {
            name: type.name
          }
        },
        editor
      });
    },
    getSelection: () => {
      let ptRange = null;
      if (editor.selection) {
        const existing = SLATE_TO_PORTABLE_TEXT_RANGE.get(editor.selection);
        if (existing)
          return existing;
        ptRange = slateRangeToSelection({
          schema: editorActor.getSnapshot().context.schema,
          editor,
          range: editor.selection
        }), SLATE_TO_PORTABLE_TEXT_RANGE.set(editor.selection, ptRange);
      }
      return ptRange;
    },
    getValue: () => fromSlateValue(editor.children, types.block.name, KEY_TO_VALUE_ELEMENT.get(editor)),
    isCollapsedSelection: () => !!editor.selection && Range.isCollapsed(editor.selection),
    isExpandedSelection: () => !!editor.selection && Range.isExpanded(editor.selection),
    insertBreak: () => {
      editor.insertBreak(), editor.onChange();
    },
    getFragment: () => fromSlateValue(editor.getFragment(), types.block.name),
    isSelectionsOverlapping: (selectionA, selectionB) => {
      const rangeA = toSlateRange({
        context: {
          schema: editorActor.getSnapshot().context.schema,
          value: editor.value,
          selection: selectionA
        },
        blockIndexMap: editor.blockIndexMap
      }), rangeB = toSlateRange({
        context: {
          schema: editorActor.getSnapshot().context.schema,
          value: editor.value,
          selection: selectionB
        },
        blockIndexMap: editor.blockIndexMap
      });
      return Range.isRange(rangeA) && Range.isRange(rangeB) && Range.includes(rangeA, rangeB);
    }
  };
}
const relayMachine = setup({
  types: {
    context: {},
    events: {},
    emitted: {}
  }
}).createMachine({
  id: "relay",
  context: {
    prevSelection: null,
    lastEventWasFocused: !1
  },
  on: {
    focused: {
      actions: [assign({
        lastEventWasFocused: !0
      }), emit(({
        event
      }) => event)]
    },
    selection: [{
      guard: ({
        context
      }) => context.lastEventWasFocused,
      actions: [assign({
        prevSelection: ({
          event
        }) => event.selection
      }), emit(({
        event
      }) => event), assign({
        lastEventWasFocused: !1
      })]
    }, {
      guard: ({
        context,
        event
      }) => context.prevSelection !== event.selection,
      actions: [assign({
        prevSelection: ({
          event
        }) => event.selection
      }), emit(({
        event
      }) => event), assign({
        lastEventWasFocused: !1
      })]
    }],
    "*": {
      actions: [emit(({
        event
      }) => event), assign({
        lastEventWasFocused: !1
      })]
    }
  }
});
function validateValue(value, types, keyGenerator) {
  let resolution = null, valid = !0;
  const validChildTypes = [types.span.name, ...types.inlineObjects.map((t) => t.name)], validBlockTypes = [types.block.name, ...types.blockObjects.map((t) => t.name)];
  return value === void 0 ? {
    valid: !0,
    resolution: null,
    value
  } : !Array.isArray(value) || value.length === 0 ? {
    valid: !1,
    resolution: {
      patches: [unset([])],
      description: "Editor value must be an array of Portable Text blocks, or undefined.",
      action: "Unset the value",
      item: value,
      i18n: {
        description: "inputs.portable-text.invalid-value.not-an-array.description",
        action: "inputs.portable-text.invalid-value.not-an-array.action"
      }
    },
    value
  } : (value.some((blk, index) => {
    if (!isPlainObject(blk))
      return resolution = {
        patches: [unset([index])],
        description: `Block must be an object, got ${String(blk)}`,
        action: "Unset invalid item",
        item: blk,
        i18n: {
          description: "inputs.portable-text.invalid-value.not-an-object.description",
          action: "inputs.portable-text.invalid-value.not-an-object.action",
          values: {
            index
          }
        }
      }, !0;
    if (!blk._key || typeof blk._key != "string")
      return resolution = {
        patches: [set({
          ...blk,
          _key: keyGenerator()
        }, [index])],
        description: `Block at index ${index} is missing required _key.`,
        action: "Set the block with a random _key value",
        item: blk,
        i18n: {
          description: "inputs.portable-text.invalid-value.missing-key.description",
          action: "inputs.portable-text.invalid-value.missing-key.action",
          values: {
            index
          }
        }
      }, !0;
    if (!blk._type || !validBlockTypes.includes(blk._type)) {
      if (blk._type === "block") {
        const currentBlockTypeName = types.block.name;
        return resolution = {
          patches: [set({
            ...blk,
            _type: currentBlockTypeName
          }, [{
            _key: blk._key
          }])],
          description: `Block with _key '${blk._key}' has invalid type name '${blk._type}'. According to the schema, the block type name is '${currentBlockTypeName}'`,
          action: `Use type '${currentBlockTypeName}'`,
          item: blk,
          i18n: {
            description: "inputs.portable-text.invalid-value.incorrect-block-type.description",
            action: "inputs.portable-text.invalid-value.incorrect-block-type.action",
            values: {
              key: blk._key,
              expectedTypeName: currentBlockTypeName
            }
          }
        }, !0;
      }
      return !blk._type && isTextBlock({
        schema: types
      }, {
        ...blk,
        _type: types.block.name
      }) ? (resolution = {
        patches: [set({
          ...blk,
          _type: types.block.name
        }, [{
          _key: blk._key
        }])],
        description: `Block with _key '${blk._key}' is missing a type name. According to the schema, the block type name is '${types.block.name}'`,
        action: `Use type '${types.block.name}'`,
        item: blk,
        i18n: {
          description: "inputs.portable-text.invalid-value.missing-block-type.description",
          action: "inputs.portable-text.invalid-value.missing-block-type.action",
          values: {
            key: blk._key,
            expectedTypeName: types.block.name
          }
        }
      }, !0) : blk._type ? (resolution = {
        patches: [unset([{
          _key: blk._key
        }])],
        description: `Block with _key '${blk._key}' has invalid _type '${blk._type}'`,
        action: "Remove the block",
        item: blk,
        i18n: {
          description: "inputs.portable-text.invalid-value.disallowed-type.description",
          action: "inputs.portable-text.invalid-value.disallowed-type.action",
          values: {
            key: blk._key,
            typeName: blk._type
          }
        }
      }, !0) : (resolution = {
        patches: [unset([{
          _key: blk._key
        }])],
        description: `Block with _key '${blk._key}' is missing an _type property`,
        action: "Remove the block",
        item: blk,
        i18n: {
          description: "inputs.portable-text.invalid-value.missing-type.description",
          action: "inputs.portable-text.invalid-value.missing-type.action",
          values: {
            key: blk._key
          }
        }
      }, !0);
    }
    if (blk._type === types.block.name) {
      const textBlock = blk;
      if (textBlock.children && !Array.isArray(textBlock.children))
        return resolution = {
          patches: [set({
            children: []
          }, [{
            _key: textBlock._key
          }])],
          description: `Text block with _key '${textBlock._key}' has a invalid required property 'children'.`,
          action: "Reset the children property",
          item: textBlock,
          i18n: {
            description: "inputs.portable-text.invalid-value.missing-or-invalid-children.description",
            action: "inputs.portable-text.invalid-value.missing-or-invalid-children.action",
            values: {
              key: textBlock._key
            }
          }
        }, !0;
      if (textBlock.children === void 0 || Array.isArray(textBlock.children) && textBlock.children.length === 0) {
        const newSpan = {
          _type: types.span.name,
          _key: keyGenerator(),
          text: "",
          marks: []
        };
        return resolution = {
          autoResolve: !0,
          patches: [setIfMissing([], [{
            _key: blk._key
          }, "children"]), insert([newSpan], "after", [{
            _key: blk._key
          }, "children", 0])],
          description: `Children for text block with _key '${blk._key}' is empty.`,
          action: "Insert an empty text",
          item: blk,
          i18n: {
            description: "inputs.portable-text.invalid-value.empty-children.description",
            action: "inputs.portable-text.invalid-value.empty-children.action",
            values: {
              key: blk._key
            }
          }
        }, !0;
      }
      const allUsedMarks = uniq(flatten(textBlock.children.filter((cld) => cld._type === types.span.name).map((cld) => cld.marks || [])));
      if (Array.isArray(blk.markDefs) && blk.markDefs.length > 0) {
        const unusedMarkDefs = uniq(blk.markDefs.map((def) => def._key).filter((key) => !allUsedMarks.includes(key)));
        if (unusedMarkDefs.length > 0)
          return resolution = {
            autoResolve: !0,
            patches: unusedMarkDefs.map((markDefKey) => unset([{
              _key: blk._key
            }, "markDefs", {
              _key: markDefKey
            }])),
            description: `Block contains orphaned data (unused mark definitions): ${unusedMarkDefs.join(", ")}.`,
            action: "Remove unused mark definition item",
            item: blk,
            i18n: {
              description: "inputs.portable-text.invalid-value.orphaned-mark-defs.description",
              action: "inputs.portable-text.invalid-value.orphaned-mark-defs.action",
              values: {
                key: blk._key,
                unusedMarkDefs: unusedMarkDefs.map((m) => m.toString())
              }
            }
          }, !0;
      }
      const orphanedMarks = allUsedMarks.filter((mark) => !types.decorators.map((dec) => dec.name).includes(mark)).filter((mark) => textBlock.markDefs === void 0 || !textBlock.markDefs.find((def) => def._key === mark));
      if (orphanedMarks.length > 0) {
        const spanChildren = textBlock.children.filter((cld) => cld._type === types.span.name && Array.isArray(cld.marks) && cld.marks.some((mark) => orphanedMarks.includes(mark)));
        if (spanChildren) {
          const orphaned = orphanedMarks.join(", ");
          return resolution = {
            autoResolve: !0,
            patches: spanChildren.map((child) => set((child.marks || []).filter((cMrk) => !orphanedMarks.includes(cMrk)), [{
              _key: blk._key
            }, "children", {
              _key: child._key
            }, "marks"])),
            description: `Block with _key '${blk._key}' contains marks (${orphaned}) not supported by the current content model.`,
            action: "Remove invalid marks",
            item: blk,
            i18n: {
              description: "inputs.portable-text.invalid-value.orphaned-marks.description",
              action: "inputs.portable-text.invalid-value.orphaned-marks.action",
              values: {
                key: blk._key,
                orphanedMarks: orphanedMarks.map((m) => m.toString())
              }
            }
          }, !0;
        }
      }
      textBlock.children.some((child, cIndex) => {
        if (!isPlainObject(child))
          return resolution = {
            patches: [unset([{
              _key: blk._key
            }, "children", cIndex])],
            description: `Child at index '${cIndex}' in block with key '${blk._key}' is not an object.`,
            action: "Remove the item",
            item: blk,
            i18n: {
              description: "inputs.portable-text.invalid-value.non-object-child.description",
              action: "inputs.portable-text.invalid-value.non-object-child.action",
              values: {
                key: blk._key,
                index: cIndex
              }
            }
          }, !0;
        if (!child._key || typeof child._key != "string") {
          const newChild = {
            ...child,
            _key: keyGenerator()
          };
          return resolution = {
            autoResolve: !0,
            patches: [set(newChild, [{
              _key: blk._key
            }, "children", cIndex])],
            description: `Child at index ${cIndex} is missing required _key in block with _key ${blk._key}.`,
            action: "Set a new random _key on the object",
            item: blk,
            i18n: {
              description: "inputs.portable-text.invalid-value.missing-child-key.description",
              action: "inputs.portable-text.invalid-value.missing-child-key.action",
              values: {
                key: blk._key,
                index: cIndex
              }
            }
          }, !0;
        }
        return child._type ? validChildTypes.includes(child._type) ? child._type === types.span.name && typeof child.text != "string" ? (resolution = {
          patches: [set({
            ...child,
            text: ""
          }, [{
            _key: blk._key
          }, "children", {
            _key: child._key
          }])],
          description: `Child with _key '${child._key}' in block with key '${blk._key}' has missing or invalid text property!`,
          action: "Write an empty text property to the object",
          item: blk,
          i18n: {
            description: "inputs.portable-text.invalid-value.invalid-span-text.description",
            action: "inputs.portable-text.invalid-value.invalid-span-text.action",
            values: {
              key: blk._key,
              childKey: child._key
            }
          }
        }, !0) : !1 : (resolution = {
          patches: [unset([{
            _key: blk._key
          }, "children", {
            _key: child._key
          }])],
          description: `Child with _key '${child._key}' in block with key '${blk._key}' has invalid '_type' property (${child._type}).`,
          action: "Remove the object",
          item: blk,
          i18n: {
            description: "inputs.portable-text.invalid-value.disallowed-child-type.description",
            action: "inputs.portable-text.invalid-value.disallowed-child-type.action",
            values: {
              key: blk._key,
              childKey: child._key,
              childType: child._type
            }
          }
        }, !0) : (resolution = {
          patches: [unset([{
            _key: blk._key
          }, "children", {
            _key: child._key
          }])],
          description: `Child with _key '${child._key}' in block with key '${blk._key}' is missing '_type' property.`,
          action: "Remove the object",
          item: blk,
          i18n: {
            description: "inputs.portable-text.invalid-value.missing-child-type.description",
            action: "inputs.portable-text.invalid-value.missing-child-type.action",
            values: {
              key: blk._key,
              childKey: child._key
            }
          }
        }, !0);
      }) && (valid = !1);
    }
    return !1;
  }) && (valid = !1), {
    valid,
    resolution,
    value
  });
}
const debug$4 = debugWithName("sync machine"), syncValueCallback = ({
  sendBack,
  input
}) => {
  updateValue({
    context: input.context,
    sendBack,
    slateEditor: input.slateEditor,
    value: input.value,
    streamBlocks: input.streamBlocks
  });
}, syncValueLogic = fromCallback(syncValueCallback), syncMachine = setup({
  types: {
    context: {},
    input: {},
    events: {},
    emitted: {}
  },
  actions: {
    "assign initial value synced": assign({
      initialValueSynced: !0
    }),
    "assign readOnly": assign({
      readOnly: ({
        event
      }) => (assertEvent(event, "update readOnly"), event.readOnly)
    }),
    "assign pending value": assign({
      pendingValue: ({
        event
      }) => (assertEvent(event, "update value"), event.value)
    }),
    "clear pending value": assign({
      pendingValue: void 0
    }),
    "assign previous value": assign({
      previousValue: ({
        event
      }) => (assertEvent(event, "done syncing"), event.value)
    }),
    "emit done syncing value": emit({
      type: "done syncing value"
    }),
    "emit syncing value": emit({
      type: "syncing value"
    })
  },
  guards: {
    "initial value synced": ({
      context
    }) => context.initialValueSynced,
    "is busy": ({
      context
    }) => {
      const isProcessingLocalChanges = context.isProcessingLocalChanges, isChanging = isChangingRemotely(context.slateEditor) ?? !1, isBusy = isProcessingLocalChanges || isChanging;
      return debug$4("isBusy", {
        isBusy,
        isProcessingLocalChanges,
        isChanging
      }), isBusy;
    },
    "is empty value": ({
      event
    }) => event.type === "update value" && event.value === void 0,
    "is empty array": ({
      event
    }) => event.type === "update value" && Array.isArray(event.value) && event.value.length === 0,
    "is new value": ({
      context,
      event
    }) => event.type === "update value" && context.previousValue !== event.value,
    "value changed while syncing": ({
      context,
      event
    }) => (assertEvent(event, "done syncing"), context.pendingValue !== event.value),
    "pending value equals previous value": ({
      context
    }) => isEqual(context.pendingValue, context.previousValue)
  },
  actors: {
    "sync value": syncValueLogic
  }
}).createMachine({
  id: "sync",
  context: ({
    input
  }) => ({
    initialValue: input.initialValue,
    initialValueSynced: !1,
    isProcessingLocalChanges: !1,
    keyGenerator: input.keyGenerator,
    schema: input.schema,
    readOnly: input.readOnly,
    slateEditor: input.slateEditor,
    pendingValue: void 0,
    previousValue: void 0
  }),
  entry: [raise$1(({
    context
  }) => ({
    type: "update value",
    value: context.initialValue
  }))],
  on: {
    "has pending mutations": {
      actions: assign({
        isProcessingLocalChanges: !0
      })
    },
    mutation: {
      actions: assign({
        isProcessingLocalChanges: !1
      })
    },
    "update readOnly": {
      actions: ["assign readOnly"]
    }
  },
  initial: "idle",
  states: {
    idle: {
      entry: [() => {
        debug$4("entry: syncing->idle");
      }],
      exit: [() => {
        debug$4("exit: syncing->idle");
      }],
      on: {
        "update value": [{
          guard: and(["is empty value", not("initial value synced")]),
          actions: ["assign initial value synced", "emit done syncing value"]
        }, {
          guard: and(["is empty array", not("initial value synced")]),
          actions: ["assign initial value synced", emit({
            type: "value changed",
            value: []
          }), "emit done syncing value"]
        }, {
          guard: and(["is busy", "is new value"]),
          target: "busy",
          actions: ["assign pending value"]
        }, {
          guard: "is new value",
          target: "syncing",
          actions: ["assign pending value"]
        }, {
          guard: not("initial value synced"),
          actions: [() => {
            debug$4("no new value \u2013 setting initial value as synced");
          }, "assign initial value synced", "emit done syncing value"]
        }, {
          actions: [() => {
            debug$4("no new value and initial value already synced");
          }]
        }]
      }
    },
    busy: {
      entry: [() => {
        debug$4("entry: syncing->busy");
      }],
      exit: [() => {
        debug$4("exit: syncing->busy");
      }],
      after: {
        1e3: [{
          guard: "is busy",
          target: ".",
          reenter: !0,
          actions: [() => {
            debug$4("reenter: syncing->busy");
          }]
        }, {
          target: "syncing"
        }]
      },
      on: {
        "update value": [{
          guard: "is new value",
          actions: ["assign pending value"]
        }]
      }
    },
    syncing: {
      entry: [() => {
        debug$4("entry: syncing->syncing");
      }, "emit syncing value"],
      exit: [() => {
        debug$4("exit: syncing->syncing");
      }, "emit done syncing value"],
      invoke: {
        src: "sync value",
        id: "sync value",
        input: ({
          context
        }) => ({
          context: {
            keyGenerator: context.keyGenerator,
            previousValue: context.previousValue,
            readOnly: context.readOnly,
            schema: context.schema
          },
          slateEditor: context.slateEditor,
          streamBlocks: !context.initialValueSynced,
          value: context.pendingValue
        })
      },
      on: {
        "update value": {
          guard: "is new value",
          actions: ["assign pending value"]
        },
        patch: {
          actions: [emit(({
            event
          }) => event)]
        },
        "invalid value": {
          actions: [emit(({
            event
          }) => event)]
        },
        "value changed": {
          actions: [emit(({
            event
          }) => event)]
        },
        "done syncing": [{
          guard: "value changed while syncing",
          actions: ["assign previous value", "assign initial value synced"],
          target: "syncing",
          reenter: !0
        }, {
          target: "idle",
          actions: ["clear pending value", "assign previous value", "assign initial value synced"]
        }]
      }
    }
  }
});
async function updateValue({
  context,
  sendBack,
  slateEditor,
  streamBlocks,
  value
}) {
  let doneSyncing = !1, isChanged = !1, isValid = !0;
  const hadSelection = !!slateEditor.selection;
  if ((!value || value.length === 0) && (debug$4("Value is empty"), Editor.withoutNormalizing(slateEditor, () => {
    withoutSaving(slateEditor, () => {
      withRemoteChanges(slateEditor, () => {
        withoutPatching(slateEditor, () => {
          if (doneSyncing)
            return;
          hadSelection && Transforms.deselect(slateEditor);
          const childrenLength = slateEditor.children.length;
          slateEditor.children.forEach((_, index) => {
            Transforms.removeNodes(slateEditor, {
              at: [childrenLength - 1 - index]
            });
          }), Transforms.insertNodes(slateEditor, slateEditor.pteCreateTextBlock({
            decorators: []
          }), {
            at: [0]
          }), hadSelection && Transforms.select(slateEditor, [0, 0]);
        });
      });
    });
  }), isChanged = !0), value && value.length > 0) {
    const slateValueFromProps = toSlateValue(value, {
      schemaTypes: context.schema
    });
    streamBlocks ? await new Promise((resolve) => {
      Editor.withoutNormalizing(slateEditor, () => {
        withRemoteChanges(slateEditor, () => {
          withoutPatching(slateEditor, () => {
            if (doneSyncing) {
              resolve();
              return;
            }
            isChanged = removeExtraBlocks({
              slateEditor,
              slateValueFromProps
            }), (async () => {
              for await (const [currentBlock, currentBlockIndex] of getStreamedBlocks({
                slateValue: slateValueFromProps
              })) {
                const {
                  blockChanged,
                  blockValid
                } = syncBlock({
                  context,
                  sendBack,
                  block: currentBlock,
                  index: currentBlockIndex,
                  slateEditor,
                  value
                });
                isChanged = blockChanged || isChanged, isValid = isValid && blockValid;
              }
              resolve();
            })();
          });
        });
      });
    }) : Editor.withoutNormalizing(slateEditor, () => {
      withRemoteChanges(slateEditor, () => {
        withoutPatching(slateEditor, () => {
          if (doneSyncing)
            return;
          isChanged = removeExtraBlocks({
            slateEditor,
            slateValueFromProps
          });
          let index = 0;
          for (const currentBlock of slateValueFromProps) {
            const {
              blockChanged,
              blockValid
            } = syncBlock({
              context,
              sendBack,
              block: currentBlock,
              index,
              slateEditor,
              value
            });
            isChanged = blockChanged || isChanged, isValid = isValid && blockValid, index++;
          }
        });
      });
    });
  }
  if (!isValid) {
    debug$4("Invalid value, returning"), doneSyncing = !0, sendBack({
      type: "done syncing",
      value
    });
    return;
  }
  if (isChanged) {
    debug$4("Server value changed, syncing editor");
    try {
      slateEditor.onChange();
    } catch (err) {
      console.error(err), sendBack({
        type: "invalid value",
        resolution: null,
        value
      }), doneSyncing = !0, sendBack({
        type: "done syncing",
        value
      });
      return;
    }
    hadSelection && !slateEditor.selection && (Transforms.select(slateEditor, {
      anchor: {
        path: [0, 0],
        offset: 0
      },
      focus: {
        path: [0, 0],
        offset: 0
      }
    }), slateEditor.onChange()), sendBack({
      type: "value changed",
      value
    });
  } else
    debug$4("Server value and editor value is equal, no need to sync.");
  doneSyncing = !0, sendBack({
    type: "done syncing",
    value
  });
}
function removeExtraBlocks({
  slateEditor,
  slateValueFromProps
}) {
  let isChanged = !1;
  const childrenLength = slateEditor.children.length;
  if (slateValueFromProps.length < childrenLength) {
    for (let i = childrenLength - 1; i > slateValueFromProps.length - 1; i--)
      Transforms.removeNodes(slateEditor, {
        at: [i]
      });
    isChanged = !0;
  }
  return isChanged;
}
async function* getStreamedBlocks({
  slateValue
}) {
  let index = 0;
  for await (const block of slateValue)
    index % 10 === 0 && await new Promise((resolve) => setTimeout(resolve, 0)), yield [block, index], index++;
}
function syncBlock({
  context,
  sendBack,
  block,
  index,
  slateEditor,
  value
}) {
  let blockChanged = !1, blockValid = !0;
  const currentBlock = block, currentBlockIndex = index, oldBlock = slateEditor.children[currentBlockIndex], hasChanges = oldBlock && !isEqual(currentBlock, oldBlock);
  return Editor.withoutNormalizing(slateEditor, () => {
    withRemoteChanges(slateEditor, () => {
      withoutPatching(slateEditor, () => {
        if (hasChanges && blockValid) {
          const validationValue = [value[currentBlockIndex]], validation = validateValue(validationValue, context.schema, context.keyGenerator);
          !validation.valid && validation.resolution?.autoResolve && validation.resolution?.patches.length > 0 && !context.readOnly && context.previousValue && context.previousValue !== value && (console.warn(`${validation.resolution.action} for block with _key '${validationValue[0]._key}'. ${validation.resolution?.description}`), validation.resolution.patches.forEach((patch) => {
            sendBack({
              type: "patch",
              patch
            });
          })), validation.valid || validation.resolution?.autoResolve ? (oldBlock._key === currentBlock._key ? (debug$4.enabled && debug$4("Updating block", oldBlock, currentBlock), _updateBlock(slateEditor, currentBlock, oldBlock, currentBlockIndex)) : (debug$4.enabled && debug$4("Replacing block", oldBlock, currentBlock), _replaceBlock(slateEditor, currentBlock, currentBlockIndex)), blockChanged = !0) : (sendBack({
            type: "invalid value",
            resolution: validation.resolution,
            value
          }), blockValid = !1);
        }
        if (!oldBlock && blockValid) {
          const validationValue = [value[currentBlockIndex]], validation = validateValue(validationValue, context.schema, context.keyGenerator);
          debug$4.enabled && debug$4("Validating and inserting new block in the end of the value", currentBlock), validation.valid || validation.resolution?.autoResolve ? Transforms.insertNodes(slateEditor, currentBlock, {
            at: [currentBlockIndex]
          }) : (debug$4("Invalid", validation), sendBack({
            type: "invalid value",
            resolution: validation.resolution,
            value
          }), blockValid = !1);
        }
      });
    });
  }), {
    blockChanged,
    blockValid
  };
}
function _replaceBlock(slateEditor, currentBlock, currentBlockIndex) {
  const currentSelection = slateEditor.selection, selectionFocusOnBlock = currentSelection && currentSelection.focus.path[0] === currentBlockIndex;
  selectionFocusOnBlock && Transforms.deselect(slateEditor), Transforms.removeNodes(slateEditor, {
    at: [currentBlockIndex]
  }), Transforms.insertNodes(slateEditor, currentBlock, {
    at: [currentBlockIndex]
  }), slateEditor.onChange(), selectionFocusOnBlock && Transforms.select(slateEditor, currentSelection);
}
function _updateBlock(slateEditor, currentBlock, oldBlock, currentBlockIndex) {
  if (Transforms.setNodes(slateEditor, currentBlock, {
    at: [currentBlockIndex]
  }), slateEditor.isTextBlock(currentBlock) && slateEditor.isTextBlock(oldBlock)) {
    const oldBlockChildrenLength = oldBlock.children.length;
    currentBlock.children.length < oldBlockChildrenLength && Array.from(Array(oldBlockChildrenLength - currentBlock.children.length)).forEach((_, index) => {
      const childIndex = oldBlockChildrenLength - 1 - index;
      childIndex > 0 && (debug$4("Removing child"), Transforms.removeNodes(slateEditor, {
        at: [currentBlockIndex, childIndex]
      }));
    }), currentBlock.children.forEach((currentBlockChild, currentBlockChildIndex) => {
      const oldBlockChild = oldBlock.children[currentBlockChildIndex], isChildChanged = !isEqual(currentBlockChild, oldBlockChild), isTextChanged = !isEqual(currentBlockChild.text, oldBlockChild?.text), path = [currentBlockIndex, currentBlockChildIndex];
      if (isChildChanged)
        if (currentBlockChild._key === oldBlockChild?._key) {
          debug$4("Updating changed child", currentBlockChild, oldBlockChild), Transforms.setNodes(slateEditor, currentBlockChild, {
            at: path
          });
          const isSpanNode2 = Text.isText(currentBlockChild) && currentBlockChild._type === "span" && Text.isText(oldBlockChild) && oldBlockChild._type === "span";
          isSpanNode2 && isTextChanged ? (oldBlockChild.text.length > 0 && deleteText(slateEditor, {
            at: {
              focus: {
                path,
                offset: 0
              },
              anchor: {
                path,
                offset: oldBlockChild.text.length
              }
            }
          }), Transforms.insertText(slateEditor, currentBlockChild.text, {
            at: path
          }), slateEditor.onChange()) : isSpanNode2 || (debug$4("Updating changed inline object child", currentBlockChild), Transforms.setNodes(slateEditor, {
            _key: VOID_CHILD_KEY
          }, {
            at: [...path, 0],
            voids: !0
          }));
        } else oldBlockChild ? (debug$4("Replacing child", currentBlockChild), Transforms.removeNodes(slateEditor, {
          at: [currentBlockIndex, currentBlockChildIndex]
        }), Transforms.insertNodes(slateEditor, currentBlockChild, {
          at: [currentBlockIndex, currentBlockChildIndex]
        }), slateEditor.onChange()) : oldBlockChild || (debug$4("Inserting new child", currentBlockChild), Transforms.insertNodes(slateEditor, currentBlockChild, {
          at: [currentBlockIndex, currentBlockChildIndex]
        }), slateEditor.onChange());
    });
  }
}
const debug$3 = debugWithName("setup");
function createInternalEditor(config) {
  debug$3("Creating new Editor instance");
  const subscriptions = [], editorActor = createActor(editorMachine, {
    input: editorConfigToMachineInput(config)
  }), relayActor = createActor(relayMachine), slateEditor = createSlateEditor({
    editorActor,
    relayActor,
    subscriptions
  }), editable = createEditableAPI(slateEditor.instance, editorActor), {
    mutationActor,
    syncActor
  } = createActors({
    editorActor,
    relayActor,
    slateEditor: slateEditor.instance,
    subscriptions
  }), editor = {
    dom: createEditorDom((event) => editorActor.send(event), slateEditor.instance),
    getSnapshot: () => getEditorSnapshot({
      editorActorSnapshot: editorActor.getSnapshot(),
      slateEditorInstance: slateEditor.instance
    }),
    registerBehavior: (behaviorConfig) => {
      const priority = createEditorPriority({
        name: "custom",
        reference: {
          priority: corePriority,
          importance: "higher"
        }
      }), behaviorConfigWithPriority = {
        ...behaviorConfig,
        priority
      };
      return editorActor.send({
        type: "add behavior",
        behaviorConfig: behaviorConfigWithPriority
      }), () => {
        editorActor.send({
          type: "remove behavior",
          behaviorConfig: behaviorConfigWithPriority
        });
      };
    },
    send: (event) => {
      switch (event.type) {
        case "update value":
          syncActor.send(event);
          break;
        case "update readOnly":
        case "patches":
        case "update maxBlocks":
          editorActor.send(event);
          break;
        case "blur":
          editorActor.send({
            type: "blur",
            editor: slateEditor.instance
          });
          break;
        case "focus":
          editorActor.send({
            type: "focus",
            editor: slateEditor.instance
          });
          break;
        case "insert.block object":
          editorActor.send({
            type: "behavior event",
            behaviorEvent: {
              type: "insert.block",
              block: {
                _type: event.blockObject.name,
                ...event.blockObject.value ?? {}
              },
              placement: event.placement
            },
            editor: slateEditor.instance
          });
          break;
        default:
          editorActor.send({
            type: "behavior event",
            behaviorEvent: event,
            editor: slateEditor.instance
          });
      }
    },
    on: (event, listener) => relayActor.on(event, (event2) => {
      switch (event2.type) {
        case "blurred":
        case "done loading":
        case "editable":
        case "focused":
        case "invalid value":
        case "loading":
        case "mutation":
        case "patch":
        case "read only":
        case "ready":
        case "selection":
        case "value changed":
          listener(event2);
          break;
      }
    }),
    _internal: {
      editable,
      editorActor,
      slateEditor
    }
  };
  return {
    actors: {
      editorActor,
      mutationActor,
      relayActor,
      syncActor
    },
    editor,
    subscriptions
  };
}
function editorConfigToMachineInput(config) {
  const {
    legacySchema,
    schema
  } = compileSchemasFromEditorConfig(config);
  return {
    converters: createCoreConverters(legacySchema),
    getLegacySchema: () => legacySchema,
    keyGenerator: config.keyGenerator ?? defaultKeyGenerator,
    maxBlocks: config.maxBlocks,
    readOnly: config.readOnly,
    schema,
    initialValue: config.initialValue
  };
}
function compileSchemasFromEditorConfig(config) {
  const legacySchema = config.schemaDefinition ? compileSchemaDefinitionToPortableTextMemberSchemaTypes(config.schemaDefinition) : createPortableTextMemberSchemaTypes(config.schema.hasOwnProperty("jsonType") ? config.schema : compileType(config.schema)), schema = config.schemaDefinition ? compileSchema(config.schemaDefinition) : portableTextMemberSchemaTypesToSchema(legacySchema);
  return {
    legacySchema,
    schema
  };
}
function createActors(config) {
  debug$3("Creating new Actors");
  const mutationActor = createActor(mutationMachine, {
    input: {
      readOnly: config.editorActor.getSnapshot().matches({
        "edit mode": "read only"
      }),
      schema: config.editorActor.getSnapshot().context.schema,
      slateEditor: config.slateEditor
    }
  }), syncActor = createActor(syncMachine, {
    input: {
      initialValue: config.editorActor.getSnapshot().context.initialValue,
      keyGenerator: config.editorActor.getSnapshot().context.keyGenerator,
      readOnly: config.editorActor.getSnapshot().matches({
        "edit mode": "read only"
      }),
      schema: config.editorActor.getSnapshot().context.schema,
      slateEditor: config.slateEditor
    }
  });
  return config.subscriptions.push(() => {
    const subscription = mutationActor.on("*", (event) => {
      event.type === "has pending mutations" && syncActor.send({
        type: "has pending mutations"
      }), event.type === "mutation" && (syncActor.send({
        type: "mutation"
      }), config.editorActor.send({
        type: "mutation",
        patches: event.patches,
        snapshot: event.snapshot,
        value: event.snapshot
      })), event.type === "patch" && config.relayActor.send(event);
    });
    return () => {
      subscription.unsubscribe();
    };
  }), config.subscriptions.push(() => {
    const subscription = syncActor.on("*", (event) => {
      switch (event.type) {
        case "invalid value":
          config.relayActor.send(event);
          break;
        case "value changed":
          config.relayActor.send(event);
          break;
        case "patch":
          config.editorActor.send({
            ...event,
            type: "internal.patch",
            value: fromSlateValue(config.slateEditor.children, config.editorActor.getSnapshot().context.schema.block.name, KEY_TO_VALUE_ELEMENT.get(config.slateEditor))
          });
          break;
        default:
          config.editorActor.send(event);
      }
    });
    return () => {
      subscription.unsubscribe();
    };
  }), config.subscriptions.push(() => {
    const subscription = config.editorActor.subscribe((snapshot) => {
      snapshot.matches({
        "edit mode": "read only"
      }) ? (mutationActor.send({
        type: "update readOnly",
        readOnly: !0
      }), syncActor.send({
        type: "update readOnly",
        readOnly: !0
      })) : (mutationActor.send({
        type: "update readOnly",
        readOnly: !1
      }), syncActor.send({
        type: "update readOnly",
        readOnly: !1
      }));
    });
    return () => {
      subscription.unsubscribe();
    };
  }), config.subscriptions.push(() => {
    const subscription = config.editorActor.on("*", (event) => {
      switch (event.type) {
        case "editable":
        case "mutation":
        case "ready":
        case "read only":
        case "selection":
          config.relayActor.send(event);
          break;
        case "internal.patch":
          mutationActor.send({
            ...event,
            type: "patch"
          });
          break;
      }
    });
    return () => {
      subscription.unsubscribe();
    };
  }), {
    mutationActor,
    syncActor
  };
}
function eventToChange(event) {
  switch (event.type) {
    case "blurred":
      return {
        type: "blur",
        event: event.event
      };
    case "patch":
      return event;
    case "loading":
      return {
        type: "loading",
        isLoading: !0
      };
    case "done loading":
      return {
        type: "loading",
        isLoading: !1
      };
    case "focused":
      return {
        type: "focus",
        event: event.event
      };
    case "value changed":
      return {
        type: "value",
        value: event.value
      };
    case "invalid value":
      return {
        type: "invalidValue",
        resolution: event.resolution,
        value: event.value
      };
    case "mutation":
      return event;
    case "ready":
      return event;
    case "selection":
      return event;
    case "unset":
      return event;
  }
}
const RelayActorContext = createContext({}), debug$2 = debugWithName("component:PortableTextEditor");
class PortableTextEditor extends Component {
  static displayName = "PortableTextEditor";
  /**
   * An observable of all the editor changes.
   */
  change$ = new Subject();
  /**
   * A lookup table for all the relevant schema types for this portable text type.
   */
  /**
   * The editor instance
   */
  /*
   * The editor API (currently implemented with Slate).
   */
  subscriptions = [];
  unsubscribers = [];
  constructor(props) {
    if (super(props), props.editor)
      this.editor = props.editor, this.schemaTypes = this.editor._internal.editorActor.getSnapshot().context.getLegacySchema();
    else {
      const {
        actors,
        editor,
        subscriptions
      } = createInternalEditor({
        initialValue: props.value,
        keyGenerator: props.keyGenerator,
        maxBlocks: props.maxBlocks === void 0 ? void 0 : Number.parseInt(props.maxBlocks.toString(), 10),
        readOnly: props.readOnly,
        schema: props.schemaType
      });
      this.subscriptions = subscriptions, this.actors = actors, this.editor = editor, this.schemaTypes = actors.editorActor.getSnapshot().context.getLegacySchema();
    }
    this.editable = this.editor._internal.editable;
  }
  componentDidMount() {
    if (!this.actors)
      return;
    for (const subscription of this.subscriptions)
      this.unsubscribers.push(subscription());
    const relayActorSubscription = this.actors.relayActor.on("*", (event) => {
      const change = eventToChange(event);
      change && (this.props.editor || this.props.onChange(change), this.change$.next(change));
    });
    this.unsubscribers.push(relayActorSubscription.unsubscribe), this.actors.editorActor.start(), this.actors.mutationActor.start(), this.actors.relayActor.start(), this.actors.syncActor.start();
  }
  componentDidUpdate(prevProps) {
    !this.props.editor && !prevProps.editor && this.props.schemaType !== prevProps.schemaType && console.warn("Updating schema type is no longer supported"), !this.props.editor && !prevProps.editor && (this.props.readOnly !== prevProps.readOnly && this.editor._internal.editorActor.send({
      type: "update readOnly",
      readOnly: this.props.readOnly ?? !1
    }), this.props.maxBlocks !== prevProps.maxBlocks && this.editor._internal.editorActor.send({
      type: "update maxBlocks",
      maxBlocks: this.props.maxBlocks === void 0 ? void 0 : Number.parseInt(this.props.maxBlocks.toString(), 10)
    }), this.props.value !== prevProps.value && this.editor.send({
      type: "update value",
      value: this.props.value
    }), this.props.editorRef !== prevProps.editorRef && this.props.editorRef && (this.props.editorRef.current = this));
  }
  componentWillUnmount() {
    for (const unsubscribe of this.unsubscribers)
      unsubscribe();
    this.actors && (stopActor(this.actors.editorActor), stopActor(this.actors.mutationActor), stopActor(this.actors.relayActor), stopActor(this.actors.syncActor));
  }
  setEditable = (editable) => {
    this.editor._internal.editable = {
      ...this.editor._internal.editable,
      ...editable
    };
  };
  render() {
    const legacyPatches = this.props.editor ? void 0 : this.props.incomingPatches$ ?? this.props.patches$;
    return /* @__PURE__ */ jsxs(Fragment, { children: [
      legacyPatches ? /* @__PURE__ */ jsx(RoutePatchesObservableToEditorActor, { editorActor: this.editor._internal.editorActor, patches$: legacyPatches }) : null,
      /* @__PURE__ */ jsx(EditorActorContext.Provider, { value: this.editor._internal.editorActor, children: /* @__PURE__ */ jsx(RelayActorContext.Provider, { value: this.actors.relayActor, children: /* @__PURE__ */ jsx(Slate, { editor: this.editor._internal.slateEditor.instance, initialValue: this.editor._internal.slateEditor.initialValue, children: /* @__PURE__ */ jsx(PortableTextEditorContext.Provider, { value: this, children: this.props.children }) }) }) })
    ] });
  }
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isActive = useEditorSelector(editor, selectors.getActiveAnnotations)
   * ```
   */
  static activeAnnotations = (editor) => editor && editor.editable ? editor.editable.activeAnnotations() : [];
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isActive = useEditorSelector(editor, selectors.isActiveAnnotation(...))
   * ```
   */
  static isAnnotationActive = (editor, annotationType) => editor && editor.editable ? editor.editable.isAnnotationActive(annotationType) : !1;
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'annotation.add',
   *  annotation: {
   *    name: '...',
   *    value: {...},
   *  }
   * })
   * ```
   */
  static addAnnotation = (editor, type, value) => editor.editable?.addAnnotation(type, value);
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'blur',
   * })
   * ```
   */
  static blur = (editor) => {
    debug$2("Host blurred"), editor.editable?.blur();
  };
  static delete = (editor, selection, options) => editor.editable?.delete(selection, options);
  static findDOMNode = (editor, element) => editor.editable?.findDOMNode(element);
  static findByPath = (editor, path) => editor.editable?.findByPath(path) || [];
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'focus',
   * })
   * ```
   */
  static focus = (editor) => {
    debug$2("Host requesting focus"), editor.editable?.focus();
  };
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const focusBlock = useEditorSelector(editor, selectors.getFocusBlock)
   * ```
   */
  static focusBlock = (editor) => editor.editable?.focusBlock();
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const focusChild = useEditorSelector(editor, selectors.getFocusChild)
   * ```
   */
  static focusChild = (editor) => editor.editable?.focusChild();
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const selection = useEditorSelector(editor, selectors.getSelection)
   * ```
   */
  static getSelection = (editor) => editor.editable ? editor.editable.getSelection() : null;
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const value = useEditorSelector(editor, selectors.getValue)
   * ```
   */
  static getValue = (editor) => editor.editable?.getValue();
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isActive = useEditorSelector(editor, selectors.isActiveStyle(...))
   * ```
   */
  static hasBlockStyle = (editor, blockStyle) => editor.editable?.hasBlockStyle(blockStyle);
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isActive = useEditorSelector(editor, selectors.isActiveListItem(...))
   * ```
   */
  static hasListStyle = (editor, listStyle) => editor.editable?.hasListStyle(listStyle);
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isSelectionCollapsed = useEditorSelector(editor, selectors.isSelectionCollapsed)
   * ```
   */
  static isCollapsedSelection = (editor) => editor.editable?.isCollapsedSelection();
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isSelectionExpanded = useEditorSelector(editor, selectors.isSelectionExpanded)
   * ```
   */
  static isExpandedSelection = (editor) => editor.editable?.isExpandedSelection();
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isActive = useEditorSelector(editor, selectors.isActiveDecorator(...))
   * ```
   */
  static isMarkActive = (editor, mark) => editor.editable?.isMarkActive(mark);
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'insert.span',
   *  text: '...',
   *  annotations: [{name: '...', value: {...}}],
   *  decorators: ['...'],
   * })
   * editor.send({
   *  type: 'insert.inline object',
   *  inlineObject: {
   *    name: '...',
   *    value: {...},
   *  },
   * })
   * ```
   */
  static insertChild = (editor, type, value) => (debug$2("Host inserting child"), editor.editable?.insertChild(type, value));
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'insert.block object',
   *  blockObject: {
   *    name: '...',
   *    value: {...},
   *  },
   *  placement: 'auto' | 'after' | 'before',
   * })
   * ```
   */
  static insertBlock = (editor, type, value) => editor.editable?.insertBlock(type, value);
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'insert.break',
   * })
   * ```
   */
  static insertBreak = (editor) => editor.editable?.insertBreak();
  static isVoid = (editor, element) => editor.editable?.isVoid(element);
  static isObjectPath = (_editor, path) => {
    if (!path || !Array.isArray(path)) return !1;
    const isChildObjectEditPath = path.length > 3 && path[1] === "children";
    return path.length > 1 && path[1] !== "children" || isChildObjectEditPath;
  };
  static marks = (editor) => editor.editable?.marks();
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'select',
   *  selection: {...},
   * })
   * ```
   */
  static select = (editor, selection) => {
    debug$2("Host setting selection", selection), editor.editable?.select(selection);
  };
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'annotation.remove',
   *  annotation: {
   *    name: '...',
   *  },
   * })
   * ```
   */
  static removeAnnotation = (editor, type) => editor.editable?.removeAnnotation(type);
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'style.toggle',
   *  style: '...',
   * })
   * ```
   */
  static toggleBlockStyle = (editor, blockStyle) => (debug$2("Host is toggling block style"), editor.editable?.toggleBlockStyle(blockStyle));
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'list item.toggle',
   *  listItem: '...',
   * })
   * ```
   */
  static toggleList = (editor, listStyle) => editor.editable?.toggleList(listStyle);
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *  type: 'decorator.toggle',
   *  decorator: '...',
   * })
   * ```
   */
  static toggleMark = (editor, mark) => {
    debug$2("Host toggling mark", mark), editor.editable?.toggleMark(mark);
  };
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const selectedSlice = useEditorSelector(editor, selectors.getSelectedSlice)
   * ```
   */
  static getFragment = (editor) => editor.editable?.getFragment();
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *   type: 'history.undo',
   * })
   * ```
   */
  static undo = (editor) => {
    debug$2("Host undoing"), editor.editable?.undo();
  };
  /**
   * @deprecated
   * Use `editor.send(...)` instead
   *
   * ```
   * const editor = useEditor()
   * editor.send({
   *   type: 'history.redo',
   * })
   * ```
   */
  static redo = (editor) => {
    debug$2("Host redoing"), editor.editable?.redo();
  };
  /**
   * @deprecated
   * Use built-in selectors or write your own: https://www.portabletext.org/reference/selectors/
   *
   * ```
   * import * as selectors from '@portabletext/editor/selectors'
   * const editor = useEditor()
   * const isOverlapping = useEditorSelector(editor, selectors.isOverlappingSelection(selectionB))
   * ```
   */
  static isSelectionsOverlapping = (editor, selectionA, selectionB) => editor.editable?.isSelectionsOverlapping(selectionA, selectionB);
}
function RoutePatchesObservableToEditorActor(props) {
  const $ = c(4);
  let t0, t1;
  return $[0] !== props.editorActor || $[1] !== props.patches$ ? (t0 = () => {
    const subscription = props.patches$.subscribe((payload) => {
      props.editorActor.send({
        type: "patches",
        ...payload
      });
    });
    return () => {
      subscription.unsubscribe();
    };
  }, t1 = [props.editorActor, props.patches$], $[0] = props.editorActor, $[1] = props.patches$, $[2] = t0, $[3] = t1) : (t0 = $[2], t1 = $[3]), useEffect(t0, t1), null;
}
function RenderSpan(props) {
  const slateEditor = useSlateStatic(), editorActor = useContext(EditorActorContext), legacySchema = useSelector(editorActor, (s) => s.context.getLegacySchema()), spanRef = useRef(null), portableTextEditor = usePortableTextEditor(), blockSelected = useSelected(), [focused, setFocused] = useState(!1), [selected, setSelected] = useState(!1), parent = props.children.props.parent, block = parent && slateEditor.isTextBlock(parent) ? parent : void 0, path = useMemo(() => block ? [{
    _key: block._key
  }, "children", {
    _key: props.leaf._key
  }] : void 0, [block, props.leaf._key]), decoratorSchemaTypes = editorActor.getSnapshot().context.schema.decorators.map((decorator) => decorator.name), decorators = uniq((props.leaf.marks ?? []).filter((mark) => decoratorSchemaTypes.includes(mark))), annotationMarkDefs = (props.leaf.marks ?? []).flatMap((mark_0) => {
    if (decoratorSchemaTypes.includes(mark_0))
      return [];
    const markDef_0 = block?.markDefs?.find((markDef) => markDef._key === mark_0);
    return markDef_0 ? [markDef_0] : [];
  }), shouldTrackSelectionAndFocus = annotationMarkDefs.length > 0 && blockSelected;
  useEffect(() => {
    if (!shouldTrackSelectionAndFocus) {
      setFocused(!1);
      return;
    }
    const sel = PortableTextEditor.getSelection(portableTextEditor);
    sel && isEqual(sel.focus.path, path) && PortableTextEditor.isCollapsedSelection(portableTextEditor) && startTransition(() => {
      setFocused(!0);
    });
  }, [shouldTrackSelectionAndFocus, path, portableTextEditor]);
  const setSelectedFromRange = useCallback(() => {
    if (!shouldTrackSelectionAndFocus)
      return;
    const winSelection = window.getSelection();
    if (!winSelection) {
      setSelected(!1);
      return;
    }
    if (winSelection && winSelection.rangeCount > 0) {
      const range = winSelection.getRangeAt(0);
      spanRef.current && range.intersectsNode(spanRef.current) ? setSelected(!0) : setSelected(!1);
    } else
      setSelected(!1);
  }, [shouldTrackSelectionAndFocus]);
  useEffect(() => {
    if (!shouldTrackSelectionAndFocus)
      return;
    const onBlur = editorActor.on("blurred", () => {
      setFocused(!1), setSelected(!1);
    }), onFocus = editorActor.on("focused", () => {
      const sel_0 = PortableTextEditor.getSelection(portableTextEditor);
      sel_0 && isEqual(sel_0.focus.path, path) && PortableTextEditor.isCollapsedSelection(portableTextEditor) && setFocused(!0), setSelectedFromRange();
    }), onSelection = editorActor.on("selection", (event) => {
      event.selection && isEqual(event.selection.focus.path, path) && PortableTextEditor.isCollapsedSelection(portableTextEditor) ? setFocused(!0) : setFocused(!1), setSelectedFromRange();
    });
    return () => {
      onBlur.unsubscribe(), onFocus.unsubscribe(), onSelection.unsubscribe();
    };
  }, [editorActor, path, portableTextEditor, setSelectedFromRange, shouldTrackSelectionAndFocus]), useEffect(() => setSelectedFromRange(), [setSelectedFromRange]);
  let children = props.children;
  for (const mark_1 of decorators) {
    const legacyDecoratorSchemaType = legacySchema.decorators.find((dec) => dec.value === mark_1);
    path && legacyDecoratorSchemaType && props.renderDecorator && (children = props.renderDecorator({
      children,
      editorElementRef: spanRef,
      focused,
      path,
      selected,
      schemaType: legacyDecoratorSchemaType,
      value: mark_1,
      type: legacyDecoratorSchemaType
    }));
  }
  for (const annotationMarkDef of annotationMarkDefs) {
    const legacyAnnotationSchemaType = legacySchema.annotations.find((t) => t.name === annotationMarkDef._type);
    legacyAnnotationSchemaType && (block && path && props.renderAnnotation ? children = /* @__PURE__ */ jsx("span", { ref: spanRef, children: props.renderAnnotation({
      block,
      children,
      editorElementRef: spanRef,
      focused,
      path,
      selected,
      schemaType: legacyAnnotationSchemaType,
      value: annotationMarkDef,
      type: legacyAnnotationSchemaType
    }) }) : children = /* @__PURE__ */ jsx("span", { ref: spanRef, children }));
  }
  if (block && path && props.renderChild) {
    const child = block.children.find((_child) => _child._key === props.leaf._key);
    child && (children = props.renderChild({
      annotations: annotationMarkDefs,
      children,
      editorElementRef: spanRef,
      focused,
      path,
      schemaType: legacySchema.span,
      selected,
      value: child,
      type: legacySchema.span
    }));
  }
  return /* @__PURE__ */ jsx("span", { ...props.attributes, ref: spanRef, children });
}
const PLACEHOLDER_STYLE = {
  position: "absolute",
  userSelect: "none",
  pointerEvents: "none",
  left: 0,
  right: 0
};
function RenderLeaf(props) {
  const $ = c(12), editorActor = useContext(EditorActorContext), schema = useSelector(editorActor, _temp);
  if (props.leaf._type !== schema.span.name)
    return props.children;
  let t0;
  $[0] !== props ? (t0 = /* @__PURE__ */ jsx(RenderSpan, { ...props }), $[0] = props, $[1] = t0) : t0 = $[1];
  let renderedSpan = t0;
  if (props.renderPlaceholder && props.leaf.placeholder && props.text.text === "") {
    let t1;
    $[2] !== props.renderPlaceholder ? (t1 = props.renderPlaceholder(), $[2] = props.renderPlaceholder, $[3] = t1) : t1 = $[3];
    let t2;
    $[4] !== t1 ? (t2 = /* @__PURE__ */ jsx("span", { style: PLACEHOLDER_STYLE, contentEditable: !1, children: t1 }), $[4] = t1, $[5] = t2) : t2 = $[5];
    let t3;
    return $[6] !== renderedSpan || $[7] !== t2 ? (t3 = /* @__PURE__ */ jsxs(Fragment, { children: [
      t2,
      renderedSpan
    ] }), $[6] = renderedSpan, $[7] = t2, $[8] = t3) : t3 = $[8], t3;
  }
  const rangeDecoration = props.leaf.rangeDecoration;
  if (rangeDecoration) {
    let t1;
    $[9] !== rangeDecoration || $[10] !== renderedSpan ? (t1 = rangeDecoration.component({
      children: renderedSpan
    }), $[9] = rangeDecoration, $[10] = renderedSpan, $[11] = t1) : t1 = $[11], renderedSpan = t1;
  }
  return renderedSpan;
}
function _temp(s) {
  return s.context.schema;
}
function RenderText(props) {
  const $ = c(5);
  let t0;
  return $[0] !== props.attributes || $[1] !== props.children || $[2] !== props.text._key || $[3] !== props.text._type ? (t0 = /* @__PURE__ */ jsx("span", { ...props.attributes, "data-child-key": props.text._key, "data-child-name": props.text._type, "data-child-type": "span", children: props.children }), $[0] = props.attributes, $[1] = props.children, $[2] = props.text._key, $[3] = props.text._type, $[4] = t0) : t0 = $[4], t0;
}
const IS_MAC = typeof window < "u" && /Mac|iPod|iPhone|iPad/.test(window.navigator.userAgent), modifiers = {
  alt: "altKey",
  control: "ctrlKey",
  meta: "metaKey",
  shift: "shiftKey"
}, aliases = {
  add: "+",
  break: "pause",
  cmd: "meta",
  command: "meta",
  ctl: "control",
  ctrl: "control",
  del: "delete",
  down: "arrowdown",
  esc: "escape",
  ins: "insert",
  left: "arrowleft",
  mod: IS_MAC ? "meta" : "control",
  opt: "alt",
  option: "alt",
  return: "enter",
  right: "arrowright",
  space: " ",
  spacebar: " ",
  up: "arrowup",
  win: "meta",
  windows: "meta"
}, keyCodes = {
  backspace: 8,
  tab: 9,
  enter: 13,
  shift: 16,
  control: 17,
  alt: 18,
  pause: 19,
  capslock: 20,
  escape: 27,
  " ": 32,
  pageup: 33,
  pagedown: 34,
  end: 35,
  home: 36,
  arrowleft: 37,
  arrowup: 38,
  arrowright: 39,
  arrowdown: 40,
  insert: 45,
  delete: 46,
  meta: 91,
  numlock: 144,
  scrolllock: 145,
  ";": 186,
  "=": 187,
  ",": 188,
  "-": 189,
  ".": 190,
  "/": 191,
  "`": 192,
  "[": 219,
  "\\": 220,
  "]": 221,
  "'": 222,
  f1: 112,
  f2: 113,
  f3: 114,
  f4: 115,
  f5: 116,
  f6: 117,
  f7: 118,
  f8: 119,
  f9: 120,
  f10: 121,
  f11: 122,
  f12: 123,
  f13: 124,
  f14: 125,
  f15: 126,
  f16: 127,
  f17: 128,
  f18: 129,
  f19: 130,
  f20: 131
};
function isHotkey(hotkey, event) {
  return compareHotkey(parseHotkey(hotkey), event);
}
function parseHotkey(hotkey) {
  const parsedHotkey = {
    altKey: !1,
    ctrlKey: !1,
    metaKey: !1,
    shiftKey: !1
  }, hotkeySegments = hotkey.replace("++", "+add").split("+");
  for (const rawHotkeySegment of hotkeySegments) {
    const optional = rawHotkeySegment.endsWith("?") && rawHotkeySegment.length > 1, hotkeySegment = optional ? rawHotkeySegment.slice(0, -1) : rawHotkeySegment, keyName = toKeyName(hotkeySegment), modifier = modifiers[keyName], alias = aliases[hotkeySegment], code2 = keyCodes[keyName];
    if (hotkeySegment.length > 1 && modifier === void 0 && alias === void 0 && code2 === void 0)
      throw new TypeError(`Unknown modifier: "${hotkeySegment}"`);
    (hotkeySegments.length === 1 || modifier === void 0) && (parsedHotkey.key = keyName, parsedHotkey.keyCode = toKeyCode(hotkeySegment)), modifier !== void 0 && (parsedHotkey[modifier] = optional ? null : !0);
  }
  return parsedHotkey;
}
function compareHotkey(parsedHotkey, event) {
  return (parsedHotkey.altKey == null || parsedHotkey.altKey === event.altKey) && (parsedHotkey.ctrlKey == null || parsedHotkey.ctrlKey === event.ctrlKey) && (parsedHotkey.metaKey == null || parsedHotkey.metaKey === event.metaKey) && (parsedHotkey.shiftKey == null || parsedHotkey.shiftKey === event.shiftKey) ? parsedHotkey.keyCode !== void 0 && event.keyCode !== void 0 ? parsedHotkey.keyCode === 91 && event.keyCode === 93 ? !0 : parsedHotkey.keyCode === event.keyCode : parsedHotkey.keyCode === event.keyCode || parsedHotkey.key === event.key.toLowerCase() : !1;
}
function toKeyCode(name) {
  const keyName = toKeyName(name);
  return keyCodes[keyName] ?? keyName.toUpperCase().charCodeAt(0);
}
function toKeyName(name) {
  const keyName = name.toLowerCase();
  return aliases[keyName] ?? keyName;
}
const debug$1 = debugWithName("plugin:withHotKeys");
function createWithHotkeys(editorActor, portableTextEditor, hotkeysFromOptions) {
  const reservedHotkeys = ["enter", "tab", "shift", "delete", "end"], activeHotkeys = hotkeysFromOptions ?? {};
  return function(editor) {
    return editor.pteWithHotKeys = (event) => {
      Object.keys(activeHotkeys).forEach((cat) => {
        if (cat === "marks")
          for (const hotkey in activeHotkeys[cat]) {
            if (reservedHotkeys.includes(hotkey))
              throw new Error(`The hotkey ${hotkey} is reserved!`);
            if (isHotkey(hotkey, event.nativeEvent)) {
              event.preventDefault();
              const possibleMark = activeHotkeys[cat];
              if (possibleMark) {
                const mark = possibleMark[hotkey];
                debug$1(`HotKey ${hotkey} to toggle ${mark}`), editorActor.send({
                  type: "behavior event",
                  behaviorEvent: {
                    type: "decorator.toggle",
                    decorator: mark
                  },
                  editor
                });
              }
            }
          }
        if (cat === "custom")
          for (const hotkey in activeHotkeys[cat]) {
            if (reservedHotkeys.includes(hotkey))
              throw new Error(`The hotkey ${hotkey} is reserved!`);
            if (isHotkey(hotkey, event.nativeEvent)) {
              const possibleCommand = activeHotkeys[cat];
              if (possibleCommand) {
                const command = possibleCommand[hotkey];
                command(event, portableTextEditor);
              }
            }
          }
      });
    }, editor;
  };
}
function moveRangeByOperation(range, operation) {
  const anchor = Point.transform(range.anchor, operation), focus = Point.transform(range.focus, operation);
  return anchor === null || focus === null ? null : Point.equals(anchor, range.anchor) && Point.equals(focus, range.focus) ? range : {
    anchor,
    focus
  };
}
const slateOperationCallback = ({
  input,
  sendBack
}) => {
  const originalApply = input.slateEditor.apply;
  return input.slateEditor.apply = (op) => {
    op.type !== "set_selection" && sendBack({
      type: "slate operation",
      operation: op
    }), originalApply(op);
  }, () => {
    input.slateEditor.apply = originalApply;
  };
}, rangeDecorationsMachine = setup({
  types: {
    context: {},
    input: {},
    events: {}
  },
  actions: {
    "update pending range decorations": assign({
      pendingRangeDecorations: ({
        context,
        event
      }) => event.type !== "range decorations updated" ? context.pendingRangeDecorations : event.rangeDecorations
    }),
    "set up initial range decorations": ({
      context
    }) => {
      const rangeDecorationState = [];
      for (const rangeDecoration of context.pendingRangeDecorations) {
        const slateRange = toSlateRange({
          context: {
            schema: context.schema,
            value: context.slateEditor.value,
            selection: rangeDecoration.selection
          },
          blockIndexMap: context.slateEditor.blockIndexMap
        });
        if (!Range.isRange(slateRange)) {
          rangeDecoration.onMoved?.({
            newSelection: null,
            rangeDecoration,
            origin: "local"
          });
          continue;
        }
        rangeDecorationState.push({
          rangeDecoration,
          ...slateRange
        });
      }
      context.slateEditor.decoratedRanges = rangeDecorationState;
    },
    "update range decorations": ({
      context,
      event
    }) => {
      if (event.type !== "range decorations updated")
        return;
      const rangeDecorationState = [];
      for (const rangeDecoration of event.rangeDecorations) {
        const slateRange = toSlateRange({
          context: {
            schema: context.schema,
            value: context.slateEditor.value,
            selection: rangeDecoration.selection
          },
          blockIndexMap: context.slateEditor.blockIndexMap
        });
        if (!Range.isRange(slateRange)) {
          rangeDecoration.onMoved?.({
            newSelection: null,
            rangeDecoration,
            origin: "local"
          });
          continue;
        }
        rangeDecorationState.push({
          rangeDecoration,
          ...slateRange
        });
      }
      context.slateEditor.decoratedRanges = rangeDecorationState;
    },
    "move range decorations": ({
      context,
      event
    }) => {
      if (event.type !== "slate operation")
        return;
      const rangeDecorationState = [];
      for (const decoratedRange of context.slateEditor.decoratedRanges) {
        const slateRange = toSlateRange({
          context: {
            schema: context.schema,
            value: context.slateEditor.value,
            selection: decoratedRange.rangeDecoration.selection
          },
          blockIndexMap: context.slateEditor.blockIndexMap
        });
        if (!Range.isRange(slateRange)) {
          decoratedRange.rangeDecoration.onMoved?.({
            newSelection: null,
            rangeDecoration: decoratedRange.rangeDecoration,
            origin: "local"
          });
          continue;
        }
        let newRange;
        if (newRange = moveRangeByOperation(slateRange, event.operation), newRange && newRange !== slateRange || newRange === null && slateRange) {
          const newRangeSelection = newRange ? slateRangeToSelection({
            schema: context.schema,
            editor: context.slateEditor,
            range: newRange
          }) : null;
          decoratedRange.rangeDecoration.onMoved?.({
            newSelection: newRangeSelection,
            rangeDecoration: decoratedRange.rangeDecoration,
            origin: "local"
          });
        }
        newRange !== null && rangeDecorationState.push({
          ...newRange || slateRange,
          rangeDecoration: {
            ...decoratedRange.rangeDecoration,
            selection: slateRangeToSelection({
              schema: context.schema,
              editor: context.slateEditor,
              range: newRange
            })
          }
        });
      }
      context.slateEditor.decoratedRanges = rangeDecorationState;
    },
    "assign readOnly": assign({
      readOnly: ({
        context,
        event
      }) => event.type !== "update read only" ? context.readOnly : event.readOnly
    }),
    "update decorate": assign({
      decorate: ({
        context
      }) => ({
        fn: createDecorate(context.schema, context.slateEditor)
      })
    })
  },
  actors: {
    "slate operation listener": fromCallback(slateOperationCallback)
  },
  guards: {
    "has pending range decorations": ({
      context
    }) => context.pendingRangeDecorations.length > 0,
    "has range decorations": ({
      context
    }) => context.slateEditor.decoratedRanges.length > 0,
    "has different decorations": ({
      context,
      event
    }) => {
      if (event.type !== "range decorations updated")
        return !1;
      const existingRangeDecorations = context.slateEditor.decoratedRanges.map((decoratedRange) => ({
        anchor: decoratedRange.rangeDecoration.selection?.anchor,
        focus: decoratedRange.rangeDecoration.selection?.focus,
        payload: decoratedRange.rangeDecoration.payload
      })), newRangeDecorations = event.rangeDecorations.map((rangeDecoration) => ({
        anchor: rangeDecoration.selection?.anchor,
        focus: rangeDecoration.selection?.focus,
        payload: rangeDecoration.payload
      }));
      return !isEqual(existingRangeDecorations, newRangeDecorations);
    },
    "not read only": ({
      context
    }) => !context.readOnly,
    "should skip setup": ({
      context
    }) => context.skipSetup
  }
}).createMachine({
  id: "range decorations",
  context: ({
    input
  }) => ({
    readOnly: input.readOnly,
    pendingRangeDecorations: input.rangeDecorations,
    decoratedRanges: [],
    skipSetup: input.skipSetup,
    schema: input.schema,
    slateEditor: input.slateEditor,
    decorate: {
      fn: createDecorate(input.schema, input.slateEditor)
    }
  }),
  invoke: {
    src: "slate operation listener",
    input: ({
      context
    }) => ({
      slateEditor: context.slateEditor
    })
  },
  on: {
    "update read only": {
      actions: ["assign readOnly"]
    }
  },
  initial: "setting up",
  states: {
    "setting up": {
      always: [{
        guard: and(["should skip setup", "has pending range decorations"]),
        target: "ready",
        actions: ["set up initial range decorations", "update decorate"]
      }, {
        guard: "should skip setup",
        target: "ready"
      }],
      on: {
        "range decorations updated": {
          actions: ["update pending range decorations"]
        },
        ready: [{
          target: "ready",
          guard: "has pending range decorations",
          actions: ["set up initial range decorations", "update decorate"]
        }, {
          target: "ready"
        }]
      }
    },
    ready: {
      initial: "idle",
      on: {
        "range decorations updated": {
          target: ".idle",
          guard: "has different decorations",
          actions: ["update range decorations", "update decorate"]
        }
      },
      states: {
        idle: {
          on: {
            "slate operation": {
              target: "moving range decorations",
              guard: and(["has range decorations", "not read only"])
            }
          }
        },
        "moving range decorations": {
          entry: ["move range decorations"],
          always: {
            target: "idle"
          }
        }
      }
    }
  }
});
function createDecorate(schema, slateEditor) {
  return function([node, path]) {
    if (isEqualToEmptyEditor(slateEditor.children, schema))
      return [{
        anchor: {
          path: [0, 0],
          offset: 0
        },
        focus: {
          path: [0, 0],
          offset: 0
        },
        placeholder: !0
      }];
    if (path.length === 0)
      return [];
    if (!Element$1.isElement(node) || node.children.length === 0)
      return [];
    const blockIndex = path.at(0);
    return blockIndex === void 0 ? [] : slateEditor.decoratedRanges.filter((decoratedRange) => Range.isCollapsed(decoratedRange) ? node.children.some((_, childIndex) => Path.equals(decoratedRange.anchor.path, [blockIndex, childIndex]) && Path.equals(decoratedRange.focus.path, [blockIndex, childIndex])) : Range.intersection(decoratedRange, {
      anchor: {
        path,
        offset: 0
      },
      focus: {
        path,
        offset: 0
      }
    }) || Range.includes(decoratedRange, path));
  };
}
const debug = debugWithName("component:Editable"), PortableTextEditable = forwardRef(function(props, forwardedRef) {
  const {
    hotkeys,
    onBlur,
    onFocus,
    onBeforeInput,
    onPaste,
    onCopy,
    onCut,
    onClick,
    onDragStart,
    onDrag,
    onDragEnd,
    onDragEnter,
    onDragOver,
    onDrop,
    onDragLeave,
    rangeDecorations,
    renderAnnotation,
    renderBlock,
    renderChild,
    renderDecorator,
    renderListItem,
    renderPlaceholder,
    renderStyle,
    selection: propsSelection,
    scrollSelectionIntoView,
    spellCheck,
    ...restProps
  } = props, portableTextEditor = usePortableTextEditor(), ref = useRef(null), [editableElement, setEditableElement] = useState(null), [hasInvalidValue, setHasInvalidValue] = useState(!1);
  useImperativeHandle(forwardedRef, () => ref.current);
  const editorActor = useContext(EditorActorContext), relayActor = useContext(RelayActorContext), readOnly = useSelector(editorActor, (s) => s.matches({
    "edit mode": "read only"
  })), slateEditor = useSlate(), rangeDecorationsActor = useActorRef(rangeDecorationsMachine, {
    input: {
      rangeDecorations: rangeDecorations ?? [],
      readOnly,
      schema: editorActor.getSnapshot().context.schema,
      slateEditor,
      skipSetup: !editorActor.getSnapshot().matches({
        setup: "setting up"
      })
    }
  }), decorate = useSelector(rangeDecorationsActor, (s_0) => s_0.context.decorate?.fn);
  useEffect(() => {
    rangeDecorationsActor.send({
      type: "update read only",
      readOnly
    });
  }, [rangeDecorationsActor, readOnly]), useEffect(() => {
    rangeDecorationsActor.send({
      type: "range decorations updated",
      rangeDecorations: rangeDecorations ?? []
    });
  }, [rangeDecorationsActor, rangeDecorations]), useMemo(() => readOnly ? slateEditor : createWithHotkeys(editorActor, portableTextEditor, hotkeys)(slateEditor), [editorActor, hotkeys, portableTextEditor, readOnly, slateEditor]);
  const renderElement = useCallback((eProps) => /* @__PURE__ */ jsx(RenderElement, { ...eProps, readOnly, renderBlock, renderChild, renderListItem, renderStyle, spellCheck }), [spellCheck, readOnly, renderBlock, renderChild, renderListItem, renderStyle]), renderLeaf = useCallback((leafProps) => /* @__PURE__ */ jsx(RenderLeaf, { ...leafProps, readOnly, renderAnnotation, renderChild, renderDecorator, renderPlaceholder }), [readOnly, renderAnnotation, renderChild, renderDecorator, renderPlaceholder]), renderText = useCallback((props_0) => /* @__PURE__ */ jsx(RenderText, { ...props_0 }), []), restoreSelectionFromProps = useCallback(() => {
    if (propsSelection) {
      debug(`Selection from props ${JSON.stringify(propsSelection)}`);
      const normalizedSelection = normalizeSelection(propsSelection, fromSlateValue(slateEditor.children, editorActor.getSnapshot().context.schema.block.name));
      if (normalizedSelection !== null) {
        debug(`Normalized selection from props ${JSON.stringify(normalizedSelection)}`);
        const slateRange = toSlateRange({
          context: {
            schema: editorActor.getSnapshot().context.schema,
            value: slateEditor.value,
            selection: normalizedSelection
          },
          blockIndexMap: slateEditor.blockIndexMap
        });
        slateRange && (Transforms.select(slateEditor, slateRange), slateEditor.operations.some((o) => o.type === "set_selection") || editorActor.send({
          type: "update selection",
          selection: normalizedSelection
        }), slateEditor.onChange());
      }
    }
  }, [editorActor, propsSelection, slateEditor]);
  useEffect(() => {
    const onReady = editorActor.on("ready", () => {
      rangeDecorationsActor.send({
        type: "ready"
      }), restoreSelectionFromProps();
    }), onInvalidValue = editorActor.on("invalid value", () => {
      setHasInvalidValue(!0);
    }), onValueChanged = editorActor.on("value changed", () => {
      setHasInvalidValue(!1);
    });
    return () => {
      onReady.unsubscribe(), onInvalidValue.unsubscribe(), onValueChanged.unsubscribe();
    };
  }, [rangeDecorationsActor, editorActor, restoreSelectionFromProps]), useEffect(() => {
    propsSelection && !hasInvalidValue && restoreSelectionFromProps();
  }, [hasInvalidValue, propsSelection, restoreSelectionFromProps]);
  const handleCopy = useCallback((event) => {
    if (onCopy)
      onCopy(event) !== void 0 && event.preventDefault();
    else if (event.nativeEvent.clipboardData) {
      event.stopPropagation(), event.preventDefault();
      const selection = slateEditor.selection ? slateRangeToSelection({
        schema: editorActor.getSnapshot().context.schema,
        editor: slateEditor,
        range: slateEditor.selection
      }) : void 0, position = selection ? {
        selection
      } : void 0;
      if (!position) {
        console.warn("Could not find position for copy event");
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "clipboard.copy",
          originEvent: {
            dataTransfer: event.nativeEvent.clipboardData
          },
          position
        },
        editor: slateEditor,
        nativeEvent: event
      });
    }
  }, [onCopy, editorActor, slateEditor]), handleCut = useCallback((event_0) => {
    if (onCut)
      onCut(event_0) !== void 0 && event_0.preventDefault();
    else if (event_0.nativeEvent.clipboardData) {
      event_0.stopPropagation(), event_0.preventDefault();
      const selection_0 = editorActor.getSnapshot().context.selection, position_0 = selection_0 ? {
        selection: selection_0
      } : void 0;
      if (!position_0) {
        console.warn("Could not find position for cut event");
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "clipboard.cut",
          originEvent: {
            dataTransfer: event_0.nativeEvent.clipboardData
          },
          position: position_0
        },
        editor: slateEditor,
        nativeEvent: event_0
      });
    }
  }, [onCut, editorActor, slateEditor]), handlePaste = useCallback((event_1) => {
    const value = fromSlateValue(slateEditor.children, editorActor.getSnapshot().context.schema.block.name, KEY_TO_VALUE_ELEMENT.get(slateEditor)), path = (slateEditor.selection ? slateRangeToSelection({
      schema: editorActor.getSnapshot().context.schema,
      editor: slateEditor,
      range: slateEditor.selection
    }) : null)?.focus.path || [], onPasteResult = onPaste?.({
      event: event_1,
      value,
      path,
      schemaTypes: portableTextEditor.schemaTypes
    });
    if (onPasteResult || !slateEditor.selection)
      event_1.preventDefault(), relayActor.send({
        type: "loading"
      }), Promise.resolve(onPasteResult).then((result_1) => {
        if (debug("Custom paste function from client resolved", result_1), !result_1 || !result_1.insert) {
          debug("No result from custom paste handler, pasting normally");
          const selection_1 = editorActor.getSnapshot().context.selection, position_1 = selection_1 ? {
            selection: selection_1
          } : void 0;
          if (!position_1) {
            console.warn("Could not find position for paste event");
            return;
          }
          editorActor.send({
            type: "behavior event",
            behaviorEvent: {
              type: "clipboard.paste",
              originEvent: {
                dataTransfer: event_1.clipboardData
              },
              position: position_1
            },
            editor: slateEditor,
            nativeEvent: event_1
          });
        } else result_1.insert ? editorActor.send({
          type: "behavior event",
          behaviorEvent: {
            type: "insert.blocks",
            blocks: parseBlocks({
              context: {
                keyGenerator: editorActor.getSnapshot().context.keyGenerator,
                schema: editorActor.getSnapshot().context.schema
              },
              blocks: result_1.insert,
              options: {
                refreshKeys: !1,
                validateFields: !1
              }
            }),
            placement: "auto"
          },
          editor: slateEditor
        }) : console.warn("Your onPaste function returned something unexpected:", result_1);
      }).catch((error) => (console.warn(error), error)).finally(() => {
        relayActor.send({
          type: "done loading"
        });
      });
    else if (event_1.nativeEvent.clipboardData) {
      event_1.preventDefault(), event_1.stopPropagation();
      const selection_2 = editorActor.getSnapshot().context.selection, position_2 = selection_2 ? {
        selection: selection_2
      } : void 0;
      if (!position_2) {
        console.warn("Could not find position for paste event");
        return;
      }
      editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "clipboard.paste",
          originEvent: {
            dataTransfer: event_1.nativeEvent.clipboardData
          },
          position: position_2
        },
        editor: slateEditor,
        nativeEvent: event_1
      });
    }
    debug("No result from custom paste handler, pasting normally");
  }, [editorActor, onPaste, portableTextEditor, relayActor, slateEditor]), handleOnFocus = useCallback((event_2) => {
    onFocus && onFocus(event_2), event_2.isDefaultPrevented() || (relayActor.send({
      type: "focused",
      event: event_2
    }), !slateEditor.selection && isEqualToEmptyEditor(slateEditor.children, editorActor.getSnapshot().context.schema) && (Transforms.select(slateEditor, Editor.start(slateEditor, [])), slateEditor.onChange()));
  }, [editorActor, onFocus, relayActor, slateEditor]), handleClick = useCallback((event_3) => {
    if (onClick && onClick(event_3), event_3.isDefaultPrevented() || event_3.isPropagationStopped())
      return;
    const position_3 = getEventPosition({
      editorActor,
      slateEditor,
      event: event_3.nativeEvent
    });
    position_3 && editorActor.send({
      type: "behavior event",
      behaviorEvent: {
        type: "mouse.click",
        position: position_3
      },
      editor: slateEditor,
      nativeEvent: event_3
    });
  }, [onClick, editorActor, slateEditor]), handleOnBlur = useCallback((event_4) => {
    onBlur && onBlur(event_4), event_4.isPropagationStopped() || relayActor.send({
      type: "blurred",
      event: event_4
    });
  }, [relayActor, onBlur]), handleOnBeforeInput = useCallback((event_5) => {
    onBeforeInput && onBeforeInput(event_5);
  }, [onBeforeInput]), validateSelection = useCallback(() => {
    if (!slateEditor.selection)
      return;
    const root = ReactEditor.findDocumentOrShadowRoot(slateEditor), {
      activeElement
    } = root;
    if (ref.current !== activeElement)
      return;
    const domSelection = ReactEditor.getWindow(slateEditor).getSelection();
    if (!domSelection || domSelection.rangeCount === 0)
      return;
    const existingDOMRange = domSelection.getRangeAt(0);
    try {
      const newDOMRange = ReactEditor.toDOMRange(slateEditor, slateEditor.selection);
      (newDOMRange.startOffset !== existingDOMRange.startOffset || newDOMRange.endOffset !== existingDOMRange.endOffset) && (debug("DOM range out of sync, validating selection"), domSelection?.removeAllRanges(), domSelection.addRange(newDOMRange));
    } catch {
      debug("Could not resolve selection, selecting top document"), Transforms.deselect(slateEditor), slateEditor.children.length > 0 && Transforms.select(slateEditor, [0, 0]), slateEditor.onChange();
    }
  }, [ref, slateEditor]);
  useEffect(() => {
    if (editableElement) {
      const mutationObserver = new MutationObserver(validateSelection);
      return mutationObserver.observe(editableElement, {
        attributeOldValue: !1,
        attributes: !1,
        characterData: !1,
        childList: !0,
        subtree: !0
      }), () => {
        mutationObserver.disconnect();
      };
    }
  }, [validateSelection, editableElement]);
  const handleKeyDown = useCallback((event_6) => {
    props.onKeyDown && props.onKeyDown(event_6), event_6.isDefaultPrevented() || slateEditor.pteWithHotKeys(event_6), event_6.isDefaultPrevented() || editorActor.send({
      type: "behavior event",
      behaviorEvent: {
        type: "keyboard.keydown",
        originEvent: {
          key: event_6.key,
          code: event_6.code,
          altKey: event_6.altKey,
          ctrlKey: event_6.ctrlKey,
          metaKey: event_6.metaKey,
          shiftKey: event_6.shiftKey
        }
      },
      editor: slateEditor,
      nativeEvent: event_6
    });
  }, [props, editorActor, slateEditor]), handleKeyUp = useCallback((event_7) => {
    props.onKeyUp && props.onKeyUp(event_7), event_7.isDefaultPrevented() || editorActor.send({
      type: "behavior event",
      behaviorEvent: {
        type: "keyboard.keyup",
        originEvent: {
          key: event_7.key,
          code: event_7.code,
          altKey: event_7.altKey,
          ctrlKey: event_7.ctrlKey,
          metaKey: event_7.metaKey,
          shiftKey: event_7.shiftKey
        }
      },
      editor: slateEditor,
      nativeEvent: event_7
    });
  }, [props, editorActor, slateEditor]), scrollSelectionIntoViewToSlate = useMemo(() => {
    if (scrollSelectionIntoView !== void 0)
      return scrollSelectionIntoView === null ? noop : (_editor, domRange) => {
        scrollSelectionIntoView(portableTextEditor, domRange);
      };
  }, [portableTextEditor, scrollSelectionIntoView]);
  useEffect(() => {
    ref.current = ReactEditor.toDOMNode(slateEditor, slateEditor), setEditableElement(ref.current);
  }, [slateEditor, ref]), useEffect(() => {
    const window_0 = ReactEditor.getWindow(slateEditor), onDragEnd_0 = () => {
      editorActor.send({
        type: "dragend"
      });
    }, onDrop_0 = () => {
      editorActor.send({
        type: "drop"
      });
    };
    return window_0.document.addEventListener("dragend", onDragEnd_0), window_0.document.addEventListener("drop", onDrop_0), () => {
      window_0.document.removeEventListener("dragend", onDragEnd_0), window_0.document.removeEventListener("drop", onDrop_0);
    };
  }, [slateEditor, editorActor]);
  const handleDragStart = useCallback((event_8) => {
    if (onDragStart?.(event_8), event_8.isDefaultPrevented() || event_8.isPropagationStopped())
      return;
    const position_4 = getEventPosition({
      editorActor,
      slateEditor,
      event: event_8.nativeEvent
    });
    if (!position_4) {
      console.warn("Could not find position for dragstart event");
      return;
    }
    return editorActor.send({
      type: "dragstart",
      origin: position_4
    }), editorActor.send({
      type: "behavior event",
      behaviorEvent: {
        type: "drag.dragstart",
        originEvent: {
          clientX: event_8.clientX,
          clientY: event_8.clientY,
          dataTransfer: event_8.dataTransfer
        },
        position: position_4
      },
      editor: slateEditor
    }), !0;
  }, [onDragStart, editorActor, slateEditor]), handleDrag = useCallback((event_9) => {
    if (onDrag?.(event_9), !(event_9.isDefaultPrevented() || event_9.isPropagationStopped() || !getEventPosition({
      editorActor,
      slateEditor,
      event: event_9.nativeEvent
    })))
      return editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "drag.drag",
          originEvent: {
            dataTransfer: event_9.dataTransfer
          }
        },
        editor: slateEditor
      }), !0;
  }, [onDrag, editorActor, slateEditor]), handleDragEnd = useCallback((event_10) => {
    if (onDragEnd?.(event_10), !(event_10.isDefaultPrevented() || event_10.isPropagationStopped()))
      return editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "drag.dragend",
          originEvent: {
            dataTransfer: event_10.dataTransfer
          }
        },
        editor: slateEditor
      }), !0;
  }, [onDragEnd, editorActor, slateEditor]), handleDragEnter = useCallback((event_11) => {
    if (onDragEnter?.(event_11), event_11.isDefaultPrevented() || event_11.isPropagationStopped())
      return;
    const position_6 = getEventPosition({
      editorActor,
      slateEditor,
      event: event_11.nativeEvent
    });
    if (position_6)
      return editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "drag.dragenter",
          originEvent: {
            dataTransfer: event_11.dataTransfer
          },
          position: position_6
        },
        editor: slateEditor
      }), !0;
  }, [onDragEnter, editorActor, slateEditor]), handleDragOver = useCallback((event_12) => {
    if (onDragOver?.(event_12), event_12.isDefaultPrevented() || event_12.isPropagationStopped())
      return;
    const position_7 = getEventPosition({
      editorActor,
      slateEditor,
      event: event_12.nativeEvent
    });
    if (position_7)
      return editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "drag.dragover",
          originEvent: {
            dataTransfer: event_12.dataTransfer
          },
          dragOrigin: editorActor.getSnapshot().context.internalDrag?.origin,
          position: position_7
        },
        editor: slateEditor,
        nativeEvent: event_12
      }), !0;
  }, [onDragOver, editorActor, slateEditor]), handleDrop = useCallback((event_13) => {
    if (onDrop?.(event_13), event_13.isDefaultPrevented() || event_13.isPropagationStopped())
      return;
    const position_8 = getEventPosition({
      editorActor,
      slateEditor,
      event: event_13.nativeEvent
    });
    if (!position_8) {
      console.warn("Could not find position for drop event");
      return;
    }
    return editorActor.send({
      type: "behavior event",
      behaviorEvent: {
        type: "drag.drop",
        originEvent: {
          dataTransfer: event_13.dataTransfer
        },
        dragOrigin: editorActor.getSnapshot().context.internalDrag?.origin,
        position: position_8
      },
      editor: slateEditor,
      nativeEvent: event_13
    }), !0;
  }, [onDrop, editorActor, slateEditor]), handleDragLeave = useCallback((event_14) => {
    if (onDragLeave?.(event_14), !(event_14.isDefaultPrevented() || event_14.isPropagationStopped() || !getEventPosition({
      editorActor,
      slateEditor,
      event: event_14.nativeEvent
    })))
      return editorActor.send({
        type: "behavior event",
        behaviorEvent: {
          type: "drag.dragleave",
          originEvent: {
            dataTransfer: event_14.dataTransfer
          }
        },
        editor: slateEditor
      }), !0;
  }, [onDragLeave, editorActor, slateEditor]);
  return portableTextEditor ? hasInvalidValue ? null : /* @__PURE__ */ jsx(
    Editable,
    {
      ...restProps,
      "data-read-only": readOnly,
      autoFocus: !1,
      className: restProps.className || "pt-editable",
      decorate,
      onBlur: handleOnBlur,
      onCopy: handleCopy,
      onCut: handleCut,
      onClick: handleClick,
      onDOMBeforeInput: handleOnBeforeInput,
      onDragStart: handleDragStart,
      onDrag: handleDrag,
      onDragEnd: handleDragEnd,
      onDragEnter: handleDragEnter,
      onDragOver: handleDragOver,
      onDrop: handleDrop,
      onDragLeave: handleDragLeave,
      onFocus: handleOnFocus,
      onKeyDown: handleKeyDown,
      onKeyUp: handleKeyUp,
      onPaste: handlePaste,
      readOnly,
      renderPlaceholder: void 0,
      renderElement,
      renderLeaf,
      renderText,
      scrollSelectionIntoView: scrollSelectionIntoViewToSlate
    }
  ) : null;
});
PortableTextEditable.displayName = "ForwardRef(PortableTextEditable)";
function useConstant(factory) {
  const ref = React.useRef(null);
  return ref.current || (ref.current = {
    constant: factory()
  }), ref.current.constant;
}
function EditorProvider(props) {
  const $ = c(28);
  let t0;
  $[0] !== props.initialConfig ? (t0 = () => {
    const internalEditor = createInternalEditor(props.initialConfig), portableTextEditor = new PortableTextEditor({
      editor: internalEditor.editor
    });
    return {
      internalEditor,
      portableTextEditor
    };
  }, $[0] = props.initialConfig, $[1] = t0) : t0 = $[1];
  const {
    internalEditor: internalEditor_0,
    portableTextEditor: portableTextEditor_0
  } = useConstant(t0);
  let t1;
  $[2] !== internalEditor_0.actors.editorActor || $[3] !== internalEditor_0.actors.mutationActor || $[4] !== internalEditor_0.actors.relayActor || $[5] !== internalEditor_0.actors.syncActor || $[6] !== internalEditor_0.subscriptions || $[7] !== portableTextEditor_0 ? (t1 = () => {
    const unsubscribers = [];
    for (const subscription of internalEditor_0.subscriptions)
      unsubscribers.push(subscription());
    const relayActorSubscription = internalEditor_0.actors.relayActor.on("*", (event) => {
      const change = eventToChange(event);
      change && portableTextEditor_0.change$.next(change);
    });
    return unsubscribers.push(relayActorSubscription.unsubscribe), internalEditor_0.actors.editorActor.start(), internalEditor_0.actors.mutationActor.start(), internalEditor_0.actors.relayActor.start(), internalEditor_0.actors.syncActor.start(), () => {
      for (const unsubscribe of unsubscribers)
        unsubscribe();
      stopActor(internalEditor_0.actors.editorActor), stopActor(internalEditor_0.actors.mutationActor), stopActor(internalEditor_0.actors.relayActor), stopActor(internalEditor_0.actors.syncActor);
    };
  }, $[2] = internalEditor_0.actors.editorActor, $[3] = internalEditor_0.actors.mutationActor, $[4] = internalEditor_0.actors.relayActor, $[5] = internalEditor_0.actors.syncActor, $[6] = internalEditor_0.subscriptions, $[7] = portableTextEditor_0, $[8] = t1) : t1 = $[8];
  let t2;
  $[9] !== internalEditor_0 || $[10] !== portableTextEditor_0 ? (t2 = [internalEditor_0, portableTextEditor_0], $[9] = internalEditor_0, $[10] = portableTextEditor_0, $[11] = t2) : t2 = $[11], useEffect(t1, t2);
  let t3;
  $[12] !== portableTextEditor_0 || $[13] !== props.children ? (t3 = /* @__PURE__ */ jsx(PortableTextEditorContext.Provider, { value: portableTextEditor_0, children: props.children }), $[12] = portableTextEditor_0, $[13] = props.children, $[14] = t3) : t3 = $[14];
  let t4;
  $[15] !== internalEditor_0.editor._internal.slateEditor.initialValue || $[16] !== internalEditor_0.editor._internal.slateEditor.instance || $[17] !== t3 ? (t4 = /* @__PURE__ */ jsx(Slate, { editor: internalEditor_0.editor._internal.slateEditor.instance, initialValue: internalEditor_0.editor._internal.slateEditor.initialValue, children: t3 }), $[15] = internalEditor_0.editor._internal.slateEditor.initialValue, $[16] = internalEditor_0.editor._internal.slateEditor.instance, $[17] = t3, $[18] = t4) : t4 = $[18];
  let t5;
  $[19] !== internalEditor_0.actors.relayActor || $[20] !== t4 ? (t5 = /* @__PURE__ */ jsx(RelayActorContext.Provider, { value: internalEditor_0.actors.relayActor, children: t4 }), $[19] = internalEditor_0.actors.relayActor, $[20] = t4, $[21] = t5) : t5 = $[21];
  let t6;
  $[22] !== internalEditor_0.actors.editorActor || $[23] !== t5 ? (t6 = /* @__PURE__ */ jsx(EditorActorContext.Provider, { value: internalEditor_0.actors.editorActor, children: t5 }), $[22] = internalEditor_0.actors.editorActor, $[23] = t5, $[24] = t6) : t6 = $[24];
  let t7;
  return $[25] !== internalEditor_0.editor || $[26] !== t6 ? (t7 = /* @__PURE__ */ jsx(EditorContext.Provider, { value: internalEditor_0.editor, children: t6 }), $[25] = internalEditor_0.editor, $[26] = t6, $[27] = t7) : t7 = $[27], t7;
}
const usePortableTextEditorSelection = () => {
  const $ = c(3), editorActor = useContext(EditorActorContext), [selection, setSelection2] = useState(null);
  let t0, t1;
  return $[0] !== editorActor ? (t0 = () => {
    const subscription = editorActor.on("selection", (event) => {
      startTransition(() => {
        setSelection2(event.selection);
      });
    });
    return () => {
      subscription.unsubscribe();
    };
  }, t1 = [editorActor], $[0] = editorActor, $[1] = t0, $[2] = t1) : (t0 = $[1], t1 = $[2]), useEffect(t0, t1), selection;
};
export {
  EditorEventListener,
  EditorProvider,
  PortableTextEditable,
  PortableTextEditor,
  defineSchema,
  defaultKeyGenerator as keyGenerator,
  useEditor,
  useEditorSelector,
  usePortableTextEditor,
  usePortableTextEditorSelection
};
//# sourceMappingURL=index.js.map
