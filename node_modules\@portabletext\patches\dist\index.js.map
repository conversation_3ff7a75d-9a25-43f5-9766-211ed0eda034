{"version": 3, "file": "index.js", "sources": ["../src/arrayInsert.ts", "../src/array.ts", "../src/number.ts", "../src/object.ts", "../src/primitive.ts", "../src/string.ts", "../src/applyPatch.ts", "../src/patches.ts"], "sourcesContent": ["export const BEFORE = 'before'\nexport const AFTER = 'after'\n\nexport default function insert(\n  array: any[],\n  position: string,\n  index: number,\n  ...args: any[]\n) {\n  if (position !== BEFORE && position !== AFTER) {\n    throw new Error(\n      `Invalid position \"${position}\", must be either ${BEFORE} or ${AFTER}`,\n    )\n  }\n\n  const items = flatten(...args)\n\n  if (array.length === 0) {\n    return items\n  }\n\n  const len = array.length\n  const idx = Math.abs((len + index) % len) % len\n\n  const normalizedIdx = position === 'after' ? idx + 1 : idx\n\n  const copy = array.slice()\n  copy.splice(normalizedIdx, 0, ...flatten(items))\n  return copy\n}\n\nfunction flatten(...values: any[]) {\n  return values.reduce((prev, item) => prev.concat(item), [])\n}\n", "import {findIndex} from 'lodash'\nimport applyPatch from './applyPatch'\nimport insert from './arrayInsert'\nimport type {JSONValue, Patch, PathSegment} from './types'\n\nfunction findTargetIndex(array: any[], pathSegment: PathSegment | undefined) {\n  if (typeof pathSegment === 'number') {\n    return pathSegment\n  }\n  const index = findIndex(array, pathSegment)\n  return index === -1 ? false : index\n}\n\nexport function applyPatchToArray(\n  value: Array<JSONValue>,\n  patch: Patch,\n): Array<JSONValue> | undefined {\n  const nextValue = value.slice() // make a copy for internal mutation\n\n  if (patch.path.length === 0) {\n    // its directed to me\n    if (patch.type === 'setIfMissing') {\n      if (!Array.isArray(patch.value)) {\n        throw new Error('Cannot set value of an array to a non-array')\n      }\n\n      return value === undefined ? patch.value : value\n    }\n\n    if (patch.type === 'set') {\n      if (!Array.isArray(patch.value)) {\n        throw new Error('Cannot set value of an array to a non-array')\n      }\n\n      return patch.value\n    }\n\n    if (patch.type === 'unset') {\n      return undefined\n    }\n\n    throw new Error(`Invalid array operation: ${patch.type}`)\n  }\n\n  const [head, ...tail] = patch.path\n\n  const index = findTargetIndex(value, head)\n\n  // If the given selector could not be found, return as-is\n  if (index === false) {\n    return nextValue\n  }\n\n  if (tail.length === 0) {\n    if (patch.type === 'insert') {\n      const {position, items} = patch\n      return insert(value, position, index, items)\n    } else if (patch.type === 'unset') {\n      if (typeof index !== 'number') {\n        throw new Error(\n          `Expected array index to be a number, instead got \"${index}\"`,\n        )\n      }\n      nextValue.splice(index, 1)\n      return nextValue\n    }\n  }\n\n  // The patch is not directed to me\n  nextValue[index] = applyPatch(nextValue[index], {\n    ...patch,\n    path: tail,\n  }) as JSONValue\n\n  return nextValue\n}\n", "import type {Patch} from './types'\n\nexport function applyPatchToNumber(value: number, patch: Patch) {\n  if (patch.path.length > 0) {\n    throw new Error(\n      `Cannot apply deep operations on primitive values. Received patch with type \"${\n        patch.type\n      }\" and path \"${patch.path\n        .map((path: any) => JSON.stringify(path))\n        .join('.')} that targeted the value \"${JSON.stringify(value)}\"`,\n    )\n  }\n\n  if (patch.type === 'set') {\n    return patch.value\n  }\n\n  if (patch.type === 'setIfMissing') {\n    return value === undefined ? patch.value : value\n  }\n\n  if (patch.type === 'unset') {\n    return undefined\n  }\n\n  if (patch.type === 'inc') {\n    if (typeof patch.value !== 'number') {\n      throw new Error('Cannot increment with a non-number')\n    }\n\n    return value + patch.value\n  }\n\n  if (patch.type === 'dec') {\n    if (typeof patch.value !== 'number') {\n      throw new Error('Cannot decrement with a non-number')\n    }\n\n    return value - patch.value\n  }\n\n  throw new Error(\n    `Received patch of unsupported type: \"${JSON.stringify(\n      patch.type,\n    )}\" for number. This is most likely a bug.`,\n  )\n}\n", "import {clone, omit} from 'lodash'\nimport applyPatch from './applyPatch'\nimport type {JSONValue, Patch} from './types'\n\nexport function applyPatchToObject(\n  value: {[key: string]: JSONValue},\n  patch: Patch,\n): {[key: string]: JSONValue} | undefined {\n  const nextValue = clone(value)\n\n  if (patch.path.length === 0) {\n    // its directed to me\n    if (patch.type === 'set') {\n      if (\n        typeof patch.value === 'object' &&\n        patch.value !== null &&\n        !Array.isArray(patch.value)\n      ) {\n        return patch.value\n      }\n\n      throw new Error('Cannot set value of an object to a non-object')\n    }\n\n    if (patch.type === 'unset') {\n      return undefined\n    }\n\n    throw new Error(`Invalid object operation: ${patch.type}`)\n  }\n\n  // The patch is not directed to me\n  const [head, ...tail] = patch.path\n\n  if (typeof head !== 'string') {\n    throw new Error(`Expected field name to be a string, instead got: ${head}`)\n  }\n\n  if (tail.length === 0 && patch.type === 'unset') {\n    return omit(nextValue, head)\n  }\n\n  nextValue[head] = applyPatch(nextValue[head], {\n    ...patch,\n    path: tail,\n  }) as JSONValue\n\n  return nextValue\n}\n", "import type {JSONValue, Patch} from './types'\n\nexport function applyPatchToUnknown(\n  value: unknown,\n  patch: Patch,\n): JSONValue | undefined {\n  if (patch.path.length > 0) {\n    throw new Error(\n      `Cannot apply deep operations on primitive values. Received patch with type \"${\n        patch.type\n      }\" and path \"${patch.path\n        .map((path: any) => JSON.stringify(path))\n        .join('.')} that targeted the value \"${JSON.stringify(value)}\"`,\n    )\n  }\n\n  if (patch.type === 'set') {\n    return patch.value\n  }\n\n  if (patch.type === 'setIfMissing') {\n    return value === undefined ? patch.value : (value as JSONValue)\n  }\n\n  if (patch.type === 'unset') {\n    return undefined\n  }\n\n  throw new Error(\n    `Received patch of unsupported type: \"${JSON.stringify(\n      patch.type,\n    )}\" for primitives. This is most likely a bug.`,\n  )\n}\n", "import {applyPatches, parsePatch} from '@sanity/diff-match-patch'\nimport type {Patch} from './types'\n\nexport function applyPatchToString(value: string, patch: Patch) {\n  if (patch.path.length > 0) {\n    throw new Error(\n      `Cannot apply deep operations on string values. Received patch with type \"${\n        patch.type\n      }\" and path \"${patch.path.join('.')} that targeted the value \"${JSON.stringify(value)}\"`,\n    )\n  }\n\n  if (patch.type === 'diffMatchPatch') {\n    const [result] = applyPatches(parsePatch(patch.value), value, {\n      allowExceedingIndices: true,\n    })\n    return result\n  }\n\n  if (patch.type === 'setIfMissing') {\n    return value === undefined ? patch.value : value\n  }\n\n  if (patch.type === 'set') {\n    return patch.value\n  }\n\n  if (patch.type === 'unset') {\n    return undefined\n  }\n\n  throw new Error(\n    `Received patch of unsupported type: \"${JSON.stringify(\n      patch.type,\n    )}\" for string. This is most likely a bug.`,\n  )\n}\n", "import {applyPatchToArray} from './array'\nimport {applyPatchToNumber} from './number'\nimport {applyPatchToObject} from './object'\nimport {applyPatchToUnknown} from './primitive'\nimport {applyPatchToString} from './string'\nimport type {JSONValue, Patch} from './types'\n\n/** @beta */\nexport function applyAll<TValue>(value: TValue, patches: Array<Patch>): TValue {\n  return patches.reduce(applyPatch, value) as TValue\n}\n\nexport default function applyPatch(value: unknown, patch: Patch) {\n  if (Array.isArray(value)) {\n    return applyPatchToArray(value, patch)\n  }\n\n  if (typeof value === 'string') {\n    return applyPatchToString(value, patch)\n  }\n\n  if (isObject(value)) {\n    return applyPatchToObject(value, patch)\n  }\n\n  if (typeof value === 'number') {\n    return applyPatchToNumber(value, patch)\n  }\n\n  return applyPatchToUnknown(value, patch)\n}\n\nfunction isObject(value: unknown): value is {[key: string]: JSONValue} {\n  return typeof value === 'object' && value !== null && !Array.isArray(value)\n}\n", "import {makePatches, stringifyPatches} from '@sanity/diff-match-patch'\nimport type {\n  DiffMatchPatch,\n  InsertPatch,\n  InsertPosition,\n  Path,\n  PathSegment,\n  SetIfMissingPatch,\n  SetPatch,\n  UnsetPatch,\n} from './types'\n\n/** @public */\nexport function setIfMissing(value: any, path: Path = []): SetIfMissingPatch {\n  return {\n    type: 'setIfMissing',\n    path,\n    value,\n  }\n}\n\n/** @public */\nexport function diffMatchPatch(\n  currentValue: string,\n  nextValue: string,\n  path: Path = [],\n): DiffMatchPatch {\n  const patches = makePatches(currentValue, nextValue)\n  const patch = stringifyPatches(patches)\n  return {type: 'diffMatchPatch', path, value: patch}\n}\n\n/** @public */\nexport function insert(\n  items: any[],\n  position: InsertPosition,\n  path: Path = [],\n): InsertPatch {\n  return {\n    type: 'insert',\n    path,\n    position,\n    items,\n  }\n}\n\n/** @public */\nexport function set(value: any, path: Path = []): SetPatch {\n  return {type: 'set', path, value}\n}\n\n/** @public */\nexport function unset(path: Path = []): UnsetPatch {\n  return {type: 'unset', path}\n}\n\n/** @internal */\nexport function prefixPath<T extends {path: Path}>(\n  patch: T,\n  segment: PathSegment,\n): T {\n  return {\n    ...patch,\n    path: [segment, ...patch.path],\n  }\n}\n"], "names": ["insert"], "mappings": ";;;;AAAO,MAAM,SAAS,UACT,QAAQ;AAErB,SAAwBA,SACtB,OACA,UACA,UACG,MACH;AACA,MAAI,aAAa,UAAU,aAAa;AACtC,UAAM,IAAI;AAAA,MACR,qBAAqB,QAAQ,qBAAqB,MAAM,OAAO,KAAK;AAAA,IAAA;AAIxE,QAAM,QAAQ,QAAQ,GAAG,IAAI;AAE7B,MAAI,MAAM,WAAW;AACnB,WAAO;AAGT,QAAM,MAAM,MAAM,QACZ,MAAM,KAAK,KAAK,MAAM,SAAS,GAAG,IAAI,KAEtC,gBAAgB,aAAa,UAAU,MAAM,IAAI,KAEjD,OAAO,MAAM,MAAA;AACnB,SAAA,KAAK,OAAO,eAAe,GAAG,GAAG,QAAQ,KAAK,CAAC,GACxC;AACT;AAEA,SAAS,WAAW,QAAe;AACjC,SAAO,OAAO,OAAO,CAAC,MAAM,SAAS,KAAK,OAAO,IAAI,GAAG,EAAE;AAC5D;AC5BA,SAAS,gBAAgB,OAAc,aAAsC;AAC3E,MAAI,OAAO,eAAgB;AACzB,WAAO;AAET,QAAM,QAAQ,UAAU,OAAO,WAAW;AAC1C,SAAO,UAAU,KAAK,KAAQ;AAChC;AAEO,SAAS,kBACd,OACA,OAC8B;AAC9B,QAAM,YAAY,MAAM,MAAA;AAExB,MAAI,MAAM,KAAK,WAAW,GAAG;AAE3B,QAAI,MAAM,SAAS,gBAAgB;AACjC,UAAI,CAAC,MAAM,QAAQ,MAAM,KAAK;AAC5B,cAAM,IAAI,MAAM,6CAA6C;AAG/D,aAAO,UAAU,SAAY,MAAM,QAAQ;AAAA,IAC7C;AAEA,QAAI,MAAM,SAAS,OAAO;AACxB,UAAI,CAAC,MAAM,QAAQ,MAAM,KAAK;AAC5B,cAAM,IAAI,MAAM,6CAA6C;AAG/D,aAAO,MAAM;AAAA,IACf;AAEA,QAAI,MAAM,SAAS;AACjB;AAGF,UAAM,IAAI,MAAM,4BAA4B,MAAM,IAAI,EAAE;AAAA,EAC1D;AAEA,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI,MAAM,MAExB,QAAQ,gBAAgB,OAAO,IAAI;AAGzC,MAAI,UAAU;AACZ,WAAO;AAGT,MAAI,KAAK,WAAW;AAClB,QAAI,MAAM,SAAS,UAAU;AAC3B,YAAM,EAAC,UAAU,MAAA,IAAS;AAC1B,aAAOA,SAAO,OAAO,UAAU,OAAO,KAAK;AAAA,IAC7C,WAAW,MAAM,SAAS,SAAS;AACjC,UAAI,OAAO,SAAU;AACnB,cAAM,IAAI;AAAA,UACR,qDAAqD,KAAK;AAAA,QAAA;AAG9D,aAAA,UAAU,OAAO,OAAO,CAAC,GAClB;AAAA,IACT;AAAA;AAIF,SAAA,UAAU,KAAK,IAAI,WAAW,UAAU,KAAK,GAAG;AAAA,IAC9C,GAAG;AAAA,IACH,MAAM;AAAA,EAAA,CACP,GAEM;AACT;ACzEO,SAAS,mBAAmB,OAAe,OAAc;AAC9D,MAAI,MAAM,KAAK,SAAS;AACtB,UAAM,IAAI;AAAA,MACR,+EACE,MAAM,IACR,eAAe,MAAM,KAClB,IAAI,CAAC,SAAc,KAAK,UAAU,IAAI,CAAC,EACvC,KAAK,GAAG,CAAC,6BAA6B,KAAK,UAAU,KAAK,CAAC;AAAA,IAAA;AAIlE,MAAI,MAAM,SAAS;AACjB,WAAO,MAAM;AAGf,MAAI,MAAM,SAAS;AACjB,WAAO,UAAU,SAAY,MAAM,QAAQ;AAG7C,MAAI,MAAM,SAAS,SAInB;AAAA,QAAI,MAAM,SAAS,OAAO;AACxB,UAAI,OAAO,MAAM,SAAU;AACzB,cAAM,IAAI,MAAM,oCAAoC;AAGtD,aAAO,QAAQ,MAAM;AAAA,IACvB;AAEA,QAAI,MAAM,SAAS,OAAO;AACxB,UAAI,OAAO,MAAM,SAAU;AACzB,cAAM,IAAI,MAAM,oCAAoC;AAGtD,aAAO,QAAQ,MAAM;AAAA,IACvB;AAEA,UAAM,IAAI;AAAA,MACR,wCAAwC,KAAK;AAAA,QAC3C,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAAA,EACH;AACF;AC1CO,SAAS,mBACd,OACA,OACwC;AACxC,QAAM,YAAY,MAAM,KAAK;AAE7B,MAAI,MAAM,KAAK,WAAW,GAAG;AAE3B,QAAI,MAAM,SAAS,OAAO;AACxB,UACE,OAAO,MAAM,SAAU,YACvB,MAAM,UAAU,QAChB,CAAC,MAAM,QAAQ,MAAM,KAAK;AAE1B,eAAO,MAAM;AAGf,YAAM,IAAI,MAAM,+CAA+C;AAAA,IACjE;AAEA,QAAI,MAAM,SAAS;AACjB;AAGF,UAAM,IAAI,MAAM,6BAA6B,MAAM,IAAI,EAAE;AAAA,EAC3D;AAGA,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI,MAAM;AAE9B,MAAI,OAAO,QAAS;AAClB,UAAM,IAAI,MAAM,oDAAoD,IAAI,EAAE;AAG5E,SAAI,KAAK,WAAW,KAAK,MAAM,SAAS,UAC/B,KAAK,WAAW,IAAI,KAG7B,UAAU,IAAI,IAAI,WAAW,UAAU,IAAI,GAAG;AAAA,IAC5C,GAAG;AAAA,IACH,MAAM;AAAA,EAAA,CACP,GAEM;AACT;AC9CO,SAAS,oBACd,OACA,OACuB;AACvB,MAAI,MAAM,KAAK,SAAS;AACtB,UAAM,IAAI;AAAA,MACR,+EACE,MAAM,IACR,eAAe,MAAM,KAClB,IAAI,CAAC,SAAc,KAAK,UAAU,IAAI,CAAC,EACvC,KAAK,GAAG,CAAC,6BAA6B,KAAK,UAAU,KAAK,CAAC;AAAA,IAAA;AAIlE,MAAI,MAAM,SAAS;AACjB,WAAO,MAAM;AAGf,MAAI,MAAM,SAAS;AACjB,WAAO,UAAU,SAAY,MAAM,QAAS;AAG9C,MAAI,MAAM,SAAS;AAInB,UAAM,IAAI;AAAA,MACR,wCAAwC,KAAK;AAAA,QAC3C,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAEL;AC9BO,SAAS,mBAAmB,OAAe,OAAc;AAC9D,MAAI,MAAM,KAAK,SAAS;AACtB,UAAM,IAAI;AAAA,MACR,4EACE,MAAM,IACR,eAAe,MAAM,KAAK,KAAK,GAAG,CAAC,6BAA6B,KAAK,UAAU,KAAK,CAAC;AAAA,IAAA;AAIzF,MAAI,MAAM,SAAS,kBAAkB;AACnC,UAAM,CAAC,MAAM,IAAI,aAAa,WAAW,MAAM,KAAK,GAAG,OAAO;AAAA,MAC5D,uBAAuB;AAAA,IAAA,CACxB;AACD,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,SAAS;AACjB,WAAO,UAAU,SAAY,MAAM,QAAQ;AAG7C,MAAI,MAAM,SAAS;AACjB,WAAO,MAAM;AAGf,MAAI,MAAM,SAAS;AAInB,UAAM,IAAI;AAAA,MACR,wCAAwC,KAAK;AAAA,QAC3C,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAEL;AC5BO,SAAS,SAAiB,OAAe,SAA+B;AAC7E,SAAO,QAAQ,OAAO,YAAY,KAAK;AACzC;AAEA,SAAwB,WAAW,OAAgB,OAAc;AAC/D,SAAI,MAAM,QAAQ,KAAK,IACd,kBAAkB,OAAO,KAAK,IAGnC,OAAO,SAAU,WACZ,mBAAmB,OAAO,KAAK,IAGpC,SAAS,KAAK,IACT,mBAAmB,OAAO,KAAK,IAGpC,OAAO,SAAU,WACZ,mBAAmB,OAAO,KAAK,IAGjC,oBAAoB,OAAO,KAAK;AACzC;AAEA,SAAS,SAAS,OAAqD;AACrE,SAAO,OAAO,SAAU,YAAY,UAAU,QAAQ,CAAC,MAAM,QAAQ,KAAK;AAC5E;ACrBO,SAAS,aAAa,OAAY,OAAa,IAAuB;AAC3E,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EAAA;AAEJ;AAGO,SAAS,eACd,cACA,WACA,OAAa,CAAA,GACG;AAChB,QAAM,UAAU,YAAY,cAAc,SAAS,GAC7C,QAAQ,iBAAiB,OAAO;AACtC,SAAO,EAAC,MAAM,kBAAkB,MAAM,OAAO,MAAA;AAC/C;AAGO,SAAS,OACd,OACA,UACA,OAAa,CAAA,GACA;AACb,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ;AAGO,SAAS,IAAI,OAAY,OAAa,IAAc;AACzD,SAAO,EAAC,MAAM,OAAO,MAAM,MAAA;AAC7B;AAGO,SAAS,MAAM,OAAa,IAAgB;AACjD,SAAO,EAAC,MAAM,SAAS,KAAA;AACzB;AAGO,SAAS,WACd,OACA,SACG;AACH,SAAO;AAAA,IACL,GAAG;AAAA,IACH,MAAM,CAAC,SAAS,GAAG,MAAM,IAAI;AAAA,EAAA;AAEjC;"}