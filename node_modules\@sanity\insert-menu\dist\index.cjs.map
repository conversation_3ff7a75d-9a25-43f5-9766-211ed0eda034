{"version": 3, "file": "index.cjs", "sources": ["../src/getSchemaTypeIcon.ts", "../src/InsertMenu.tsx"], "sourcesContent": ["import {type ReferenceSchemaType, type SchemaType} from '@sanity/types'\nimport {type ComponentType} from 'react'\n\n/** @internal */\nexport function getSchemaTypeIcon(schemaType: SchemaType): ComponentType | undefined {\n  // Use reference icon if reference is to one schemaType only\n  const referenceIcon =\n    isReferenceSchemaType(schemaType) && (schemaType.to ?? []).length === 1\n      ? schemaType.to[0]!.icon\n      : undefined\n\n  return schemaType.icon ?? schemaType.type?.icon ?? referenceIcon\n}\n\nfunction isReferenceSchemaType(type: unknown): type is ReferenceSchemaType {\n  return isRecord(type) && (type['name'] === 'reference' || isReferenceSchemaType(type['type']))\n}\n\nfunction isRecord(value: unknown): value is Record<string, unknown> {\n  return !!value && (typeof value == 'object' || typeof value == 'function')\n}\n", "import {SearchIcon, ThLargeIcon, UlistIcon} from '@sanity/icons'\nimport {type SchemaType} from '@sanity/types'\nimport {\n  Box,\n  Button,\n  Flex,\n  Grid,\n  Menu,\n  MenuItem,\n  Stack,\n  Tab,\n  TabList,\n  Text,\n  TextInput,\n  Tooltip,\n  type MenuItemProps,\n} from '@sanity/ui/_visual-editing'\nimport {startCase} from 'lodash'\nimport {useReducer, useState, type ChangeEvent, type CSSProperties} from 'react'\nimport {isValidElementType} from 'react-is'\nimport {getSchemaTypeIcon} from './getSchemaTypeIcon'\nimport type {InsertMenuOptions} from './InsertMenuOptions'\n\ntype InsertMenuGroup = NonNullable<InsertMenuOptions['groups']>[number] & {selected: boolean}\ntype InsertMenuViews = NonNullable<InsertMenuOptions['views']>\ntype InsertMenuView = InsertMenuViews[number]\n\ntype InsertMenuEvent =\n  | {type: 'toggle view'; name: InsertMenuView['name']}\n  | {type: 'change query'; query: string}\n  | {type: 'select group'; name: string | undefined}\n\ntype InsertMenuState = {\n  query: string\n  groups: Array<InsertMenuGroup>\n  views: Array<InsertMenuViews[number] & {selected: boolean}>\n}\n\nfunction fullInsertMenuReducer(state: InsertMenuState, event: InsertMenuEvent): InsertMenuState {\n  return {\n    query: event.type === 'change query' ? event.query : state.query,\n    groups:\n      event.type === 'select group'\n        ? state.groups.map((group) => ({...group, selected: event.name === group.name}))\n        : state.groups,\n    views:\n      event.type === 'toggle view'\n        ? state.views.map((view) => ({...view, selected: event.name === view.name}))\n        : state.views,\n  }\n}\n\nconst ALL_ITEMS_GROUP_NAME = 'all-items'\n\nconst gridStyle: CSSProperties = {\n  gridTemplateColumns: 'repeat(auto-fill, minmax(118px, 1fr))',\n  alignItems: 'start',\n}\n\n/** @alpha */\nexport type InsertMenuProps = InsertMenuOptions & {\n  schemaTypes: Array<SchemaType>\n  onSelect: (schemaType: SchemaType) => void\n  labels: {\n    'insert-menu.filter.all-items': string\n    'insert-menu.search.no-results': string\n    'insert-menu.search.placeholder': string\n    'insert-menu.toggle-grid-view.tooltip': string\n    'insert-menu.toggle-list-view.tooltip': string\n  }\n}\n\n/** @alpha */\nexport function InsertMenu(props: InsertMenuProps): React.JSX.Element {\n  const showIcons = props.showIcons === undefined ? true : props.showIcons\n  const showFilter =\n    props.filter === undefined || props.filter === 'auto'\n      ? props.schemaTypes.length > 5\n      : props.filter\n  const [state, send] = useReducer(fullInsertMenuReducer, {\n    query: '',\n    groups: props.groups\n      ? [\n          {\n            name: ALL_ITEMS_GROUP_NAME,\n            title: props.labels['insert-menu.filter.all-items'],\n            selected: true,\n          },\n          ...props.groups.map((group) => ({...group, selected: false})),\n        ]\n      : [],\n    views: (props.views ?? [{name: 'list'}]).map((view, index) => ({\n      ...view,\n      selected: index === 0,\n    })),\n  })\n  const filteredSchemaTypes = filterSchemaTypes(props.schemaTypes, state.query, state.groups)\n  const selectedView = state.views.find((view) => view.selected)\n  const showingFilterOrViews = showFilter || state.views.length > 1\n  const showingTabs = state.groups && state.groups.length > 0\n  const showingAnyOptions = showingFilterOrViews || showingTabs\n\n  return (\n    <Menu padding={0}>\n      <Flex direction=\"column\" height=\"fill\">\n        <Box\n          {...(showingAnyOptions\n            ? {\n                style: {borderBottom: '1px solid var(--card-border-color)'},\n                paddingBottom: 1,\n              }\n            : {})}\n        >\n          {/* filter and views button */}\n          {showingFilterOrViews ? (\n            <Flex flex=\"none\" align=\"center\" paddingTop={1} paddingX={1} gap={1}>\n              {showFilter ? (\n                <Box flex={1}>\n                  <TextInput\n                    autoFocus\n                    border={false}\n                    fontSize={1}\n                    icon={SearchIcon}\n                    onChange={(event: ChangeEvent<HTMLInputElement>) => {\n                      send({type: 'change query', query: event.target.value})\n                    }}\n                    placeholder={props.labels['insert-menu.search.placeholder']}\n                    value={state.query}\n                  />\n                </Box>\n              ) : null}\n              {state.views.length > 1 ? (\n                <Box flex=\"none\">\n                  <ViewToggle\n                    views={state.views}\n                    onToggle={(name) => {\n                      send({type: 'toggle view', name})\n                    }}\n                    labels={props.labels}\n                  />\n                </Box>\n              ) : null}\n            </Flex>\n          ) : null}\n\n          {/* tabs */}\n          {showingTabs ? (\n            <Box paddingTop={1} paddingX={1}>\n              <TabList space={1}>\n                {state.groups.map((group) => (\n                  <Tab\n                    id={`${group.name}-tab`}\n                    aria-controls={`${group.name}-panel`}\n                    key={group.name}\n                    label={group.title ?? startCase(group.name)}\n                    selected={group.selected}\n                    onClick={() => {\n                      send({type: 'select group', name: group.name})\n                    }}\n                  />\n                ))}\n              </TabList>\n            </Box>\n          ) : null}\n        </Box>\n\n        {/* results */}\n        <Box padding={1}>\n          {filteredSchemaTypes.length === 0 ? (\n            <Box padding={2}>\n              <Text muted size={1}>\n                {props.labels['insert-menu.search.no-results']}\n              </Text>\n            </Box>\n          ) : !selectedView ? null : selectedView.name === 'grid' ? (\n            <Grid autoRows=\"auto\" flex={1} gap={1} style={gridStyle}>\n              {filteredSchemaTypes.map((schemaType) => (\n                <GridMenuItem\n                  key={schemaType.name}\n                  icon={showIcons ? getSchemaTypeIcon(schemaType) : undefined}\n                  onClick={() => {\n                    props.onSelect(schemaType)\n                  }}\n                  previewImageUrl={selectedView.previewImageUrl?.(schemaType.name)}\n                  schemaType={schemaType}\n                />\n              ))}\n            </Grid>\n          ) : (\n            <Stack flex={1} space={1}>\n              {filteredSchemaTypes.map((schemaType) => (\n                <MenuItem\n                  key={schemaType.name}\n                  icon={showIcons ? getSchemaTypeIcon(schemaType) : undefined}\n                  onClick={() => {\n                    props.onSelect(schemaType)\n                  }}\n                  text={schemaType.title ?? startCase(schemaType.name)}\n                />\n              ))}\n            </Stack>\n          )}\n        </Box>\n      </Flex>\n    </Menu>\n  )\n}\n\nconst viewToggleIcon: Record<InsertMenuView['name'], React.ElementType> = {\n  grid: ThLargeIcon,\n  list: UlistIcon,\n}\n\nconst viewToggleTooltip: Record<InsertMenuView['name'], keyof ViewToggleProps['labels']> = {\n  grid: 'insert-menu.toggle-grid-view.tooltip',\n  list: 'insert-menu.toggle-list-view.tooltip',\n}\n\ntype ViewToggleProps = {\n  views: InsertMenuState['views']\n  onToggle: (viewName: InsertMenuView['name']) => void\n  labels: Pick<\n    InsertMenuProps['labels'],\n    'insert-menu.toggle-grid-view.tooltip' | 'insert-menu.toggle-list-view.tooltip'\n  >\n}\n\nfunction ViewToggle(props: ViewToggleProps) {\n  const viewIndex = props.views.findIndex((view) => view.selected)\n  const nextView = props.views[viewIndex + 1] ?? props.views[0]!\n\n  return (\n    <Tooltip\n      content={<Text size={1}>{props.labels[viewToggleTooltip[nextView.name]]}</Text>}\n      placement=\"top\"\n      portal\n    >\n      <Button\n        mode=\"bleed\"\n        icon={viewToggleIcon[nextView.name]}\n        onClick={() => {\n          props.onToggle(nextView.name)\n        }}\n      />\n    </Tooltip>\n  )\n}\n\ntype GridMenuItemProps = {\n  onClick: () => void\n  schemaType: SchemaType\n  icon: MenuItemProps['icon']\n  previewImageUrl: ReturnType<\n    NonNullable<\n      Extract<NonNullable<InsertMenuOptions['views']>[number], {name: 'grid'}>['previewImageUrl']\n    >\n  >\n}\n\nfunction GridMenuItem(props: GridMenuItemProps) {\n  const [failedToLoad, setFailedToLoad] = useState(false)\n  const Icon = props.icon\n\n  return (\n    <MenuItem padding={0} radius={2} onClick={props.onClick} style={{overflow: 'hidden'}}>\n      <Flex direction=\"column\" gap={1} padding={1}>\n        <Box\n          flex=\"none\"\n          style={{\n            backgroundColor: 'var(--card-muted-bg-color)',\n            paddingBottom: '66.6%',\n            position: 'relative',\n          }}\n        >\n          {isValidElementType(Icon) ? (\n            <Flex\n              align=\"center\"\n              justify=\"center\"\n              style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100%',\n                height: '100%',\n              }}\n            >\n              <Text size={1}>\n                <Icon />\n              </Text>\n            </Flex>\n          ) : null}\n          {!props.previewImageUrl || failedToLoad ? null : (\n            <img\n              src={props.previewImageUrl}\n              style={{\n                objectFit: 'contain',\n                width: '100%',\n                height: '100%',\n                position: 'absolute',\n                inset: 0,\n              }}\n              onError={() => {\n                setFailedToLoad(true)\n              }}\n            />\n          )}\n\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              width: '100%',\n              height: '100%',\n              boxShadow: 'inset 0 0 0 0.5px var(--card-fg-color)',\n              opacity: 0.1,\n            }}\n          />\n        </Box>\n        <Box flex={1} padding={2}>\n          <Text size={1} weight=\"medium\">\n            {props.schemaType.title ?? props.schemaType.name}\n          </Text>\n        </Box>\n      </Flex>\n    </MenuItem>\n  )\n}\n\nfunction filterSchemaTypes(\n  schemaTypes: Array<SchemaType>,\n  query: string,\n  groups: Array<InsertMenuGroup>,\n) {\n  return schemaTypes.filter(\n    (schemaType) => passesGroupFilter(schemaType, groups) && passesQueryFilter(schemaType, query),\n  )\n}\n\nfunction passesQueryFilter(schemaType: SchemaType, query: string) {\n  const sanitizedQuery = query.trim().toLowerCase()\n\n  return schemaType.title\n    ? schemaType.title?.toLowerCase().includes(sanitizedQuery)\n    : schemaType.name.includes(sanitizedQuery)\n}\n\nfunction passesGroupFilter(schemaType: SchemaType, groups: Array<InsertMenuGroup>) {\n  const selectedGroup = groups.find((group) => group.selected)\n\n  return selectedGroup\n    ? selectedGroup.name === ALL_ITEMS_GROUP_NAME\n      ? true\n      : selectedGroup.of?.includes(schemaType.name)\n    : true\n}\n"], "names": ["getSchemaTypeIcon", "schemaType", "referenceIcon", "isReferenceSchemaType", "to", "length", "icon", "undefined", "type", "isRecord", "value", "fullInsertMenuReducer", "state", "event", "query", "groups", "map", "group", "selected", "name", "views", "view", "ALL_ITEMS_GROUP_NAME", "gridStyle", "gridTemplateColumns", "alignItems", "InsertMenu", "props", "$", "_c", "showIcons", "showFilter", "filter", "schemaTypes", "t0", "labels", "title", "_temp", "t1", "t2", "_temp2", "t3", "send", "useReducer", "T0", "T1", "T2", "t4", "t5", "t6", "t7", "t8", "t9", "filteredSchemaTypes", "filterSchemaTypes", "<PERSON><PERSON><PERSON><PERSON>", "find", "_temp3", "showingFilterOrViews", "showingTabs", "showingAnyOptions", "<PERSON><PERSON>", "Flex", "t10", "style", "borderBottom", "paddingBottom", "t11", "jsx", "Box", "TextInput", "SearchIcon", "target", "t12", "TabList", "group_0", "Tab", "startCase", "jsxs", "Text", "Grid", "onSelect", "previewImageUrl", "<PERSON><PERSON>", "schemaType_0", "MenuItem", "view_0", "index", "viewToggleIcon", "grid", "ThLargeIcon", "list", "UlistIcon", "viewToggleTooltip", "ViewToggle", "viewIndex", "findIndex", "_temp4", "next<PERSON>iew", "onToggle", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "GridMenuItem", "failedToLoad", "setFailedToLoad", "useState", "Icon", "Symbol", "for", "overflow", "backgroundColor", "position", "isValidElementType", "top", "left", "width", "height", "objectFit", "inset", "boxShadow", "opacity", "onClick", "passesGroupFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sanitized<PERSON>uery", "trim", "toLowerCase", "includes", "selectedGroup", "of"], "mappings": ";;;;;;;AAIO,SAASA,kBAAkBC,YAAmD;AAEnF,QAAMC,gBACJC,sBAAsBF,UAAU,MAAMA,WAAWG,MAAM,CAAA,GAAIC,WAAW,IAClEJ,WAAWG,GAAG,CAAC,EAAGE,OAClBC;AAEN,SAAON,WAAWK,QAAQL,WAAWO,MAAMF,QAAQJ;AACrD;AAEA,SAASC,sBAAsBK,MAA4C;AACzE,SAAOC,SAASD,IAAI,MAAMA,KAAK,SAAY,eAAeL,sBAAsBK,KAAK,IAAO;AAC9F;AAEA,SAASC,SAASC,OAAkD;AAClE,SAAO,CAAC,CAACA,UAAU,OAAOA,SAAS,YAAY,OAAOA,SAAS;AACjE;ACkBA,SAASC,sBAAsBC,OAAwBC,OAAyC;AAC9F,SAAO;AAAA,IACLC,OAAOD,MAAML,SAAS,iBAAiBK,MAAMC,QAAQF,MAAME;AAAAA,IAC3DC,QACEF,MAAML,SAAS,iBACXI,MAAMG,OAAOC,IAAKC,CAAAA,WAAW;AAAA,MAAC,GAAGA;AAAAA,MAAOC,UAAUL,MAAMM,SAASF,MAAME;AAAAA,IAAAA,EAAM,IAC7EP,MAAMG;AAAAA,IACZK,OACEP,MAAML,SAAS,gBACXI,MAAMQ,MAAMJ,IAAKK,CAAAA,UAAU;AAAA,MAAC,GAAGA;AAAAA,MAAMH,UAAUL,MAAMM,SAASE,KAAKF;AAAAA,IAAAA,EAAM,IACzEP,MAAMQ;AAAAA,EAAAA;AAEhB;AAEA,MAAME,uBAAuB,aAEvBC,YAA2B;AAAA,EAC/BC,qBAAqB;AAAA,EACrBC,YAAY;AACd;AAgBO,SAAAC,WAAAC,OAAA;AAAA,QAAAC,IAAAC,qBAAAA,EAAA,EAAA,GACLC,YAAkBH,MAAKG,cAAAvB,SAAwB,KAAUoB,MAAKG,WAC9DC,aACEJ,MAAKK,WAAAzB,UAAyBoB,MAAKK,WAAY,SAC3CL,MAAKM,YAAA5B,SAAA,IACLsB,MAAKK;AAAO,MAAAE;AAAAN,IAAA,CAAA,MAAAD,MAAAZ,UAAAa,EAAA,CAAA,MAAAD,MAAAQ,UAGRD,KAAAP,MAAKZ,SAAA,CAAA;AAAA,IAAAI,MAAAG;AAAAA,IAAAc,OAIET,MAAKQ,OAAQ,8BAA8B;AAAA,IAAAjB,UAAA;AAAA,EAAA,GAAA,GAGjDS,MAAKZ,OAAAC,IAAAqB,KAAoD,CAAC,IAAA,CAAA,GAE7DT,EAAA,CAAA,IAAAD,MAAAZ,QAAAa,EAAA,CAAA,IAAAD,MAAAQ,QAAAP,OAAAM,MAAAA,KAAAN,EAAA,CAAA;AAAA,MAAAU;AAAAV,IAAA,CAAA,MAAAD,MAAAP,SACEkB,KAAAX,MAAKP,SAAA,CAAA;AAAA,IAAAD,MAAkB;AAAA,EAAA,CAAM,GAAES,EAAA,CAAA,IAAAD,MAAAP,OAAAQ,OAAAU,MAAAA,KAAAV,EAAA,CAAA;AAAA,MAAAW;AAAAX,WAAAU,MAAhCC,KAACD,GAA+BtB,IAAAwB,MAGrC,GAACZ,OAAAU,IAAAV,OAAAW,MAAAA,KAAAX,EAAA,CAAA;AAAA,MAAAa;AAAAb,IAAA,CAAA,MAAAM,MAAAN,SAAAW,MAfmDE,KAAA;AAAA,IAAA3B,OAC/C;AAAA,IAAEC,QACDmB;AAAAA,IASFd,OACCmB;AAAAA,EAAAA,GAIRX,OAAAM,IAAAN,OAAAW,IAAAX,OAAAa,MAAAA,KAAAb,EAAA,CAAA;AAhBD,QAAA,CAAAhB,OAAA8B,IAAA,IAAsBC,MAAAA,WAAAhC,uBAAkC8B,EAgBvD;AAAC,MAAAG,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC;AAAA,MAAAxB,EAAA,EAAA,MAAAD,SAAAC,EAAA,EAAA,MAAAG,cAAAH,EAAA,EAAA,MAAAE,aAAAF,EAAA,EAAA,MAAAhB,MAAAG,UAAAa,EAAA,EAAA,MAAAhB,MAAAE,SAAAc,EAAA,EAAA,MAAAhB,MAAAQ,OAAA;AACF,UAAAiC,sBAA4BC,kBAAkB3B,MAAKM,aAAcrB,MAAKE,OAAQF,MAAKG,MAAO,GAC1FwC,eAAqB3C,MAAKQ,MAAAoC,KAAAC,MAAmC,GAC7DC,uBAA6B3B,cAAcnB,MAAKQ,MAAAf,SAAA,GAChDsD,cAAoB/C,MAAKG,UAAWH,MAAKG,OAAAV,SAAA,GACzCuD,oBAA0BF,wBAAwBC;AAG/Cb,SAAAe,eAAAA,MAAcT,KAAA,GACZP,KAAAiB,eAAAA,MAAeb,KAAA,UAAgBC,KAAA;AAAM,QAAAa;AAAAnC,cAAAgC,qBAE7BG,OAAAH,oBAAiB;AAAA,MAAAI,OAAA;AAAA,QAAAC,cAEM;AAAA,MAAA;AAAA,MAAoCC,eAAA;AAAA,IAAA,IAAA,IAG1DtC,QAAAgC,mBAAAhC,QAAAmC,QAAAA,OAAAnC,EAAA,EAAA;AAAA,QAAAuC;AAAAvC,MAAA,EAAA,MAAAD,MAAAQ,UAAAP,EAAA,EAAA,MAAAG,cAAAH,EAAA,EAAA,MAAA8B,wBAAA9B,EAAA,EAAA,MAAAhB,MAAAE,SAAAc,EAAA,EAAA,MAAAhB,MAAAQ,SAGL+C,OAAAT,uDACEI,eAAAA,MAAA,EAAU,MAAA,QAAa,OAAA,UAAqB,YAAA,GAAa,UAAA,GAAQ,KAAA,GAC/D/B,UAAAA;AAAAA,MAAAA,aACCqC,2BAAAA,IAACC,eAAAA,KAAA,EAAU,MAAA,GACT,yCAACC,eAAAA,WAAA,EACC,WAAA,IACQ,QAAA,IACE,UAAA,GACJC,MAAAA,MAAAA,YACI,UAAA1D,CAAAA,UAAA;AACR6B,aAAI;AAAA,UAAAlC,MAAQ;AAAA,UAAcM,OAASD,MAAK2D,OAAA9D;AAAAA,QAAAA,CAAc;AAAA,MAAC,GAE5C,aAAAiB,MAAKQ,OAAQ,gCAAgC,GACnD,OAAAvB,MAAKE,OAAM,EAAA,CAEtB,IAAM;AAAA,MAEPF,MAAKQ,MAAAf,SAAA,mCACHgE,eAAAA,KAAA,EAAS,MAAA,QACR,UAAAD,2BAAAA,IAAC,YAAA,EACQ,OAAAxD,MAAKQ,OACF,UAAAD,CAAAA,SAAA;AACRuB,aAAI;AAAA,UAAAlC,MAAQ;AAAA,UAAaW;AAAAA,QAAAA,CAAO;AAAA,MAAC,GAE3B,QAAAQ,MAAKQ,OAAAA,CAAO,GAExB,IAAM;AAAA,IAAA,EAAA,CAEV,IAAO,MACDP,EAAA,EAAA,IAAAD,MAAAQ,QAAAP,QAAAG,YAAAH,QAAA8B,sBAAA9B,EAAA,EAAA,IAAAhB,MAAAE,OAAAc,EAAA,EAAA,IAAAhB,MAAAQ,OAAAQ,QAAAuC,QAAAA,OAAAvC,EAAA,EAAA;AAAA,QAAA6C;AAAA7C,cAAA+B,eAAA/B,EAAA,EAAA,MAAAhB,MAAAG,UAGP0D,OAAAd,6CACEU,eAAAA,KAAA,EAAgB,YAAA,GAAa,UAAA,GAC5B,yCAACK,wBAAA,EAAe,OAAA,GACb9D,UAAAA,MAAKG,OAAAC,IAAA2D,aACJP,2BAAAA,IAACQ,oBAAA,EACK,OAAG3D,QAAKE,IAAA,QACG,iBAAA,GAAGF,QAAKE,IAAA,UAEhB,OAAAF,QAAKmB,SAAUyC,mBAAAA,QAAU5D,QAAKE,IAAK,GAChC,UAAAF,QAAKC,UACN,SAAA,MAAA;AACPwB,WAAI;AAAA,QAAAlC,MAAQ;AAAA,QAAcW,MAAQF,QAAKE;AAAAA,MAAAA,CAAM;AAAA,IAAC,KAJ3CF,QAAKE,KAOb,EAAA,CACH,GACF,IAAM,MACAS,QAAA+B,aAAA/B,EAAA,EAAA,IAAAhB,MAAAG,QAAAa,QAAA6C,QAAAA,OAAA7C,EAAA,EAAA,GAAAA,EAAA,EAAA,MAAAmC,QAAAnC,UAAAuC,QAAAvC,EAAA,EAAA,MAAA6C,QA1DVtB,KAAA2B,2BAAAA,KAACT,oBAAA,EAAG,GACGN,MAQJI,UAAAA;AAAAA,MAAAA;AAAAA,MAgCAM;AAAAA,IAAAA,EAAAA,CAkBH,GAAM7C,QAAAmC,MAAAnC,QAAAuC,MAAAvC,QAAA6C,MAAA7C,QAAAuB,MAAAA,KAAAvB,EAAA,EAAA,GAGLgB,KAAAyB,eAAAA,KAAatB,KAAA,GACXC,KAAAK,oBAAmBhD,eAClB+D,2BAAAA,IAACC,eAAAA,KAAA,EAAa,YACZ,UAAAD,+BAACW,eAAAA,MAAA,EAAK,OAAA,IAAY,MAAA,GACfpD,UAAAA,MAAKQ,OAAQ,+BAA+B,EAAA,CAC/C,EAAA,CACF,IACGoB,eAAsBA,aAAYpC,SAAU,SAC/CiD,2BAAAA,IAACY,eAAAA,MAAA,EAAc,UAAA,QAAa,MAAA,GAAQ,KAAA,GAAUzD,OAAAA,WAC3C8B,UAAAA,oBAAmBrC,IAAAf,CAAAA,eAClBmE,2BAAAA,IAAC,cAAA,EAEO,MAAAtC,YAAY9B,kBAAkBC,UAAU,IAACM,QACtC,SAAA,MAAA;AACPoB,YAAKsD,SAAUhF,UAAU;AAAA,IAAC,GAEX,iBAAAsD,aAAY2B,kBAAmBjF,WAAUkB,IAAA,GAC9ClB,WAAAA,GANPA,WAAUkB,IAMO,CAEzB,EAAA,CACH,IAEAiD,2BAAAA,IAACe,eAAAA,OAAA,EAAY,MAAA,GAAU,OAAA,GACpB9B,UAAAA,oBAAmBrC,IAAAoE,kBAClBhB,+BAACiB,eAAAA,UAAA,EAEO,MAAAvD,YAAY9B,kBAAkBC,YAAU,IAACM,QACtC,SAAA,MAAA;AACPoB,YAAKsD,SAAUhF,YAAU;AAAA,IAAC,GAEtB,MAAAA,aAAUmC,SAAUyC,mBAAAA,QAAU5E,aAAUkB,IAAK,EAAA,GAL9ClB,aAAUkB,KAOlB,EAAA,CACH,IA1Be,MA2BhBS,QAAAD,OAAAC,QAAAG,YAAAH,QAAAE,WAAAF,EAAA,EAAA,IAAAhB,MAAAG,QAAAa,EAAA,EAAA,IAAAhB,MAAAE,OAAAc,EAAA,EAAA,IAAAhB,MAAAQ,OAAAQ,QAAAgB,IAAAhB,QAAAiB,IAAAjB,QAAAkB,IAAAlB,QAAAmB,IAAAnB,QAAAoB,IAAApB,QAAAqB,IAAArB,QAAAsB,IAAAtB,QAAAuB,IAAAvB,QAAAwB;AAAAA,EAAA;AAAAR,SAAAhB,EAAA,EAAA,GAAAiB,KAAAjB,EAAA,EAAA,GAAAkB,KAAAlB,EAAA,EAAA,GAAAmB,KAAAnB,EAAA,EAAA,GAAAoB,KAAApB,EAAA,EAAA,GAAAqB,KAAArB,EAAA,EAAA,GAAAsB,KAAAtB,EAAA,EAAA,GAAAuB,KAAAvB,EAAA,EAAA,GAAAwB,KAAAxB,EAAA,EAAA;AAAA,MAAAmC;AAAAnC,IAAA,EAAA,MAAAgB,MAAAhB,UAAAmB,MAAAnB,EAAA,EAAA,MAAAoB,MAlCHe,MAAAK,2BAAAA,IAAC,IAAA,EAAa,SAAArB,IACXC,UAAAA,GAAAA,CAkCH,GAAMpB,QAAAgB,IAAAhB,QAAAmB,IAAAnB,QAAAoB,IAAApB,QAAAmC,OAAAA,MAAAnC,EAAA,EAAA;AAAA,MAAAuC;AAAAvC,IAAA,EAAA,MAAAiB,MAAAjB,EAAA,EAAA,MAAAmC,OAAAnC,EAAA,EAAA,MAAAqB,MAAArB,EAAA,EAAA,MAAAsB,MAAAtB,UAAAuB,MAlGRgB,MAAAW,2BAAAA,KAAC,IAAA,EAAe,WAAA7B,IAAgB,QAAAC,IAC9BC,UAAAA;AAAAA,IAAAA;AAAAA,IA8DAY;AAAAA,EAAAA,EAAAA,CAoCF,GAAOnC,QAAAiB,IAAAjB,QAAAmC,KAAAnC,QAAAqB,IAAArB,QAAAsB,IAAAtB,QAAAuB,IAAAvB,QAAAuC,OAAAA,MAAAvC,EAAA,EAAA;AAAA,MAAA6C;AAAA,SAAA7C,EAAA,EAAA,MAAAkB,MAAAlB,UAAAuC,OAAAvC,EAAA,EAAA,MAAAwB,MApGTqB,qCAAC,IAAA,EAAc,SAAArB,IACbe,UAAAA,IAAAA,CAoGF,GAAOvC,QAAAkB,IAAAlB,QAAAuC,KAAAvC,QAAAwB,IAAAxB,QAAA6C,OAAAA,MAAA7C,EAAA,EAAA,GArGP6C;AAqGO;AAnIJ,SAAAhB,OAAA6B,QAAA;AAAA,SAwB2CjE,OAAIH;AAAA;AAxB/C,SAAAsB,OAAAnB,MAAAkE,OAAA;AAAA,SAAA;AAAA,IAAA,GAmBElE;AAAAA,IAAIH,UACGqE,UAAK;AAAA,EAAA;AAAM;AApBpB,SAAAlD,MAAApB,OAAA;AAAA,SAAA;AAAA,IAAA,GAeuCA;AAAAA,IAAKC,UAAA;AAAA,EAAA;AAAA;AAwHnD,MAAMsE,iBAAoE;AAAA,EACxEC,MAAMC,MAAAA;AAAAA,EACNC,MAAMC,MAAAA;AACR,GAEMC,oBAAqF;AAAA,EACzFJ,MAAM;AAAA,EACNE,MAAM;AACR;AAWA,SAAAG,WAAAnE,OAAA;AAAA,QAAAC,IAAAC,qBAAAA,EAAA,EAAA,GACEkE,YAAkBpE,MAAKP,MAAA4E,UAAAC,MAAwC,GAC/DC,WAAiBvE,MAAKP,MAAO2E,YAAS,CAAI,KAAKpE,MAAKP,MAAA,CAAA,GAIvBc,KAAAP,MAAKQ,OAAA0D,kBAA0BK,SAAQ/E,IAAA,CAAA;AAAO,MAAAmB;AAAAV,WAAAM,MAA9DI,KAAA8B,+BAACW,eAAAA,MAAA,EAAW,MAAA,GAAI7C,UAAAA,IAA+C,GAAON,OAAAM,IAAAN,OAAAU,MAAAA,KAAAV,EAAA,CAAA;AAMvE,QAAAW,KAAAiD,eAAeU,SAAQ/E,IAAA;AAAM,MAAAsB;AAAAb,WAAAsE,SAAA/E,QAAAS,SAAAD,SAC1Bc,KAAAA,MAAA;AACPd,UAAKwE,SAAUD,SAAQ/E,IAAK;AAAA,EAAC,GAC9BS,EAAA,CAAA,IAAAsE,SAAA/E,MAAAS,OAAAD,OAAAC,OAAAa,MAAAA,KAAAb,EAAA,CAAA;AAAA,MAAAmB;AAAAnB,IAAA,CAAA,MAAAW,MAAAX,SAAAa,MALHM,oCAACqD,eAAAA,QAAA,EACM,MAAA,SACC,MAAA7D,IACG,SAAAE,GAAAA,CAER,GACDb,OAAAW,IAAAX,OAAAa,IAAAb,OAAAmB,MAAAA,KAAAnB,EAAA,CAAA;AAAA,MAAAoB;AAAA,SAAApB,EAAA,CAAA,MAAAU,MAAAV,SAAAmB,MAXJC,oCAACqD,eAAAA,SAAA,EACU,SAAA/D,IACC,WAAA,OACV,QAAA,IAEAS,UAAAA,GAAAA,CAOF,GAAUnB,OAAAU,IAAAV,OAAAmB,IAAAnB,QAAAoB,MAAAA,KAAApB,EAAA,EAAA,GAZVoB;AAYU;AAjBd,SAAAiD,OAAA5E,MAAA;AAAA,SACoDA,KAAIH;AAAA;AA+BxD,SAAAoF,aAAA3E,OAAA;AAAA,QAAAC,IAAAC,qBAAAA,EAAA,EAAA,GACE,CAAA0E,cAAAC,eAAA,IAAwCC,MAAAA,WAAc,GACtDC,OAAa/E,MAAKrB;AAAK,MAAA4B;AAAAN,IAAA,CAAA,MAAA+E,OAAAC,IAAA,2BAAA,KAG2C1E,KAAA;AAAA,IAAA2E,UAAW;AAAA,EAAA,GAASjF,OAAAM,MAAAA,KAAAN,EAAA,CAAA;AAAA,MAAAU;AAAAV,IAAA,CAAA,MAAA+E,OAAAC,IAAA,2BAAA,KAIvEtE,KAAA;AAAA,IAAAwE,iBACY;AAAA,IAA4B5C,eAC9B;AAAA,IAAO6C,UACZ;AAAA,EAAA,GACXnF,OAAAU,MAAAA,KAAAV,EAAA,CAAA;AAAA,MAAAW;AAAAX,WAAA8E,QAEAnE,KAAAyE,QAAAA,mBAAmBN,IAAI,IACtBtC,2BAAAA,IAACN,eAAAA,MAAA,EACO,OAAA,UACE,SAAA,UACD,OAAA;AAAA,IAAAiD,UACK;AAAA,IAAUE,KAAA;AAAA,IAAAC,MAAA;AAAA,IAAAC,OAGb;AAAA,IAAMC,QACL;AAAA,EAAA,GAGV,yCAACrC,eAAAA,MAAA,EAAW,MAAA,GACV,UAAAX,2BAAAA,IAAC,MAAA,CAAA,IACH,EAAA,CACF,IAAO,MACDxC,OAAA8E,MAAA9E,OAAAW,MAAAA,KAAAX,EAAA,CAAA;AAAA,MAAAa;AAAAb,WAAA2E,gBAAA3E,EAAA,CAAA,MAAAD,MAAAuD,mBACPzC,MAACd,MAAKuD,mBAAoBqB,sBACzBnC,+BAAA,SACO,KAAAzC,MAAKuD,iBACH,OAAA;AAAA,IAAAmC,WACM;AAAA,IAASF,OACb;AAAA,IAAMC,QACL;AAAA,IAAML,UACJ;AAAA,IAAUO,OAAA;AAAA,EAAA,GAGb,SAAA,MAAA;AACPd,sBAAoB;AAAA,EAAC,GACtB,GAEJ5E,OAAA2E,cAAA3E,EAAA,CAAA,IAAAD,MAAAuD,iBAAAtD,OAAAa,MAAAA,KAAAb,EAAA,CAAA;AAAA,MAAAmB;AAAAnB,IAAA,CAAA,MAAA+E,OAAAC,IAAA,2BAAA,KAED7D,KAAAqB,2BAAAA,IAAA,OAAA,EACS,OAAA;AAAA,IAAA2C,UACK;AAAA,IAAUE,KAAA;AAAA,IAAAC,MAAA;AAAA,IAAAC,OAGb;AAAA,IAAMC,QACL;AAAA,IAAMG,WACH;AAAA,IAAwCC,SAAA;AAAA,EAAA,EAAA,CAEpD,GACD5F,OAAAmB,MAAAA,KAAAnB,EAAA,CAAA;AAAA,MAAAoB;AAAApB,IAAA,CAAA,MAAAW,MAAAX,SAAAa,MAnDJO,KAAA8B,2BAAAA,KAACT,eAAAA,KAAA,EACM,MAAA,QACE,OAAA/B,IAMNC,UAAAA;AAAAA,IAAAA;AAAAA,IAiBAE;AAAAA,IAgBDM;AAAAA,EAAAA,EAAAA,CAWF,GAAMnB,OAAAW,IAAAX,OAAAa,IAAAb,QAAAoB,MAAAA,KAAApB,EAAA,EAAA;AAGD,QAAAqB,KAAAtB,MAAK1B,WAAAmC,SAAqBT,MAAK1B,WAAAkB;AAAgB,MAAA+B;AAAAtB,YAAAqB,MAFpDC,KAAAkB,2BAAAA,IAACC,eAAAA,KAAA,EAAU,MAAA,GAAY,SAAA,GACrB,UAAAD,2BAAAA,IAACW,eAAAA,QAAW,MAAA,GAAU,QAAA,UACnB9B,UAAAA,GAAAA,CACH,EAAA,CACF,GAAMrB,QAAAqB,IAAArB,QAAAsB,MAAAA,KAAAtB,EAAA,EAAA;AAAA,MAAAuB;AAAAvB,IAAA,EAAA,MAAAoB,MAAApB,UAAAsB,MA1DRC,KAAA2B,2BAAAA,KAAChB,eAAAA,QAAe,WAAA,UAAc,KAAA,GAAY,SAAA,GACxCd,UAAAA;AAAAA,IAAAA;AAAAA,IAqDAE;AAAAA,EAAAA,EAAAA,CAKF,GAAOtB,QAAAoB,IAAApB,QAAAsB,IAAAtB,QAAAuB,MAAAA,KAAAvB,EAAA,EAAA;AAAA,MAAAwB;AAAA,SAAAxB,UAAAD,MAAA8F,WAAA7F,UAAAuB,MA5DTC,oCAACiC,eAAAA,UAAA,EAAkB,SAAA,GAAW,QAAA,GAAY,SAAA1D,MAAK8F,SAAiB,OAAAvF,IAC9DiB,cA4DF,GAAWvB,EAAA,EAAA,IAAAD,MAAA8F,SAAA7F,QAAAuB,IAAAvB,QAAAwB,MAAAA,KAAAxB,EAAA,EAAA,GA7DXwB;AA6DW;AAIf,SAASE,kBACPrB,aACAnB,OACAC,QACA;AACA,SAAOkB,YAAYD,OAChB/B,CAAAA,eAAeyH,kBAAkBzH,YAAYc,MAAM,KAAK4G,kBAAkB1H,YAAYa,KAAK,CAC9F;AACF;AAEA,SAAS6G,kBAAkB1H,YAAwBa,OAAe;AAChE,QAAM8G,iBAAiB9G,MAAM+G,KAAAA,EAAOC,YAAAA;AAEpC,SAAO7H,WAAWmC,QACdnC,WAAWmC,OAAO0F,YAAAA,EAAcC,SAASH,cAAc,IACvD3H,WAAWkB,KAAK4G,SAASH,cAAc;AAC7C;AAEA,SAASF,kBAAkBzH,YAAwBc,QAAgC;AACjF,QAAMiH,gBAAgBjH,OAAOyC,KAAMvC,CAAAA,UAAUA,MAAMC,QAAQ;AAE3D,SAAO8G,gBACHA,cAAc7G,SAASG,uBACrB,KACA0G,cAAcC,IAAIF,SAAS9H,WAAWkB,IAAI,IAC5C;AACN;;"}