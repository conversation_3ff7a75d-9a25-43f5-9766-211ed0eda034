{"version": 3, "file": "parse.cjs", "sources": ["../../src/path/parser/parse.ts"], "sourcesContent": ["import {type PathElement} from '../types'\nimport {type StringToPath} from './types'\n\nexport function parse<const T extends string>(path: T): StringToPath<T> {\n  return path\n    .split(/[[.\\]]/g)\n    .filter(Boolean)\n    .map(seg => (seg.includes('==') ? parseSegment(seg) : coerce(seg))) as any\n}\n\nconst IS_NUMERIC = /^-?\\d+$/\n\nfunction unquote(str: string) {\n  return str.replace(/^['\"]/, '').replace(/['\"]$/, '')\n}\n\nfunction parseSegment(segment: string): PathElement {\n  const [key, value] = segment.split('==')\n  if (key !== '_key') {\n    throw new Error(\n      `Currently only \"_key\" is supported as path segment. Found ${key}`,\n    )\n  }\n  if (typeof value === 'undefined') {\n    throw new Error('Invalid path segment, expected `key==\"value\"`')\n  }\n  return {_key: unquote(value)}\n}\n\nfunction coerce(segment: string): PathElement {\n  return IS_NUMERIC.test(segment) ? Number(segment) : segment\n}\n"], "names": [], "mappings": ";AAGO,SAAS,MAA8B,MAA0B;AACtE,SAAO,KACJ,MAAM,SAAS,EACf,OAAO,OAAO,EACd,IAAI,CAAA,QAAQ,IAAI,SAAS,IAAI,IAAI,aAAa,GAAG,IAAI,OAAO,GAAG,CAAE;AACtE;AAEA,MAAM,aAAa;AAEnB,SAAS,QAAQ,KAAa;AAC5B,SAAO,IAAI,QAAQ,SAAS,EAAE,EAAE,QAAQ,SAAS,EAAE;AACrD;AAEA,SAAS,aAAa,SAA8B;AAClD,QAAM,CAAC,KAAK,KAAK,IAAI,QAAQ,MAAM,IAAI;AACvC,MAAI,QAAQ;AACV,UAAM,IAAI;AAAA,MACR,6DAA6D,GAAG;AAAA,IAClE;AAEF,MAAI,OAAO,QAAU;AACb,UAAA,IAAI,MAAM,+CAA+C;AAEjE,SAAO,EAAC,MAAM,QAAQ,KAAK,EAAC;AAC9B;AAEA,SAAS,OAAO,SAA8B;AAC5C,SAAO,WAAW,KAAK,OAAO,IAAI,OAAO,OAAO,IAAI;AACtD;;"}