{"version": 3, "file": "index.cjs", "sources": ["../src/portable-text-member-schema-types.ts", "../src/portable-text-member-schema-types-to-schema.ts", "../src/sanity-schema-to-portable-text-schema.ts", "../src/key-generator.ts", "../src/schema-definition-to-portable-text-member-schema-types.ts"], "sourcesContent": ["import type {\n  ArraySchemaType,\n  BlockDecoratorDefinition,\n  BlockListDefinition,\n  BlockSchemaType,\n  BlockStyleDefinition,\n  ObjectSchemaType,\n  PortableTextBlock,\n  SchemaType,\n  SpanSchemaType,\n} from '@sanity/types'\n\n/**\n * @public\n * Sanity-specific schema types for Portable Text.\n */\nexport type PortableTextMemberSchemaTypes = {\n  annotations: (ObjectSchemaType & {i18nTitleKey?: string})[]\n  block: ObjectSchemaType\n  blockObjects: ObjectSchemaType[]\n  decorators: BlockDecoratorDefinition[]\n  inlineObjects: ObjectSchemaType[]\n  portableText: ArraySchemaType<PortableTextBlock>\n  span: ObjectSchemaType\n  styles: BlockStyleDefinition[]\n  lists: BlockListDefinition[]\n}\n\n/**\n * @public\n * Create Sanity-specific schema types for Portable Text from a Sanity array\n * schema type.\n */\nexport function createPortableTextMemberSchemaTypes(\n  portableTextType: ArraySchemaType<PortableTextBlock>,\n): PortableTextMemberSchemaTypes {\n  if (!portableTextType) {\n    throw new Error(\"Parameter 'portabletextType' missing (required)\")\n  }\n  const blockType = portableTextType.of?.find(findBlockType) as\n    | BlockSchemaType\n    | undefined\n  if (!blockType) {\n    throw new Error('Block type is not defined in this schema (required)')\n  }\n  const childrenField = blockType.fields?.find(\n    (field) => field.name === 'children',\n  ) as {type: ArraySchemaType} | undefined\n  if (!childrenField) {\n    throw new Error('Children field for block type found in schema (required)')\n  }\n  const ofType = childrenField.type.of\n  if (!ofType) {\n    throw new Error(\n      'Valid types for block children not found in schema (required)',\n    )\n  }\n  const spanType = ofType.find((memberType) => memberType.name === 'span') as\n    | ObjectSchemaType\n    | undefined\n  if (!spanType) {\n    throw new Error('Span type not found in schema (required)')\n  }\n  const inlineObjectTypes = (ofType.filter(\n    (memberType) => memberType.name !== 'span',\n  ) || []) as ObjectSchemaType[]\n  const blockObjectTypes = (portableTextType.of?.filter(\n    (field) => field.name !== blockType.name,\n  ) || []) as ObjectSchemaType[]\n  return {\n    styles: resolveEnabledStyles(blockType),\n    decorators: resolveEnabledDecorators(spanType),\n    lists: resolveEnabledListItems(blockType),\n    block: blockType,\n    span: spanType,\n    portableText: portableTextType,\n    inlineObjects: inlineObjectTypes,\n    blockObjects: blockObjectTypes,\n    annotations: (spanType as SpanSchemaType).annotations,\n  }\n}\n\nfunction resolveEnabledStyles(blockType: ObjectSchemaType) {\n  const styleField = blockType.fields?.find(\n    (btField) => btField.name === 'style',\n  )\n  if (!styleField) {\n    throw new Error(\n      \"A field with name 'style' is not defined in the block type (required).\",\n    )\n  }\n  const textStyles =\n    styleField.type.options?.list &&\n    styleField.type.options.list?.filter(\n      (style: {value: string}) => style.value,\n    )\n  if (!textStyles || textStyles.length === 0) {\n    throw new Error(\n      'The style fields need at least one style ' +\n        \"defined. I.e: {title: 'Normal', value: 'normal'}.\",\n    )\n  }\n  return textStyles\n}\n\nfunction resolveEnabledDecorators(spanType: ObjectSchemaType) {\n  return (spanType as any).decorators\n}\n\nfunction resolveEnabledListItems(blockType: ObjectSchemaType) {\n  const listField = blockType.fields?.find(\n    (btField) => btField.name === 'listItem',\n  )\n  if (!listField) {\n    throw new Error(\n      \"A field with name 'listItem' is not defined in the block type (required).\",\n    )\n  }\n  const listItems =\n    listField.type.options?.list &&\n    listField.type.options.list.filter((list: {value: string}) => list.value)\n  if (!listItems) {\n    throw new Error('The list field need at least to be an empty array')\n  }\n  return listItems\n}\n\nfunction findBlockType(type: SchemaType): BlockSchemaType | null {\n  if (type.type) {\n    return findBlockType(type.type)\n  }\n\n  if (type.name === 'block') {\n    return type as BlockSchemaType\n  }\n\n  return null\n}\n", "import type {Schema} from '@portabletext/schema'\nimport type {PortableTextMemberSchemaTypes} from './portable-text-member-schema-types'\n\n/**\n * @public\n * Convert Sanity-specific schema types for Portable Text to a first-class\n * Portable Text schema.\n */\nexport function portableTextMemberSchemaTypesToSchema(\n  schema: PortableTextMemberSchemaTypes,\n): Schema {\n  return {\n    annotations: schema.annotations.map((annotation) => ({\n      name: annotation.name,\n      fields: annotation.fields.map((field) => ({\n        name: field.name,\n        type: field.type.jsonType,\n        title: field.type.title,\n      })),\n      title: annotation.title,\n    })),\n    block: {\n      name: schema.block.name,\n    },\n    blockObjects: schema.blockObjects.map((blockObject) => ({\n      name: blockObject.name,\n      fields: blockObject.fields.map((field) => ({\n        name: field.name,\n        type: field.type.jsonType,\n        title: field.type.title,\n      })),\n      title: blockObject.title,\n    })),\n    decorators: schema.decorators.map((decorator) => ({\n      name: decorator.value,\n      title: decorator.title,\n      value: decorator.value,\n    })),\n    inlineObjects: schema.inlineObjects.map((inlineObject) => ({\n      name: inlineObject.name,\n      fields: inlineObject.fields.map((field) => ({\n        name: field.name,\n        type: field.type.jsonType,\n        title: field.type.title,\n      })),\n      title: inlineObject.title,\n    })),\n    span: {\n      name: schema.span.name,\n    },\n    styles: schema.styles.map((style) => ({\n      name: style.value,\n      title: style.title,\n      value: style.value,\n    })),\n    lists: schema.lists.map((list) => ({\n      name: list.value,\n      title: list.title,\n      value: list.value,\n    })),\n  }\n}\n", "import type {Schema} from '@portabletext/schema'\nimport {Schema as SanitySchema} from '@sanity/schema'\nimport type {ArrayDefinition, ArraySchemaType} from '@sanity/types'\nimport {createPortableTextMemberSchemaTypes} from './portable-text-member-schema-types'\nimport {portableTextMemberSchemaTypesToSchema} from './portable-text-member-schema-types-to-schema'\n\n/**\n * @public\n * Compile a Sanity schema to a Portable Text `Schema`.\n *\n * A Portable Text `Schema` is compatible with a Portable Text\n * `SchemaDefinition` and can be used as configuration for the Portable Text\n * Editor.\n *\n * @example\n * ```tsx\n * const schema = sanitySchemaToPortableTextSchema(sanitySchema)\n *\n * return (\n *   <EditorProvider\n *     initialConfig={{\n *       // ...\n *       schemaDefinition: schema,\n *     }}\n *   >\n *     // ...\n *   </EditorProvider>\n * ```\n */\nexport function sanitySchemaToPortableTextSchema(\n  sanitySchema: ArraySchemaType<unknown> | ArrayDefinition,\n): Schema {\n  const portableTextMemberSchemaTypes = createPortableTextMemberSchemaTypes(\n    sanitySchema.hasOwnProperty('jsonType')\n      ? sanitySchema\n      : compileType(sanitySchema),\n  )\n\n  return portableTextMemberSchemaTypesToSchema(portableTextMemberSchemaTypes)\n}\n\nfunction compileType(rawType: any) {\n  return SanitySchema.compile({\n    name: 'blockTypeSchema',\n    types: [rawType],\n  }).get(rawType.name)\n}\n", "import getRandomValues from 'get-random-values-esm'\n\nexport const keyGenerator = (): string => randomKey(12)\n\nconst getByteHexTable = (() => {\n  let table: any[]\n  return () => {\n    if (table) {\n      return table\n    }\n\n    table = []\n    for (let i = 0; i < 256; ++i) {\n      table[i] = (i + 0x100).toString(16).slice(1)\n    }\n    return table\n  }\n})()\n\n// WHATWG crypto RNG - https://w3c.github.io/webcrypto/Overview.html\nfunction whatwgRNG(length = 16) {\n  const rnds8 = new Uint8Array(length)\n  getRandomValues(rnds8)\n  return rnds8\n}\n\nfunction randomKey(length?: number): string {\n  const table = getByteHexTable()\n  return whatwgRNG(length)\n    .reduce((str, n) => str + table[n], '')\n    .slice(0, length)\n}\n", "import type {SchemaDefinition} from '@portabletext/schema'\nimport {Schema as SanitySchema} from '@sanity/schema'\nimport {\n  defineField,\n  defineType,\n  isArraySchemaType,\n  isObjectSchemaType,\n  type ObjectField,\n  type ObjectSchemaType,\n} from '@sanity/types'\nimport startCase from 'lodash.startcase'\nimport {keyGenerator} from './key-generator'\nimport {\n  createPortableTextMemberSchemaTypes,\n  type PortableTextMemberSchemaTypes,\n} from './portable-text-member-schema-types'\n\nconst temporaryImageBlockObjectName = `tmp-${keyGenerator()}-image`\nconst temporaryUrlBlockObjectName = `tmp-${keyGenerator()}-url`\nconst temporaryImageInlineObjectName = `tmp-${keyGenerator()}-image`\nconst temporaryUrlInlineObjectName = `tmp-${keyGenerator()}-url`\n\nconst temporaryBlockObjectNames: Record<string, string> = {\n  image: temporaryImageBlockObjectName,\n  url: temporaryUrlBlockObjectName,\n}\n\nconst temporaryInlineObjectNames: Record<string, string> = {\n  image: temporaryImageInlineObjectName,\n  url: temporaryUrlInlineObjectName,\n}\n\nconst blockObjectNames: Record<string, string> = {\n  [temporaryImageBlockObjectName]: 'image',\n  [temporaryUrlBlockObjectName]: 'url',\n}\n\nconst inlineObjectNames: Record<string, string> = {\n  [temporaryImageInlineObjectName]: 'image',\n  [temporaryUrlInlineObjectName]: 'url',\n}\n\nconst defaultObjectTitles: Record<string, string> = {\n  image: 'Image',\n  url: 'URL',\n}\n\n/**\n * @public\n * Compile a Portable Text schema definition to Sanity-specific schema types for\n * Portable Text.\n */\nexport function compileSchemaDefinitionToPortableTextMemberSchemaTypes(\n  definition?: SchemaDefinition,\n): PortableTextMemberSchemaTypes {\n  const blockObjects =\n    definition?.blockObjects?.map((blockObject) =>\n      defineType({\n        type: 'object',\n        // Very naive way to work around `SanitySchema.compile` adding default\n        // fields to objects with certain names.\n        name: temporaryBlockObjectNames[blockObject.name] ?? blockObject.name,\n        title:\n          blockObject.title === undefined\n            ? // This avoids the default title which is a title case of the object name\n              defaultObjectTitles[blockObject.name]\n            : blockObject.title,\n        fields:\n          blockObject.fields?.map((field) => ({\n            name: field.name,\n            type: field.type,\n            title: field.title ?? startCase(field.name),\n          })) ?? [],\n      }),\n    ) ?? []\n\n  const inlineObjects =\n    definition?.inlineObjects?.map((inlineObject) =>\n      defineType({\n        type: 'object',\n        // Very naive way to work around `SanitySchema.compile` adding default\n        // fields to objects with certain names.\n        name:\n          temporaryInlineObjectNames[inlineObject.name] ?? inlineObject.name,\n\n        title:\n          inlineObject.title === undefined\n            ? // This avoids the default title which is a title case of the object name\n              defaultObjectTitles[inlineObject.name]\n            : inlineObject.title,\n        fields:\n          inlineObject.fields?.map((field) => ({\n            name: field.name,\n            type: field.type,\n            title: field.title ?? startCase(field.name),\n          })) ?? [],\n      }),\n    ) ?? []\n\n  const portableTextSchema = defineField({\n    type: 'array',\n    name: 'portable-text',\n    of: [\n      ...blockObjects.map((blockObject) => ({type: blockObject.name})),\n      {\n        type: 'block',\n        name: 'block',\n        of: inlineObjects.map((inlineObject) => ({type: inlineObject.name})),\n        marks: {\n          decorators:\n            definition?.decorators?.map((decorator) => ({\n              title: decorator.title ?? startCase(decorator.name),\n              value: decorator.name,\n            })) ?? [],\n          annotations:\n            definition?.annotations?.map((annotation) => ({\n              name: annotation.name,\n              type: 'object',\n              title: annotation.title,\n              fields:\n                annotation.fields?.map((field) => ({\n                  name: field.name,\n                  title: field.title ?? startCase(field.name),\n                  type: field.type,\n                })) ?? [],\n            })) ?? [],\n        },\n        lists:\n          definition?.lists?.map((list) => ({\n            value: list.name,\n            title: list.title ?? startCase(list.name),\n          })) ?? [],\n        styles:\n          definition?.styles?.map((style) => ({\n            value: style.name,\n            title: style.title ?? startCase(style.name),\n          })) ?? [],\n      },\n    ],\n  })\n\n  const schema = SanitySchema.compile({\n    types: [portableTextSchema, ...blockObjects, ...inlineObjects],\n  }).get('portable-text')\n\n  const pteSchema = createPortableTextMemberSchemaTypes(schema)\n\n  return {\n    ...pteSchema,\n    portableText: {\n      ...pteSchema.portableText,\n      of: pteSchema.portableText.of.map((schemaType) => {\n        if (!isObjectSchemaType(schemaType)) {\n          return schemaType\n        }\n\n        const nameMapping = blockObjectNames[schemaType.name]\n\n        return {\n          ...schemaType,\n          name: nameMapping ?? schemaType.name,\n          fields: schemaType.fields.map((field) => {\n            if (field.name !== 'children' || !isArraySchemaType(field.type)) {\n              return field\n            }\n\n            return {\n              ...field,\n              type: {\n                of: field.type.of.map((ofSchemaType) => {\n                  const nameMapping = inlineObjectNames[ofSchemaType.name]\n\n                  if (!nameMapping) {\n                    return ofSchemaType\n                  }\n\n                  return {\n                    ...ofSchemaType,\n                    name: nameMapping,\n                  }\n                }),\n              },\n            } as ObjectField\n          }),\n        }\n      }),\n    },\n    blockObjects: pteSchema.blockObjects.map((blockObject) =>\n      blockObjectNames[blockObject.name] !== undefined\n        ? ({\n            ...blockObject,\n            name: blockObjectNames[blockObject.name],\n            type: {\n              ...blockObject.type,\n              name: blockObjectNames[blockObject.name],\n            },\n          } as ObjectSchemaType)\n        : blockObject,\n    ),\n    inlineObjects: pteSchema.inlineObjects.map((inlineObject) =>\n      inlineObjectNames[inlineObject.name] !== undefined\n        ? ({\n            ...inlineObject,\n            name: inlineObjectNames[inlineObject.name],\n          } as ObjectSchemaType)\n        : inlineObject,\n    ),\n  } satisfies PortableTextMemberSchemaTypes\n}\n"], "names": ["schema", "SanitySchema", "getRandomValues", "defineType", "startCase", "defineField", "isObjectSchemaType", "isArraySchemaType", "nameMapping"], "mappings": ";;;;;;;AAiCO,SAAS,oCACd,kBAC+B;AAC/B,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,iDAAiD;AAEnE,QAAM,YAAY,iBAAiB,IAAI,KAAK,aAAa;AAGzD,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,qDAAqD;AAEvE,QAAM,gBAAgB,UAAU,QAAQ;AAAA,IACtC,CAAC,UAAU,MAAM,SAAS;AAAA,EAAA;AAE5B,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,0DAA0D;AAE5E,QAAM,SAAS,cAAc,KAAK;AAClC,MAAI,CAAC;AACH,UAAM,IAAI;AAAA,MACR;AAAA,IAAA;AAGJ,QAAM,WAAW,OAAO,KAAK,CAAC,eAAe,WAAW,SAAS,MAAM;AAGvE,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,0CAA0C;AAE5D,QAAM,oBAAqB,OAAO;AAAA,IAChC,CAAC,eAAe,WAAW,SAAS;AAAA,EAAA,KACjC,IACC,mBAAoB,iBAAiB,IAAI;AAAA,IAC7C,CAAC,UAAU,MAAM,SAAS,UAAU;AAAA,EAAA,KACjC,CAAA;AACL,SAAO;AAAA,IACL,QAAQ,qBAAqB,SAAS;AAAA,IACtC,YAAY,yBAAyB,QAAQ;AAAA,IAC7C,OAAO,wBAAwB,SAAS;AAAA,IACxC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,cAAc;AAAA,IACd,eAAe;AAAA,IACf,cAAc;AAAA,IACd,aAAc,SAA4B;AAAA,EAAA;AAE9C;AAEA,SAAS,qBAAqB,WAA6B;AACzD,QAAM,aAAa,UAAU,QAAQ;AAAA,IACnC,CAAC,YAAY,QAAQ,SAAS;AAAA,EAAA;AAEhC,MAAI,CAAC;AACH,UAAM,IAAI;AAAA,MACR;AAAA,IAAA;AAGJ,QAAM,aACJ,WAAW,KAAK,SAAS,QACzB,WAAW,KAAK,QAAQ,MAAM;AAAA,IAC5B,CAAC,UAA2B,MAAM;AAAA,EAAA;AAEtC,MAAI,CAAC,cAAc,WAAW,WAAW;AACvC,UAAM,IAAI;AAAA,MACR;AAAA,IAAA;AAIJ,SAAO;AACT;AAEA,SAAS,yBAAyB,UAA4B;AAC5D,SAAQ,SAAiB;AAC3B;AAEA,SAAS,wBAAwB,WAA6B;AAC5D,QAAM,YAAY,UAAU,QAAQ;AAAA,IAClC,CAAC,YAAY,QAAQ,SAAS;AAAA,EAAA;AAEhC,MAAI,CAAC;AACH,UAAM,IAAI;AAAA,MACR;AAAA,IAAA;AAGJ,QAAM,YACJ,UAAU,KAAK,SAAS,QACxB,UAAU,KAAK,QAAQ,KAAK,OAAO,CAAC,SAA0B,KAAK,KAAK;AAC1E,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,mDAAmD;AAErE,SAAO;AACT;AAEA,SAAS,cAAc,MAA0C;AAC/D,SAAI,KAAK,OACA,cAAc,KAAK,IAAI,IAG5B,KAAK,SAAS,UACT,OAGF;AACT;ACjIO,SAAS,sCACdA,SACQ;AACR,SAAO;AAAA,IACL,aAAaA,QAAO,YAAY,IAAI,CAAC,gBAAgB;AAAA,MACnD,MAAM,WAAW;AAAA,MACjB,QAAQ,WAAW,OAAO,IAAI,CAAC,WAAW;AAAA,QACxC,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM,KAAK;AAAA,QACjB,OAAO,MAAM,KAAK;AAAA,MAAA,EAClB;AAAA,MACF,OAAO,WAAW;AAAA,IAAA,EAClB;AAAA,IACF,OAAO;AAAA,MACL,MAAMA,QAAO,MAAM;AAAA,IAAA;AAAA,IAErB,cAAcA,QAAO,aAAa,IAAI,CAAC,iBAAiB;AAAA,MACtD,MAAM,YAAY;AAAA,MAClB,QAAQ,YAAY,OAAO,IAAI,CAAC,WAAW;AAAA,QACzC,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM,KAAK;AAAA,QACjB,OAAO,MAAM,KAAK;AAAA,MAAA,EAClB;AAAA,MACF,OAAO,YAAY;AAAA,IAAA,EACnB;AAAA,IACF,YAAYA,QAAO,WAAW,IAAI,CAAC,eAAe;AAAA,MAChD,MAAM,UAAU;AAAA,MAChB,OAAO,UAAU;AAAA,MACjB,OAAO,UAAU;AAAA,IAAA,EACjB;AAAA,IACF,eAAeA,QAAO,cAAc,IAAI,CAAC,kBAAkB;AAAA,MACzD,MAAM,aAAa;AAAA,MACnB,QAAQ,aAAa,OAAO,IAAI,CAAC,WAAW;AAAA,QAC1C,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM,KAAK;AAAA,QACjB,OAAO,MAAM,KAAK;AAAA,MAAA,EAClB;AAAA,MACF,OAAO,aAAa;AAAA,IAAA,EACpB;AAAA,IACF,MAAM;AAAA,MACJ,MAAMA,QAAO,KAAK;AAAA,IAAA;AAAA,IAEpB,QAAQA,QAAO,OAAO,IAAI,CAAC,WAAW;AAAA,MACpC,MAAM,MAAM;AAAA,MACZ,OAAO,MAAM;AAAA,MACb,OAAO,MAAM;AAAA,IAAA,EACb;AAAA,IACF,OAAOA,QAAO,MAAM,IAAI,CAAC,UAAU;AAAA,MACjC,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,IAAA,EACZ;AAAA,EAAA;AAEN;AChCO,SAAS,iCACd,cACQ;AACR,QAAM,gCAAgC;AAAA,IACpC,aAAa,eAAe,UAAU,IAClC,eACA,YAAY,YAAY;AAAA,EAAA;AAG9B,SAAO,sCAAsC,6BAA6B;AAC5E;AAEA,SAAS,YAAY,SAAc;AACjC,SAAOC,OAAAA,OAAa,QAAQ;AAAA,IAC1B,MAAM;AAAA,IACN,OAAO,CAAC,OAAO;AAAA,EAAA,CAChB,EAAE,IAAI,QAAQ,IAAI;AACrB;AC5CO,MAAM,eAAe,MAAc,UAAU,EAAE,GAEhD,kBAAmB,uBAAM;AAC7B,MAAI;AACJ,SAAO,MAAM;AACX,QAAI;AACF,aAAO;AAGT,YAAQ,CAAA;AACR,aAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACzB,YAAM,CAAC,KAAK,IAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC;AAE7C,WAAO;AAAA,EACT;AACF,GAAA;AAGA,SAAS,UAAU,SAAS,IAAI;AAC9B,QAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,SAAAC,yBAAAA,QAAgB,KAAK,GACd;AACT;AAEA,SAAS,UAAU,QAAyB;AAC1C,QAAM,QAAQ,gBAAA;AACd,SAAO,UAAU,MAAM,EACpB,OAAO,CAAC,KAAK,MAAM,MAAM,MAAM,CAAC,GAAG,EAAE,EACrC,MAAM,GAAG,MAAM;AACpB;ACdA,MAAM,gCAAgC,OAAO,aAAA,CAAc,UACrD,8BAA8B,OAAO,cAAc,QACnD,iCAAiC,OAAO,cAAc,UACtD,+BAA+B,OAAO,aAAA,CAAc,QAEpD,4BAAoD;AAAA,EACxD,OAAO;AAAA,EACP,KAAK;AACP,GAEM,6BAAqD;AAAA,EACzD,OAAO;AAAA,EACP,KAAK;AACP,GAEM,mBAA2C;AAAA,EAC/C,CAAC,6BAA6B,GAAG;AAAA,EACjC,CAAC,2BAA2B,GAAG;AACjC,GAEM,oBAA4C;AAAA,EAChD,CAAC,8BAA8B,GAAG;AAAA,EAClC,CAAC,4BAA4B,GAAG;AAClC,GAEM,sBAA8C;AAAA,EAClD,OAAO;AAAA,EACP,KAAK;AACP;AAOO,SAAS,uDACd,YAC+B;AAC/B,QAAM,eACJ,YAAY,cAAc;AAAA,IAAI,CAAC,gBAC7BC,MAAAA,WAAW;AAAA,MACT,MAAM;AAAA;AAAA;AAAA,MAGN,MAAM,0BAA0B,YAAY,IAAI,KAAK,YAAY;AAAA,MACjE,OACE,YAAY,UAAU;AAAA;AAAA,QAElB,oBAAoB,YAAY,IAAI;AAAA,UACpC,YAAY;AAAA,MAClB,QACE,YAAY,QAAQ,IAAI,CAAC,WAAW;AAAA,QAClC,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,OAAO,MAAM,SAASC,mBAAAA,QAAU,MAAM,IAAI;AAAA,MAAA,EAC1C,KAAK,CAAA;AAAA,IAAC,CACX;AAAA,EAAA,KACE,IAED,gBACJ,YAAY,eAAe;AAAA,IAAI,CAAC,iBAC9BD,MAAAA,WAAW;AAAA,MACT,MAAM;AAAA;AAAA;AAAA,MAGN,MACE,2BAA2B,aAAa,IAAI,KAAK,aAAa;AAAA,MAEhE,OACE,aAAa,UAAU;AAAA;AAAA,QAEnB,oBAAoB,aAAa,IAAI;AAAA,UACrC,aAAa;AAAA,MACnB,QACE,aAAa,QAAQ,IAAI,CAAC,WAAW;AAAA,QACnC,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,OAAO,MAAM,SAASC,mBAAAA,QAAU,MAAM,IAAI;AAAA,MAAA,EAC1C,KAAK,CAAA;AAAA,IAAC,CACX;AAAA,EAAA,KACE,CAAA,GAED,qBAAqBC,kBAAY;AAAA,IACrC,MAAM;AAAA,IACN,MAAM;AAAA,IACN,IAAI;AAAA,MACF,GAAG,aAAa,IAAI,CAAC,iBAAiB,EAAC,MAAM,YAAY,KAAA,EAAM;AAAA,MAC/D;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,IAAI,cAAc,IAAI,CAAC,kBAAkB,EAAC,MAAM,aAAa,KAAA,EAAM;AAAA,QACnE,OAAO;AAAA,UACL,YACE,YAAY,YAAY,IAAI,CAAC,eAAe;AAAA,YAC1C,OAAO,UAAU,SAASD,mBAAAA,QAAU,UAAU,IAAI;AAAA,YAClD,OAAO,UAAU;AAAA,UAAA,EACjB,KAAK,CAAA;AAAA,UACT,aACE,YAAY,aAAa,IAAI,CAAC,gBAAgB;AAAA,YAC5C,MAAM,WAAW;AAAA,YACjB,MAAM;AAAA,YACN,OAAO,WAAW;AAAA,YAClB,QACE,WAAW,QAAQ,IAAI,CAAC,WAAW;AAAA,cACjC,MAAM,MAAM;AAAA,cACZ,OAAO,MAAM,SAASA,mBAAAA,QAAU,MAAM,IAAI;AAAA,cAC1C,MAAM,MAAM;AAAA,YAAA,EACZ,KAAK,CAAA;AAAA,UAAC,EACV,KAAK,CAAA;AAAA,QAAC;AAAA,QAEZ,OACE,YAAY,OAAO,IAAI,CAAC,UAAU;AAAA,UAChC,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK,SAASA,mBAAAA,QAAU,KAAK,IAAI;AAAA,QAAA,EACxC,KAAK,CAAA;AAAA,QACT,QACE,YAAY,QAAQ,IAAI,CAAC,WAAW;AAAA,UAClC,OAAO,MAAM;AAAA,UACb,OAAO,MAAM,SAASA,mBAAAA,QAAU,MAAM,IAAI;AAAA,QAAA,EAC1C,KAAK,CAAA;AAAA,MAAC;AAAA,IACZ;AAAA,EACF,CACD,GAEKJ,WAASC,OAAAA,OAAa,QAAQ;AAAA,IAClC,OAAO,CAAC,oBAAoB,GAAG,cAAc,GAAG,aAAa;AAAA,EAAA,CAC9D,EAAE,IAAI,eAAe,GAEhB,YAAY,oCAAoCD,QAAM;AAE5D,SAAO;AAAA,IACL,GAAG;AAAA,IACH,cAAc;AAAA,MACZ,GAAG,UAAU;AAAA,MACb,IAAI,UAAU,aAAa,GAAG,IAAI,CAAC,eAAe;AAChD,YAAI,CAACM,MAAAA,mBAAmB,UAAU;AAChC,iBAAO;AAGT,cAAM,cAAc,iBAAiB,WAAW,IAAI;AAEpD,eAAO;AAAA,UACL,GAAG;AAAA,UACH,MAAM,eAAe,WAAW;AAAA,UAChC,QAAQ,WAAW,OAAO,IAAI,CAAC,UACzB,MAAM,SAAS,cAAc,CAACC,MAAAA,kBAAkB,MAAM,IAAI,IACrD,QAGF;AAAA,YACL,GAAG;AAAA,YACH,MAAM;AAAA,cACJ,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB;AACtC,sBAAMC,eAAc,kBAAkB,aAAa,IAAI;AAEvD,uBAAKA,eAIE;AAAA,kBACL,GAAG;AAAA,kBACH,MAAMA;AAAAA,gBAAA,IALC;AAAA,cAOX,CAAC;AAAA,YAAA;AAAA,UACH,CAEH;AAAA,QAAA;AAAA,MAEL,CAAC;AAAA,IAAA;AAAA,IAEH,cAAc,UAAU,aAAa;AAAA,MAAI,CAAC,gBACxC,iBAAiB,YAAY,IAAI,MAAM,SAClC;AAAA,QACC,GAAG;AAAA,QACH,MAAM,iBAAiB,YAAY,IAAI;AAAA,QACvC,MAAM;AAAA,UACJ,GAAG,YAAY;AAAA,UACf,MAAM,iBAAiB,YAAY,IAAI;AAAA,QAAA;AAAA,MACzC,IAEF;AAAA,IAAA;AAAA,IAEN,eAAe,UAAU,cAAc;AAAA,MAAI,CAAC,iBAC1C,kBAAkB,aAAa,IAAI,MAAM,SACpC;AAAA,QACC,GAAG;AAAA,QACH,MAAM,kBAAkB,aAAa,IAAI;AAAA,MAAA,IAE3C;AAAA,IAAA;AAAA,EACN;AAEJ;;;;;"}