{"version": 3, "file": "util.merge-text-blocks.js", "sources": ["../../src/utils/util.is-text-block.ts", "../../src/utils/util.merge-text-blocks.ts"], "sourcesContent": ["import type {PortableTextTextBlock} from '@sanity/types'\nimport type {EditorContext} from '..'\nimport {isTypedObject} from '../internal-utils/asserters'\n\n/**\n * @public\n */\nexport function isTextBlock(\n  context: Pick<EditorContext, 'schema'>,\n  block: unknown,\n): block is PortableTextTextBlock {\n  return isTypedObject(block) && block._type === context.schema.block.name\n}\n", "import type {PortableTextTextBlock} from '@sanity/types'\nimport type {EditorContext} from '..'\nimport {parseBlock} from '../internal-utils/parse-blocks'\nimport {isTextBlock} from './util.is-text-block'\n\n/**\n * @beta\n */\nexport function mergeTextBlocks({\n  context,\n  targetBlock,\n  incomingBlock,\n}: {\n  context: Pick<EditorContext, 'keyGenerator' | 'schema'>\n  targetBlock: PortableTextTextBlock\n  incomingBlock: PortableTextTextBlock\n}) {\n  const parsedIncomingBlock = parseBlock({\n    context,\n    block: incomingBlock,\n    options: {refreshKeys: false, validateFields: false},\n  })\n\n  if (!parsedIncomingBlock || !isTextBlock(context, parsedIncomingBlock)) {\n    return targetBlock\n  }\n\n  return {\n    ...targetBlock,\n    children: [...targetBlock.children, ...parsedIncomingBlock.children],\n    markDefs: [\n      ...(targetBlock.markDefs ?? []),\n      ...(parsedIncomingBlock.markDefs ?? []),\n    ],\n  }\n}\n"], "names": ["isTextBlock", "context", "block", "isTypedObject", "_type", "schema", "name", "mergeTextBlocks", "targetBlock", "incomingBlock", "parsedIncomingBlock", "parseBlock", "options", "refreshKeys", "validateFields", "children", "markDefs"], "mappings": ";AAOO,SAASA,YACdC,SACAC,OACgC;AAChC,SAAOC,cAAcD,KAAK,KAAKA,MAAME,UAAUH,QAAQI,OAAOH,MAAMI;AACtE;ACJO,SAASC,gBAAgB;AAAA,EAC9BN;AAAAA,EACAO;AAAAA,EACAC;AAKF,GAAG;AACD,QAAMC,sBAAsBC,WAAW;AAAA,IACrCV;AAAAA,IACAC,OAAOO;AAAAA,IACPG,SAAS;AAAA,MAACC,aAAa;AAAA,MAAOC,gBAAgB;AAAA,IAAA;AAAA,EAAK,CACpD;AAED,SAAI,CAACJ,uBAAuB,CAACV,YAAYC,SAASS,mBAAmB,IAC5DF,cAGF;AAAA,IACL,GAAGA;AAAAA,IACHO,UAAU,CAAC,GAAGP,YAAYO,UAAU,GAAGL,oBAAoBK,QAAQ;AAAA,IACnEC,UAAU,CACR,GAAIR,YAAYQ,YAAY,CAAA,GAC5B,GAAIN,oBAAoBM,YAAY,CAAA,CAAG;AAAA,EAAA;AAG7C;"}