/**
 * @beta
 * A keyboard event definition that can be used to create a keyboard shortcut.
 *
 * At least one of `key` or `code` must be provided while the `alt`, `ctrl`,
 * `meta`, and `shift` modifier configurations are optional.
 *
 * The `key` represents a https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key
 * and is treated as case-insensitive.
 *
 * The `code` represents a https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/code
 * and is treated as case-insensitive.
 *
 * @example
 * ```typescript
 * const boldEvent: KeyboardEventDefinition = {
 *   key: 'B',
 *   alt: false,
 *   ctrl: true,
 *   meta: false,
 *   shift: false,
 * }
 * ```
 */
type KeyboardEventDefinition = ({
  key: KeyboardEvent['key'];
  code: KeyboardEvent['code'];
} | {
  key: KeyboardEvent['key'];
  code?: undefined;
} | {
  key?: undefined;
  code: KeyboardEvent['code'];
}) & {
  alt?: KeyboardEvent['altKey'];
  ctrl?: KeyboardEvent['ctrlKey'];
  meta?: KeyboardEvent['metaKey'];
  shift?: KeyboardEvent['shiftKey'];
};
/**
 * @beta
 * Definition of a keyboard shortcut with platform-specific keyboard event
 * definitions.
 *
 * `default` keyboard event definitions are required while the `apple`
 * keyboard event definitions are optional.
 *
 * @example
 * ```typescript
 * const boldShortcut: KeyboardShortcutDefinition = {
 *   default: [{
 *     key: 'B',
 *     alt: false,
 *     ctrl: true,
 *     meta: false,
 *     shift: false,
 *   }],
 *   apple: [{
 *     key: 'B',
 *     alt: false,
 *     ctrl: false,
 *     meta: true,
 *     shift: false,
 *   }],
 * }
 * ```
 */
type KeyboardShortcutDefinition = {
  default: ReadonlyArray<KeyboardEventDefinition>;
  apple?: ReadonlyArray<KeyboardEventDefinition>;
};
/**
 * @beta
 * A resolved keyboard shortcut for the current platform that has been
 * processed by `createKeyboardShortcut(...)` to select the appropriate
 * platform-specific key combination. The `guard` function determines if the
 * shortcut applies to the current `KeyboardEvent`, while `keys` contains the
 * display-friendly key combination for the current platform.
 */
type KeyboardShortcut<TKeyboardEvent extends Pick<KeyboardEvent, 'key' | 'code' | 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey'> = Pick<KeyboardEvent, 'key' | 'code' | 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey'>> = {
  guard: (event: TKeyboardEvent) => boolean;
  keys: ReadonlyArray<string>;
};
/**
 * @beta
 * Creates a `KeyboardShortcut` from a `KeyboardShortcutDefinition`.
 *
 * `default` keyboard event definitions are required while the `apple`
 * keyboard event definitions are optional.
 *
 * @example
 * ```typescript
 * const shortcut = createKeyboardShortcut({
 *   default: [{
 *     key: 'B',
 *     alt: false,
 *     ctrl: true,
 *     meta: false,
 *     shift: false,
 *   }],
 *   apple: [{
 *     key: 'B',
 *     alt: false,
 *     ctrl: false,
 *     meta: true,
 *     shift: false,
 *   }],
 * })
 * ```
 */
declare function createKeyboardShortcut<TKeyboardEvent extends Pick<KeyboardEvent, 'key' | 'code' | 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey'> = Pick<KeyboardEvent, 'key' | 'code' | 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey'>>(definition: KeyboardShortcutDefinition): KeyboardShortcut<TKeyboardEvent>;
/**
 * @beta
 */
declare const bold: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const italic: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const code: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const underline: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const strikeThrough: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const link: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const normal: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const h1: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const h2: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const h3: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const h4: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const h5: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const h6: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const blockquote: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const undo: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
/**
 * @beta
 */
declare const redo: KeyboardShortcut<Pick<KeyboardEvent, "key" | "code" | "altKey" | "ctrlKey" | "metaKey" | "shiftKey">>;
export { KeyboardEventDefinition, KeyboardShortcut, KeyboardShortcutDefinition, blockquote, bold, code, createKeyboardShortcut, h1, h2, h3, h4, h5, h6, italic, link, normal, redo, strikeThrough, underline, undo };